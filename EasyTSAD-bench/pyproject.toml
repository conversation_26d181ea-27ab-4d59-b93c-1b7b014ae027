[tool.poetry]
name = "EasyTSAD"
version = "*******"
description = "A Suite for TimeSeries Anomaly Detection Benchmark"
authors = ["HT Si <<EMAIL>>"]
license = "GPL-3.0"
readme = "README.md"
packages = [{include = "EasyTSAD"}]

[tool.poetry.dependencies]
python = ">=3.9 <3.13"
toml = "^0.10.2"
numpy = "^1.26.1"
matplotlib = "^3.8.0"
tqdm = "^4.66.1"
scikit-learn = "^1.3.2"
pandas = "^2.1.2"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
