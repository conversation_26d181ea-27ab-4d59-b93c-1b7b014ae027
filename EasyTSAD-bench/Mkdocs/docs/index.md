# Introduction
EasyTSAD is a suite to facilitate the quick implementation and iteration of your ***time series anomaly detection algorithms***. You can also easily develop a new set of ***evaluation metrics*** based on this suite and assess them against baseline methods. 

We welcome you to send the algorithm code implemented based on this suite to our email. We will integrate your method into the algorithm library, making it convenient for researchers and practitioners to utilize your approach or evaluation protocol.

# Features
### For Algorithm Researches
- Flexible interface for algorithm implementation, training and testing new algorithms on naive, all-in-one and zero-shot training schemas.
- Full pipeline for load dataset, run experiments, do evaluations and analysis(e.g. plots and compares anomaly scores, or generate CSVs for intuitional comparison) the performance of methods.
- Diversity evaluation protocols for preformance evaluations.

### For Evaluation Researches
- Flexible interface for evaluation protocol implementation based on anomaly scores and ground truth labels.
- Easily perform evaluations on existing methods according to your protocol. 
- Evaluation based on offline scores of methods, which generated by merely once training and test phase.

### For Practitioners of Community or Enterprise
- Unified and clear Datasets format, easy for introduction of private datasets.
- Easy performance comparison of baselines on your dataset. E.g. *Overall performance in CSV format* based on protocols suitable for your applications; *Plots of all methods* on specify curves.
- Record runtime statistics (e.g. model parameter size, inference time) for performance, cost, and efficiency trade-off.
- An Evaluation protocol designed for real-time AD scenarios (EasyTSAD.Evaluations.Protocols.EventF1PA, for details please refer to our paper).

### Leaderboard Representation
- We provide a continuous integrated leaderboard (https://adeval.cstcloud.cn/content/home) based on this suite and make it vivid to show state-of-the-art algorithms rankings based on various training schemas and evaluation protocols. 
- ***Welcome to provide us your algorithms or evaluation criterion based on this suite by e-mails.*** We will add it into the leaderboard after checking, running, and obtaining your permission.