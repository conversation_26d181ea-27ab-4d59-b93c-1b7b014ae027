[Data_Params]
preprocess = "z-score"

[Model_Params.Default]
  is_training = 1
  features = 'M' 
  seq_len = 100
  pred_len = 0
  d_model = 768
  d_ff = 768
  gpt_layers = 6
  enc_in = 1
  c_out = 1
  anomaly_ratio = 0.5
  batch_size = 128
  patch_size = 1
  stride = 1
  learning_rate = 0.0001
  epochs = 20
  embed = "timeF"
  freq = "h"
  dropout = 0.1
  mlp = 0
  patience = 3
  model_path = "pre_train"
  lradj = "type1"
