from typing import Dict
import torchinfo
import tqdm
from ...DataFactory import <PERSON><PERSON>ata
from ...Exptools import EarlyStoppingTorch
from .. import BaseMethod
import numpy as np
import torch
from torch import nn, optim
from torch.utils.data import DataLoader

from ...DataFactory.TorchDataSet.ReconstructWindow import UTSAllInOneDataset, UTSOneByOneDataset

SOS_token = 0

class LSTMModel(nn.Module):
    def __init__(self, window_size, input_size, 
                 hidden_dim, num_layers, batch_size, device) -> None:
        super().__init__()
        self.batch_size = batch_size
        self.input_size = input_size
        self.window_size = window_size
        self.device = device
        
        self.lstm_encoder = nn.LSTM(input_size=input_size, hidden_size=hidden_dim, num_layers=num_layers, batch_first=True)
        self.lstm_decoder = nn.LSTM(input_size=input_size, hidden_size=hidden_dim, num_layers=num_layers, batch_first=True)
        
        self.relu = nn.ReLU()
        self.fc = nn.Linear(hidden_dim, input_size)
        
    def forward(self, src, phase="train"):
        src = torch.unsqueeze(src, -1)
        _, decoder_hidden = self.lstm_encoder(src)
        cur_batch = src.shape[0]
        
        if phase == "test":
        # decoder_input = torch.zeros(cur_batch, 1, self.input_size).to(self.device)
            decoder_input = src[:, -1, :]
            decoder_input = torch.unsqueeze(decoder_input, dim=-2)
            outputs = torch.zeros(self.window_size, cur_batch, self.input_size).to(self.device)
            
            for t in range(self.window_size):
                decoder_output, decoder_hidden = self.lstm_decoder(decoder_input, decoder_hidden)
                decoder_output = self.relu(decoder_output)
                decoder_input = self.fc(decoder_output)
                
                outputs[self.window_size - t - 1] = torch.squeeze(decoder_input, dim=-2)
                
            return outputs
        
        else:
            src_td = torch.flip(src, (1,))
            decoder_output, _ = self.lstm_decoder(src_td, decoder_hidden)
            decoder_output = self.relu(decoder_output)
            recon_x = self.fc(decoder_output)
            recon_x = torch.flip(recon_x, (1,))
            recon_x = torch.transpose(recon_x, 0, 1)
            
            return recon_x
    
class EncDecAD(BaseMethod):
    def __init__(self, params:dict) -> None:
        super().__init__()
        self.__anomaly_score = None
        
        cuda = True
        self.y_hats = None
        
        self.cuda = cuda
        if self.cuda == True and torch.cuda.is_available():
            self.device = torch.device("cuda")
            print("=== Using CUDA ===")
        else:
            if self.cuda == True and not torch.cuda.is_available():
                print("=== CUDA is unavailable ===")
            self.device = torch.device("cpu")
            print("=== Using CPU ===")
        
        self.window_size = params["window_size"]
        self.batch_size = params["batch_size"]
        self.epochs = params["epochs"]
        
        input_size = params["input_size"]
        hidden_dim = params["hidden_dim"]
        num_layer = params["num_layer"]
        lr = params["lr"]
        
        self.model = LSTMModel(self.window_size, input_size, hidden_dim, num_layer, batch_size=self.batch_size, device=self.device).to(self.device)
        
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr)
        self.scheduler = optim.lr_scheduler.StepLR(self.optimizer, step_size=5, gamma=0.75)
        self.loss = nn.MSELoss()
        self.save_path = None
        self.early_stopping = EarlyStoppingTorch(save_path=self.save_path, patience=3)
        
        self.mu = None
        self.sigma = None
        self.eps = 1e-10
        
    def train_valid_phase(self, tsTrain: TSData):
        train_loader = DataLoader(
            UTSOneByOneDataset(tsTrain, phase="train", 
                               window_size=self.window_size),
            batch_size=self.batch_size,
            shuffle=True
        )
        
        valid_loader = DataLoader(
            UTSOneByOneDataset(tsTrain, phase="valid", 
                               window_size=self.window_size),
            batch_size=self.batch_size,
            shuffle=False
        )
        
        for epoch in range(1, self.epochs + 1):
            self.model.train(mode=True)
            avg_loss = 0
            loop = tqdm.tqdm(enumerate(train_loader),total=len(train_loader),leave=True)
            for idx, (x, target) in loop:
                x, target = x.to(self.device), target.to(self.device)
                self.optimizer.zero_grad()
                
                output = self.model(x)
                
                output = torch.transpose(output, 0, 1)
                output = torch.squeeze(output, -1)
                
                loss = self.loss(output, target)
                loss.backward()

                self.optimizer.step()
                
                avg_loss += loss.cpu().item()
                loop.set_description(f'Training Epoch [{epoch}/{self.epochs}]')
                loop.set_postfix(loss=loss.item(), avg_loss=avg_loss/(idx+1))
            
            
            self.model.eval()
            scores = []
            avg_loss = 0
            loop = tqdm.tqdm(enumerate(valid_loader),total=len(valid_loader),leave=True)
            with torch.no_grad():
                for idx, (x, target) in loop:
                    x, target = x.to(self.device), target.to(self.device)
                    output = self.model(x)
                    
                    output = torch.transpose(output, 0, 1)
                    output = torch.squeeze(output, -1)
                    
                    loss = self.loss(output, target)
                    avg_loss += loss.cpu().item()
                    loop.set_description(f'Validation Epoch [{epoch}/{self.epochs}]')
                    loop.set_postfix(loss=loss.item(), avg_loss=avg_loss/(idx+1))
                    
                    mse = torch.sub(output, target).pow(2)
                    scores.append(mse[:,-1].cpu())
                    
            
            valid_loss = avg_loss/max(len(valid_loader), 1)
            self.scheduler.step()
            
            self.early_stopping(valid_loss, self.model)
            if self.early_stopping.early_stop or epoch == self.epochs - 1:
                # fitting Gaussian Distribution
                if len(scores) > 0:
                    scores = torch.cat(scores, dim=0)
                    self.mu = torch.mean(scores)
                    self.sigma = torch.var(scores)
                    print(self.mu.size(), self.sigma.size())
                if self.early_stopping.early_stop:
                    print("   Early stopping<<<")
                break

    def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):
        train_loader = DataLoader(
            UTSAllInOneDataset(tsTrains, phase="train", 
                               window_size=self.window_size),
            batch_size=self.batch_size,
            shuffle=True
        )
        
        valid_loader = DataLoader(
            UTSAllInOneDataset(tsTrains, phase="valid", 
                               window_size=self.window_size),
            batch_size=self.batch_size,
            shuffle=False
        )
        
        for epoch in range(1, self.epochs + 1):
            self.model.train(mode=True)
            avg_loss = 0
            loop = tqdm.tqdm(enumerate(train_loader),total=len(train_loader),leave=True)
            for idx, (x, target) in loop:
                x, target = x.to(self.device), target.to(self.device)
                self.optimizer.zero_grad()
                
                output = self.model(x)
                
                output = torch.transpose(output, 0, 1)
                output = torch.squeeze(output, -1)

                loss = self.loss(output, target)
                loss.backward()

                self.optimizer.step()
                
                avg_loss += loss.cpu().item()
                loop.set_description(f'Training Epoch [{epoch}/{self.epochs}]')
                loop.set_postfix(loss=loss.item(), avg_loss=avg_loss/(idx+1))
            
            
            self.model.eval()
            avg_loss = 0
            scores = []
            loop = tqdm.tqdm(enumerate(valid_loader),total=len(valid_loader),leave=True)
            with torch.no_grad():
                for idx, (x, target) in loop:
                    x, target = x.to(self.device), target.to(self.device)
                    output = self.model(x)
                    
                    output = torch.transpose(output, 0, 1)
                    output = torch.squeeze(output, -1)
                    
                    loss = self.loss(output, target)
                    avg_loss += loss.cpu().item()
                    loop.set_description(f'Validation Epoch [{epoch}/{self.epochs}]')
                    loop.set_postfix(loss=loss.item(), avg_loss=avg_loss/(idx+1))
                    
                    mse = torch.sub(output, target).pow(2)
                    scores.append(mse[:,-1].cpu())
                    
            
            valid_loss = avg_loss/max(len(valid_loader), 1)
            self.scheduler.step()
            
            self.early_stopping(valid_loss, self.model)
            if self.early_stopping.early_stop or epoch == self.epochs - 1:
                # fitting Gaussian Distribution
                scores = torch.cat(scores, dim=0)
                self.mu = torch.mean(scores)
                self.sigma = torch.var(scores)
                print(self.mu.size(), self.sigma.size())
                if self.early_stopping.early_stop:
                    print("   Early stopping<<<")
                break

    def test_phase(self, tsData: TSData):
        test_loader = DataLoader(
            UTSOneByOneDataset(tsData, phase="test", 
                               window_size=self.window_size),
            batch_size=self.batch_size,
            shuffle=False
        )
        
        self.model.eval()
        scores = []
        y_hats = []
        loop = tqdm.tqdm(enumerate(test_loader),total=len(test_loader),leave=True)
        with torch.no_grad():
            for idx, (x, target) in loop:
                x, target = x.to(self.device), target.to(self.device)
                output = self.model(x, "test")
                
                output = torch.transpose(output, 0, 1)
                output = torch.squeeze(output, -1)

                mse = torch.sub(output, target).pow(2)
                y_hats.append(output[:,-1].cpu())
                scores.append(mse[:,-1].cpu())
                loop.set_description(f'Testing: ')

        scores = torch.cat(scores, dim=0)
        # scores = 0.5 * (torch.log(self.sigma + self.eps) + (scores - self.mu)**2 / (self.sigma+self.eps))
        
        scores = scores.numpy().flatten()
        y_hats = torch.cat(y_hats, dim=0)
        y_hats = y_hats.numpy().flatten()

        assert scores.ndim == 1
        self.__anomaly_score = scores
        self.y_hats = y_hats
        
    def anomaly_score(self) -> np.ndarray:
        return self.__anomaly_score
    
    def get_y_hat(self) -> np.ndarray:
        return self.y_hats
    
    def param_statistic(self, save_file):
        model_stats = torchinfo.summary(self.model, (self.batch_size, self.window_size), verbose=0)
        with open(save_file, 'w') as f:
            f.write(str(model_stats))