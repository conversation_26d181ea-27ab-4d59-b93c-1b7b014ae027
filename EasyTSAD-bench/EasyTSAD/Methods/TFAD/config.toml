[Data_Params]
preprocess = "z-score"

[Model_Params.Default]
 "epochs"= 1000
 ts_channels = 1
 "limit_val_batches"= 1.0
 "num_sanity_val_steps"= 0
 "injection_method"= "local_outliers"
 "ratio_injected_spikes"= 0.01176
 "window_length"= 150
 "suspect_window_length"= 12
 "validation_portion"= 0.3
 "train_split_method"= "past_future_with_warmup"
 "num_series_in_train_batch"= 8
 "num_crops_per_series"= 16
 "rate_true_anomalies"= 0.0
 "num_workers_loader"= 0
 "tcn_kernel_size"= 7
 "tcn_layers"= 4
 "tcn_out_channels"= 35
 "tcn_maxpool_out_channels"= 50
 "embedding_rep_dim"= 40
 "normalize_embedding"= true
 "distance"= "L2"
 "classifier_threshold"= 0.5
 "threshold_grid_length_val"= 0.05
 "threshold_grid_length_test"= 0.02
 "coe_rate"= 0.6
 "mixup_rate"= 0.4
 "fft_sea_rate"= 0.1
 "fft_noise_rate"= 0.1
 "learning_rate"= 0.0005
 "check_val_every_n_epoch"= 50
 "stride_roll_pred_val_test"= 1
 "val_labels_adj"= true
 "test_labels_adj"= true
 "max_windows_unfold_batch"= 5000
 "rnd_seed"= 123
 label_reduction_method= "any"

[Model_Params.AIOPS]
 "epochs"= 1000
 ts_channels = 1
 "limit_val_batches"= 1.0
 "num_sanity_val_steps"= 0
 "injection_method"= "local_outliers"
 "ratio_injected_spikes"= 0.01176
 "window_length"= 2000
 "suspect_window_length"= 12
 "validation_portion"= 0.3
 "train_split_method"= "past_future_with_warmup"
 "num_series_in_train_batch"= 4
 "num_crops_per_series"= 18
 "rate_true_anomalies"= 0.0
 "num_workers_loader"= 2
 "tcn_kernel_size"= 8
 "tcn_layers"= 9
 "tcn_out_channels"= 22
 "tcn_maxpool_out_channels"= 6
 "embedding_rep_dim"= 40
 "normalize_embedding"= true
 "distance"= "L2"
 "classifier_threshold"= 0.5
 "threshold_grid_length_val"= 0.05
 "threshold_grid_length_test"= 0.02
 "coe_rate"= 0.345
 "mixup_rate"= 0.5
 "fft_sea_rate"= 0.5
 "fft_noise_rate"= 0.5
 "learning_rate"= 0.00166
 "check_val_every_n_epoch"= 50
 "stride_roll_pred_val_test"= 10
 "val_labels_adj"= true
 "test_labels_adj"= true
 "max_windows_unfold_batch"= 5000
 "rnd_seed"= 123
 label_reduction_method= "any"

[Model_Params.Yahoo]
 "epochs"= 100
 ts_channels = 1
 "limit_val_batches"= 1.0
 "num_sanity_val_steps"= 0
 "injection_method"= "local_outliers"
 "ratio_injected_spikes"= 0.01176
 "window_length"= 150
 "suspect_window_length"= 1
 "validation_portion"= 0.3
 "train_split_method"= "past_future_with_warmup"
 "num_series_in_train_batch"= 8
 "num_crops_per_series"= 16
 "rate_true_anomalies"= 0.0
 "num_workers_loader"= 2
 "tcn_kernel_size"= 7
 "tcn_layers"= 4
 "tcn_out_channels"= 35
 "tcn_maxpool_out_channels"= 50
 "embedding_rep_dim"= 198
 "normalize_embedding"= true
 "distance"= "L2"
 "classifier_threshold"= 0.5
 "threshold_grid_length_val"= 0.05
 "threshold_grid_length_test"= 0.02
 "coe_rate"= 0.6
 "mixup_rate"= 0.4
 "fft_sea_rate"= 0.1
 "fft_noise_rate"= 0.1
 "learning_rate"= 0.000436
 "check_val_every_n_epoch"= 25
 "stride_roll_pred_val_test"= 1
 "val_labels_adj"= true
 "test_labels_adj"= true
 "max_windows_unfold_batch"= 5000
 "rnd_seed"= 123
 label_reduction_method= "any"