{"cells": [{"cell_type": "code", "execution_count": 1, "id": "f15d37d2", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../../\")\n", "from EasyTSAD.Controller import TSADController"]}, {"cell_type": "code", "execution_count": 2, "id": "c4b46faa", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-07-29 15:00:24,624) [INFO]: \n", "                         \n", "███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ \n", "██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗\n", "█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║\n", "██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║\n", "███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝\n", "╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ \n", "                                                                      \n", "                         \n", "(2025-07-29 15:00:24,626) [INFO]: Dataset Directory has been loaded.\n", "(2025-07-29 15:00:26,279) [INFO]: Run Experiments. Method[AR], <PERSON><PERSON><PERSON>[naive].\n", "(2025-07-29 15:00:26,280) [INFO]:     Use Default Method Config. Path: /data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/EasyTSAD/Methods/AR/config.toml\n", "(2025-07-29 15:00:26,281) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-07-29 15:00:26,447) [INFO]:     [AR] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 250.77it/s, avg_loss=0.00967, loss=0.000575]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 327.22it/s, avg_loss=0.000718, loss=0.00055] \n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 254.80it/s, avg_loss=0.000613, loss=0.000549]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 299.38it/s, avg_loss=0.000704, loss=0.000539]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 258.01it/s, avg_loss=0.000597, loss=0.000307]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 312.64it/s, avg_loss=0.000683, loss=0.000525]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 265.49it/s, avg_loss=0.000572, loss=0.000557]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 329.53it/s, avg_loss=0.000648, loss=0.000498]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 420.07it/s]\n", "(2025-07-29 15:00:54,294) [INFO]:     [AR] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 692/692 [00:02<00:00, 265.78it/s, avg_loss=0.0211, loss=3.78e-5] \n", "Validation Epoch [1/100]: 100%|██████████| 173/173 [00:00<00:00, 321.45it/s, avg_loss=2.44e-5, loss=7.04e-6] \n", "Training Epoch [2/100]: 100%|██████████| 692/692 [00:02<00:00, 256.08it/s, avg_loss=0.000129, loss=0.00018] \n", "Validation Epoch [2/100]: 100%|██████████| 173/173 [00:00<00:00, 310.48it/s, avg_loss=2.46e-5, loss=7.19e-6] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 692/692 [00:02<00:00, 262.87it/s, avg_loss=0.000129, loss=0.000128]\n", "Validation Epoch [3/100]: 100%|██████████| 173/173 [00:00<00:00, 327.30it/s, avg_loss=2.44e-5, loss=6.99e-6] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 692/692 [00:02<00:00, 265.81it/s, avg_loss=0.000129, loss=1.74e-5] \n", "Validation Epoch [4/100]: 100%|██████████| 173/173 [00:00<00:00, 332.99it/s, avg_loss=2.43e-5, loss=6.97e-6] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 865/865 [00:02<00:00, 426.61it/s]\n", "(2025-07-29 15:01:13,472) [INFO]:     [AR] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 245.77it/s, avg_loss=0.017, loss=0.000234] \n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 302.45it/s, avg_loss=0.000408, loss=0.000407]\n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 251.32it/s, avg_loss=0.00038, loss=0.00023]  \n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 306.36it/s, avg_loss=0.000405, loss=0.000404]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 258.57it/s, avg_loss=0.000375, loss=0.000249]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 316.92it/s, avg_loss=0.000399, loss=0.000398]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 265.48it/s, avg_loss=0.000368, loss=0.000557]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 317.01it/s, avg_loss=0.000392, loss=0.00039] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 431.38it/s]\n", "(2025-07-29 15:01:39,852) [INFO]:     [AR] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 257.34it/s, avg_loss=0.0452, loss=0.000132]\n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 328.67it/s, avg_loss=3.75e-5, loss=4.78e-5] \n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 251.65it/s, avg_loss=6.76e-5, loss=5.03e-5] \n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 297.00it/s, avg_loss=3.74e-5, loss=4.78e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 259.11it/s, avg_loss=6.76e-5, loss=4.83e-5] \n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 327.95it/s, avg_loss=3.74e-5, loss=4.77e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 258.02it/s, avg_loss=6.75e-5, loss=4.17e-5] \n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 332.06it/s, avg_loss=3.73e-5, loss=4.76e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 424.16it/s]\n", "(2025-07-29 15:02:03,095) [INFO]:     [AR] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 56/56 [00:00<00:00, 272.49it/s, avg_loss=0.256, loss=0.0855]\n", "Validation Epoch [1/100]: 100%|██████████| 14/14 [00:00<00:00, 330.34it/s, avg_loss=0.0795, loss=0.0794]\n", "Training Epoch [2/100]: 100%|██████████| 56/56 [00:00<00:00, 271.55it/s, avg_loss=0.0289, loss=0.00565]\n", "Validation Epoch [2/100]: 100%|██████████| 14/14 [00:00<00:00, 340.89it/s, avg_loss=0.00438, loss=0.00409]\n", "Training Epoch [3/100]: 100%|██████████| 56/56 [00:00<00:00, 274.30it/s, avg_loss=0.00188, loss=0.00078] \n", "Validation Epoch [3/100]: 100%|██████████| 14/14 [00:00<00:00, 342.30it/s, avg_loss=0.000634, loss=0.000303]\n", "Training Epoch [4/100]: 100%|██████████| 56/56 [00:00<00:00, 267.24it/s, avg_loss=0.000932, loss=0.000752]\n", "Validation Epoch [4/100]: 100%|██████████| 14/14 [00:00<00:00, 320.65it/s, avg_loss=0.000597, loss=0.000262]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 56/56 [00:00<00:00, 275.69it/s, avg_loss=0.000927, loss=0.000912]\n", "Validation Epoch [5/100]: 100%|██████████| 14/14 [00:00<00:00, 336.24it/s, avg_loss=0.000597, loss=0.000262]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 56/56 [00:00<00:00, 265.94it/s, avg_loss=0.00093, loss=0.00139]  \n", "Validation Epoch [6/100]: 100%|██████████| 14/14 [00:00<00:00, 340.90it/s, avg_loss=0.000596, loss=0.000262]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 70/70 [00:00<00:00, 445.47it/s]\n", "(2025-07-29 15:02:05,219) [INFO]:     [AR] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 265.66it/s, avg_loss=0.00157, loss=0.000374]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 327.08it/s, avg_loss=0.000186, loss=9.05e-5] \n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 256.88it/s, avg_loss=0.000289, loss=0.000342]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 311.78it/s, avg_loss=0.000185, loss=8.99e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 250.56it/s, avg_loss=0.000285, loss=0.000161]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 298.98it/s, avg_loss=0.000182, loss=8.9e-5]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 249.77it/s, avg_loss=0.000279, loss=0.000279]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 291.23it/s, avg_loss=0.000177, loss=8.64e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 425.12it/s]\n", "(2025-07-29 15:02:31,713) [INFO]:     [AR] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 244.78it/s, avg_loss=0.00654, loss=1.44e-7] \n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 319.65it/s, avg_loss=2.16e-6, loss=6.39e-10]\n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 262.28it/s, avg_loss=3.81e-5, loss=5.97e-8] \n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 321.87it/s, avg_loss=2.23e-6, loss=6.97e-8]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 266.61it/s, avg_loss=3.81e-5, loss=1.88e-9] \n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 310.29it/s, avg_loss=2.17e-6, loss=6.59e-9]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 255.01it/s, avg_loss=3.81e-5, loss=9.81e-8] \n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 329.23it/s, avg_loss=2.24e-6, loss=8.33e-8]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 428.60it/s]\n", "(2025-07-29 15:02:54,870) [INFO]:     [AR] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 696/696 [00:02<00:00, 264.05it/s, avg_loss=0.0928, loss=7.89e-5] \n", "Validation Epoch [1/100]: 100%|██████████| 174/174 [00:00<00:00, 274.73it/s, avg_loss=0.000106, loss=7.02e-5] \n", "Training Epoch [2/100]: 100%|██████████| 696/696 [00:03<00:00, 231.71it/s, avg_loss=0.000304, loss=8.14e-5] \n", "Validation Epoch [2/100]: 100%|██████████| 174/174 [00:00<00:00, 325.20it/s, avg_loss=0.000106, loss=7e-5]    \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 696/696 [00:02<00:00, 264.84it/s, avg_loss=0.000304, loss=9.27e-5] \n", "Validation Epoch [3/100]: 100%|██████████| 174/174 [00:00<00:00, 332.09it/s, avg_loss=0.000106, loss=6.99e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 696/696 [00:02<00:00, 256.75it/s, avg_loss=0.000303, loss=0.000102]\n", "Validation Epoch [4/100]: 100%|██████████| 174/174 [00:00<00:00, 330.08it/s, avg_loss=0.000105, loss=6.96e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 870/870 [00:02<00:00, 402.04it/s]\n", "(2025-07-29 15:03:15,021) [INFO]:     [AR] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 256.19it/s, avg_loss=0.00507, loss=5.8e-6]  \n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 325.66it/s, avg_loss=9.31e-5, loss=4.91e-6]  \n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 244.87it/s, avg_loss=5.93e-5, loss=8.55e-6] \n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 290.90it/s, avg_loss=9.29e-5, loss=4.91e-6]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 248.05it/s, avg_loss=5.91e-5, loss=2.45e-6] \n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 306.61it/s, avg_loss=9.26e-5, loss=4.88e-6]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 253.90it/s, avg_loss=5.89e-5, loss=6.32e-6] \n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 315.89it/s, avg_loss=9.22e-5, loss=5.04e-6]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 418.11it/s]\n", "(2025-07-29 15:03:38,625) [INFO]:     [AR] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 263.26it/s, avg_loss=0.0035, loss=0.000139] \n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 321.82it/s, avg_loss=9.86e-5, loss=0.000119]\n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 258.77it/s, avg_loss=0.000157, loss=0.000318]\n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 321.93it/s, avg_loss=9.81e-5, loss=0.000119]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 267.21it/s, avg_loss=0.000156, loss=6.48e-5] \n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 335.16it/s, avg_loss=9.72e-5, loss=0.000118]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 268.00it/s, avg_loss=0.000154, loss=0.000108]\n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 332.13it/s, avg_loss=9.6e-5, loss=0.000116] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 428.71it/s]\n", "(2025-07-29 15:04:01,293) [INFO]:     [AR] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 265.62it/s, avg_loss=0.000699, loss=9e-5]    \n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 284.21it/s, avg_loss=9.23e-5, loss=0.000117]\n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 242.68it/s, avg_loss=0.000128, loss=0.000126]\n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 311.24it/s, avg_loss=9.11e-5, loss=0.000115]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 252.12it/s, avg_loss=0.000126, loss=0.0001]  \n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 316.52it/s, avg_loss=8.93e-5, loss=0.000113]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 262.14it/s, avg_loss=0.000124, loss=9.92e-5] \n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 303.48it/s, avg_loss=8.69e-5, loss=0.000109]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 409.77it/s]\n", "(2025-07-29 15:04:24,725) [INFO]:     [AR] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 55/55 [00:00<00:00, 269.30it/s, avg_loss=0.703, loss=0.381]\n", "Validation Epoch [1/100]: 100%|██████████| 14/14 [00:00<00:00, 260.65it/s, avg_loss=0.365, loss=0.374]\n", "Training Epoch [2/100]: 100%|██████████| 55/55 [00:00<00:00, 253.65it/s, avg_loss=0.197, loss=0.0814]\n", "Validation Epoch [2/100]: 100%|██████████| 14/14 [00:00<00:00, 299.87it/s, avg_loss=0.0835, loss=0.0814]\n", "Training Epoch [3/100]: 100%|██████████| 55/55 [00:00<00:00, 249.85it/s, avg_loss=0.0404, loss=0.0184]\n", "Validation Epoch [3/100]: 100%|██████████| 14/14 [00:00<00:00, 297.36it/s, avg_loss=0.0172, loss=0.0129]\n", "Training Epoch [4/100]: 100%|██████████| 55/55 [00:00<00:00, 269.07it/s, avg_loss=0.00927, loss=0.00469]\n", "Validation Epoch [4/100]: 100%|██████████| 14/14 [00:00<00:00, 319.90it/s, avg_loss=0.00764, loss=0.00312]\n", "Training Epoch [5/100]: 100%|██████████| 55/55 [00:00<00:00, 266.39it/s, avg_loss=0.00546, loss=0.00452]\n", "Validation Epoch [5/100]: 100%|██████████| 14/14 [00:00<00:00, 337.23it/s, avg_loss=0.00683, loss=0.00236]\n", "Training Epoch [6/100]: 100%|██████████| 55/55 [00:00<00:00, 252.30it/s, avg_loss=0.00519, loss=0.00388]\n", "Validation Epoch [6/100]: 100%|██████████| 14/14 [00:00<00:00, 336.45it/s, avg_loss=0.00679, loss=0.00234]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [7/100]: 100%|██████████| 55/55 [00:00<00:00, 268.12it/s, avg_loss=0.00518, loss=0.00586]\n", "Validation Epoch [7/100]: 100%|██████████| 14/14 [00:00<00:00, 347.97it/s, avg_loss=0.00678, loss=0.00234]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [8/100]: 100%|██████████| 55/55 [00:00<00:00, 271.78it/s, avg_loss=0.00518, loss=0.00708]\n", "Validation Epoch [8/100]: 100%|██████████| 14/14 [00:00<00:00, 338.39it/s, avg_loss=0.00677, loss=0.00234]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 69/69 [00:00<00:00, 425.39it/s]\n", "(2025-07-29 15:04:27,373) [INFO]:     [AR] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 254.20it/s, avg_loss=0.0604, loss=4.57e-5] \n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 317.24it/s, avg_loss=0.000574, loss=0.000568]\n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 245.98it/s, avg_loss=0.000161, loss=8.86e-5] \n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 312.30it/s, avg_loss=0.000574, loss=0.000567]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 246.61it/s, avg_loss=0.000161, loss=0.000126]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 291.64it/s, avg_loss=0.000573, loss=0.000566]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 259.31it/s, avg_loss=0.000161, loss=6.5e-5]  \n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 330.57it/s, avg_loss=0.000572, loss=0.000565]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 425.73it/s]\n", "(2025-07-29 15:04:53,967) [INFO]:     [AR] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 263.21it/s, avg_loss=0.00849, loss=0.000163]\n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 326.92it/s, avg_loss=0.000223, loss=8.19e-5] \n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 258.55it/s, avg_loss=0.000224, loss=0.000135]\n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 317.79it/s, avg_loss=0.000223, loss=8.23e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 264.71it/s, avg_loss=0.000223, loss=0.000272]\n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 333.91it/s, avg_loss=0.000222, loss=8.2e-5]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 259.96it/s, avg_loss=0.000222, loss=0.000113]\n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 325.34it/s, avg_loss=0.00022, loss=8.12e-5]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 427.80it/s]\n", "(2025-07-29 15:05:16,848) [INFO]:     [AR] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 245.37it/s, avg_loss=0.00904, loss=0.000196]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 290.53it/s, avg_loss=0.000248, loss=9.14e-5] \n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 246.86it/s, avg_loss=0.00033, loss=0.000332] \n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 304.02it/s, avg_loss=0.000247, loss=9.17e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 241.31it/s, avg_loss=0.000326, loss=0.000228]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 325.55it/s, avg_loss=0.000243, loss=8.96e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 263.75it/s, avg_loss=0.000322, loss=0.000905]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 295.47it/s, avg_loss=0.000239, loss=8.78e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 431.13it/s]\n", "(2025-07-29 15:05:43,737) [INFO]:     [AR] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 265.15it/s, avg_loss=0.0196, loss=0.00016] \n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 322.22it/s, avg_loss=0.000244, loss=0.000286]\n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 255.59it/s, avg_loss=0.000201, loss=0.000178]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 306.39it/s, avg_loss=0.000243, loss=0.000284]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 253.08it/s, avg_loss=0.000199, loss=0.000156]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 320.89it/s, avg_loss=0.000242, loss=0.000282]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 256.30it/s, avg_loss=0.000198, loss=0.000163]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 318.72it/s, avg_loss=0.000239, loss=0.000275]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 426.62it/s]\n", "(2025-07-29 15:06:09,974) [INFO]:     [AR] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 56/56 [00:00<00:00, 264.48it/s, avg_loss=0.011, loss=0.00612] \n", "Validation Epoch [1/100]: 100%|██████████| 14/14 [00:00<00:00, 342.57it/s, avg_loss=0.00514, loss=0.00126]\n", "Training Epoch [2/100]: 100%|██████████| 56/56 [00:00<00:00, 266.43it/s, avg_loss=0.00366, loss=0.00519]\n", "Validation Epoch [2/100]: 100%|██████████| 14/14 [00:00<00:00, 337.60it/s, avg_loss=0.00503, loss=0.00116]\n", "Training Epoch [3/100]: 100%|██████████| 56/56 [00:00<00:00, 274.64it/s, avg_loss=0.00362, loss=0.00248]\n", "Validation Epoch [3/100]: 100%|██████████| 14/14 [00:00<00:00, 320.78it/s, avg_loss=0.00501, loss=0.00115]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 56/56 [00:00<00:00, 275.60it/s, avg_loss=0.0036, loss=0.00243] \n", "Validation Epoch [4/100]: 100%|██████████| 14/14 [00:00<00:00, 348.31it/s, avg_loss=0.00498, loss=0.00114]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 56/56 [00:00<00:00, 274.34it/s, avg_loss=0.0036, loss=0.0053]  \n", "Validation Epoch [5/100]: 100%|██████████| 14/14 [00:00<00:00, 315.74it/s, avg_loss=0.00495, loss=0.00113]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 70/70 [00:00<00:00, 427.89it/s]\n", "(2025-07-29 15:06:11,825) [INFO]:     [AR] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 55/55 [00:00<00:00, 266.93it/s, avg_loss=0.0204, loss=0.00114] \n", "Validation Epoch [1/100]: 100%|██████████| 14/14 [00:00<00:00, 345.99it/s, avg_loss=0.000919, loss=0.000943]\n", "Training Epoch [2/100]: 100%|██████████| 55/55 [00:00<00:00, 262.76it/s, avg_loss=0.00116, loss=0.000947]\n", "Validation Epoch [2/100]: 100%|██████████| 14/14 [00:00<00:00, 276.65it/s, avg_loss=0.000827, loss=0.000956]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 55/55 [00:00<00:00, 256.10it/s, avg_loss=0.00111, loss=0.00112] \n", "Validation Epoch [3/100]: 100%|██████████| 14/14 [00:00<00:00, 247.28it/s, avg_loss=0.000826, loss=0.000932]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 55/55 [00:00<00:00, 207.07it/s, avg_loss=0.00111, loss=0.000989]\n", "Validation Epoch [4/100]: 100%|██████████| 14/14 [00:00<00:00, 267.68it/s, avg_loss=0.000824, loss=0.000937]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 69/69 [00:00<00:00, 448.33it/s]\n", "(2025-07-29 15:06:13,498) [INFO]:     [AR] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 248.47it/s, avg_loss=0.00921, loss=0.000128]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 279.78it/s, avg_loss=0.000328, loss=8.73e-5] \n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 237.52it/s, avg_loss=0.000269, loss=0.000186]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 271.60it/s, avg_loss=0.000328, loss=8.79e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 250.18it/s, avg_loss=0.000267, loss=0.000198]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 298.59it/s, avg_loss=0.000325, loss=8.85e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 246.88it/s, avg_loss=0.000263, loss=0.000138]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 299.64it/s, avg_loss=0.000318, loss=8.55e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 432.07it/s]\n", "(2025-07-29 15:06:40,674) [INFO]:     [AR] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 55/55 [00:00<00:00, 276.13it/s, avg_loss=0.239, loss=0.0766]\n", "Validation Epoch [1/100]: 100%|██████████| 14/14 [00:00<00:00, 355.27it/s, avg_loss=0.0721, loss=0.0801]\n", "Training Epoch [2/100]: 100%|██████████| 55/55 [00:00<00:00, 274.22it/s, avg_loss=0.0269, loss=0.00433]\n", "Validation Epoch [2/100]: 100%|██████████| 14/14 [00:00<00:00, 345.22it/s, avg_loss=0.00444, loss=0.00528]\n", "Training Epoch [3/100]: 100%|██████████| 55/55 [00:00<00:00, 269.89it/s, avg_loss=0.00324, loss=0.00195]\n", "Validation Epoch [3/100]: 100%|██████████| 14/14 [00:00<00:00, 350.21it/s, avg_loss=0.00148, loss=0.00135]\n", "Training Epoch [4/100]: 100%|██████████| 55/55 [00:00<00:00, 271.57it/s, avg_loss=0.00256, loss=0.00207]\n", "Validation Epoch [4/100]: 100%|██████████| 14/14 [00:00<00:00, 350.14it/s, avg_loss=0.00146, loss=0.00127]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 55/55 [00:00<00:00, 273.40it/s, avg_loss=0.00256, loss=0.00364]\n", "Validation Epoch [5/100]: 100%|██████████| 14/14 [00:00<00:00, 308.86it/s, avg_loss=0.00146, loss=0.00127]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 55/55 [00:00<00:00, 266.35it/s, avg_loss=0.00255, loss=0.00209]\n", "Validation Epoch [6/100]: 100%|██████████| 14/14 [00:00<00:00, 346.02it/s, avg_loss=0.00146, loss=0.00127]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 69/69 [00:00<00:00, 421.52it/s]\n", "(2025-07-29 15:06:42,738) [INFO]:     [AR] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 824/824 [00:03<00:00, 264.48it/s, avg_loss=0.00999, loss=2.55e-5]\n", "Validation Epoch [1/100]: 100%|██████████| 206/206 [00:00<00:00, 326.63it/s, avg_loss=3.1e-5, loss=2.91e-5] \n", "Training Epoch [2/100]: 100%|██████████| 824/824 [00:03<00:00, 260.46it/s, avg_loss=7.74e-5, loss=4.3e-5]  \n", "Validation Epoch [2/100]: 100%|██████████| 206/206 [00:00<00:00, 314.62it/s, avg_loss=3.1e-5, loss=2.92e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 824/824 [00:03<00:00, 263.53it/s, avg_loss=7.74e-5, loss=6.65e-5] \n", "Validation Epoch [3/100]: 100%|██████████| 206/206 [00:00<00:00, 314.75it/s, avg_loss=3.09e-5, loss=2.91e-5]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 824/824 [00:03<00:00, 259.93it/s, avg_loss=7.73e-5, loss=1.95e-5]  \n", "Validation Epoch [4/100]: 100%|██████████| 206/206 [00:00<00:00, 281.66it/s, avg_loss=3.1e-5, loss=2.92e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1030/1030 [00:02<00:00, 425.30it/s]\n", "(2025-07-29 15:07:05,645) [INFO]:     [AR] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 264.19it/s, avg_loss=0.00126, loss=0.00018] \n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 315.66it/s, avg_loss=0.000199, loss=8.68e-5] \n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 253.42it/s, avg_loss=0.000199, loss=0.000136]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 314.79it/s, avg_loss=0.000199, loss=8.76e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 258.67it/s, avg_loss=0.000197, loss=0.000164]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 286.80it/s, avg_loss=0.000196, loss=8.64e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 252.92it/s, avg_loss=0.000194, loss=0.000168]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 312.31it/s, avg_loss=0.000192, loss=8.46e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 414.12it/s]\n", "(2025-07-29 15:07:32,111) [INFO]:     [AR] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 692/692 [00:02<00:00, 259.77it/s, avg_loss=0.00314, loss=0.00015] \n", "Validation Epoch [1/100]: 100%|██████████| 173/173 [00:00<00:00, 285.23it/s, avg_loss=1.68e-5, loss=5.58e-6]\n", "Training Epoch [2/100]: 100%|██████████| 692/692 [00:02<00:00, 254.24it/s, avg_loss=9.53e-5, loss=4.03e-5]  \n", "Validation Epoch [2/100]: 100%|██████████| 173/173 [00:00<00:00, 305.54it/s, avg_loss=1.67e-5, loss=5.54e-6]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 692/692 [00:02<00:00, 261.25it/s, avg_loss=9.52e-5, loss=1.15e-5]  \n", "Validation Epoch [3/100]: 100%|██████████| 173/173 [00:00<00:00, 309.04it/s, avg_loss=1.67e-5, loss=5.57e-6] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 692/692 [00:02<00:00, 262.47it/s, avg_loss=9.5e-5, loss=1.37e-5]   \n", "Validation Epoch [4/100]: 100%|██████████| 173/173 [00:00<00:00, 328.65it/s, avg_loss=1.66e-5, loss=5.51e-6] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 865/865 [00:02<00:00, 428.31it/s]\n", "(2025-07-29 15:07:51,570) [INFO]:     [AR] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 78/78 [00:00<00:00, 268.39it/s, avg_loss=0.979, loss=0.305]\n", "Validation Epoch [1/100]: 100%|██████████| 20/20 [00:00<00:00, 340.11it/s, avg_loss=0.298, loss=0.288]\n", "Training Epoch [2/100]: 100%|██████████| 78/78 [00:00<00:00, 264.79it/s, avg_loss=0.105, loss=0.0168]\n", "Validation Epoch [2/100]: 100%|██████████| 20/20 [00:00<00:00, 343.53it/s, avg_loss=0.0189, loss=0.015]\n", "Training Epoch [3/100]: 100%|██████████| 78/78 [00:00<00:00, 258.82it/s, avg_loss=0.00482, loss=0.000603]\n", "Validation Epoch [3/100]: 100%|██████████| 20/20 [00:00<00:00, 319.58it/s, avg_loss=0.00398, loss=0.000281]\n", "Training Epoch [4/100]: 100%|██████████| 78/78 [00:00<00:00, 265.05it/s, avg_loss=0.000903, loss=0.000391]\n", "Validation Epoch [4/100]: 100%|██████████| 20/20 [00:00<00:00, 335.54it/s, avg_loss=0.00377, loss=5.2e-5]\n", "Training Epoch [5/100]: 100%|██████████| 78/78 [00:00<00:00, 271.96it/s, avg_loss=0.000859, loss=0.000675]\n", "Validation Epoch [5/100]: 100%|██████████| 20/20 [00:00<00:00, 347.80it/s, avg_loss=0.00377, loss=4.98e-5]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 78/78 [00:00<00:00, 267.22it/s, avg_loss=0.000858, loss=0.000336]\n", "Validation Epoch [6/100]: 100%|██████████| 20/20 [00:00<00:00, 351.68it/s, avg_loss=0.00377, loss=4.95e-5]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [7/100]: 100%|██████████| 78/78 [00:00<00:00, 265.46it/s, avg_loss=0.000858, loss=0.000346]\n", "Validation Epoch [7/100]: 100%|██████████| 20/20 [00:00<00:00, 320.45it/s, avg_loss=0.00377, loss=4.97e-5]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 98/98 [00:00<00:00, 444.25it/s]\n", "(2025-07-29 15:07:54,841) [INFO]:     [AR] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 264.20it/s, avg_loss=0.00171, loss=0.000548]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 310.79it/s, avg_loss=0.000608, loss=0.000948]\n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 256.20it/s, avg_loss=0.00056, loss=0.00045]  \n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 316.29it/s, avg_loss=0.00057, loss=0.000886] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 260.31it/s, avg_loss=0.000518, loss=0.000521]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 305.44it/s, avg_loss=0.000528, loss=0.000811]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 238.98it/s, avg_loss=0.000467, loss=0.000333]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 331.31it/s, avg_loss=0.00047, loss=0.000712] \n", "Training Epoch [5/100]: 100%|██████████| 935/935 [00:03<00:00, 256.57it/s, avg_loss=0.000416, loss=0.000381]\n", "Validation Epoch [5/100]: 100%|██████████| 234/234 [00:00<00:00, 305.45it/s, avg_loss=0.000418, loss=0.000622]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 935/935 [00:03<00:00, 251.95it/s, avg_loss=0.000376, loss=0.000348]\n", "Validation Epoch [6/100]: 100%|██████████| 234/234 [00:00<00:00, 305.25it/s, avg_loss=0.000387, loss=0.000563]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [7/100]: 100%|██████████| 935/935 [00:03<00:00, 257.39it/s, avg_loss=0.000351, loss=0.000276]\n", "Validation Epoch [7/100]: 100%|██████████| 234/234 [00:00<00:00, 316.10it/s, avg_loss=0.000365, loss=0.000521]\n", "Training Epoch [8/100]: 100%|██████████| 935/935 [00:03<00:00, 253.86it/s, avg_loss=0.000333, loss=0.000165]\n", "Validation Epoch [8/100]: 100%|██████████| 234/234 [00:00<00:00, 312.73it/s, avg_loss=0.000355, loss=0.000495]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 935/935 [00:03<00:00, 264.75it/s, avg_loss=0.00032, loss=0.000143] \n", "Validation Epoch [9/100]: 100%|██████████| 234/234 [00:00<00:00, 323.30it/s, avg_loss=0.000336, loss=0.000462]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [10/100]: 100%|██████████| 935/935 [00:03<00:00, 259.00it/s, avg_loss=0.000312, loss=0.00116] \n", "Validation Epoch [10/100]: 100%|██████████| 234/234 [00:00<00:00, 331.72it/s, avg_loss=0.000331, loss=0.000447]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 424.08it/s]\n", "(2025-07-29 15:08:47,499) [INFO]:     [AR] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 257.51it/s, avg_loss=0.0471, loss=0.000417]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 317.72it/s, avg_loss=0.000522, loss=0.000516]\n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 257.28it/s, avg_loss=0.000525, loss=0.000519]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 305.06it/s, avg_loss=0.000516, loss=0.000509]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 259.58it/s, avg_loss=0.000517, loss=0.0004]  \n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 325.50it/s, avg_loss=0.000506, loss=0.000497]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 264.59it/s, avg_loss=0.000504, loss=0.000451]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 320.35it/s, avg_loss=0.000491, loss=0.000479]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 423.83it/s]\n", "(2025-07-29 15:09:13,614) [INFO]:     [AR] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 935/935 [00:03<00:00, 259.63it/s, avg_loss=0.0123, loss=0.000232]\n", "Validation Epoch [1/100]: 100%|██████████| 234/234 [00:00<00:00, 292.95it/s, avg_loss=0.000297, loss=0.000209]\n", "Training Epoch [2/100]: 100%|██████████| 935/935 [00:03<00:00, 241.21it/s, avg_loss=0.000366, loss=0.000307]\n", "Validation Epoch [2/100]: 100%|██████████| 234/234 [00:00<00:00, 309.29it/s, avg_loss=0.000295, loss=0.000207]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 935/935 [00:03<00:00, 247.21it/s, avg_loss=0.000363, loss=0.000265]\n", "Validation Epoch [3/100]: 100%|██████████| 234/234 [00:00<00:00, 289.45it/s, avg_loss=0.000291, loss=0.000205]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 935/935 [00:03<00:00, 243.27it/s, avg_loss=0.000357, loss=0.000381]\n", "Validation Epoch [4/100]: 100%|██████████| 234/234 [00:00<00:00, 318.66it/s, avg_loss=0.000286, loss=0.0002]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1169/1169 [00:02<00:00, 402.32it/s]\n", "(2025-07-29 15:09:40,684) [INFO]:     [AR] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 55/55 [00:00<00:00, 266.18it/s, avg_loss=0.106, loss=0.0135]\n", "Validation Epoch [1/100]: 100%|██████████| 14/14 [00:00<00:00, 344.89it/s, avg_loss=0.0127, loss=0.013]\n", "Training Epoch [2/100]: 100%|██████████| 55/55 [00:00<00:00, 273.70it/s, avg_loss=0.00279, loss=0.000282]\n", "Validation Epoch [2/100]: 100%|██████████| 14/14 [00:00<00:00, 344.18it/s, avg_loss=0.000214, loss=0.000163]\n", "Training Epoch [3/100]: 100%|██████████| 55/55 [00:00<00:00, 273.66it/s, avg_loss=0.000385, loss=0.000229]\n", "Validation Epoch [3/100]: 100%|██████████| 14/14 [00:00<00:00, 342.43it/s, avg_loss=0.000214, loss=0.000162]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 55/55 [00:00<00:00, 275.42it/s, avg_loss=0.000384, loss=0.000269]\n", "Validation Epoch [4/100]: 100%|██████████| 14/14 [00:00<00:00, 354.84it/s, avg_loss=0.000213, loss=0.000162]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 55/55 [00:00<00:00, 262.12it/s, avg_loss=0.000385, loss=0.000583]\n", "Validation Epoch [5/100]: 100%|██████████| 14/14 [00:00<00:00, 318.01it/s, avg_loss=0.000213, loss=0.000162]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 69/69 [00:00<00:00, 435.46it/s]\n", "(2025-07-29 15:09:42,487) [INFO]:     [AR] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 675/675 [00:02<00:00, 258.28it/s, avg_loss=0.0317, loss=0.000606]\n", "Validation Epoch [1/100]: 100%|██████████| 169/169 [00:00<00:00, 305.12it/s, avg_loss=0.000954, loss=0.00233] \n", "Training Epoch [2/100]: 100%|██████████| 675/675 [00:02<00:00, 256.91it/s, avg_loss=0.000679, loss=0.000967]\n", "Validation Epoch [2/100]: 100%|██████████| 169/169 [00:00<00:00, 328.37it/s, avg_loss=0.000951, loss=0.00231] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 675/675 [00:02<00:00, 261.10it/s, avg_loss=0.000675, loss=0.00104] \n", "Validation Epoch [3/100]: 100%|██████████| 169/169 [00:00<00:00, 326.60it/s, avg_loss=0.000943, loss=0.00229] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 675/675 [00:02<00:00, 255.05it/s, avg_loss=0.000669, loss=0.000586]\n", "Validation Epoch [4/100]: 100%|██████████| 169/169 [00:00<00:00, 277.49it/s, avg_loss=0.000932, loss=0.00226] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 844/844 [00:02<00:00, 421.46it/s]\n", "(2025-07-29 15:10:01,480) [INFO]: Run Experiments. Method[AE], <PERSON><PERSON><PERSON>[naive].\n", "(2025-07-29 15:10:01,480) [INFO]:     Use Default Method Config. Path: /data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/EasyTSAD/Methods/AE/config.toml\n", "(2025-07-29 15:10:01,481) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-07-29 15:10:01,582) [INFO]:     [AE] handling dataset AIOPS | curve c69a50cf-ee03-3bd7-831e-407d36c7ee91 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 205.62it/s, avg_loss=0.0348, loss=0.000431]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 268.67it/s, avg_loss=0.00396, loss=0.000928]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 218.33it/s, avg_loss=0.000522, loss=0.000502]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 259.47it/s, avg_loss=0.00373, loss=0.000827]\n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 209.26it/s, avg_loss=0.000495, loss=0.000427]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 276.47it/s, avg_loss=0.00348, loss=0.000736]\n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:05<00:00, 185.68it/s, avg_loss=0.000463, loss=0.000467]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 239.10it/s, avg_loss=0.00301, loss=0.000562]\n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:05<00:00, 176.04it/s, avg_loss=0.000402, loss=0.000464]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:01<00:00, 227.70it/s, avg_loss=0.00256, loss=0.000373]\n", "Training Epoch [6/100]: 100%|██████████| 934/934 [00:05<00:00, 177.79it/s, avg_loss=0.000374, loss=0.000512]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:01<00:00, 223.20it/s, avg_loss=0.00243, loss=0.000322]\n", "Training Epoch [7/100]: 100%|██████████| 934/934 [00:05<00:00, 171.85it/s, avg_loss=0.000367, loss=0.000195]\n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:01<00:00, 217.82it/s, avg_loss=0.00241, loss=0.000315]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [8/100]: 100%|██████████| 934/934 [00:04<00:00, 227.54it/s, avg_loss=0.000365, loss=0.000329]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:00<00:00, 301.31it/s, avg_loss=0.00239, loss=0.000311]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 934/934 [00:04<00:00, 225.67it/s, avg_loss=0.000364, loss=0.00025] \n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:00<00:00, 299.72it/s, avg_loss=0.00237, loss=0.000313]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 383.24it/s]\n", "(2025-07-29 15:10:58,546) [INFO]:     [AE] handling dataset AIOPS | curve 43115f2a-baeb-3b01-96f7-4ea14188343c \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 691/691 [00:03<00:00, 214.69it/s, avg_loss=0.0148, loss=0.00769] \n", "Validation Epoch [1/100]: 100%|██████████| 172/172 [00:00<00:00, 306.45it/s, avg_loss=0.000135, loss=6.14e-5] \n", "Training Epoch [2/100]: 100%|██████████| 691/691 [00:03<00:00, 213.28it/s, avg_loss=0.00167, loss=0.000305]\n", "Validation Epoch [2/100]: 100%|██████████| 172/172 [00:00<00:00, 295.96it/s, avg_loss=8.72e-5, loss=3.49e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 691/691 [00:03<00:00, 200.97it/s, avg_loss=0.00137, loss=0.00014] \n", "Validation Epoch [3/100]: 100%|██████████| 172/172 [00:00<00:00, 300.80it/s, avg_loss=6.55e-5, loss=2.11e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 691/691 [00:03<00:00, 215.60it/s, avg_loss=0.00119, loss=0.0029]  \n", "Validation Epoch [4/100]: 100%|██████████| 172/172 [00:00<00:00, 309.97it/s, avg_loss=6.12e-5, loss=1.94e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 864/864 [00:02<00:00, 389.82it/s]\n", "(2025-07-29 15:11:18,375) [INFO]:     [AE] handling dataset AIOPS | curve 1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 202.63it/s, avg_loss=0.032, loss=0.000442] \n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 289.49it/s, avg_loss=0.00387, loss=0.000843]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 207.19it/s, avg_loss=0.000519, loss=0.000497]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 240.13it/s, avg_loss=0.00348, loss=0.000692]\n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 206.22it/s, avg_loss=0.000477, loss=0.00074] \n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 282.21it/s, avg_loss=0.0031, loss=0.000556] \n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 194.55it/s, avg_loss=0.000439, loss=0.000258]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:01<00:00, 227.18it/s, avg_loss=0.00278, loss=0.000446]\n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:05<00:00, 174.24it/s, avg_loss=0.00041, loss=0.000339] \n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 239.77it/s, avg_loss=0.00256, loss=0.000378]\n", "Training Epoch [6/100]: 100%|██████████| 934/934 [00:05<00:00, 177.48it/s, avg_loss=0.000391, loss=0.000277]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:01<00:00, 220.96it/s, avg_loss=0.00241, loss=0.00033] \n", "Training Epoch [7/100]: 100%|██████████| 934/934 [00:05<00:00, 170.91it/s, avg_loss=0.000378, loss=0.000204]\n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:01<00:00, 207.50it/s, avg_loss=0.00231, loss=0.000302]\n", "Training Epoch [8/100]: 100%|██████████| 934/934 [00:05<00:00, 164.64it/s, avg_loss=0.000371, loss=0.000569]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:01<00:00, 207.88it/s, avg_loss=0.00224, loss=0.000289]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 934/934 [00:04<00:00, 217.28it/s, avg_loss=0.000366, loss=0.00025] \n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:00<00:00, 260.48it/s, avg_loss=0.00219, loss=0.00028] \n", "Training Epoch [10/100]: 100%|██████████| 934/934 [00:04<00:00, 207.35it/s, avg_loss=0.000362, loss=0.000413]\n", "Validation Epoch [10/100]: 100%|██████████| 233/233 [00:00<00:00, 292.16it/s, avg_loss=0.00213, loss=0.000276]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [11/100]: 100%|██████████| 934/934 [00:04<00:00, 210.36it/s, avg_loss=0.000358, loss=0.000257]\n", "Validation Epoch [11/100]: 100%|██████████| 233/233 [00:00<00:00, 290.91it/s, avg_loss=0.00204, loss=0.000273]\n", "Training Epoch [12/100]: 100%|██████████| 934/934 [00:04<00:00, 208.03it/s, avg_loss=0.000353, loss=0.000502]\n", "Validation Epoch [12/100]: 100%|██████████| 233/233 [00:00<00:00, 292.03it/s, avg_loss=0.00188, loss=0.000273]\n", "Training Epoch [13/100]: 100%|██████████| 934/934 [00:04<00:00, 207.81it/s, avg_loss=0.000343, loss=0.000259]\n", "Validation Epoch [13/100]: 100%|██████████| 233/233 [00:00<00:00, 285.47it/s, avg_loss=0.00178, loss=0.000291]\n", "Training Epoch [14/100]: 100%|██████████| 934/934 [00:04<00:00, 195.24it/s, avg_loss=0.00031, loss=0.000356] \n", "Validation Epoch [14/100]: 100%|██████████| 233/233 [00:00<00:00, 245.85it/s, avg_loss=0.00126, loss=0.000276]\n", "Training Epoch [15/100]: 100%|██████████| 934/934 [00:05<00:00, 181.40it/s, avg_loss=0.000295, loss=0.000407]\n", "Validation Epoch [15/100]: 100%|██████████| 233/233 [00:01<00:00, 228.67it/s, avg_loss=0.00115, loss=0.000266]\n", "Training Epoch [16/100]: 100%|██████████| 934/934 [00:05<00:00, 170.45it/s, avg_loss=0.000291, loss=0.000288]\n", "Validation Epoch [16/100]: 100%|██████████| 233/233 [00:01<00:00, 208.81it/s, avg_loss=0.00109, loss=0.000265]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [17/100]: 100%|██████████| 934/934 [00:04<00:00, 220.89it/s, avg_loss=0.000288, loss=0.000201]\n", "Validation Epoch [17/100]: 100%|██████████| 233/233 [00:00<00:00, 304.19it/s, avg_loss=0.00105, loss=0.000268]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [18/100]: 100%|██████████| 934/934 [00:04<00:00, 221.20it/s, avg_loss=0.000286, loss=0.000178]\n", "Validation Epoch [18/100]: 100%|██████████| 233/233 [00:00<00:00, 305.70it/s, avg_loss=0.00102, loss=0.000265]\n", "Training Epoch [19/100]: 100%|██████████| 934/934 [00:04<00:00, 217.90it/s, avg_loss=0.000284, loss=0.000213]\n", "Validation Epoch [19/100]: 100%|██████████| 233/233 [00:00<00:00, 292.98it/s, avg_loss=0.000989, loss=0.00026] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [20/100]: 100%|██████████| 934/934 [00:04<00:00, 224.01it/s, avg_loss=0.000282, loss=0.000192]\n", "Validation Epoch [20/100]: 100%|██████████| 233/233 [00:00<00:00, 295.02it/s, avg_loss=0.000968, loss=0.00026] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [21/100]: 100%|██████████| 934/934 [00:04<00:00, 213.26it/s, avg_loss=0.000281, loss=0.000196]\n", "Validation Epoch [21/100]: 100%|██████████| 233/233 [00:00<00:00, 292.42it/s, avg_loss=0.000948, loss=0.000255]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 368.37it/s]\n", "(2025-07-29 15:13:22,814) [INFO]:     [AE] handling dataset AIOPS | curve ffb82d38-5f00-37db-abc0-5d2e4e4cb6aa \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:03<00:00, 206.12it/s, avg_loss=0.00831, loss=0.000603]\n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 299.26it/s, avg_loss=0.00026, loss=0.000138] \n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:03<00:00, 205.98it/s, avg_loss=0.000275, loss=0.000144]\n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 262.67it/s, avg_loss=0.000109, loss=0.000123]\n", "Training Epoch [3/100]: 100%|██████████| 823/823 [00:03<00:00, 206.84it/s, avg_loss=0.000189, loss=0.000115]\n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 287.56it/s, avg_loss=9.95e-5, loss=0.000122] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 223.17it/s, avg_loss=0.000173, loss=0.000106]\n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 303.31it/s, avg_loss=8.99e-5, loss=0.000116]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 823/823 [00:03<00:00, 222.78it/s, avg_loss=0.000143, loss=7.04e-5] \n", "Validation Epoch [5/100]: 100%|██████████| 205/205 [00:00<00:00, 294.80it/s, avg_loss=6.47e-5, loss=8.04e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 385.65it/s]\n", "(2025-07-29 15:13:51,471) [INFO]:     [AE] handling dataset AIOPS | curve a8c06b47-cc41-3738-9110-12df0ee4c721 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 55/55 [00:00<00:00, 211.04it/s, avg_loss=0.0324, loss=0.0299]\n", "Validation Epoch [1/100]: 100%|██████████| 13/13 [00:00<00:00, 292.55it/s, avg_loss=0.028, loss=0.0282]\n", "Training Epoch [2/100]: 100%|██████████| 55/55 [00:00<00:00, 224.83it/s, avg_loss=0.0265, loss=0.0247]\n", "Validation Epoch [2/100]: 100%|██████████| 13/13 [00:00<00:00, 305.23it/s, avg_loss=0.023, loss=0.023]\n", "Training Epoch [3/100]: 100%|██████████| 55/55 [00:00<00:00, 226.35it/s, avg_loss=0.0219, loss=0.0198]\n", "Validation Epoch [3/100]: 100%|██████████| 13/13 [00:00<00:00, 302.26it/s, avg_loss=0.0188, loss=0.0188]\n", "Training Epoch [4/100]: 100%|██████████| 55/55 [00:00<00:00, 227.27it/s, avg_loss=0.0179, loss=0.0168]\n", "Validation Epoch [4/100]: 100%|██████████| 13/13 [00:00<00:00, 314.69it/s, avg_loss=0.0152, loss=0.0151]\n", "Training Epoch [5/100]: 100%|██████████| 55/55 [00:00<00:00, 225.93it/s, avg_loss=0.0145, loss=0.0128]\n", "Validation Epoch [5/100]: 100%|██████████| 13/13 [00:00<00:00, 312.48it/s, avg_loss=0.012, loss=0.0118]\n", "Training Epoch [6/100]: 100%|██████████| 55/55 [00:00<00:00, 226.55it/s, avg_loss=0.0114, loss=0.0105] \n", "Validation Epoch [6/100]: 100%|██████████| 13/13 [00:00<00:00, 301.80it/s, avg_loss=0.00924, loss=0.00892]\n", "Training Epoch [7/100]: 100%|██████████| 55/55 [00:00<00:00, 211.08it/s, avg_loss=0.00869, loss=0.00752]\n", "Validation Epoch [7/100]: 100%|██████████| 13/13 [00:00<00:00, 242.78it/s, avg_loss=0.00689, loss=0.00648]\n", "Training Epoch [8/100]: 100%|██████████| 55/55 [00:00<00:00, 207.99it/s, avg_loss=0.00652, loss=0.00611]\n", "Validation Epoch [8/100]: 100%|██████████| 13/13 [00:00<00:00, 276.57it/s, avg_loss=0.00507, loss=0.00459]\n", "Training Epoch [9/100]: 100%|██████████| 55/55 [00:00<00:00, 194.60it/s, avg_loss=0.00489, loss=0.00414]\n", "Validation Epoch [9/100]: 100%|██████████| 13/13 [00:00<00:00, 291.29it/s, avg_loss=0.00377, loss=0.00324]\n", "Training Epoch [10/100]: 100%|██████████| 55/55 [00:00<00:00, 225.80it/s, avg_loss=0.00376, loss=0.00313]\n", "Validation Epoch [10/100]: 100%|██████████| 13/13 [00:00<00:00, 308.87it/s, avg_loss=0.00291, loss=0.00234]\n", "Training Epoch [11/100]: 100%|██████████| 55/55 [00:00<00:00, 225.04it/s, avg_loss=0.00303, loss=0.00295]\n", "Validation Epoch [11/100]: 100%|██████████| 13/13 [00:00<00:00, 308.43it/s, avg_loss=0.00237, loss=0.00178]\n", "Training Epoch [12/100]: 100%|██████████| 55/55 [00:00<00:00, 212.41it/s, avg_loss=0.00257, loss=0.00266]\n", "Validation Epoch [12/100]: 100%|██████████| 13/13 [00:00<00:00, 237.22it/s, avg_loss=0.00204, loss=0.00143]\n", "Training Epoch [13/100]: 100%|██████████| 55/55 [00:00<00:00, 216.66it/s, avg_loss=0.00229, loss=0.0021] \n", "Validation Epoch [13/100]: 100%|██████████| 13/13 [00:00<00:00, 291.28it/s, avg_loss=0.00184, loss=0.00122]\n", "Training Epoch [14/100]: 100%|██████████| 55/55 [00:00<00:00, 215.45it/s, avg_loss=0.00212, loss=0.00194]\n", "Validation Epoch [14/100]: 100%|██████████| 13/13 [00:00<00:00, 308.25it/s, avg_loss=0.00172, loss=0.00109]\n", "Training Epoch [15/100]: 100%|██████████| 55/55 [00:00<00:00, 214.49it/s, avg_loss=0.00202, loss=0.00179]\n", "Validation Epoch [15/100]: 100%|██████████| 13/13 [00:00<00:00, 238.15it/s, avg_loss=0.00165, loss=0.00102]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [16/100]: 100%|██████████| 55/55 [00:00<00:00, 194.62it/s, avg_loss=0.00196, loss=0.00189]\n", "Validation Epoch [16/100]: 100%|██████████| 13/13 [00:00<00:00, 240.40it/s, avg_loss=0.0016, loss=0.000973]\n", "Training Epoch [17/100]: 100%|██████████| 55/55 [00:00<00:00, 205.33it/s, avg_loss=0.00192, loss=0.00205]\n", "Validation Epoch [17/100]: 100%|██████████| 13/13 [00:00<00:00, 320.91it/s, avg_loss=0.00158, loss=0.000946]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [18/100]: 100%|██████████| 55/55 [00:00<00:00, 205.62it/s, avg_loss=0.0019, loss=0.00149] \n", "Validation Epoch [18/100]: 100%|██████████| 13/13 [00:00<00:00, 305.66it/s, avg_loss=0.00156, loss=0.000932]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [19/100]: 100%|██████████| 55/55 [00:00<00:00, 226.63it/s, avg_loss=0.00188, loss=0.00163]\n", "Validation Epoch [19/100]: 100%|██████████| 13/13 [00:00<00:00, 299.79it/s, avg_loss=0.00155, loss=0.000924]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 69/69 [00:00<00:00, 393.41it/s]\n", "(2025-07-29 15:13:57,654) [INFO]:     [AE] handling dataset AIOPS | curve 55f8b8b8-b659-38df-b3df-e4a5a8a54bc9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 211.74it/s, avg_loss=0.0687, loss=0.00267]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 281.32it/s, avg_loss=0.00163, loss=0.000918]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 218.26it/s, avg_loss=0.00203, loss=0.00148]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 288.21it/s, avg_loss=0.00131, loss=0.000841]\n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 210.57it/s, avg_loss=0.00161, loss=0.00149]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 275.51it/s, avg_loss=0.001, loss=0.000684]  \n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:05<00:00, 186.60it/s, avg_loss=0.00136, loss=0.00145] \n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 246.10it/s, avg_loss=0.000824, loss=0.000578]\n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:05<00:00, 174.93it/s, avg_loss=0.0012, loss=0.00127]  \n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:01<00:00, 215.30it/s, avg_loss=0.000697, loss=0.000517]\n", "Training Epoch [6/100]: 100%|██████████| 934/934 [00:05<00:00, 169.23it/s, avg_loss=0.00107, loss=0.000789]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:01<00:00, 219.82it/s, avg_loss=0.000606, loss=0.000459]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [7/100]: 100%|██████████| 934/934 [00:04<00:00, 220.68it/s, avg_loss=0.000939, loss=0.000782]\n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:00<00:00, 301.31it/s, avg_loss=0.000519, loss=0.000397]\n", "Training Epoch [8/100]: 100%|██████████| 934/934 [00:04<00:00, 215.72it/s, avg_loss=0.000787, loss=0.00102] \n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:00<00:00, 281.74it/s, avg_loss=0.000425, loss=0.000305]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 934/934 [00:04<00:00, 221.03it/s, avg_loss=0.000662, loss=0.000494]\n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:00<00:00, 297.87it/s, avg_loss=0.00037, loss=0.000268] \n", "Training Epoch [10/100]: 100%|██████████| 934/934 [00:04<00:00, 215.24it/s, avg_loss=0.000604, loss=0.000581]\n", "Validation Epoch [10/100]: 100%|██████████| 233/233 [00:00<00:00, 292.71it/s, avg_loss=0.000353, loss=0.000236]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [11/100]: 100%|██████████| 934/934 [00:04<00:00, 221.97it/s, avg_loss=0.000585, loss=0.00057] \n", "Validation Epoch [11/100]: 100%|██████████| 233/233 [00:00<00:00, 298.75it/s, avg_loss=0.000346, loss=0.000242]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [12/100]: 100%|██████████| 934/934 [00:04<00:00, 216.25it/s, avg_loss=0.000572, loss=0.000548]\n", "Validation Epoch [12/100]: 100%|██████████| 233/233 [00:00<00:00, 271.45it/s, avg_loss=0.00034, loss=0.000228] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 386.45it/s]\n", "(2025-07-29 15:15:08,731) [INFO]:     [AE] handling dataset AIOPS | curve 57051487-3a40-3828-9084-a12f7f23ee38 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:03<00:00, 216.11it/s, avg_loss=0.00414, loss=0.000172]\n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 298.66it/s, avg_loss=7.83e-5, loss=7.74e-5] \n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:03<00:00, 217.32it/s, avg_loss=0.000145, loss=0.00019] \n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 286.09it/s, avg_loss=9.32e-7, loss=7.17e-8]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 823/823 [00:03<00:00, 211.15it/s, avg_loss=0.000133, loss=2.03e-7] \n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 293.50it/s, avg_loss=9.5e-7, loss=9.29e-8] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 213.90it/s, avg_loss=0.000132, loss=5.6e-5]  \n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 305.72it/s, avg_loss=9.61e-7, loss=1.07e-7]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 384.28it/s]\n", "(2025-07-29 15:15:32,181) [INFO]:     [AE] handling dataset AIOPS | curve a07ac296-de40-3a7c-8df3-91f642cc14d0 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 696/696 [00:03<00:00, 224.19it/s, avg_loss=0.0385, loss=0.000368]\n", "Validation Epoch [1/100]: 100%|██████████| 174/174 [00:00<00:00, 307.88it/s, avg_loss=0.000498, loss=0.000641]\n", "Training Epoch [2/100]: 100%|██████████| 696/696 [00:03<00:00, 205.68it/s, avg_loss=0.00212, loss=0.000361]\n", "Validation Epoch [2/100]: 100%|██████████| 174/174 [00:00<00:00, 298.32it/s, avg_loss=0.000357, loss=0.00042] \n", "Training Epoch [3/100]: 100%|██████████| 696/696 [00:03<00:00, 200.65it/s, avg_loss=0.00185, loss=0.000368]\n", "Validation Epoch [3/100]: 100%|██████████| 174/174 [00:00<00:00, 243.46it/s, avg_loss=0.000312, loss=0.000322]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 696/696 [00:03<00:00, 214.32it/s, avg_loss=0.00168, loss=0.000256]\n", "Validation Epoch [4/100]: 100%|██████████| 174/174 [00:00<00:00, 295.58it/s, avg_loss=0.000291, loss=0.00031] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 696/696 [00:03<00:00, 222.80it/s, avg_loss=0.00156, loss=0.000582]\n", "Validation Epoch [5/100]: 100%|██████████| 174/174 [00:00<00:00, 307.69it/s, avg_loss=0.000275, loss=0.000283]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 870/870 [00:02<00:00, 386.17it/s]\n", "(2025-07-29 15:15:56,050) [INFO]:     [AE] handling dataset AIOPS | curve 7103fa0f-cac4-314f-addc-866190247439 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:03<00:00, 221.24it/s, avg_loss=0.00277, loss=8.57e-5] \n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 290.66it/s, avg_loss=0.000133, loss=3.69e-5] \n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:03<00:00, 218.16it/s, avg_loss=7.44e-5, loss=2.14e-5] \n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 280.04it/s, avg_loss=0.000114, loss=1.93e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 823/823 [00:03<00:00, 218.15it/s, avg_loss=7.22e-5, loss=5.38e-5] \n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 304.83it/s, avg_loss=0.000114, loss=1.92e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 221.13it/s, avg_loss=7.18e-5, loss=0.000177]\n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 275.65it/s, avg_loss=0.000113, loss=1.92e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 354.94it/s]\n", "(2025-07-29 15:16:19,429) [INFO]:     [AE] handling dataset AIOPS | curve 6a757df4-95e5-3357-8406-165e2bd49360 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:04<00:00, 200.18it/s, avg_loss=0.00985, loss=0.000743]\n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 271.20it/s, avg_loss=0.000405, loss=0.000344]\n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:04<00:00, 198.64it/s, avg_loss=0.00046, loss=0.000264] \n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 280.80it/s, avg_loss=0.000186, loss=0.00023] \n", "Training Epoch [3/100]: 100%|██████████| 823/823 [00:04<00:00, 200.60it/s, avg_loss=0.000302, loss=0.000225]\n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 263.50it/s, avg_loss=0.000167, loss=0.000229]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 219.14it/s, avg_loss=0.000283, loss=0.000242]\n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 298.52it/s, avg_loss=0.000164, loss=0.000227]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 823/823 [00:03<00:00, 222.05it/s, avg_loss=0.000277, loss=0.000347]\n", "Validation Epoch [5/100]: 100%|██████████| 205/205 [00:00<00:00, 286.40it/s, avg_loss=0.000163, loss=0.000224]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 389.45it/s]\n", "(2025-07-29 15:16:48,334) [INFO]:     [AE] handling dataset AIOPS | curve 4d2af31a-9916-3d9f-8a8e-8a268a48c095 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:03<00:00, 221.88it/s, avg_loss=0.00883, loss=0.000312]\n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 296.32it/s, avg_loss=0.000257, loss=9.24e-5] \n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:03<00:00, 212.20it/s, avg_loss=0.000401, loss=9.43e-5] \n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 287.46it/s, avg_loss=7.08e-5, loss=5.3e-5]  \n", "Training Epoch [3/100]: 100%|██████████| 823/823 [00:03<00:00, 205.84it/s, avg_loss=0.000251, loss=8.04e-5] \n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 283.93it/s, avg_loss=5.28e-5, loss=4.51e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 214.63it/s, avg_loss=0.000232, loss=5.62e-5] \n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 266.75it/s, avg_loss=4.91e-5, loss=4.17e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 823/823 [00:03<00:00, 222.02it/s, avg_loss=0.000221, loss=5.32e-5] \n", "Validation Epoch [5/100]: 100%|██████████| 205/205 [00:00<00:00, 291.29it/s, avg_loss=4.74e-5, loss=3.85e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 387.68it/s]\n", "(2025-07-29 15:17:16,367) [INFO]:     [AE] handling dataset AIOPS | curve e0747cad-8dc8-38a9-a9ab-855b61f5551d \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 54/54 [00:00<00:00, 225.49it/s, avg_loss=0.217, loss=0.192]\n", "Validation Epoch [1/100]: 100%|██████████| 13/13 [00:00<00:00, 320.06it/s, avg_loss=0.131, loss=0.135]\n", "Training Epoch [2/100]: 100%|██████████| 54/54 [00:00<00:00, 227.39it/s, avg_loss=0.198, loss=0.186]\n", "Validation Epoch [2/100]: 100%|██████████| 13/13 [00:00<00:00, 316.92it/s, avg_loss=0.117, loss=0.122]\n", "Training Epoch [3/100]: 100%|██████████| 54/54 [00:00<00:00, 225.17it/s, avg_loss=0.176, loss=0.158]\n", "Validation Epoch [3/100]: 100%|██████████| 13/13 [00:00<00:00, 289.32it/s, avg_loss=0.0993, loss=0.108]\n", "Training Epoch [4/100]: 100%|██████████| 54/54 [00:00<00:00, 229.70it/s, avg_loss=0.141, loss=0.128]\n", "Validation Epoch [4/100]: 100%|██████████| 13/13 [00:00<00:00, 295.71it/s, avg_loss=0.0758, loss=0.0911]\n", "Training Epoch [5/100]: 100%|██████████| 54/54 [00:00<00:00, 228.77it/s, avg_loss=0.102, loss=0.0876]\n", "Validation Epoch [5/100]: 100%|██████████| 13/13 [00:00<00:00, 313.70it/s, avg_loss=0.0554, loss=0.0752]\n", "Training Epoch [6/100]: 100%|██████████| 54/54 [00:00<00:00, 226.29it/s, avg_loss=0.0721, loss=0.064] \n", "Validation Epoch [6/100]: 100%|██████████| 13/13 [00:00<00:00, 321.08it/s, avg_loss=0.0412, loss=0.0634]\n", "Training Epoch [7/100]: 100%|██████████| 54/54 [00:00<00:00, 221.35it/s, avg_loss=0.0529, loss=0.0433]\n", "Validation Epoch [7/100]: 100%|██████████| 13/13 [00:00<00:00, 307.38it/s, avg_loss=0.0324, loss=0.0556]\n", "Training Epoch [8/100]: 100%|██████████| 54/54 [00:00<00:00, 223.11it/s, avg_loss=0.0417, loss=0.0401]\n", "Validation Epoch [8/100]: 100%|██████████| 13/13 [00:00<00:00, 314.44it/s, avg_loss=0.0272, loss=0.0507]\n", "Training Epoch [9/100]: 100%|██████████| 54/54 [00:00<00:00, 221.88it/s, avg_loss=0.0352, loss=0.0345]\n", "Validation Epoch [9/100]: 100%|██████████| 13/13 [00:00<00:00, 302.37it/s, avg_loss=0.0241, loss=0.0472]\n", "Training Epoch [10/100]: 100%|██████████| 54/54 [00:00<00:00, 219.93it/s, avg_loss=0.0315, loss=0.0314]\n", "Validation Epoch [10/100]: 100%|██████████| 13/13 [00:00<00:00, 299.83it/s, avg_loss=0.0223, loss=0.0446]\n", "Training Epoch [11/100]: 100%|██████████| 54/54 [00:00<00:00, 197.58it/s, avg_loss=0.0291, loss=0.0285]\n", "Validation Epoch [11/100]: 100%|██████████| 13/13 [00:00<00:00, 284.25it/s, avg_loss=0.0209, loss=0.0422]\n", "Training Epoch [12/100]: 100%|██████████| 54/54 [00:00<00:00, 223.36it/s, avg_loss=0.0271, loss=0.0257]\n", "Validation Epoch [12/100]: 100%|██████████| 13/13 [00:00<00:00, 302.30it/s, avg_loss=0.0195, loss=0.0394]\n", "Training Epoch [13/100]: 100%|██████████| 54/54 [00:00<00:00, 223.61it/s, avg_loss=0.0252, loss=0.0229]\n", "Validation Epoch [13/100]: 100%|██████████| 13/13 [00:00<00:00, 308.17it/s, avg_loss=0.0181, loss=0.0365]\n", "Training Epoch [14/100]: 100%|██████████| 54/54 [00:00<00:00, 215.69it/s, avg_loss=0.0231, loss=0.0257]\n", "Validation Epoch [14/100]: 100%|██████████| 13/13 [00:00<00:00, 290.62it/s, avg_loss=0.0166, loss=0.0329]\n", "Training Epoch [15/100]: 100%|██████████| 54/54 [00:00<00:00, 202.82it/s, avg_loss=0.0208, loss=0.0188]\n", "Validation Epoch [15/100]: 100%|██████████| 13/13 [00:00<00:00, 268.91it/s, avg_loss=0.0149, loss=0.0291]\n", "Training Epoch [16/100]: 100%|██████████| 54/54 [00:00<00:00, 209.78it/s, avg_loss=0.0183, loss=0.0161]\n", "Validation Epoch [16/100]: 100%|██████████| 13/13 [00:00<00:00, 307.07it/s, avg_loss=0.0131, loss=0.0251]\n", "Training Epoch [17/100]: 100%|██████████| 54/54 [00:00<00:00, 216.82it/s, avg_loss=0.0158, loss=0.0138]\n", "Validation Epoch [17/100]: 100%|██████████| 13/13 [00:00<00:00, 280.40it/s, avg_loss=0.0115, loss=0.0216]\n", "Training Epoch [18/100]: 100%|██████████| 54/54 [00:00<00:00, 223.47it/s, avg_loss=0.0137, loss=0.0134]\n", "Validation Epoch [18/100]: 100%|██████████| 13/13 [00:00<00:00, 308.37it/s, avg_loss=0.0103, loss=0.0186]\n", "Training Epoch [19/100]: 100%|██████████| 54/54 [00:00<00:00, 222.81it/s, avg_loss=0.012, loss=0.0121] \n", "Validation Epoch [19/100]: 100%|██████████| 13/13 [00:00<00:00, 304.67it/s, avg_loss=0.00921, loss=0.0163]\n", "Training Epoch [20/100]: 100%|██████████| 54/54 [00:00<00:00, 218.23it/s, avg_loss=0.0107, loss=0.00889]\n", "Validation Epoch [20/100]: 100%|██████████| 13/13 [00:00<00:00, 308.97it/s, avg_loss=0.00836, loss=0.0145]\n", "Training Epoch [21/100]: 100%|██████████| 54/54 [00:00<00:00, 222.08it/s, avg_loss=0.00955, loss=0.00907]\n", "Validation Epoch [21/100]: 100%|██████████| 13/13 [00:00<00:00, 312.12it/s, avg_loss=0.00768, loss=0.0129]\n", "Training Epoch [22/100]: 100%|██████████| 54/54 [00:00<00:00, 225.71it/s, avg_loss=0.00868, loss=0.00749]\n", "Validation Epoch [22/100]: 100%|██████████| 13/13 [00:00<00:00, 295.42it/s, avg_loss=0.00708, loss=0.0116]\n", "Training Epoch [23/100]: 100%|██████████| 54/54 [00:00<00:00, 225.24it/s, avg_loss=0.00798, loss=0.00726]\n", "Validation Epoch [23/100]: 100%|██████████| 13/13 [00:00<00:00, 311.07it/s, avg_loss=0.00663, loss=0.0106]\n", "Training Epoch [24/100]: 100%|██████████| 54/54 [00:00<00:00, 224.35it/s, avg_loss=0.0074, loss=0.00666] \n", "Validation Epoch [24/100]: 100%|██████████| 13/13 [00:00<00:00, 302.51it/s, avg_loss=0.00621, loss=0.00964]\n", "Training Epoch [25/100]: 100%|██████████| 54/54 [00:00<00:00, 218.57it/s, avg_loss=0.00688, loss=0.00673]\n", "Validation Epoch [25/100]: 100%|██████████| 13/13 [00:00<00:00, 290.43it/s, avg_loss=0.00582, loss=0.00876]\n", "Training Epoch [26/100]: 100%|██████████| 54/54 [00:00<00:00, 218.99it/s, avg_loss=0.00642, loss=0.00629]\n", "Validation Epoch [26/100]: 100%|██████████| 13/13 [00:00<00:00, 301.98it/s, avg_loss=0.00547, loss=0.00792]\n", "Training Epoch [27/100]: 100%|██████████| 54/54 [00:00<00:00, 209.41it/s, avg_loss=0.006, loss=0.00564]  \n", "Validation Epoch [27/100]: 100%|██████████| 13/13 [00:00<00:00, 298.90it/s, avg_loss=0.00515, loss=0.00713]\n", "Training Epoch [28/100]: 100%|██████████| 54/54 [00:00<00:00, 218.88it/s, avg_loss=0.00561, loss=0.00596]\n", "Validation Epoch [28/100]: 100%|██████████| 13/13 [00:00<00:00, 302.20it/s, avg_loss=0.0048, loss=0.00647]\n", "Training Epoch [29/100]: 100%|██████████| 54/54 [00:00<00:00, 212.05it/s, avg_loss=0.00523, loss=0.00537]\n", "Validation Epoch [29/100]: 100%|██████████| 13/13 [00:00<00:00, 297.70it/s, avg_loss=0.00455, loss=0.00575]\n", "Training Epoch [30/100]: 100%|██████████| 54/54 [00:00<00:00, 212.49it/s, avg_loss=0.00488, loss=0.00473]\n", "Validation Epoch [30/100]: 100%|██████████| 13/13 [00:00<00:00, 289.47it/s, avg_loss=0.00431, loss=0.00506]\n", "Training Epoch [31/100]: 100%|██████████| 54/54 [00:00<00:00, 213.05it/s, avg_loss=0.00454, loss=0.00432]\n", "Validation Epoch [31/100]: 100%|██████████| 13/13 [00:00<00:00, 302.81it/s, avg_loss=0.00394, loss=0.00439]\n", "Training Epoch [32/100]: 100%|██████████| 54/54 [00:00<00:00, 220.79it/s, avg_loss=0.00418, loss=0.00374]\n", "Validation Epoch [32/100]: 100%|██████████| 13/13 [00:00<00:00, 304.51it/s, avg_loss=0.00371, loss=0.00374]\n", "Training Epoch [33/100]: 100%|██████████| 54/54 [00:00<00:00, 213.75it/s, avg_loss=0.00386, loss=0.00402]\n", "Validation Epoch [33/100]: 100%|██████████| 13/13 [00:00<00:00, 303.64it/s, avg_loss=0.00345, loss=0.00323]\n", "Training Epoch [34/100]: 100%|██████████| 54/54 [00:00<00:00, 219.53it/s, avg_loss=0.00358, loss=0.00356]\n", "Validation Epoch [34/100]: 100%|██████████| 13/13 [00:00<00:00, 301.16it/s, avg_loss=0.0032, loss=0.00279]\n", "Training Epoch [35/100]: 100%|██████████| 54/54 [00:00<00:00, 215.91it/s, avg_loss=0.00336, loss=0.00326]\n", "Validation Epoch [35/100]: 100%|██████████| 13/13 [00:00<00:00, 298.40it/s, avg_loss=0.00303, loss=0.00247]\n", "Training Epoch [36/100]: 100%|██████████| 54/54 [00:00<00:00, 218.09it/s, avg_loss=0.00317, loss=0.00331]\n", "Validation Epoch [36/100]: 100%|██████████| 13/13 [00:00<00:00, 303.15it/s, avg_loss=0.0029, loss=0.00222]\n", "Training Epoch [37/100]: 100%|██████████| 54/54 [00:00<00:00, 209.89it/s, avg_loss=0.00302, loss=0.00302]\n", "Validation Epoch [37/100]: 100%|██████████| 13/13 [00:00<00:00, 291.57it/s, avg_loss=0.00277, loss=0.002]\n", "Training Epoch [38/100]: 100%|██████████| 54/54 [00:00<00:00, 221.01it/s, avg_loss=0.0029, loss=0.00311] \n", "Validation Epoch [38/100]: 100%|██████████| 13/13 [00:00<00:00, 301.94it/s, avg_loss=0.00263, loss=0.00187]\n", "Training Epoch [39/100]: 100%|██████████| 54/54 [00:00<00:00, 221.26it/s, avg_loss=0.00279, loss=0.00273]\n", "Validation Epoch [39/100]: 100%|██████████| 13/13 [00:00<00:00, 298.74it/s, avg_loss=0.00255, loss=0.00174]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [40/100]: 100%|██████████| 54/54 [00:00<00:00, 226.17it/s, avg_loss=0.0027, loss=0.00291] \n", "Validation Epoch [40/100]: 100%|██████████| 13/13 [00:00<00:00, 313.22it/s, avg_loss=0.00246, loss=0.00166]\n", "Training Epoch [41/100]: 100%|██████████| 54/54 [00:00<00:00, 224.34it/s, avg_loss=0.00263, loss=0.00273]\n", "Validation Epoch [41/100]: 100%|██████████| 13/13 [00:00<00:00, 323.53it/s, avg_loss=0.0024, loss=0.00161]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [42/100]: 100%|██████████| 54/54 [00:00<00:00, 231.35it/s, avg_loss=0.00256, loss=0.00276]\n", "Validation Epoch [42/100]: 100%|██████████| 13/13 [00:00<00:00, 325.80it/s, avg_loss=0.00232, loss=0.00155]\n", "Training Epoch [43/100]: 100%|██████████| 54/54 [00:00<00:00, 215.43it/s, avg_loss=0.0025, loss=0.00233] \n", "Validation Epoch [43/100]: 100%|██████████| 13/13 [00:00<00:00, 297.65it/s, avg_loss=0.00225, loss=0.00149]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [44/100]: 100%|██████████| 54/54 [00:00<00:00, 183.46it/s, avg_loss=0.00244, loss=0.00225]\n", "Validation Epoch [44/100]: 100%|██████████| 13/13 [00:00<00:00, 242.94it/s, avg_loss=0.00219, loss=0.00147]\n", "Training Epoch [45/100]: 100%|██████████| 54/54 [00:00<00:00, 222.58it/s, avg_loss=0.00239, loss=0.0027] \n", "Validation Epoch [45/100]: 100%|██████████| 13/13 [00:00<00:00, 321.88it/s, avg_loss=0.00216, loss=0.00142]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [46/100]: 100%|██████████| 54/54 [00:00<00:00, 227.08it/s, avg_loss=0.00235, loss=0.00245]\n", "Validation Epoch [46/100]: 100%|██████████| 13/13 [00:00<00:00, 325.22it/s, avg_loss=0.0021, loss=0.00139]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [47/100]: 100%|██████████| 54/54 [00:00<00:00, 206.35it/s, avg_loss=0.00231, loss=0.00238]\n", "Validation Epoch [47/100]: 100%|██████████| 13/13 [00:00<00:00, 244.05it/s, avg_loss=0.00207, loss=0.00139]\n", "Training Epoch [48/100]: 100%|██████████| 54/54 [00:00<00:00, 211.43it/s, avg_loss=0.00227, loss=0.00208]\n", "Validation Epoch [48/100]: 100%|██████████| 13/13 [00:00<00:00, 318.52it/s, avg_loss=0.00201, loss=0.00135]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [49/100]: 100%|██████████| 54/54 [00:00<00:00, 214.42it/s, avg_loss=0.00223, loss=0.0022] \n", "Validation Epoch [49/100]: 100%|██████████| 13/13 [00:00<00:00, 271.49it/s, avg_loss=0.00198, loss=0.00134]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [50/100]: 100%|██████████| 54/54 [00:00<00:00, 211.44it/s, avg_loss=0.0022, loss=0.00229] \n", "Validation Epoch [50/100]: 100%|██████████| 13/13 [00:00<00:00, 318.39it/s, avg_loss=0.00195, loss=0.00132]\n", "Training Epoch [51/100]: 100%|██████████| 54/54 [00:00<00:00, 230.65it/s, avg_loss=0.00217, loss=0.00228]\n", "Validation Epoch [51/100]: 100%|██████████| 13/13 [00:00<00:00, 303.43it/s, avg_loss=0.00192, loss=0.00131]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [52/100]: 100%|██████████| 54/54 [00:00<00:00, 230.79it/s, avg_loss=0.00214, loss=0.00223]\n", "Validation Epoch [52/100]: 100%|██████████| 13/13 [00:00<00:00, 297.23it/s, avg_loss=0.00189, loss=0.00131]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [53/100]: 100%|██████████| 54/54 [00:00<00:00, 210.81it/s, avg_loss=0.00211, loss=0.0022] \n", "Validation Epoch [53/100]: 100%|██████████| 13/13 [00:00<00:00, 277.31it/s, avg_loss=0.00185, loss=0.0013]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 68/68 [00:00<00:00, 386.09it/s]\n", "(2025-07-29 15:17:32,390) [INFO]:     [AE] handling dataset AIOPS | curve 6efa3a07-4544-34a0-b921-a155bd1a05e8 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 222.14it/s, avg_loss=0.0128, loss=0.000848]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 307.97it/s, avg_loss=0.0022, loss=0.00138]  \n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 220.02it/s, avg_loss=0.000762, loss=0.000932]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 277.13it/s, avg_loss=0.00187, loss=0.00132] \n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 212.27it/s, avg_loss=0.000553, loss=0.000236]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 274.33it/s, avg_loss=0.00177, loss=0.0013]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 223.82it/s, avg_loss=0.000501, loss=0.000214]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 291.34it/s, avg_loss=0.00174, loss=0.0013]  \n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 218.66it/s, avg_loss=0.000484, loss=0.000399]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 288.39it/s, avg_loss=0.00169, loss=0.00129] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 934/934 [00:04<00:00, 222.30it/s, avg_loss=0.000451, loss=0.000161]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:00<00:00, 303.43it/s, avg_loss=0.00153, loss=0.00128]  \n", "Training Epoch [7/100]: 100%|██████████| 934/934 [00:04<00:00, 219.41it/s, avg_loss=0.000401, loss=0.000347]\n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:00<00:00, 292.68it/s, avg_loss=0.0014, loss=0.00128]   \n", "Training Epoch [8/100]: 100%|██████████| 934/934 [00:04<00:00, 207.77it/s, avg_loss=0.000374, loss=0.000269]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:00<00:00, 276.24it/s, avg_loss=0.00135, loss=0.00127]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 934/934 [00:04<00:00, 197.78it/s, avg_loss=0.000365, loss=0.000199]\n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:00<00:00, 299.74it/s, avg_loss=0.00133, loss=0.00127]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [10/100]: 100%|██████████| 934/934 [00:04<00:00, 209.98it/s, avg_loss=0.000361, loss=0.000271]\n", "Validation Epoch [10/100]: 100%|██████████| 233/233 [00:00<00:00, 282.33it/s, avg_loss=0.00132, loss=0.00126]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 354.23it/s]\n", "(2025-07-29 15:18:30,602) [INFO]:     [AE] handling dataset AIOPS | curve 431a8542-c468-3988-a508-3afd06a218da \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:03<00:00, 205.91it/s, avg_loss=0.0232, loss=0.00183]\n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 271.82it/s, avg_loss=0.00112, loss=0.000921]\n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:04<00:00, 202.75it/s, avg_loss=0.00125, loss=0.000752]\n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 288.91it/s, avg_loss=0.000846, loss=0.00041] \n", "Training Epoch [3/100]: 100%|██████████| 823/823 [00:03<00:00, 209.54it/s, avg_loss=0.001, loss=0.00095]    \n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 295.26it/s, avg_loss=0.000826, loss=0.000409]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 224.52it/s, avg_loss=0.000915, loss=0.0008]  \n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 305.49it/s, avg_loss=0.000811, loss=0.000389]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 823/823 [00:03<00:00, 222.41it/s, avg_loss=0.000828, loss=0.000648]\n", "Validation Epoch [5/100]: 100%|██████████| 205/205 [00:00<00:00, 296.58it/s, avg_loss=0.000662, loss=0.000224]\n", "Training Epoch [6/100]: 100%|██████████| 823/823 [00:03<00:00, 215.04it/s, avg_loss=0.000627, loss=0.000459]\n", "Validation Epoch [6/100]: 100%|██████████| 205/205 [00:00<00:00, 292.83it/s, avg_loss=0.000548, loss=9.44e-5] \n", "Training Epoch [7/100]: 100%|██████████| 823/823 [00:03<00:00, 210.17it/s, avg_loss=0.000573, loss=0.000894]\n", "Validation Epoch [7/100]: 100%|██████████| 205/205 [00:00<00:00, 286.90it/s, avg_loss=0.000536, loss=8.79e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [8/100]: 100%|██████████| 823/823 [00:03<00:00, 219.00it/s, avg_loss=0.000564, loss=0.000375]\n", "Validation Epoch [8/100]: 100%|██████████| 205/205 [00:00<00:00, 292.80it/s, avg_loss=0.000533, loss=8.97e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 823/823 [00:03<00:00, 220.31it/s, avg_loss=0.000558, loss=0.00115] \n", "Validation Epoch [9/100]: 100%|██████████| 205/205 [00:00<00:00, 294.92it/s, avg_loss=0.00053, loss=8.47e-5]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 390.38it/s]\n", "(2025-07-29 15:19:17,087) [INFO]:     [AE] handling dataset AIOPS | curve ba5f3328-9f3f-3ff5-a683-84437d16d554 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 221.12it/s, avg_loss=0.0684, loss=0.00212]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 295.57it/s, avg_loss=0.00171, loss=0.000839]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 217.72it/s, avg_loss=0.00185, loss=0.00193]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 289.36it/s, avg_loss=0.00153, loss=0.000746]\n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 208.99it/s, avg_loss=0.00164, loss=0.00129]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 277.27it/s, avg_loss=0.00132, loss=0.000668]\n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 189.29it/s, avg_loss=0.00134, loss=0.00121] \n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 245.19it/s, avg_loss=0.00105, loss=0.000523] \n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:05<00:00, 185.62it/s, avg_loss=0.00117, loss=0.000831]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 236.88it/s, avg_loss=0.000966, loss=0.000484]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 934/934 [00:04<00:00, 222.58it/s, avg_loss=0.00104, loss=0.000797]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:00<00:00, 296.45it/s, avg_loss=0.000834, loss=0.000395]\n", "Training Epoch [7/100]: 100%|██████████| 934/934 [00:04<00:00, 220.40it/s, avg_loss=0.000831, loss=0.00115] \n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:00<00:00, 290.87it/s, avg_loss=0.00067, loss=0.000314] \n", "Training Epoch [8/100]: 100%|██████████| 934/934 [00:04<00:00, 212.58it/s, avg_loss=0.000698, loss=0.000571]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:00<00:00, 266.53it/s, avg_loss=0.000615, loss=0.000279]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 934/934 [00:04<00:00, 221.16it/s, avg_loss=0.000663, loss=0.000831]\n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:00<00:00, 300.58it/s, avg_loss=0.000604, loss=0.000269]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [10/100]: 100%|██████████| 934/934 [00:04<00:00, 222.85it/s, avg_loss=0.000657, loss=0.000588]\n", "Validation Epoch [10/100]: 100%|██████████| 233/233 [00:00<00:00, 304.61it/s, avg_loss=0.000601, loss=0.000269]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 386.78it/s]\n", "(2025-07-29 15:20:15,734) [INFO]:     [AE] handling dataset AIOPS | curve 847e8ecc-f8d2-3a93-9107-f367a0aab37d \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 224.67it/s, avg_loss=0.0163, loss=0.000281]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 283.07it/s, avg_loss=0.000687, loss=0.000485]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 202.00it/s, avg_loss=0.000288, loss=0.000202]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 288.70it/s, avg_loss=0.000602, loss=0.00041] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 216.90it/s, avg_loss=0.000263, loss=0.00022] \n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 283.68it/s, avg_loss=0.000509, loss=0.000329]\n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 214.63it/s, avg_loss=0.000237, loss=0.000287]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 249.98it/s, avg_loss=0.000427, loss=0.00026] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 220.07it/s, avg_loss=0.000211, loss=0.000153]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 300.09it/s, avg_loss=0.000303, loss=0.000156]\n", "Training Epoch [6/100]: 100%|██████████| 934/934 [00:04<00:00, 217.07it/s, avg_loss=0.000179, loss=9.33e-5] \n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:00<00:00, 294.67it/s, avg_loss=0.000246, loss=0.000108]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [7/100]: 100%|██████████| 934/934 [00:04<00:00, 224.27it/s, avg_loss=0.00017, loss=0.000126] \n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:00<00:00, 302.22it/s, avg_loss=0.000234, loss=9.45e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [8/100]: 100%|██████████| 934/934 [00:04<00:00, 222.52it/s, avg_loss=0.000167, loss=0.000143]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:00<00:00, 303.49it/s, avg_loss=0.000229, loss=9.63e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 385.29it/s]\n", "(2025-07-29 15:21:02,618) [INFO]:     [AE] handling dataset AIOPS | curve 54350a12-7a9d-3ca8-b81f-f886b9d156fd \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 55/55 [00:00<00:00, 227.05it/s, avg_loss=0.0988, loss=0.0929]\n", "Validation Epoch [1/100]: 100%|██████████| 13/13 [00:00<00:00, 319.37it/s, avg_loss=0.0898, loss=0.0963]\n", "Training Epoch [2/100]: 100%|██████████| 55/55 [00:00<00:00, 231.12it/s, avg_loss=0.0865, loss=0.0837]\n", "Validation Epoch [2/100]: 100%|██████████| 13/13 [00:00<00:00, 296.53it/s, avg_loss=0.0781, loss=0.0835]\n", "Training Epoch [3/100]: 100%|██████████| 55/55 [00:00<00:00, 230.14it/s, avg_loss=0.0754, loss=0.0691]\n", "Validation Epoch [3/100]: 100%|██████████| 13/13 [00:00<00:00, 317.06it/s, avg_loss=0.0674, loss=0.0716]\n", "Training Epoch [4/100]: 100%|██████████| 55/55 [00:00<00:00, 219.52it/s, avg_loss=0.0639, loss=0.0578]\n", "Validation Epoch [4/100]: 100%|██████████| 13/13 [00:00<00:00, 315.08it/s, avg_loss=0.0558, loss=0.0588]\n", "Training Epoch [5/100]: 100%|██████████| 55/55 [00:00<00:00, 226.85it/s, avg_loss=0.0511, loss=0.0443]\n", "Validation Epoch [5/100]: 100%|██████████| 13/13 [00:00<00:00, 296.82it/s, avg_loss=0.0431, loss=0.0444]\n", "Training Epoch [6/100]: 100%|██████████| 55/55 [00:00<00:00, 214.60it/s, avg_loss=0.0381, loss=0.033] \n", "Validation Epoch [6/100]: 100%|██████████| 13/13 [00:00<00:00, 312.50it/s, avg_loss=0.0317, loss=0.0316]\n", "Training Epoch [7/100]: 100%|██████████| 55/55 [00:00<00:00, 226.69it/s, avg_loss=0.0274, loss=0.0232]\n", "Validation Epoch [7/100]: 100%|██████████| 13/13 [00:00<00:00, 312.29it/s, avg_loss=0.0229, loss=0.0218]\n", "Training Epoch [8/100]: 100%|██████████| 55/55 [00:00<00:00, 222.23it/s, avg_loss=0.0196, loss=0.0163]\n", "Validation Epoch [8/100]: 100%|██████████| 13/13 [00:00<00:00, 305.56it/s, avg_loss=0.0166, loss=0.0148]\n", "Training Epoch [9/100]: 100%|██████████| 55/55 [00:00<00:00, 225.47it/s, avg_loss=0.014, loss=0.0123] \n", "Validation Epoch [9/100]: 100%|██████████| 13/13 [00:00<00:00, 301.29it/s, avg_loss=0.0123, loss=0.0101]\n", "Training Epoch [10/100]: 100%|██████████| 55/55 [00:00<00:00, 226.56it/s, avg_loss=0.0104, loss=0.00899]\n", "Validation Epoch [10/100]: 100%|██████████| 13/13 [00:00<00:00, 307.95it/s, avg_loss=0.00957, loss=0.00715]\n", "Training Epoch [11/100]: 100%|██████████| 55/55 [00:00<00:00, 227.36it/s, avg_loss=0.00813, loss=0.00676]\n", "Validation Epoch [11/100]: 100%|██████████| 13/13 [00:00<00:00, 307.28it/s, avg_loss=0.0079, loss=0.0053]\n", "Training Epoch [12/100]: 100%|██████████| 55/55 [00:00<00:00, 217.47it/s, avg_loss=0.00677, loss=0.00654]\n", "Validation Epoch [12/100]: 100%|██████████| 13/13 [00:00<00:00, 309.13it/s, avg_loss=0.00691, loss=0.00421]\n", "Training Epoch [13/100]: 100%|██████████| 55/55 [00:00<00:00, 221.95it/s, avg_loss=0.00599, loss=0.00607]\n", "Validation Epoch [13/100]: 100%|██████████| 13/13 [00:00<00:00, 308.98it/s, avg_loss=0.00636, loss=0.00359]\n", "Training Epoch [14/100]: 100%|██████████| 55/55 [00:00<00:00, 229.03it/s, avg_loss=0.00556, loss=0.00548]\n", "Validation Epoch [14/100]: 100%|██████████| 13/13 [00:00<00:00, 308.97it/s, avg_loss=0.00606, loss=0.00328]\n", "Training Epoch [15/100]: 100%|██████████| 55/55 [00:00<00:00, 221.40it/s, avg_loss=0.00533, loss=0.00529]\n", "Validation Epoch [15/100]: 100%|██████████| 13/13 [00:00<00:00, 288.01it/s, avg_loss=0.00591, loss=0.00311]\n", "Training Epoch [16/100]: 100%|██████████| 55/55 [00:00<00:00, 220.88it/s, avg_loss=0.00521, loss=0.00511]\n", "Validation Epoch [16/100]: 100%|██████████| 13/13 [00:00<00:00, 310.63it/s, avg_loss=0.00583, loss=0.00302]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [17/100]: 100%|██████████| 55/55 [00:00<00:00, 229.46it/s, avg_loss=0.00516, loss=0.0045] \n", "Validation Epoch [17/100]: 100%|██████████| 13/13 [00:00<00:00, 320.15it/s, avg_loss=0.00579, loss=0.00299]\n", "Training Epoch [18/100]: 100%|██████████| 55/55 [00:00<00:00, 225.62it/s, avg_loss=0.00513, loss=0.00565]\n", "Validation Epoch [18/100]: 100%|██████████| 13/13 [00:00<00:00, 310.32it/s, avg_loss=0.00578, loss=0.00297]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [19/100]: 100%|██████████| 55/55 [00:00<00:00, 228.03it/s, avg_loss=0.00511, loss=0.00524]\n", "Validation Epoch [19/100]: 100%|██████████| 13/13 [00:00<00:00, 310.59it/s, avg_loss=0.00576, loss=0.00297]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [20/100]: 100%|██████████| 55/55 [00:00<00:00, 222.15it/s, avg_loss=0.00511, loss=0.0055] \n", "Validation Epoch [20/100]: 100%|██████████| 13/13 [00:00<00:00, 313.76it/s, avg_loss=0.00575, loss=0.00295]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 69/69 [00:00<00:00, 396.15it/s]\n", "(2025-07-29 15:21:08,823) [INFO]:     [AE] handling dataset AIOPS | curve 0efb375b-b902-3661-ab23-9a0bb799f4e3 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 54/54 [00:00<00:00, 227.35it/s, avg_loss=0.208, loss=0.208]\n", "Validation Epoch [1/100]: 100%|██████████| 13/13 [00:00<00:00, 314.92it/s, avg_loss=0.13, loss=0.143]\n", "Training Epoch [2/100]: 100%|██████████| 54/54 [00:00<00:00, 216.67it/s, avg_loss=0.194, loss=0.184]\n", "Validation Epoch [2/100]: 100%|██████████| 13/13 [00:00<00:00, 307.32it/s, avg_loss=0.119, loss=0.134]\n", "Training Epoch [3/100]: 100%|██████████| 54/54 [00:00<00:00, 229.92it/s, avg_loss=0.17, loss=0.145] \n", "Validation Epoch [3/100]: 100%|██████████| 13/13 [00:00<00:00, 319.40it/s, avg_loss=0.0985, loss=0.12]\n", "Training Epoch [4/100]: 100%|██████████| 54/54 [00:00<00:00, 227.37it/s, avg_loss=0.131, loss=0.111]\n", "Validation Epoch [4/100]: 100%|██████████| 13/13 [00:00<00:00, 311.47it/s, avg_loss=0.0753, loss=0.101]\n", "Training Epoch [5/100]: 100%|██████████| 54/54 [00:00<00:00, 228.34it/s, avg_loss=0.0957, loss=0.0813]\n", "Validation Epoch [5/100]: 100%|██████████| 13/13 [00:00<00:00, 311.56it/s, avg_loss=0.0553, loss=0.0829]\n", "Training Epoch [6/100]: 100%|██████████| 54/54 [00:00<00:00, 223.89it/s, avg_loss=0.0675, loss=0.0542]\n", "Validation Epoch [6/100]: 100%|██████████| 13/13 [00:00<00:00, 313.51it/s, avg_loss=0.0399, loss=0.0678]\n", "Training Epoch [7/100]: 100%|██████████| 54/54 [00:00<00:00, 229.13it/s, avg_loss=0.048, loss=0.0418] \n", "Validation Epoch [7/100]: 100%|██████████| 13/13 [00:00<00:00, 312.65it/s, avg_loss=0.0303, loss=0.0573]\n", "Training Epoch [8/100]: 100%|██████████| 54/54 [00:00<00:00, 227.74it/s, avg_loss=0.0371, loss=0.0357]\n", "Validation Epoch [8/100]: 100%|██████████| 13/13 [00:00<00:00, 320.49it/s, avg_loss=0.0253, loss=0.0514]\n", "Training Epoch [9/100]: 100%|██████████| 54/54 [00:00<00:00, 220.57it/s, avg_loss=0.0315, loss=0.0264]\n", "Validation Epoch [9/100]: 100%|██████████| 13/13 [00:00<00:00, 314.39it/s, avg_loss=0.0226, loss=0.0477]\n", "Training Epoch [10/100]: 100%|██████████| 54/54 [00:00<00:00, 227.93it/s, avg_loss=0.0281, loss=0.0279]\n", "Validation Epoch [10/100]: 100%|██████████| 13/13 [00:00<00:00, 314.70it/s, avg_loss=0.0207, loss=0.0445]\n", "Training Epoch [11/100]: 100%|██████████| 54/54 [00:00<00:00, 223.65it/s, avg_loss=0.0255, loss=0.0227]\n", "Validation Epoch [11/100]: 100%|██████████| 13/13 [00:00<00:00, 306.58it/s, avg_loss=0.0192, loss=0.0416]\n", "Training Epoch [12/100]: 100%|██████████| 54/54 [00:00<00:00, 218.06it/s, avg_loss=0.0231, loss=0.0216]\n", "Validation Epoch [12/100]: 100%|██████████| 13/13 [00:00<00:00, 285.13it/s, avg_loss=0.0177, loss=0.0384]\n", "Training Epoch [13/100]: 100%|██████████| 54/54 [00:00<00:00, 217.69it/s, avg_loss=0.0206, loss=0.0198]\n", "Validation Epoch [13/100]: 100%|██████████| 13/13 [00:00<00:00, 311.49it/s, avg_loss=0.016, loss=0.0349]\n", "Training Epoch [14/100]: 100%|██████████| 54/54 [00:00<00:00, 224.55it/s, avg_loss=0.0183, loss=0.0152]\n", "Validation Epoch [14/100]: 100%|██████████| 13/13 [00:00<00:00, 309.93it/s, avg_loss=0.0146, loss=0.0317]\n", "Training Epoch [15/100]: 100%|██████████| 54/54 [00:00<00:00, 227.68it/s, avg_loss=0.0163, loss=0.0149]\n", "Validation Epoch [15/100]: 100%|██████████| 13/13 [00:00<00:00, 302.57it/s, avg_loss=0.0134, loss=0.0288]\n", "Training Epoch [16/100]: 100%|██████████| 54/54 [00:00<00:00, 213.68it/s, avg_loss=0.0148, loss=0.0138]\n", "Validation Epoch [16/100]: 100%|██████████| 13/13 [00:00<00:00, 307.28it/s, avg_loss=0.0124, loss=0.0264]\n", "Training Epoch [17/100]: 100%|██████████| 54/54 [00:00<00:00, 216.61it/s, avg_loss=0.0135, loss=0.0119]\n", "Validation Epoch [17/100]: 100%|██████████| 13/13 [00:00<00:00, 313.32it/s, avg_loss=0.0116, loss=0.0243]\n", "Training Epoch [18/100]: 100%|██████████| 54/54 [00:00<00:00, 222.83it/s, avg_loss=0.0126, loss=0.0119]\n", "Validation Epoch [18/100]: 100%|██████████| 13/13 [00:00<00:00, 307.47it/s, avg_loss=0.011, loss=0.0227]\n", "Training Epoch [19/100]: 100%|██████████| 54/54 [00:00<00:00, 213.07it/s, avg_loss=0.0118, loss=0.0112]\n", "Validation Epoch [19/100]: 100%|██████████| 13/13 [00:00<00:00, 297.88it/s, avg_loss=0.0105, loss=0.0213]\n", "Training Epoch [20/100]: 100%|██████████| 54/54 [00:00<00:00, 221.51it/s, avg_loss=0.0112, loss=0.0109] \n", "Validation Epoch [20/100]: 100%|██████████| 13/13 [00:00<00:00, 309.36it/s, avg_loss=0.0101, loss=0.0202]\n", "Training Epoch [21/100]: 100%|██████████| 54/54 [00:00<00:00, 225.79it/s, avg_loss=0.0107, loss=0.0103] \n", "Validation Epoch [21/100]: 100%|██████████| 13/13 [00:00<00:00, 307.12it/s, avg_loss=0.00973, loss=0.0191]\n", "Training Epoch [22/100]: 100%|██████████| 54/54 [00:00<00:00, 221.79it/s, avg_loss=0.0102, loss=0.0105] \n", "Validation Epoch [22/100]: 100%|██████████| 13/13 [00:00<00:00, 306.74it/s, avg_loss=0.00928, loss=0.0179]\n", "Training Epoch [23/100]: 100%|██████████| 54/54 [00:00<00:00, 222.01it/s, avg_loss=0.00969, loss=0.00893]\n", "Validation Epoch [23/100]: 100%|██████████| 13/13 [00:00<00:00, 306.02it/s, avg_loss=0.00883, loss=0.0166]\n", "Training Epoch [24/100]: 100%|██████████| 54/54 [00:00<00:00, 223.13it/s, avg_loss=0.00912, loss=0.00884]\n", "Validation Epoch [24/100]: 100%|██████████| 13/13 [00:00<00:00, 291.58it/s, avg_loss=0.00828, loss=0.015]\n", "Training Epoch [25/100]: 100%|██████████| 54/54 [00:00<00:00, 219.84it/s, avg_loss=0.00835, loss=0.0075] \n", "Validation Epoch [25/100]: 100%|██████████| 13/13 [00:00<00:00, 310.41it/s, avg_loss=0.00757, loss=0.0134]\n", "Training Epoch [26/100]: 100%|██████████| 54/54 [00:00<00:00, 215.05it/s, avg_loss=0.00761, loss=0.00662]\n", "Validation Epoch [26/100]: 100%|██████████| 13/13 [00:00<00:00, 304.69it/s, avg_loss=0.00695, loss=0.0117]\n", "Training Epoch [27/100]: 100%|██████████| 54/54 [00:00<00:00, 173.17it/s, avg_loss=0.00691, loss=0.00696]\n", "Validation Epoch [27/100]: 100%|██████████| 13/13 [00:00<00:00, 235.25it/s, avg_loss=0.00633, loss=0.01]\n", "Training Epoch [28/100]: 100%|██████████| 54/54 [00:00<00:00, 214.37it/s, avg_loss=0.00625, loss=0.00597]\n", "Validation Epoch [28/100]: 100%|██████████| 13/13 [00:00<00:00, 305.70it/s, avg_loss=0.00575, loss=0.00861]\n", "Training Epoch [29/100]: 100%|██████████| 54/54 [00:00<00:00, 209.94it/s, avg_loss=0.00565, loss=0.00548]\n", "Validation Epoch [29/100]: 100%|██████████| 13/13 [00:00<00:00, 297.41it/s, avg_loss=0.00529, loss=0.00738]\n", "Training Epoch [30/100]: 100%|██████████| 54/54 [00:00<00:00, 205.76it/s, avg_loss=0.00514, loss=0.0048] \n", "Validation Epoch [30/100]: 100%|██████████| 13/13 [00:00<00:00, 297.29it/s, avg_loss=0.00485, loss=0.00621]\n", "Training Epoch [31/100]: 100%|██████████| 54/54 [00:00<00:00, 199.74it/s, avg_loss=0.00467, loss=0.00451]\n", "Validation Epoch [31/100]: 100%|██████████| 13/13 [00:00<00:00, 285.00it/s, avg_loss=0.00447, loss=0.00518]\n", "Training Epoch [32/100]: 100%|██████████| 54/54 [00:00<00:00, 204.02it/s, avg_loss=0.00427, loss=0.00429]\n", "Validation Epoch [32/100]: 100%|██████████| 13/13 [00:00<00:00, 285.42it/s, avg_loss=0.00412, loss=0.00427]\n", "Training Epoch [33/100]: 100%|██████████| 54/54 [00:00<00:00, 201.35it/s, avg_loss=0.00393, loss=0.00375]\n", "Validation Epoch [33/100]: 100%|██████████| 13/13 [00:00<00:00, 231.32it/s, avg_loss=0.00386, loss=0.00357]\n", "Training Epoch [34/100]: 100%|██████████| 54/54 [00:00<00:00, 172.38it/s, avg_loss=0.00366, loss=0.00372]\n", "Validation Epoch [34/100]: 100%|██████████| 13/13 [00:00<00:00, 231.53it/s, avg_loss=0.00365, loss=0.00306]\n", "Training Epoch [35/100]: 100%|██████████| 54/54 [00:00<00:00, 196.11it/s, avg_loss=0.00345, loss=0.00332]\n", "Validation Epoch [35/100]: 100%|██████████| 13/13 [00:00<00:00, 279.35it/s, avg_loss=0.00347, loss=0.00268]\n", "Training Epoch [36/100]: 100%|██████████| 54/54 [00:00<00:00, 202.60it/s, avg_loss=0.00329, loss=0.00304]\n", "Validation Epoch [36/100]: 100%|██████████| 13/13 [00:00<00:00, 299.96it/s, avg_loss=0.00332, loss=0.00239]\n", "Training Epoch [37/100]: 100%|██████████| 54/54 [00:00<00:00, 216.43it/s, avg_loss=0.00315, loss=0.00352]\n", "Validation Epoch [37/100]: 100%|██████████| 13/13 [00:00<00:00, 298.03it/s, avg_loss=0.0032, loss=0.00223]\n", "Training Epoch [38/100]: 100%|██████████| 54/54 [00:00<00:00, 208.17it/s, avg_loss=0.00304, loss=0.00339]\n", "Validation Epoch [38/100]: 100%|██████████| 13/13 [00:00<00:00, 287.77it/s, avg_loss=0.00311, loss=0.00206]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [39/100]: 100%|██████████| 54/54 [00:00<00:00, 223.32it/s, avg_loss=0.00295, loss=0.00303]\n", "Validation Epoch [39/100]: 100%|██████████| 13/13 [00:00<00:00, 316.09it/s, avg_loss=0.00302, loss=0.00198]\n", "Training Epoch [40/100]: 100%|██████████| 54/54 [00:00<00:00, 231.20it/s, avg_loss=0.00287, loss=0.00282]\n", "Validation Epoch [40/100]: 100%|██████████| 13/13 [00:00<00:00, 322.73it/s, avg_loss=0.00293, loss=0.00188]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [41/100]: 100%|██████████| 54/54 [00:00<00:00, 231.03it/s, avg_loss=0.0028, loss=0.00265] \n", "Validation Epoch [41/100]: 100%|██████████| 13/13 [00:00<00:00, 322.58it/s, avg_loss=0.00286, loss=0.00179]\n", "Training Epoch [42/100]: 100%|██████████| 54/54 [00:00<00:00, 230.25it/s, avg_loss=0.00274, loss=0.0025] \n", "Validation Epoch [42/100]: 100%|██████████| 13/13 [00:00<00:00, 307.30it/s, avg_loss=0.0028, loss=0.00174]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [43/100]: 100%|██████████| 54/54 [00:00<00:00, 223.99it/s, avg_loss=0.00268, loss=0.00274]\n", "Validation Epoch [43/100]: 100%|██████████| 13/13 [00:00<00:00, 273.64it/s, avg_loss=0.00273, loss=0.00167]\n", "Training Epoch [44/100]: 100%|██████████| 54/54 [00:00<00:00, 226.45it/s, avg_loss=0.00263, loss=0.00262]\n", "Validation Epoch [44/100]: 100%|██████████| 13/13 [00:00<00:00, 319.04it/s, avg_loss=0.00269, loss=0.00166]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [45/100]: 100%|██████████| 54/54 [00:00<00:00, 220.47it/s, avg_loss=0.00258, loss=0.00245]\n", "Validation Epoch [45/100]: 100%|██████████| 13/13 [00:00<00:00, 315.50it/s, avg_loss=0.00265, loss=0.00161]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [46/100]: 100%|██████████| 54/54 [00:00<00:00, 229.41it/s, avg_loss=0.00254, loss=0.00221]\n", "Validation Epoch [46/100]: 100%|██████████| 13/13 [00:00<00:00, 292.96it/s, avg_loss=0.00259, loss=0.00159]\n", "Training Epoch [47/100]: 100%|██████████| 54/54 [00:00<00:00, 226.88it/s, avg_loss=0.0025, loss=0.0025]  \n", "Validation Epoch [47/100]: 100%|██████████| 13/13 [00:00<00:00, 302.07it/s, avg_loss=0.00253, loss=0.00162]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [48/100]: 100%|██████████| 54/54 [00:00<00:00, 220.52it/s, avg_loss=0.00247, loss=0.0023] \n", "Validation Epoch [48/100]: 100%|██████████| 13/13 [00:00<00:00, 313.27it/s, avg_loss=0.0025, loss=0.00156]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [49/100]: 100%|██████████| 54/54 [00:00<00:00, 227.09it/s, avg_loss=0.00243, loss=0.00249]\n", "Validation Epoch [49/100]: 100%|██████████| 13/13 [00:00<00:00, 313.37it/s, avg_loss=0.00247, loss=0.0015]\n", "Training Epoch [50/100]: 100%|██████████| 54/54 [00:00<00:00, 206.95it/s, avg_loss=0.0024, loss=0.00237] \n", "Validation Epoch [50/100]: 100%|██████████| 13/13 [00:00<00:00, 282.56it/s, avg_loss=0.00245, loss=0.00147]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [51/100]: 100%|██████████| 54/54 [00:00<00:00, 230.37it/s, avg_loss=0.00237, loss=0.0021] \n", "Validation Epoch [51/100]: 100%|██████████| 13/13 [00:00<00:00, 313.01it/s, avg_loss=0.00238, loss=0.00152]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [52/100]: 100%|██████████| 54/54 [00:00<00:00, 222.61it/s, avg_loss=0.00234, loss=0.00216]\n", "Validation Epoch [52/100]: 100%|██████████| 13/13 [00:00<00:00, 296.54it/s, avg_loss=0.00236, loss=0.0015]\n", "Training Epoch [53/100]: 100%|██████████| 54/54 [00:00<00:00, 227.95it/s, avg_loss=0.00232, loss=0.00233]\n", "Validation Epoch [53/100]: 100%|██████████| 13/13 [00:00<00:00, 320.13it/s, avg_loss=0.00232, loss=0.00144]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [54/100]: 100%|██████████| 54/54 [00:00<00:00, 227.02it/s, avg_loss=0.00229, loss=0.00233]\n", "Validation Epoch [54/100]: 100%|██████████| 13/13 [00:00<00:00, 312.43it/s, avg_loss=0.00229, loss=0.00144]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [55/100]: 100%|██████████| 54/54 [00:00<00:00, 221.72it/s, avg_loss=0.00226, loss=0.00179]\n", "Validation Epoch [55/100]: 100%|██████████| 13/13 [00:00<00:00, 296.85it/s, avg_loss=0.00226, loss=0.00147]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 68/68 [00:00<00:00, 395.47it/s]\n", "(2025-07-29 15:21:25,461) [INFO]:     [AE] handling dataset AIOPS | curve 05f10d3a-239c-3bef-9bdc-a2feeb0037aa \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 219.15it/s, avg_loss=0.0348, loss=0.000588]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 290.56it/s, avg_loss=0.00122, loss=0.000318]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 216.69it/s, avg_loss=0.000509, loss=0.000422]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 293.76it/s, avg_loss=0.000793, loss=0.000191]\n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 211.21it/s, avg_loss=0.000401, loss=0.000433]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 271.01it/s, avg_loss=0.000528, loss=0.00012] \n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 193.11it/s, avg_loss=0.000356, loss=0.000231]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 248.69it/s, avg_loss=0.000454, loss=0.000104]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 222.53it/s, avg_loss=0.000344, loss=0.000308]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 300.81it/s, avg_loss=0.000436, loss=9.81e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 934/934 [00:04<00:00, 224.57it/s, avg_loss=0.000339, loss=0.000247]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:00<00:00, 298.47it/s, avg_loss=0.000429, loss=9.94e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 387.47it/s]\n", "(2025-07-29 15:22:02,615) [INFO]:     [AE] handling dataset AIOPS | curve 301c70d8-1630-35ac-8f96-bc1b6f4359ea \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 54/54 [00:00<00:00, 224.82it/s, avg_loss=0.274, loss=0.254]\n", "Validation Epoch [1/100]: 100%|██████████| 13/13 [00:00<00:00, 319.84it/s, avg_loss=0.206, loss=0.184]\n", "Training Epoch [2/100]: 100%|██████████| 54/54 [00:00<00:00, 229.38it/s, avg_loss=0.244, loss=0.224]\n", "Validation Epoch [2/100]: 100%|██████████| 13/13 [00:00<00:00, 312.00it/s, avg_loss=0.18, loss=0.167]\n", "Training Epoch [3/100]: 100%|██████████| 54/54 [00:00<00:00, 224.14it/s, avg_loss=0.204, loss=0.182]\n", "Validation Epoch [3/100]: 100%|██████████| 13/13 [00:00<00:00, 290.56it/s, avg_loss=0.144, loss=0.146]\n", "Training Epoch [4/100]: 100%|██████████| 54/54 [00:00<00:00, 217.18it/s, avg_loss=0.157, loss=0.128]\n", "Validation Epoch [4/100]: 100%|██████████| 13/13 [00:00<00:00, 315.36it/s, avg_loss=0.11, loss=0.127]\n", "Training Epoch [5/100]: 100%|██████████| 54/54 [00:00<00:00, 225.74it/s, avg_loss=0.118, loss=0.106] \n", "Validation Epoch [5/100]: 100%|██████████| 13/13 [00:00<00:00, 314.27it/s, avg_loss=0.0835, loss=0.11]\n", "Training Epoch [6/100]: 100%|██████████| 54/54 [00:00<00:00, 222.11it/s, avg_loss=0.0884, loss=0.0787]\n", "Validation Epoch [6/100]: 100%|██████████| 13/13 [00:00<00:00, 314.66it/s, avg_loss=0.0649, loss=0.0983]\n", "Training Epoch [7/100]: 100%|██████████| 54/54 [00:00<00:00, 228.34it/s, avg_loss=0.0689, loss=0.0617]\n", "Validation Epoch [7/100]: 100%|██████████| 13/13 [00:00<00:00, 313.10it/s, avg_loss=0.0533, loss=0.0901]\n", "Training Epoch [8/100]: 100%|██████████| 54/54 [00:00<00:00, 206.03it/s, avg_loss=0.057, loss=0.0524] \n", "Validation Epoch [8/100]: 100%|██████████| 13/13 [00:00<00:00, 319.69it/s, avg_loss=0.0463, loss=0.0845]\n", "Training Epoch [9/100]: 100%|██████████| 54/54 [00:00<00:00, 229.51it/s, avg_loss=0.05, loss=0.0492]  \n", "Validation Epoch [9/100]: 100%|██████████| 13/13 [00:00<00:00, 304.67it/s, avg_loss=0.0423, loss=0.0806]\n", "Training Epoch [10/100]: 100%|██████████| 54/54 [00:00<00:00, 206.40it/s, avg_loss=0.0461, loss=0.0421]\n", "Validation Epoch [10/100]: 100%|██████████| 13/13 [00:00<00:00, 291.77it/s, avg_loss=0.0399, loss=0.0777]\n", "Training Epoch [11/100]: 100%|██████████| 54/54 [00:00<00:00, 203.73it/s, avg_loss=0.0436, loss=0.0458]\n", "Validation Epoch [11/100]: 100%|██████████| 13/13 [00:00<00:00, 310.52it/s, avg_loss=0.0383, loss=0.0754]\n", "Training Epoch [12/100]: 100%|██████████| 54/54 [00:00<00:00, 214.05it/s, avg_loss=0.0417, loss=0.0385]\n", "Validation Epoch [12/100]: 100%|██████████| 13/13 [00:00<00:00, 311.00it/s, avg_loss=0.0367, loss=0.0727]\n", "Training Epoch [13/100]: 100%|██████████| 54/54 [00:00<00:00, 220.73it/s, avg_loss=0.0397, loss=0.0406]\n", "Validation Epoch [13/100]: 100%|██████████| 13/13 [00:00<00:00, 306.52it/s, avg_loss=0.0345, loss=0.0679]\n", "Training Epoch [14/100]: 100%|██████████| 54/54 [00:00<00:00, 222.82it/s, avg_loss=0.0367, loss=0.0354]\n", "Validation Epoch [14/100]: 100%|██████████| 13/13 [00:00<00:00, 309.70it/s, avg_loss=0.0315, loss=0.0613]\n", "Training Epoch [15/100]: 100%|██████████| 54/54 [00:00<00:00, 221.51it/s, avg_loss=0.0328, loss=0.0307]\n", "Validation Epoch [15/100]: 100%|██████████| 13/13 [00:00<00:00, 292.44it/s, avg_loss=0.0277, loss=0.0528]\n", "Training Epoch [16/100]: 100%|██████████| 54/54 [00:00<00:00, 219.67it/s, avg_loss=0.0285, loss=0.028] \n", "Validation Epoch [16/100]: 100%|██████████| 13/13 [00:00<00:00, 299.62it/s, avg_loss=0.0241, loss=0.0449]\n", "Training Epoch [17/100]: 100%|██████████| 54/54 [00:00<00:00, 222.90it/s, avg_loss=0.0245, loss=0.0212]\n", "Validation Epoch [17/100]: 100%|██████████| 13/13 [00:00<00:00, 297.39it/s, avg_loss=0.0207, loss=0.0377]\n", "Training Epoch [18/100]: 100%|██████████| 54/54 [00:00<00:00, 217.12it/s, avg_loss=0.0208, loss=0.0182]\n", "Validation Epoch [18/100]: 100%|██████████| 13/13 [00:00<00:00, 310.13it/s, avg_loss=0.0179, loss=0.0319]\n", "Training Epoch [19/100]: 100%|██████████| 54/54 [00:00<00:00, 218.08it/s, avg_loss=0.018, loss=0.0161] \n", "Validation Epoch [19/100]: 100%|██████████| 13/13 [00:00<00:00, 306.59it/s, avg_loss=0.0159, loss=0.0279]\n", "Training Epoch [20/100]: 100%|██████████| 54/54 [00:00<00:00, 214.72it/s, avg_loss=0.016, loss=0.0151] \n", "Validation Epoch [20/100]: 100%|██████████| 13/13 [00:00<00:00, 302.46it/s, avg_loss=0.0144, loss=0.0249]\n", "Training Epoch [21/100]: 100%|██████████| 54/54 [00:00<00:00, 210.96it/s, avg_loss=0.0145, loss=0.0142]\n", "Validation Epoch [21/100]: 100%|██████████| 13/13 [00:00<00:00, 283.50it/s, avg_loss=0.0132, loss=0.0224]\n", "Training Epoch [22/100]: 100%|██████████| 54/54 [00:00<00:00, 225.40it/s, avg_loss=0.0132, loss=0.0127]\n", "Validation Epoch [22/100]: 100%|██████████| 13/13 [00:00<00:00, 313.47it/s, avg_loss=0.0121, loss=0.0203]\n", "Training Epoch [23/100]: 100%|██████████| 54/54 [00:00<00:00, 213.39it/s, avg_loss=0.0121, loss=0.0114]\n", "Validation Epoch [23/100]: 100%|██████████| 13/13 [00:00<00:00, 289.14it/s, avg_loss=0.0112, loss=0.0184]\n", "Training Epoch [24/100]: 100%|██████████| 54/54 [00:00<00:00, 213.14it/s, avg_loss=0.011, loss=0.0106]  \n", "Validation Epoch [24/100]: 100%|██████████| 13/13 [00:00<00:00, 301.67it/s, avg_loss=0.0102, loss=0.0163]\n", "Training Epoch [25/100]: 100%|██████████| 54/54 [00:00<00:00, 222.91it/s, avg_loss=0.00992, loss=0.00866]\n", "Validation Epoch [25/100]: 100%|██████████| 13/13 [00:00<00:00, 302.47it/s, avg_loss=0.00917, loss=0.0143]\n", "Training Epoch [26/100]: 100%|██████████| 54/54 [00:00<00:00, 221.44it/s, avg_loss=0.00888, loss=0.00876]\n", "Validation Epoch [26/100]: 100%|██████████| 13/13 [00:00<00:00, 302.27it/s, avg_loss=0.00827, loss=0.0122]\n", "Training Epoch [27/100]: 100%|██████████| 54/54 [00:00<00:00, 217.32it/s, avg_loss=0.00792, loss=0.0077] \n", "Validation Epoch [27/100]: 100%|██████████| 13/13 [00:00<00:00, 301.56it/s, avg_loss=0.00731, loss=0.0101]\n", "Training Epoch [28/100]: 100%|██████████| 54/54 [00:00<00:00, 221.29it/s, avg_loss=0.00699, loss=0.0068] \n", "Validation Epoch [28/100]: 100%|██████████| 13/13 [00:00<00:00, 285.22it/s, avg_loss=0.00641, loss=0.00814]\n", "Training Epoch [29/100]: 100%|██████████| 54/54 [00:00<00:00, 221.91it/s, avg_loss=0.00611, loss=0.00611]\n", "Validation Epoch [29/100]: 100%|██████████| 13/13 [00:00<00:00, 309.53it/s, avg_loss=0.00562, loss=0.00638]\n", "Training Epoch [30/100]: 100%|██████████| 54/54 [00:00<00:00, 216.84it/s, avg_loss=0.00538, loss=0.00518]\n", "Validation Epoch [30/100]: 100%|██████████| 13/13 [00:00<00:00, 300.07it/s, avg_loss=0.00503, loss=0.00502]\n", "Training Epoch [31/100]: 100%|██████████| 54/54 [00:00<00:00, 217.67it/s, avg_loss=0.00482, loss=0.00459]\n", "Validation Epoch [31/100]: 100%|██████████| 13/13 [00:00<00:00, 302.80it/s, avg_loss=0.00456, loss=0.00405]\n", "Training Epoch [32/100]: 100%|██████████| 54/54 [00:00<00:00, 214.05it/s, avg_loss=0.00441, loss=0.00459]\n", "Validation Epoch [32/100]: 100%|██████████| 13/13 [00:00<00:00, 298.68it/s, avg_loss=0.00422, loss=0.00338]\n", "Training Epoch [33/100]: 100%|██████████| 54/54 [00:00<00:00, 206.91it/s, avg_loss=0.00412, loss=0.00389]\n", "Validation Epoch [33/100]: 100%|██████████| 13/13 [00:00<00:00, 288.90it/s, avg_loss=0.00396, loss=0.00292]\n", "Training Epoch [34/100]: 100%|██████████| 54/54 [00:00<00:00, 212.57it/s, avg_loss=0.0039, loss=0.00364] \n", "Validation Epoch [34/100]: 100%|██████████| 13/13 [00:00<00:00, 291.06it/s, avg_loss=0.00377, loss=0.0026]\n", "Training Epoch [35/100]: 100%|██████████| 54/54 [00:00<00:00, 199.70it/s, avg_loss=0.00374, loss=0.00383]\n", "Validation Epoch [35/100]: 100%|██████████| 13/13 [00:00<00:00, 291.55it/s, avg_loss=0.00364, loss=0.00238]\n", "Training Epoch [36/100]: 100%|██████████| 54/54 [00:00<00:00, 189.82it/s, avg_loss=0.00362, loss=0.00328]\n", "Validation Epoch [36/100]: 100%|██████████| 13/13 [00:00<00:00, 220.88it/s, avg_loss=0.00353, loss=0.00224]\n", "Training Epoch [37/100]: 100%|██████████| 54/54 [00:00<00:00, 166.19it/s, avg_loss=0.00352, loss=0.00321]\n", "Validation Epoch [37/100]: 100%|██████████| 13/13 [00:00<00:00, 288.48it/s, avg_loss=0.00343, loss=0.00212]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [38/100]: 100%|██████████| 54/54 [00:00<00:00, 218.04it/s, avg_loss=0.00344, loss=0.00325]\n", "Validation Epoch [38/100]: 100%|██████████| 13/13 [00:00<00:00, 251.54it/s, avg_loss=0.00337, loss=0.00205]\n", "Training Epoch [39/100]: 100%|██████████| 54/54 [00:00<00:00, 202.62it/s, avg_loss=0.00337, loss=0.00328]\n", "Validation Epoch [39/100]: 100%|██████████| 13/13 [00:00<00:00, 285.23it/s, avg_loss=0.00329, loss=0.00197]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [40/100]: 100%|██████████| 54/54 [00:00<00:00, 226.12it/s, avg_loss=0.00331, loss=0.00337]\n", "Validation Epoch [40/100]: 100%|██████████| 13/13 [00:00<00:00, 310.25it/s, avg_loss=0.00322, loss=0.00189]\n", "Training Epoch [41/100]: 100%|██████████| 54/54 [00:00<00:00, 219.29it/s, avg_loss=0.00326, loss=0.00303]\n", "Validation Epoch [41/100]: 100%|██████████| 13/13 [00:00<00:00, 311.78it/s, avg_loss=0.00316, loss=0.00187]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [42/100]: 100%|██████████| 54/54 [00:00<00:00, 226.11it/s, avg_loss=0.00321, loss=0.00319]\n", "Validation Epoch [42/100]: 100%|██████████| 13/13 [00:00<00:00, 316.39it/s, avg_loss=0.00313, loss=0.00183]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [43/100]: 100%|██████████| 54/54 [00:00<00:00, 228.76it/s, avg_loss=0.00316, loss=0.00321]\n", "Validation Epoch [43/100]: 100%|██████████| 13/13 [00:00<00:00, 324.42it/s, avg_loss=0.00306, loss=0.00178]\n", "Training Epoch [44/100]: 100%|██████████| 54/54 [00:00<00:00, 228.51it/s, avg_loss=0.00312, loss=0.00294]\n", "Validation Epoch [44/100]: 100%|██████████| 13/13 [00:00<00:00, 319.71it/s, avg_loss=0.00304, loss=0.00176]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [45/100]: 100%|██████████| 54/54 [00:00<00:00, 211.65it/s, avg_loss=0.00308, loss=0.00317]\n", "Validation Epoch [45/100]: 100%|██████████| 13/13 [00:00<00:00, 313.54it/s, avg_loss=0.00299, loss=0.00172]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [46/100]: 100%|██████████| 54/54 [00:00<00:00, 223.09it/s, avg_loss=0.00304, loss=0.00282]\n", "Validation Epoch [46/100]: 100%|██████████| 13/13 [00:00<00:00, 319.33it/s, avg_loss=0.00294, loss=0.0017]\n", "Training Epoch [47/100]: 100%|██████████| 54/54 [00:00<00:00, 201.57it/s, avg_loss=0.00301, loss=0.0028] \n", "Validation Epoch [47/100]: 100%|██████████| 13/13 [00:00<00:00, 272.19it/s, avg_loss=0.00292, loss=0.0017]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [48/100]: 100%|██████████| 54/54 [00:00<00:00, 193.26it/s, avg_loss=0.00297, loss=0.00285]\n", "Validation Epoch [48/100]: 100%|██████████| 13/13 [00:00<00:00, 272.36it/s, avg_loss=0.00287, loss=0.00167]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [49/100]: 100%|██████████| 54/54 [00:00<00:00, 193.52it/s, avg_loss=0.00294, loss=0.0029] \n", "Validation Epoch [49/100]: 100%|██████████| 13/13 [00:00<00:00, 275.56it/s, avg_loss=0.00284, loss=0.00163]\n", "Training Epoch [50/100]: 100%|██████████| 54/54 [00:00<00:00, 192.73it/s, avg_loss=0.00291, loss=0.0028] \n", "Validation Epoch [50/100]: 100%|██████████| 13/13 [00:00<00:00, 217.39it/s, avg_loss=0.0028, loss=0.0016]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [51/100]: 100%|██████████| 54/54 [00:00<00:00, 200.71it/s, avg_loss=0.00288, loss=0.0029] \n", "Validation Epoch [51/100]: 100%|██████████| 13/13 [00:00<00:00, 269.49it/s, avg_loss=0.00276, loss=0.00155]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [52/100]: 100%|██████████| 54/54 [00:00<00:00, 211.35it/s, avg_loss=0.00285, loss=0.00308]\n", "Validation Epoch [52/100]: 100%|██████████| 13/13 [00:00<00:00, 319.62it/s, avg_loss=0.00271, loss=0.00156]\n", "Training Epoch [53/100]: 100%|██████████| 54/54 [00:00<00:00, 225.68it/s, avg_loss=0.00282, loss=0.00277]\n", "Validation Epoch [53/100]: 100%|██████████| 13/13 [00:00<00:00, 312.12it/s, avg_loss=0.00269, loss=0.00151]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [54/100]: 100%|██████████| 54/54 [00:00<00:00, 226.53it/s, avg_loss=0.00279, loss=0.00269]\n", "Validation Epoch [54/100]: 100%|██████████| 13/13 [00:00<00:00, 313.53it/s, avg_loss=0.00266, loss=0.0015]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [55/100]: 100%|██████████| 54/54 [00:00<00:00, 225.58it/s, avg_loss=0.00276, loss=0.00283]\n", "Validation Epoch [55/100]: 100%|██████████| 13/13 [00:00<00:00, 313.82it/s, avg_loss=0.00262, loss=0.00148]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 68/68 [00:00<00:00, 347.43it/s]\n", "(2025-07-29 15:22:19,522) [INFO]:     [AE] handling dataset AIOPS | curve f0932edd-6400-3e63-9559-0a9860a1baa9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 823/823 [00:03<00:00, 220.50it/s, avg_loss=0.00411, loss=0.000154]\n", "Validation Epoch [1/100]: 100%|██████████| 205/205 [00:00<00:00, 302.24it/s, avg_loss=0.000151, loss=0.000318]\n", "Training Epoch [2/100]: 100%|██████████| 823/823 [00:03<00:00, 218.61it/s, avg_loss=0.000338, loss=5.68e-5] \n", "Validation Epoch [2/100]: 100%|██████████| 205/205 [00:00<00:00, 290.08it/s, avg_loss=7.25e-5, loss=0.000203]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 823/823 [00:03<00:00, 222.88it/s, avg_loss=0.000318, loss=0.000387]\n", "Validation Epoch [3/100]: 100%|██████████| 205/205 [00:00<00:00, 299.50it/s, avg_loss=6.69e-5, loss=0.000181]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 823/823 [00:03<00:00, 222.37it/s, avg_loss=0.000308, loss=0.00125] \n", "Validation Epoch [4/100]: 100%|██████████| 205/205 [00:00<00:00, 289.55it/s, avg_loss=5.84e-5, loss=0.00015] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1029/1029 [00:02<00:00, 391.16it/s]\n", "(2025-07-29 15:22:42,522) [INFO]:     [AE] handling dataset AIOPS | curve 8723f0fb-eaef-32e6-b372-6034c9c04b80 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 221.87it/s, avg_loss=0.0881, loss=0.00356]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 300.98it/s, avg_loss=0.00467, loss=0.00516]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 221.78it/s, avg_loss=0.00228, loss=0.00174]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 283.85it/s, avg_loss=0.00287, loss=0.00261] \n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 211.17it/s, avg_loss=0.00191, loss=0.00152]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 275.00it/s, avg_loss=0.00265, loss=0.0023]  \n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 195.67it/s, avg_loss=0.00176, loss=0.00133]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 255.25it/s, avg_loss=0.00248, loss=0.00209] \n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 188.79it/s, avg_loss=0.00163, loss=0.00168]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 242.69it/s, avg_loss=0.00228, loss=0.00186] \n", "Training Epoch [6/100]: 100%|██████████| 934/934 [00:05<00:00, 181.65it/s, avg_loss=0.00145, loss=0.00133]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:00<00:00, 236.87it/s, avg_loss=0.00187, loss=0.00168] \n", "Training Epoch [7/100]: 100%|██████████| 934/934 [00:05<00:00, 176.67it/s, avg_loss=0.00112, loss=0.000856]\n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:01<00:00, 204.90it/s, avg_loss=0.00118, loss=0.00154] \n", "Training Epoch [8/100]: 100%|██████████| 934/934 [00:05<00:00, 166.99it/s, avg_loss=0.000813, loss=0.000702]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:01<00:00, 196.78it/s, avg_loss=0.000832, loss=0.0015]  \n", "Training Epoch [9/100]: 100%|██████████| 934/934 [00:06<00:00, 146.45it/s, avg_loss=0.000711, loss=0.000623]\n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:01<00:00, 196.16it/s, avg_loss=0.000759, loss=0.00147] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [10/100]: 100%|██████████| 934/934 [00:04<00:00, 219.57it/s, avg_loss=0.000692, loss=0.000651]\n", "Validation Epoch [10/100]: 100%|██████████| 233/233 [00:00<00:00, 294.56it/s, avg_loss=0.000746, loss=0.00147] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [11/100]: 100%|██████████| 934/934 [00:04<00:00, 224.43it/s, avg_loss=0.000688, loss=0.000683]\n", "Validation Epoch [11/100]: 100%|██████████| 233/233 [00:00<00:00, 298.46it/s, avg_loss=0.000744, loss=0.00146] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:02<00:00, 389.68it/s]\n", "(2025-07-29 15:23:52,336) [INFO]:     [AE] handling dataset AIOPS | curve 9c639a46-34c8-39bc-aaf0-9144b37adfc8 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 691/691 [00:03<00:00, 222.88it/s, avg_loss=0.0187, loss=0.00288]\n", "Validation Epoch [1/100]: 100%|██████████| 172/172 [00:00<00:00, 303.26it/s, avg_loss=0.00116, loss=0.00109]\n", "Training Epoch [2/100]: 100%|██████████| 691/691 [00:03<00:00, 219.01it/s, avg_loss=0.00183, loss=0.000176]\n", "Validation Epoch [2/100]: 100%|██████████| 172/172 [00:00<00:00, 294.05it/s, avg_loss=6.97e-5, loss=2.22e-5] \n", "Training Epoch [3/100]: 100%|██████████| 691/691 [00:03<00:00, 212.92it/s, avg_loss=0.00128, loss=0.000348]\n", "Validation Epoch [3/100]: 100%|██████████| 172/172 [00:00<00:00, 280.15it/s, avg_loss=5.7e-5, loss=1.6e-5]   \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 691/691 [00:03<00:00, 222.10it/s, avg_loss=0.00118, loss=0.00185] \n", "Validation Epoch [4/100]: 100%|██████████| 172/172 [00:00<00:00, 299.24it/s, avg_loss=5.5e-5, loss=1.66e-5]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 691/691 [00:03<00:00, 222.64it/s, avg_loss=0.00113, loss=9.56e-5]  \n", "Validation Epoch [5/100]: 100%|██████████| 172/172 [00:00<00:00, 308.04it/s, avg_loss=5.27e-5, loss=1.47e-5] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 864/864 [00:02<00:00, 374.58it/s]\n", "(2025-07-29 15:24:15,517) [INFO]:     [AE] handling dataset AIOPS | curve ab216663-dcc2-3a24-b1ee-2c3e550e06c9 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 77/77 [00:00<00:00, 208.95it/s, avg_loss=0.169, loss=0.148]\n", "Validation Epoch [1/100]: 100%|██████████| 19/19 [00:00<00:00, 317.24it/s, avg_loss=0.112, loss=0.101]\n", "Training Epoch [2/100]: 100%|██████████| 77/77 [00:00<00:00, 202.71it/s, avg_loss=0.135, loss=0.114]\n", "Validation Epoch [2/100]: 100%|██████████| 19/19 [00:00<00:00, 246.34it/s, avg_loss=0.0792, loss=0.0698]\n", "Training Epoch [3/100]: 100%|██████████| 77/77 [00:00<00:00, 186.87it/s, avg_loss=0.0866, loss=0.0688]\n", "Validation Epoch [3/100]: 100%|██████████| 19/19 [00:00<00:00, 316.62it/s, avg_loss=0.0486, loss=0.0426]\n", "Training Epoch [4/100]: 100%|██████████| 77/77 [00:00<00:00, 209.85it/s, avg_loss=0.0519, loss=0.0393]\n", "Validation Epoch [4/100]: 100%|██████████| 19/19 [00:00<00:00, 271.75it/s, avg_loss=0.0299, loss=0.0274]\n", "Training Epoch [5/100]: 100%|██████████| 77/77 [00:00<00:00, 202.32it/s, avg_loss=0.0318, loss=0.0255]\n", "Validation Epoch [5/100]: 100%|██████████| 19/19 [00:00<00:00, 270.17it/s, avg_loss=0.0199, loss=0.02]\n", "Training Epoch [6/100]: 100%|██████████| 77/77 [00:00<00:00, 202.66it/s, avg_loss=0.0218, loss=0.0185]\n", "Validation Epoch [6/100]: 100%|██████████| 19/19 [00:00<00:00, 310.02it/s, avg_loss=0.0153, loss=0.0167]\n", "Training Epoch [7/100]: 100%|██████████| 77/77 [00:00<00:00, 217.84it/s, avg_loss=0.0171, loss=0.0159]\n", "Validation Epoch [7/100]: 100%|██████████| 19/19 [00:00<00:00, 311.71it/s, avg_loss=0.0128, loss=0.0147]\n", "Training Epoch [8/100]: 100%|██████████| 77/77 [00:00<00:00, 209.36it/s, avg_loss=0.0142, loss=0.0144]\n", "Validation Epoch [8/100]: 100%|██████████| 19/19 [00:00<00:00, 288.70it/s, avg_loss=0.011, loss=0.0131]\n", "Training Epoch [9/100]: 100%|██████████| 77/77 [00:00<00:00, 212.43it/s, avg_loss=0.0121, loss=0.0114]\n", "Validation Epoch [9/100]: 100%|██████████| 19/19 [00:00<00:00, 308.25it/s, avg_loss=0.00947, loss=0.0117]\n", "Training Epoch [10/100]: 100%|██████████| 77/77 [00:00<00:00, 203.24it/s, avg_loss=0.0104, loss=0.0093] \n", "Validation Epoch [10/100]: 100%|██████████| 19/19 [00:00<00:00, 289.29it/s, avg_loss=0.00826, loss=0.0104]\n", "Training Epoch [11/100]: 100%|██████████| 77/77 [00:00<00:00, 216.17it/s, avg_loss=0.00902, loss=0.00829]\n", "Validation Epoch [11/100]: 100%|██████████| 19/19 [00:00<00:00, 285.25it/s, avg_loss=0.00729, loss=0.00936]\n", "Training Epoch [12/100]: 100%|██████████| 77/77 [00:00<00:00, 217.45it/s, avg_loss=0.008, loss=0.0082]   \n", "Validation Epoch [12/100]: 100%|██████████| 19/19 [00:00<00:00, 311.45it/s, avg_loss=0.00654, loss=0.00852]\n", "Training Epoch [13/100]: 100%|██████████| 77/77 [00:00<00:00, 223.79it/s, avg_loss=0.00723, loss=0.007]  \n", "Validation Epoch [13/100]: 100%|██████████| 19/19 [00:00<00:00, 305.19it/s, avg_loss=0.00599, loss=0.0079]\n", "Training Epoch [14/100]: 100%|██████████| 77/77 [00:00<00:00, 222.90it/s, avg_loss=0.00666, loss=0.00667]\n", "Validation Epoch [14/100]: 100%|██████████| 19/19 [00:00<00:00, 305.43it/s, avg_loss=0.00555, loss=0.00744]\n", "Training Epoch [15/100]: 100%|██████████| 77/77 [00:00<00:00, 221.25it/s, avg_loss=0.00623, loss=0.00641]\n", "Validation Epoch [15/100]: 100%|██████████| 19/19 [00:00<00:00, 316.12it/s, avg_loss=0.00523, loss=0.00707]\n", "Training Epoch [16/100]: 100%|██████████| 77/77 [00:00<00:00, 221.63it/s, avg_loss=0.00589, loss=0.00575]\n", "Validation Epoch [16/100]: 100%|██████████| 19/19 [00:00<00:00, 281.35it/s, avg_loss=0.00495, loss=0.00677]\n", "Training Epoch [17/100]: 100%|██████████| 77/77 [00:00<00:00, 196.60it/s, avg_loss=0.00563, loss=0.00519]\n", "Validation Epoch [17/100]: 100%|██████████| 19/19 [00:00<00:00, 268.55it/s, avg_loss=0.00474, loss=0.00657]\n", "Training Epoch [18/100]: 100%|██████████| 77/77 [00:00<00:00, 199.31it/s, avg_loss=0.00542, loss=0.00511]\n", "Validation Epoch [18/100]: 100%|██████████| 19/19 [00:00<00:00, 268.01it/s, avg_loss=0.00456, loss=0.0064]\n", "Training Epoch [19/100]: 100%|██████████| 77/77 [00:00<00:00, 205.76it/s, avg_loss=0.00523, loss=0.00562]\n", "Validation Epoch [19/100]: 100%|██████████| 19/19 [00:00<00:00, 313.74it/s, avg_loss=0.0044, loss=0.00626]\n", "Training Epoch [20/100]: 100%|██████████| 77/77 [00:00<00:00, 214.21it/s, avg_loss=0.00507, loss=0.00534]\n", "Validation Epoch [20/100]: 100%|██████████| 19/19 [00:00<00:00, 301.17it/s, avg_loss=0.00427, loss=0.00617]\n", "Training Epoch [21/100]: 100%|██████████| 77/77 [00:00<00:00, 180.09it/s, avg_loss=0.00493, loss=0.00447]\n", "Validation Epoch [21/100]: 100%|██████████| 19/19 [00:00<00:00, 261.00it/s, avg_loss=0.00412, loss=0.00607]\n", "Training Epoch [22/100]: 100%|██████████| 77/77 [00:00<00:00, 193.22it/s, avg_loss=0.00479, loss=0.00437]\n", "Validation Epoch [22/100]: 100%|██████████| 19/19 [00:00<00:00, 262.00it/s, avg_loss=0.004, loss=0.00598]\n", "Training Epoch [23/100]: 100%|██████████| 77/77 [00:00<00:00, 212.15it/s, avg_loss=0.00466, loss=0.00471]\n", "Validation Epoch [23/100]: 100%|██████████| 19/19 [00:00<00:00, 304.60it/s, avg_loss=0.00388, loss=0.00589]\n", "Training Epoch [24/100]: 100%|██████████| 77/77 [00:00<00:00, 216.43it/s, avg_loss=0.00453, loss=0.00445]\n", "Validation Epoch [24/100]: 100%|██████████| 19/19 [00:00<00:00, 292.24it/s, avg_loss=0.00377, loss=0.00583]\n", "Training Epoch [25/100]: 100%|██████████| 77/77 [00:00<00:00, 209.94it/s, avg_loss=0.0044, loss=0.00429] \n", "Validation Epoch [25/100]: 100%|██████████| 19/19 [00:00<00:00, 278.52it/s, avg_loss=0.00365, loss=0.00574]\n", "Training Epoch [26/100]: 100%|██████████| 77/77 [00:00<00:00, 213.15it/s, avg_loss=0.00427, loss=0.00422]\n", "Validation Epoch [26/100]: 100%|██████████| 19/19 [00:00<00:00, 300.14it/s, avg_loss=0.00355, loss=0.00567]\n", "Training Epoch [27/100]: 100%|██████████| 77/77 [00:00<00:00, 208.53it/s, avg_loss=0.00414, loss=0.00423]\n", "Validation Epoch [27/100]: 100%|██████████| 19/19 [00:00<00:00, 254.80it/s, avg_loss=0.00345, loss=0.00563]\n", "Training Epoch [28/100]: 100%|██████████| 77/77 [00:00<00:00, 169.61it/s, avg_loss=0.00401, loss=0.00434]\n", "Validation Epoch [28/100]: 100%|██████████| 19/19 [00:00<00:00, 229.63it/s, avg_loss=0.00332, loss=0.00548]\n", "Training Epoch [29/100]: 100%|██████████| 77/77 [00:00<00:00, 168.59it/s, avg_loss=0.00386, loss=0.00401]\n", "Validation Epoch [29/100]: 100%|██████████| 19/19 [00:00<00:00, 295.57it/s, avg_loss=0.00319, loss=0.00529]\n", "Training Epoch [30/100]: 100%|██████████| 77/77 [00:00<00:00, 208.78it/s, avg_loss=0.00371, loss=0.00345]\n", "Validation Epoch [30/100]: 100%|██████████| 19/19 [00:00<00:00, 294.38it/s, avg_loss=0.00308, loss=0.00519]\n", "Training Epoch [31/100]: 100%|██████████| 77/77 [00:00<00:00, 204.18it/s, avg_loss=0.00355, loss=0.00327]\n", "Validation Epoch [31/100]: 100%|██████████| 19/19 [00:00<00:00, 294.51it/s, avg_loss=0.00291, loss=0.0049]\n", "Training Epoch [32/100]: 100%|██████████| 77/77 [00:00<00:00, 211.33it/s, avg_loss=0.00339, loss=0.00325]\n", "Validation Epoch [32/100]: 100%|██████████| 19/19 [00:00<00:00, 295.44it/s, avg_loss=0.0028, loss=0.00476]\n", "Training Epoch [33/100]: 100%|██████████| 77/77 [00:00<00:00, 210.91it/s, avg_loss=0.00322, loss=0.00331]\n", "Validation Epoch [33/100]: 100%|██████████| 19/19 [00:00<00:00, 287.40it/s, avg_loss=0.00266, loss=0.00453]\n", "Training Epoch [34/100]: 100%|██████████| 77/77 [00:00<00:00, 196.55it/s, avg_loss=0.00304, loss=0.00322]\n", "Validation Epoch [34/100]: 100%|██████████| 19/19 [00:00<00:00, 290.74it/s, avg_loss=0.00251, loss=0.00428]\n", "Training Epoch [35/100]: 100%|██████████| 77/77 [00:00<00:00, 208.06it/s, avg_loss=0.00287, loss=0.00282]\n", "Validation Epoch [35/100]: 100%|██████████| 19/19 [00:00<00:00, 295.74it/s, avg_loss=0.00236, loss=0.00404]\n", "Training Epoch [36/100]: 100%|██████████| 77/77 [00:00<00:00, 203.46it/s, avg_loss=0.0027, loss=0.00274] \n", "Validation Epoch [36/100]: 100%|██████████| 19/19 [00:00<00:00, 288.06it/s, avg_loss=0.0022, loss=0.00372]\n", "Training Epoch [37/100]: 100%|██████████| 77/77 [00:00<00:00, 210.51it/s, avg_loss=0.00254, loss=0.00221]\n", "Validation Epoch [37/100]: 100%|██████████| 19/19 [00:00<00:00, 271.06it/s, avg_loss=0.00207, loss=0.00348]\n", "Training Epoch [38/100]: 100%|██████████| 77/77 [00:00<00:00, 204.32it/s, avg_loss=0.00238, loss=0.00228]\n", "Validation Epoch [38/100]: 100%|██████████| 19/19 [00:00<00:00, 290.16it/s, avg_loss=0.00193, loss=0.00323]\n", "Training Epoch [39/100]: 100%|██████████| 77/77 [00:00<00:00, 179.67it/s, avg_loss=0.00224, loss=0.00219]\n", "Validation Epoch [39/100]: 100%|██████████| 19/19 [00:00<00:00, 291.33it/s, avg_loss=0.00182, loss=0.00303]\n", "Training Epoch [40/100]: 100%|██████████| 77/77 [00:00<00:00, 199.84it/s, avg_loss=0.00211, loss=0.00203]\n", "Validation Epoch [40/100]: 100%|██████████| 19/19 [00:00<00:00, 263.34it/s, avg_loss=0.00169, loss=0.00277]\n", "Training Epoch [41/100]: 100%|██████████| 77/77 [00:00<00:00, 195.21it/s, avg_loss=0.00199, loss=0.00206]\n", "Validation Epoch [41/100]: 100%|██████████| 19/19 [00:00<00:00, 269.32it/s, avg_loss=0.0016, loss=0.00262]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [42/100]: 100%|██████████| 77/77 [00:00<00:00, 210.75it/s, avg_loss=0.00188, loss=0.00192]\n", "Validation Epoch [42/100]: 100%|██████████| 19/19 [00:00<00:00, 314.93it/s, avg_loss=0.00149, loss=0.00245]\n", "Training Epoch [43/100]: 100%|██████████| 77/77 [00:00<00:00, 227.77it/s, avg_loss=0.00178, loss=0.00183]\n", "Validation Epoch [43/100]: 100%|██████████| 19/19 [00:00<00:00, 315.39it/s, avg_loss=0.00141, loss=0.00227]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [44/100]: 100%|██████████| 77/77 [00:00<00:00, 228.68it/s, avg_loss=0.00169, loss=0.00155]\n", "Validation Epoch [44/100]: 100%|██████████| 19/19 [00:00<00:00, 316.15it/s, avg_loss=0.00133, loss=0.00214]\n", "Training Epoch [45/100]: 100%|██████████| 77/77 [00:00<00:00, 222.16it/s, avg_loss=0.00161, loss=0.00139]\n", "Validation Epoch [45/100]: 100%|██████████| 19/19 [00:00<00:00, 325.22it/s, avg_loss=0.00125, loss=0.002]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [46/100]: 100%|██████████| 77/77 [00:00<00:00, 230.71it/s, avg_loss=0.00153, loss=0.00146]\n", "Validation Epoch [46/100]: 100%|██████████| 19/19 [00:00<00:00, 317.83it/s, avg_loss=0.0012, loss=0.00193]\n", "Training Epoch [47/100]: 100%|██████████| 77/77 [00:00<00:00, 220.56it/s, avg_loss=0.00147, loss=0.00141]\n", "Validation Epoch [47/100]: 100%|██████████| 19/19 [00:00<00:00, 311.87it/s, avg_loss=0.00113, loss=0.00179]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [48/100]: 100%|██████████| 77/77 [00:00<00:00, 212.48it/s, avg_loss=0.0014, loss=0.0013]  \n", "Validation Epoch [48/100]: 100%|██████████| 19/19 [00:00<00:00, 299.57it/s, avg_loss=0.00108, loss=0.00174]\n", "Training Epoch [49/100]: 100%|██████████| 77/77 [00:00<00:00, 188.55it/s, avg_loss=0.00134, loss=0.00121]\n", "Validation Epoch [49/100]: 100%|██████████| 19/19 [00:00<00:00, 287.54it/s, avg_loss=0.00102, loss=0.00165]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [50/100]: 100%|██████████| 77/77 [00:00<00:00, 215.65it/s, avg_loss=0.00128, loss=0.00122]\n", "Validation Epoch [50/100]: 100%|██████████| 19/19 [00:00<00:00, 321.76it/s, avg_loss=0.000987, loss=0.0016]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [51/100]: 100%|██████████| 77/77 [00:00<00:00, 226.54it/s, avg_loss=0.00123, loss=0.00116]\n", "Validation Epoch [51/100]: 100%|██████████| 19/19 [00:00<00:00, 319.56it/s, avg_loss=0.000933, loss=0.00152]\n", "Training Epoch [52/100]: 100%|██████████| 77/77 [00:00<00:00, 208.37it/s, avg_loss=0.00118, loss=0.00136]\n", "Validation Epoch [52/100]: 100%|██████████| 19/19 [00:00<00:00, 316.06it/s, avg_loss=0.000891, loss=0.00146]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [53/100]: 100%|██████████| 77/77 [00:00<00:00, 224.10it/s, avg_loss=0.00113, loss=0.000982]\n", "Validation Epoch [53/100]: 100%|██████████| 19/19 [00:00<00:00, 319.31it/s, avg_loss=0.000855, loss=0.0014]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [54/100]: 100%|██████████| 77/77 [00:00<00:00, 207.49it/s, avg_loss=0.00109, loss=0.00116] \n", "Validation Epoch [54/100]: 100%|██████████| 19/19 [00:00<00:00, 306.07it/s, avg_loss=0.000819, loss=0.00136]\n", "Training Epoch [55/100]: 100%|██████████| 77/77 [00:00<00:00, 222.39it/s, avg_loss=0.00105, loss=0.0011]  \n", "Validation Epoch [55/100]: 100%|██████████| 19/19 [00:00<00:00, 245.53it/s, avg_loss=0.000801, loss=0.00135]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [56/100]: 100%|██████████| 77/77 [00:00<00:00, 223.90it/s, avg_loss=0.00101, loss=0.00105] \n", "Validation Epoch [56/100]: 100%|██████████| 19/19 [00:00<00:00, 319.71it/s, avg_loss=0.000768, loss=0.0013]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [57/100]: 100%|██████████| 77/77 [00:00<00:00, 223.67it/s, avg_loss=0.000976, loss=0.000922]\n", "Validation Epoch [57/100]: 100%|██████████| 19/19 [00:00<00:00, 316.99it/s, avg_loss=0.000738, loss=0.00126]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 97/97 [00:00<00:00, 383.21it/s]\n", "(2025-07-29 15:24:41,182) [INFO]:     [AE] handling dataset AIOPS | curve adb2fde9-8589-3f5b-a410-5fe14386c7af \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 211.29it/s, avg_loss=0.0209, loss=0.000341]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 293.38it/s, avg_loss=0.000512, loss=0.000778]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 216.15it/s, avg_loss=0.000685, loss=0.000273]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 285.00it/s, avg_loss=0.000422, loss=0.000497]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 221.57it/s, avg_loss=0.000559, loss=0.000229]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 294.88it/s, avg_loss=0.000379, loss=0.000365]\n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 213.82it/s, avg_loss=0.000499, loss=0.00107] \n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 296.35it/s, avg_loss=0.000355, loss=0.000297]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 216.46it/s, avg_loss=0.000474, loss=0.000218]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 305.28it/s, avg_loss=0.000347, loss=0.000279]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 934/934 [00:04<00:00, 207.24it/s, avg_loss=0.000458, loss=0.00037] \n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:00<00:00, 295.22it/s, avg_loss=0.000338, loss=0.000276]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 379.82it/s]\n", "(2025-07-29 15:25:18,158) [INFO]:     [AE] handling dataset AIOPS | curve 42d6616d-c9c5-370a-a8ba-17ead74f3114 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 219.96it/s, avg_loss=0.0176, loss=0.00123] \n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 298.33it/s, avg_loss=0.000445, loss=0.000257]\n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 214.60it/s, avg_loss=0.000599, loss=0.000837]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 288.63it/s, avg_loss=0.000341, loss=0.000176]\n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 202.28it/s, avg_loss=0.000489, loss=0.000673]\n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 278.15it/s, avg_loss=0.000303, loss=0.000151]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 220.28it/s, avg_loss=0.000445, loss=0.00113] \n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 290.90it/s, avg_loss=0.000291, loss=0.000144]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 218.57it/s, avg_loss=0.000431, loss=0.000577]\n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:00<00:00, 292.83it/s, avg_loss=0.000287, loss=0.000142]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 364.54it/s]\n", "(2025-07-29 15:25:50,747) [INFO]:     [AE] handling dataset AIOPS | curve 6d1114ae-be04-3c46-b5aa-be1a003a57cd \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 934/934 [00:04<00:00, 211.30it/s, avg_loss=0.0708, loss=0.00209]\n", "Validation Epoch [1/100]: 100%|██████████| 233/233 [00:00<00:00, 305.83it/s, avg_loss=0.00308, loss=0.00326] \n", "Training Epoch [2/100]: 100%|██████████| 934/934 [00:04<00:00, 211.79it/s, avg_loss=0.00204, loss=0.00158]\n", "Validation Epoch [2/100]: 100%|██████████| 233/233 [00:00<00:00, 277.64it/s, avg_loss=0.00271, loss=0.00269] \n", "Training Epoch [3/100]: 100%|██████████| 934/934 [00:04<00:00, 206.99it/s, avg_loss=0.00184, loss=0.0015] \n", "Validation Epoch [3/100]: 100%|██████████| 233/233 [00:00<00:00, 281.64it/s, avg_loss=0.00246, loss=0.0023]  \n", "Training Epoch [4/100]: 100%|██████████| 934/934 [00:04<00:00, 191.14it/s, avg_loss=0.00164, loss=0.00147]\n", "Validation Epoch [4/100]: 100%|██████████| 233/233 [00:00<00:00, 250.00it/s, avg_loss=0.00216, loss=0.00198] \n", "Training Epoch [5/100]: 100%|██████████| 934/934 [00:04<00:00, 189.64it/s, avg_loss=0.00141, loss=0.00132] \n", "Validation Epoch [5/100]: 100%|██████████| 233/233 [00:01<00:00, 232.63it/s, avg_loss=0.00165, loss=0.00179] \n", "Training Epoch [6/100]: 100%|██████████| 934/934 [00:05<00:00, 172.27it/s, avg_loss=0.00102, loss=0.000751]\n", "Validation Epoch [6/100]: 100%|██████████| 233/233 [00:01<00:00, 224.95it/s, avg_loss=0.000903, loss=0.00169] \n", "Training Epoch [7/100]: 100%|██████████| 934/934 [00:05<00:00, 172.96it/s, avg_loss=0.000747, loss=0.000627]\n", "Validation Epoch [7/100]: 100%|██████████| 233/233 [00:01<00:00, 218.28it/s, avg_loss=0.000706, loss=0.00168] \n", "Training Epoch [8/100]: 100%|██████████| 934/934 [00:05<00:00, 163.85it/s, avg_loss=0.000699, loss=0.000681]\n", "Validation Epoch [8/100]: 100%|██████████| 233/233 [00:01<00:00, 203.01it/s, avg_loss=0.000682, loss=0.00166] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [9/100]: 100%|██████████| 934/934 [00:04<00:00, 223.44it/s, avg_loss=0.000694, loss=0.000564]\n", "Validation Epoch [9/100]: 100%|██████████| 233/233 [00:00<00:00, 300.47it/s, avg_loss=0.000677, loss=0.00165] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [10/100]: 100%|██████████| 934/934 [00:04<00:00, 221.10it/s, avg_loss=0.000693, loss=0.00068] \n", "Validation Epoch [10/100]: 100%|██████████| 233/233 [00:00<00:00, 298.05it/s, avg_loss=0.000675, loss=0.00164] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 1168/1168 [00:03<00:00, 360.09it/s]\n", "(2025-07-29 15:26:54,239) [INFO]:     [AE] handling dataset AIOPS | curve c02607e8-7399-3dde-9d28-8a8da5e5d251 \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 54/54 [00:00<00:00, 225.62it/s, avg_loss=0.151, loss=0.14] \n", "Validation Epoch [1/100]: 100%|██████████| 13/13 [00:00<00:00, 316.46it/s, avg_loss=0.111, loss=0.11]\n", "Training Epoch [2/100]: 100%|██████████| 54/54 [00:00<00:00, 225.16it/s, avg_loss=0.14, loss=0.142] \n", "Validation Epoch [2/100]: 100%|██████████| 13/13 [00:00<00:00, 309.79it/s, avg_loss=0.102, loss=0.101]\n", "Training Epoch [3/100]: 100%|██████████| 54/54 [00:00<00:00, 215.83it/s, avg_loss=0.127, loss=0.113]\n", "Validation Epoch [3/100]: 100%|██████████| 13/13 [00:00<00:00, 296.17it/s, avg_loss=0.0894, loss=0.0905]\n", "Training Epoch [4/100]: 100%|██████████| 54/54 [00:00<00:00, 222.48it/s, avg_loss=0.107, loss=0.091] \n", "Validation Epoch [4/100]: 100%|██████████| 13/13 [00:00<00:00, 317.74it/s, avg_loss=0.0723, loss=0.0785]\n", "Training Epoch [5/100]: 100%|██████████| 54/54 [00:00<00:00, 222.17it/s, avg_loss=0.0817, loss=0.0741]\n", "Validation Epoch [5/100]: 100%|██████████| 13/13 [00:00<00:00, 312.83it/s, avg_loss=0.0545, loss=0.0669]\n", "Training Epoch [6/100]: 100%|██████████| 54/54 [00:00<00:00, 223.64it/s, avg_loss=0.0597, loss=0.0501]\n", "Validation Epoch [6/100]: 100%|██████████| 13/13 [00:00<00:00, 309.63it/s, avg_loss=0.0407, loss=0.0576]\n", "Training Epoch [7/100]: 100%|██████████| 54/54 [00:00<00:00, 227.33it/s, avg_loss=0.044, loss=0.0394] \n", "Validation Epoch [7/100]: 100%|██████████| 13/13 [00:00<00:00, 291.79it/s, avg_loss=0.0314, loss=0.0512]\n", "Training Epoch [8/100]: 100%|██████████| 54/54 [00:00<00:00, 223.19it/s, avg_loss=0.0339, loss=0.0305]\n", "Validation Epoch [8/100]: 100%|██████████| 13/13 [00:00<00:00, 309.99it/s, avg_loss=0.0258, loss=0.0472]\n", "Training Epoch [9/100]: 100%|██████████| 54/54 [00:00<00:00, 225.27it/s, avg_loss=0.0276, loss=0.0258]\n", "Validation Epoch [9/100]: 100%|██████████| 13/13 [00:00<00:00, 304.89it/s, avg_loss=0.0219, loss=0.0436]\n", "Training Epoch [10/100]: 100%|██████████| 54/54 [00:00<00:00, 225.03it/s, avg_loss=0.0232, loss=0.021] \n", "Validation Epoch [10/100]: 100%|██████████| 13/13 [00:00<00:00, 312.10it/s, avg_loss=0.019, loss=0.0396]\n", "Training Epoch [11/100]: 100%|██████████| 54/54 [00:00<00:00, 223.69it/s, avg_loss=0.02, loss=0.0189]  \n", "Validation Epoch [11/100]: 100%|██████████| 13/13 [00:00<00:00, 301.09it/s, avg_loss=0.0168, loss=0.0358]\n", "Training Epoch [12/100]: 100%|██████████| 54/54 [00:00<00:00, 226.08it/s, avg_loss=0.0176, loss=0.0167]\n", "Validation Epoch [12/100]: 100%|██████████| 13/13 [00:00<00:00, 318.78it/s, avg_loss=0.0151, loss=0.0324]\n", "Training Epoch [13/100]: 100%|██████████| 54/54 [00:00<00:00, 227.37it/s, avg_loss=0.0157, loss=0.0157]\n", "Validation Epoch [13/100]: 100%|██████████| 13/13 [00:00<00:00, 316.88it/s, avg_loss=0.0136, loss=0.0291]\n", "Training Epoch [14/100]: 100%|██████████| 54/54 [00:00<00:00, 226.63it/s, avg_loss=0.014, loss=0.014]  \n", "Validation Epoch [14/100]: 100%|██████████| 13/13 [00:00<00:00, 280.73it/s, avg_loss=0.0123, loss=0.0261]\n", "Training Epoch [15/100]: 100%|██████████| 54/54 [00:00<00:00, 224.42it/s, avg_loss=0.0126, loss=0.0124]\n", "Validation Epoch [15/100]: 100%|██████████| 13/13 [00:00<00:00, 317.27it/s, avg_loss=0.0111, loss=0.0232]\n", "Training Epoch [16/100]: 100%|██████████| 54/54 [00:00<00:00, 227.13it/s, avg_loss=0.0113, loss=0.0108] \n", "Validation Epoch [16/100]: 100%|██████████| 13/13 [00:00<00:00, 302.80it/s, avg_loss=0.01, loss=0.0206]\n", "Training Epoch [17/100]: 100%|██████████| 54/54 [00:00<00:00, 216.56it/s, avg_loss=0.0101, loss=0.00964]\n", "Validation Epoch [17/100]: 100%|██████████| 13/13 [00:00<00:00, 296.45it/s, avg_loss=0.00906, loss=0.0182]\n", "Training Epoch [18/100]: 100%|██████████| 54/54 [00:00<00:00, 215.27it/s, avg_loss=0.00906, loss=0.00823]\n", "Validation Epoch [18/100]: 100%|██████████| 13/13 [00:00<00:00, 306.04it/s, avg_loss=0.0081, loss=0.0158]\n", "Training Epoch [19/100]: 100%|██████████| 54/54 [00:00<00:00, 225.15it/s, avg_loss=0.00802, loss=0.00815]\n", "Validation Epoch [19/100]: 100%|██████████| 13/13 [00:00<00:00, 310.68it/s, avg_loss=0.00716, loss=0.0134]\n", "Training Epoch [20/100]: 100%|██████████| 54/54 [00:00<00:00, 222.73it/s, avg_loss=0.00697, loss=0.0071] \n", "Validation Epoch [20/100]: 100%|██████████| 13/13 [00:00<00:00, 235.68it/s, avg_loss=0.00622, loss=0.011]\n", "Training Epoch [21/100]: 100%|██████████| 54/54 [00:00<00:00, 187.86it/s, avg_loss=0.00599, loss=0.00527]\n", "Validation Epoch [21/100]: 100%|██████████| 13/13 [00:00<00:00, 291.05it/s, avg_loss=0.00536, loss=0.00881]\n", "Training Epoch [22/100]: 100%|██████████| 54/54 [00:00<00:00, 217.73it/s, avg_loss=0.00513, loss=0.00446]\n", "Validation Epoch [22/100]: 100%|██████████| 13/13 [00:00<00:00, 301.22it/s, avg_loss=0.00464, loss=0.00695]\n", "Training Epoch [23/100]: 100%|██████████| 54/54 [00:00<00:00, 213.62it/s, avg_loss=0.00442, loss=0.00403]\n", "Validation Epoch [23/100]: 100%|██████████| 13/13 [00:00<00:00, 295.80it/s, avg_loss=0.00407, loss=0.0055]\n", "Training Epoch [24/100]: 100%|██████████| 54/54 [00:00<00:00, 194.90it/s, avg_loss=0.00388, loss=0.00363]\n", "Validation Epoch [24/100]: 100%|██████████| 13/13 [00:00<00:00, 310.93it/s, avg_loss=0.00361, loss=0.00444]\n", "Training Epoch [25/100]: 100%|██████████| 54/54 [00:00<00:00, 220.15it/s, avg_loss=0.00347, loss=0.00343]\n", "Validation Epoch [25/100]: 100%|██████████| 13/13 [00:00<00:00, 308.56it/s, avg_loss=0.00327, loss=0.00363]\n", "Training Epoch [26/100]: 100%|██████████| 54/54 [00:00<00:00, 220.79it/s, avg_loss=0.00315, loss=0.00304]\n", "Validation Epoch [26/100]: 100%|██████████| 13/13 [00:00<00:00, 309.38it/s, avg_loss=0.00301, loss=0.00309]\n", "Training Epoch [27/100]: 100%|██████████| 54/54 [00:00<00:00, 223.02it/s, avg_loss=0.00292, loss=0.00284]\n", "Validation Epoch [27/100]: 100%|██████████| 13/13 [00:00<00:00, 304.97it/s, avg_loss=0.00283, loss=0.0027]\n", "Training Epoch [28/100]: 100%|██████████| 54/54 [00:00<00:00, 196.01it/s, avg_loss=0.00274, loss=0.00249]\n", "Validation Epoch [28/100]: 100%|██████████| 13/13 [00:00<00:00, 304.27it/s, avg_loss=0.00267, loss=0.00242]\n", "Training Epoch [29/100]: 100%|██████████| 54/54 [00:00<00:00, 221.11it/s, avg_loss=0.0026, loss=0.00249] \n", "Validation Epoch [29/100]: 100%|██████████| 13/13 [00:00<00:00, 307.48it/s, avg_loss=0.00257, loss=0.00221]\n", "Training Epoch [30/100]: 100%|██████████| 54/54 [00:00<00:00, 218.03it/s, avg_loss=0.00249, loss=0.00223]\n", "Validation Epoch [30/100]: 100%|██████████| 13/13 [00:00<00:00, 303.87it/s, avg_loss=0.00243, loss=0.00204]\n", "Training Epoch [31/100]: 100%|██████████| 54/54 [00:00<00:00, 218.18it/s, avg_loss=0.0024, loss=0.00233] \n", "Validation Epoch [31/100]: 100%|██████████| 13/13 [00:00<00:00, 300.50it/s, avg_loss=0.00237, loss=0.00193]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [32/100]: 100%|██████████| 54/54 [00:00<00:00, 222.67it/s, avg_loss=0.00232, loss=0.00215]\n", "Validation Epoch [32/100]: 100%|██████████| 13/13 [00:00<00:00, 310.00it/s, avg_loss=0.00227, loss=0.0018]\n", "Training Epoch [33/100]: 100%|██████████| 54/54 [00:00<00:00, 223.92it/s, avg_loss=0.00225, loss=0.00218]\n", "Validation Epoch [33/100]: 100%|██████████| 13/13 [00:00<00:00, 308.60it/s, avg_loss=0.00222, loss=0.00174]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [34/100]: 100%|██████████| 54/54 [00:00<00:00, 223.47it/s, avg_loss=0.00219, loss=0.00225]\n", "Validation Epoch [34/100]: 100%|██████████| 13/13 [00:00<00:00, 271.53it/s, avg_loss=0.00216, loss=0.00168]\n", "Training Epoch [35/100]: 100%|██████████| 54/54 [00:00<00:00, 227.88it/s, avg_loss=0.00213, loss=0.00209]\n", "Validation Epoch [35/100]: 100%|██████████| 13/13 [00:00<00:00, 311.19it/s, avg_loss=0.0021, loss=0.00164]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [36/100]: 100%|██████████| 54/54 [00:00<00:00, 224.73it/s, avg_loss=0.00208, loss=0.00192]\n", "Validation Epoch [36/100]: 100%|██████████| 13/13 [00:00<00:00, 317.60it/s, avg_loss=0.00206, loss=0.0016]\n", "Training Epoch [37/100]: 100%|██████████| 54/54 [00:00<00:00, 224.47it/s, avg_loss=0.00204, loss=0.00191]\n", "Validation Epoch [37/100]: 100%|██████████| 13/13 [00:00<00:00, 322.91it/s, avg_loss=0.00201, loss=0.00157]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [38/100]: 100%|██████████| 54/54 [00:00<00:00, 230.26it/s, avg_loss=0.002, loss=0.00188]  \n", "Validation Epoch [38/100]: 100%|██████████| 13/13 [00:00<00:00, 322.40it/s, avg_loss=0.00197, loss=0.00153]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [39/100]: 100%|██████████| 54/54 [00:00<00:00, 230.93it/s, avg_loss=0.00196, loss=0.00206]\n", "Validation Epoch [39/100]: 100%|██████████| 13/13 [00:00<00:00, 322.59it/s, avg_loss=0.00193, loss=0.00151]\n", "Training Epoch [40/100]: 100%|██████████| 54/54 [00:00<00:00, 220.62it/s, avg_loss=0.00192, loss=0.00202]\n", "Validation Epoch [40/100]: 100%|██████████| 13/13 [00:00<00:00, 308.13it/s, avg_loss=0.00188, loss=0.00148]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [41/100]: 100%|██████████| 54/54 [00:00<00:00, 210.71it/s, avg_loss=0.00188, loss=0.00192]\n", "Validation Epoch [41/100]: 100%|██████████| 13/13 [00:00<00:00, 295.12it/s, avg_loss=0.00184, loss=0.00143]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [42/100]: 100%|██████████| 54/54 [00:00<00:00, 205.27it/s, avg_loss=0.00185, loss=0.002]  \n", "Validation Epoch [42/100]: 100%|██████████| 13/13 [00:00<00:00, 301.67it/s, avg_loss=0.0018, loss=0.00142]\n", "Training Epoch [43/100]: 100%|██████████| 54/54 [00:00<00:00, 224.80it/s, avg_loss=0.00181, loss=0.002]  \n", "Validation Epoch [43/100]: 100%|██████████| 13/13 [00:00<00:00, 299.61it/s, avg_loss=0.00177, loss=0.00139]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [44/100]: 100%|██████████| 54/54 [00:00<00:00, 225.52it/s, avg_loss=0.00178, loss=0.00168]\n", "Validation Epoch [44/100]: 100%|██████████| 13/13 [00:00<00:00, 317.63it/s, avg_loss=0.00173, loss=0.00138]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [45/100]: 100%|██████████| 54/54 [00:00<00:00, 221.07it/s, avg_loss=0.00174, loss=0.0017] \n", "Validation Epoch [45/100]: 100%|██████████| 13/13 [00:00<00:00, 315.09it/s, avg_loss=0.00169, loss=0.00135]\n", "Training Epoch [46/100]: 100%|██████████| 54/54 [00:00<00:00, 226.93it/s, avg_loss=0.00171, loss=0.00166]\n", "Validation Epoch [46/100]: 100%|██████████| 13/13 [00:00<00:00, 314.19it/s, avg_loss=0.00165, loss=0.00133]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [47/100]: 100%|██████████| 54/54 [00:00<00:00, 231.38it/s, avg_loss=0.00168, loss=0.00153]\n", "Validation Epoch [47/100]: 100%|██████████| 13/13 [00:00<00:00, 311.36it/s, avg_loss=0.00161, loss=0.00132]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [48/100]: 100%|██████████| 54/54 [00:00<00:00, 229.22it/s, avg_loss=0.00165, loss=0.0018] \n", "Validation Epoch [48/100]: 100%|██████████| 13/13 [00:00<00:00, 287.56it/s, avg_loss=0.00159, loss=0.0013]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 68/68 [00:00<00:00, 391.04it/s]\n", "(2025-07-29 15:27:08,630) [INFO]:     [AE] handling dataset AIOPS | curve da10a69f-d836-3baa-ad40-3e548ecf1fbd \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [1/100]: 100%|██████████| 675/675 [00:03<00:00, 221.87it/s, avg_loss=0.0118, loss=0.00166]\n", "Validation Epoch [1/100]: 100%|██████████| 168/168 [00:00<00:00, 267.09it/s, avg_loss=0.00244, loss=0.00292] \n", "Training Epoch [2/100]: 100%|██████████| 675/675 [00:03<00:00, 198.66it/s, avg_loss=0.00167, loss=0.00333] \n", "Validation Epoch [2/100]: 100%|██████████| 168/168 [00:00<00:00, 296.92it/s, avg_loss=0.00174, loss=0.0027]  \n", "Training Epoch [3/100]: 100%|██████████| 675/675 [00:03<00:00, 202.82it/s, avg_loss=0.00124, loss=0.000816]\n", "Validation Epoch [3/100]: 100%|██████████| 168/168 [00:00<00:00, 226.73it/s, avg_loss=0.00133, loss=0.00242] \n", "Training Epoch [4/100]: 100%|██████████| 675/675 [00:03<00:00, 194.68it/s, avg_loss=0.000869, loss=0.00235] \n", "Validation Epoch [4/100]: 100%|██████████| 168/168 [00:00<00:00, 277.55it/s, avg_loss=0.00121, loss=0.0024]  \n", "Training Epoch [5/100]: 100%|██████████| 675/675 [00:03<00:00, 190.79it/s, avg_loss=0.000813, loss=2.57e-5] \n", "Validation Epoch [5/100]: 100%|██████████| 168/168 [00:00<00:00, 251.17it/s, avg_loss=0.00119, loss=0.00238]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [6/100]: 100%|██████████| 675/675 [00:03<00:00, 218.72it/s, avg_loss=0.000803, loss=0.00111] \n", "Validation Epoch [6/100]: 100%|██████████| 168/168 [00:00<00:00, 304.93it/s, avg_loss=0.00118, loss=0.00237]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [7/100]: 100%|██████████| 675/675 [00:03<00:00, 217.17it/s, avg_loss=0.000797, loss=0.00104] \n", "Validation Epoch [7/100]: 100%|██████████| 168/168 [00:00<00:00, 308.92it/s, avg_loss=0.00117, loss=0.00237]  \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 843/843 [00:02<00:00, 387.40it/s]\n"]}], "source": ["# Create a global controller\n", "gctrl = TSADController()\n", "\n", "\"\"\"============= [DATASET SETTINGS] =============\"\"\"\n", "# Specifying datasets\n", "datasets = [\"AIOPS\"]\n", "dirname = \"../../../datasets\"\n", "# set datasets path, dirname is the absolute/relative path of dataset.\n", "\n", "# Use all curves in datasets:\n", "gctrl.set_dataset(\n", "    dataset_type=\"UTS\",\n", "    dirname=dirname,\n", "    datasets=datasets,\n", ")\n", "\n", "\"\"\"============= [EXPERIMENTAL SETTINGS] =============\"\"\"\n", "# Specifying methods and training schemas\n", "from EasyTSAD.Methods import AE, Donut, AR\n", "\n", "methods = [\"AR\", \"AE\"]\n", "training_schema = \"naive\"\n", "\n", "for method in methods:\n", "    # run models\n", "    gctrl.run_exps(\n", "        method=method,\n", "        training_schema=training_schema\n", "    )\n", "    "]}, {"cell_type": "code", "execution_count": 3, "id": "dd9da4af", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-07-29 15:27:41,147) [INFO]: Register evaluations\n", "(2025-07-29 15:27:41,148) [INFO]: Perform evaluations. Method[AR], <PERSON><PERSON><PERSON>[naive].\n", "(2025-07-29 15:27:41,148) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-07-29 15:27:41,196) [INFO]:     [AR] Eval dataset AIOPS <<<\n", "(2025-07-29 15:27:41,196) [INFO]:         [AIOPS] Using margins (0, 5)\n", "(2025-07-29 15:28:02,640) [INFO]: Perform evaluations. Method[AE], <PERSON><PERSON><PERSON>[naive].\n", "(2025-07-29 15:28:02,641) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-07-29 15:28:02,688) [INFO]:     [AE] Eval dataset AIOPS <<<\n", "(2025-07-29 15:28:02,688) [INFO]:         [AIOPS] Using margins (0, 5)\n", "(2025-07-29 15:28:23,909) [INFO]: Plot<PERSON>. Method[AR], <PERSON><PERSON><PERSON>[naive].\n", "(2025-07-29 15:28:23,910) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-07-29 15:28:23,957) [INFO]:     [AR] Plot dataset AIOPS score only \n", "(2025-07-29 15:28:47,255) [INFO]: Plotting. Method[AE], <PERSON><PERSON><PERSON>[naive].\n", "(2025-07-29 15:28:47,256) [INFO]:     [Load Data (All)] DataSets: AIOPS \n", "(2025-07-29 15:28:47,303) [INFO]:     [AE] Plot dataset AIOPS score only \n"]}], "source": ["\"\"\"============= [EVALUATION SETTINGS] =============\"\"\"\n", "\n", "from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA\n", "# Specifying evaluation protocols\n", "gctrl.set_evals(\n", "    [\n", "        PointF1PA(),\n", "        EventF1PA(),\n", "        EventF1PA(mode=\"squeeze\")\n", "    ]\n", ")\n", "\n", "for method in methods:\n", "    gctrl.do_evals(\n", "        method=method,\n", "        training_schema=training_schema\n", "    )\n", "    \n", "    \n", "\"\"\"============= [PLOTTING SETTINGS] =============\"\"\"\n", "\n", "# plot anomaly scores for each curve\n", "for method in methods:\n", "    gctrl.plots(\n", "        method=method,\n", "        training_schema=training_schema\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.11)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}