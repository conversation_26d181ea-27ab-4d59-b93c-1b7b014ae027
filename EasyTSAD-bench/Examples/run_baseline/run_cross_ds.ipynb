{"cells": [{"cell_type": "code", "execution_count": 1, "id": "bff415f5", "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../../\")\n", "from EasyTSAD.Controller import TSADController"]}, {"cell_type": "code", "execution_count": null, "id": "9dd8cc31", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-07-28 16:22:37,696) [INFO]: \n", "                         \n", "███████╗ █████╗ ███████╗██╗   ██╗    ████████╗███████╗ █████╗ ██████╗ \n", "██╔════╝██╔══██╗██╔════╝╚██╗ ██╔╝    ╚══██╔══╝██╔════╝██╔══██╗██╔══██╗\n", "█████╗  ███████║███████╗ ╚████╔╝        ██║   ███████╗███████║██║  ██║\n", "██╔══╝  ██╔══██║╚════██║  ╚██╔╝         ██║   ╚════██║██╔══██║██║  ██║\n", "███████╗██║  ██║███████║   ██║          ██║   ███████║██║  ██║██████╔╝\n", "╚══════╝╚═╝  ╚═╝╚══════╝   ╚═╝          ╚═╝   ╚══════╝╚═╝  ╚═╝╚═════╝ \n", "                                                                      \n", "                         \n", "(2025-07-28 16:22:37,698) [INFO]: Dataset Directory has been loaded.\n", "(2025-07-28 16:22:39,312) [INFO]: Run Experiments. Method[AR], <PERSON><PERSON><PERSON>[zero_shot_cross_ds].\n", "(2025-07-28 16:22:39,313) [INFO]:     Use Default Method Config. Path: /data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/EasyTSAD/Methods/AR/config.toml\n", "(2025-07-28 16:22:39,313) [INFO]:     [Load Data (All)] DataSets: TODS,UCR,AIOPS,NAB,Yahoo,WSD \n"]}, {"name": "stdout", "output_type": "stream", "text": ["=== Using CUDA ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["(2025-07-28 16:22:42,257) [INFO]:     [AR] is training using dict_keys(['AIOPSc69a50cf-ee03-3bd7-831e-407d36c7ee91', 'AIOPS43115f2a-baeb-3b01-96f7-4ea14188343c', 'AIOPS1c6d7a26-1f1a-3321-bb4d-7a9d969ec8f0', 'AIOPSffb82d38-5f00-37db-abc0-5d2e4e4cb6aa', 'AIOPSa8c06b47-cc41-3738-9110-12df0ee4c721', 'AIOPS55f8b8b8-b659-38df-b3df-e4a5a8a54bc9', 'AIOPS57051487-3a40-3828-9084-a12f7f23ee38', 'AIOPSa07ac296-de40-3a7c-8df3-91f642cc14d0', 'AIOPS7103fa0f-cac4-314f-addc-866190247439', 'AIOPS6a757df4-95e5-3357-8406-165e2bd49360', 'AIOPS4d2af31a-9916-3d9f-8a8e-8a268a48c095', 'AIOPSe0747cad-8dc8-38a9-a9ab-855b61f5551d', 'AIOPS6efa3a07-4544-34a0-b921-a155bd1a05e8', 'AIOPS431a8542-c468-3988-a508-3afd06a218da', 'AIOPSba5f3328-9f3f-3ff5-a683-84437d16d554', 'AIOPS847e8ecc-f8d2-3a93-9107-f367a0aab37d', 'AIOPS54350a12-7a9d-3ca8-b81f-f886b9d156fd', 'AIOPS0efb375b-b902-3661-ab23-9a0bb799f4e3', 'AIOPS05f10d3a-239c-3bef-9bdc-a2feeb0037aa', 'AIOPS301c70d8-1630-35ac-8f96-bc1b6f4359ea', 'AIOPSf0932edd-6400-3e63-9559-0a9860a1baa9', 'AIOPS8723f0fb-eaef-32e6-b372-6034c9c04b80', 'AIOPS9c639a46-34c8-39bc-aaf0-9144b37adfc8', 'AIOPSab216663-dcc2-3a24-b1ee-2c3e550e06c9', 'AIOPSadb2fde9-8589-3f5b-a410-5fe14386c7af', 'AIOPS42d6616d-c9c5-370a-a8ba-17ead74f3114', 'AIOPS6d1114ae-be04-3c46-b5aa-be1a003a57cd', 'AIOPSc02607e8-7399-3dde-9d28-8a8da5e5d251', 'AIOPSda10a69f-d836-3baa-ad40-3e548ecf1fbd']). The number of curves is 29.\n", "Training Epoch [1/100]: 100%|██████████| 19207/19207 [01:47<00:00, 179.32it/s, avg_loss=0.000289, loss=5.63e-5] \n", "Validation Epoch [1/100]: 100%|██████████| 4799/4799 [00:33<00:00, 142.13it/s, avg_loss=0.000216, loss=0.00153] \n", "Training Epoch [2/100]: 100%|██████████| 19207/19207 [03:17<00:00, 97.18it/s, avg_loss=0.000207, loss=0.000166] \n", "Validation Epoch [2/100]: 100%|██████████| 4799/4799 [02:38<00:00, 30.31it/s, avg_loss=0.000216, loss=0.00148] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 1 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [3/100]: 100%|██████████| 19207/19207 [01:47<00:00, 179.12it/s, avg_loss=0.000206, loss=0.000185]\n", "Validation Epoch [3/100]: 100%|██████████| 4799/4799 [00:34<00:00, 140.86it/s, avg_loss=0.000215, loss=0.00148] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 2 out of 3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Training Epoch [4/100]: 100%|██████████| 19207/19207 [01:44<00:00, 183.43it/s, avg_loss=0.000206, loss=9.3e-5]  \n", "Validation Epoch [4/100]: 100%|██████████| 4799/4799 [00:32<00:00, 146.17it/s, avg_loss=0.000213, loss=0.00148] \n"]}, {"name": "stdout", "output_type": "stream", "text": ["EarlyStopping counter: 3 out of 3\n", "   Early stopping<<<\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Testing: : 100%|██████████| 67/67 [00:00<00:00, 332.07it/s]\n", "Testing: : 100%|██████████| 352/352 [00:00<00:00, 435.23it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 453.30it/s]\n", "Testing: : 100%|██████████| 188/188 [00:00<00:00, 444.36it/s]\n", "Testing: : 100%|██████████| 317/317 [00:00<00:00, 436.04it/s]\n", "Testing: : 100%|██████████| 67/67 [00:00<00:00, 446.03it/s]\n", "Testing: : 100%|██████████| 356/356 [00:00<00:00, 432.38it/s]\n", "Testing: : 100%|██████████| 109/109 [00:00<00:00, 418.91it/s]\n", "Testing: : 100%|██████████| 94/94 [00:00<00:00, 420.79it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 438.05it/s]\n", "Testing: : 100%|██████████| 34/34 [00:00<00:00, 443.41it/s]\n", "Testing: : 100%|██████████| 195/195 [00:00<00:00, 401.48it/s]\n", "Testing: : 100%|██████████| 352/352 [00:00<00:00, 428.29it/s]\n", "Testing: : 100%|██████████| 261/261 [00:00<00:00, 433.58it/s]\n", "Testing: : 100%|██████████| 157/157 [00:00<00:00, 437.68it/s]\n", "Testing: : 100%|██████████| 63/63 [00:00<00:00, 435.92it/s]\n", "Testing: : 100%|██████████| 356/356 [00:00<00:00, 431.40it/s]\n", "Testing: : 100%|██████████| 318/318 [00:00<00:00, 425.04it/s]\n", "Testing: : 100%|██████████| 219/219 [00:00<00:00, 413.80it/s]\n", "Testing: : 100%|██████████| 252/252 [00:00<00:00, 431.28it/s]\n", "Testing: : 100%|██████████| 61/61 [00:00<00:00, 409.13it/s]\n", "Testing: : 100%|██████████| 317/317 [00:00<00:00, 420.62it/s]\n", "Testing: : 100%|██████████| 352/352 [00:00<00:00, 424.81it/s]\n", "Testing: : 100%|██████████| 223/223 [00:00<00:00, 406.90it/s]\n", "Testing: : 100%|██████████| 232/232 [00:00<00:00, 425.31it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 421.84it/s]\n", "Testing: : 100%|██████████| 1251/1251 [00:03<00:00, 414.33it/s]\n", "Testing: : 100%|██████████| 38/38 [00:00<00:00, 426.28it/s]\n", "Testing: : 100%|██████████| 252/252 [00:00<00:00, 415.15it/s]\n", "Testing: : 100%|██████████| 203/203 [00:00<00:00, 362.39it/s]\n", "Testing: : 100%|██████████| 61/61 [00:00<00:00, 432.41it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 421.40it/s]\n", "Testing: : 100%|██████████| 195/195 [00:00<00:00, 413.12it/s]\n", "Testing: : 100%|██████████| 62/62 [00:00<00:00, 412.35it/s]\n", "Testing: : 100%|██████████| 302/302 [00:00<00:00, 413.96it/s]\n", "Testing: : 100%|██████████| 164/164 [00:00<00:00, 404.60it/s]\n", "Testing: : 100%|██████████| 125/125 [00:00<00:00, 431.21it/s]\n", "Testing: : 100%|██████████| 1149/1149 [00:02<00:00, 405.07it/s]\n", "Testing: : 100%|██████████| 46/46 [00:00<00:00, 421.49it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 399.77it/s]\n", "Testing: : 100%|██████████| 979/979 [00:02<00:00, 387.93it/s]\n", "Testing: : 100%|██████████| 356/356 [00:00<00:00, 411.01it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 412.36it/s]\n", "Testing: : 100%|██████████| 62/62 [00:00<00:00, 418.58it/s]\n", "Testing: : 100%|██████████| 235/235 [00:00<00:00, 413.15it/s]\n", "Testing: : 100%|██████████| 179/179 [00:00<00:00, 420.71it/s]\n", "Testing: : 100%|██████████| 1227/1227 [00:02<00:00, 410.19it/s]\n", "Testing: : 100%|██████████| 203/203 [00:00<00:00, 401.06it/s]\n", "Testing: : 100%|██████████| 196/196 [00:00<00:00, 413.69it/s]\n", "Testing: : 100%|██████████| 195/195 [00:00<00:00, 405.33it/s]\n", "Testing: : 100%|██████████| 571/571 [00:01<00:00, 372.01it/s]\n", "Testing: : 100%|██████████| 38/38 [00:00<00:00, 383.03it/s]\n", "Testing: : 100%|██████████| 41/41 [00:00<00:00, 359.92it/s]\n", "Testing: : 100%|██████████| 179/179 [00:00<00:00, 386.82it/s]\n", "Testing: : 100%|██████████| 261/261 [00:00<00:00, 381.99it/s]\n", "Testing: : 100%|██████████| 26/26 [00:00<00:00, 392.21it/s]\n", "Testing: : 100%|██████████| 48/48 [00:00<00:00, 381.43it/s]\n", "Testing: : 100%|██████████| 352/352 [00:00<00:00, 372.47it/s]\n", "Testing: : 100%|██████████| 109/109 [00:00<00:00, 379.15it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 384.89it/s]\n", "Testing: : 100%|██████████| 41/41 [00:00<00:00, 366.52it/s]\n", "Testing: : 100%|██████████| 616/616 [00:01<00:00, 373.65it/s]\n", "Testing: : 100%|██████████| 48/48 [00:00<00:00, 373.80it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 386.22it/s]\n", "Testing: : 100%|██████████| 1149/1149 [00:03<00:00, 371.33it/s]\n", "Testing: : 100%|██████████| 115/115 [00:00<00:00, 374.87it/s]\n", "Testing: : 100%|██████████| 1134/1134 [00:03<00:00, 365.89it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 335.09it/s]\n", "Testing: : 100%|██████████| 274/274 [00:00<00:00, 365.71it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 359.07it/s]\n", "Testing: : 100%|██████████| 67/67 [00:00<00:00, 380.10it/s]\n", "Testing: : 100%|██████████| 31/31 [00:00<00:00, 368.67it/s]\n", "Testing: : 100%|██████████| 219/219 [00:00<00:00, 361.79it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 370.40it/s]\n", "Testing: : 100%|██████████| 118/118 [00:00<00:00, 370.30it/s]\n", "Testing: : 100%|██████████| 844/844 [00:02<00:00, 326.28it/s]\n", "Testing: : 100%|██████████| 5156/5156 [00:14<00:00, 345.37it/s]\n", "Testing: : 100%|██████████| 41/41 [00:00<00:00, 356.70it/s]\n", "Testing: : 100%|██████████| 356/356 [00:01<00:00, 341.93it/s]\n", "Testing: : 100%|██████████| 252/252 [00:00<00:00, 345.91it/s]\n", "Testing: : 100%|██████████| 289/289 [00:00<00:00, 338.25it/s]\n", "Testing: : 100%|██████████| 469/469 [00:01<00:00, 345.85it/s]\n", "Testing: : 100%|██████████| 1329/1329 [00:03<00:00, 332.87it/s]\n", "Testing: : 100%|██████████| 195/195 [00:00<00:00, 339.00it/s]\n", "Testing: : 100%|██████████| 235/235 [00:00<00:00, 328.36it/s]\n", "Testing: : 100%|██████████| 172/172 [00:00<00:00, 297.48it/s]\n", "Testing: : 100%|██████████| 188/188 [00:00<00:00, 336.59it/s]\n", "Testing: : 100%|██████████| 31/31 [00:00<00:00, 329.11it/s]\n", "Testing: : 100%|██████████| 188/188 [00:00<00:00, 332.96it/s]\n", "Testing: : 100%|██████████| 274/274 [00:00<00:00, 311.44it/s]\n", "Testing: : 100%|██████████| 607/607 [00:01<00:00, 336.10it/s]\n", "Testing: : 100%|██████████| 34/34 [00:00<00:00, 325.06it/s]\n", "Testing: : 100%|██████████| 1459/1459 [00:04<00:00, 324.55it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 316.06it/s]\n", "Testing: : 100%|██████████| 38/38 [00:00<00:00, 321.54it/s]\n", "Testing: : 100%|██████████| 196/196 [00:00<00:00, 308.45it/s]\n", "Testing: : 100%|██████████| 831/831 [00:02<00:00, 321.05it/s]\n", "Testing: : 100%|██████████| 36/36 [00:00<00:00, 317.11it/s]\n", "Testing: : 100%|██████████| 782/782 [00:02<00:00, 314.39it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 319.58it/s]\n", "Testing: : 100%|██████████| 250/250 [00:00<00:00, 294.25it/s]\n", "Testing: : 100%|██████████| 1565/1565 [00:05<00:00, 311.83it/s]\n", "Testing: : 100%|██████████| 287/287 [00:00<00:00, 311.58it/s]\n", "Testing: : 100%|██████████| 118/118 [00:00<00:00, 304.81it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 306.61it/s]\n", "Testing: : 100%|██████████| 250/250 [00:00<00:00, 319.67it/s]\n", "Testing: : 100%|██████████| 700/700 [00:02<00:00, 312.07it/s]\n", "Testing: : 100%|██████████| 61/61 [00:00<00:00, 313.31it/s]\n", "Testing: : 100%|██████████| 223/223 [00:00<00:00, 311.55it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 312.14it/s]\n", "Testing: : 100%|██████████| 48/48 [00:00<00:00, 309.34it/s]\n", "Testing: : 100%|██████████| 1134/1134 [00:03<00:00, 299.21it/s]\n", "Testing: : 100%|██████████| 118/118 [00:00<00:00, 304.04it/s]\n", "Testing: : 100%|██████████| 2018/2018 [00:06<00:00, 291.09it/s]\n", "Testing: : 100%|██████████| 1071/1071 [00:03<00:00, 303.77it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 302.88it/s]\n", "Testing: : 100%|██████████| 235/235 [00:00<00:00, 300.19it/s]\n", "Testing: : 100%|██████████| 62/62 [00:00<00:00, 299.52it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 299.06it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 289.24it/s]\n", "Testing: : 100%|██████████| 235/235 [00:00<00:00, 285.07it/s]\n", "Testing: : 100%|██████████| 61/61 [00:00<00:00, 284.15it/s]\n", "Testing: : 100%|██████████| 352/352 [00:01<00:00, 291.09it/s]\n", "Testing: : 100%|██████████| 250/250 [00:00<00:00, 298.00it/s]\n", "Testing: : 100%|██████████| 196/196 [00:00<00:00, 295.46it/s]\n", "Testing: : 100%|██████████| 115/115 [00:00<00:00, 294.30it/s]\n", "Testing: : 100%|██████████| 157/157 [00:00<00:00, 298.57it/s]\n", "Testing: : 100%|██████████| 336/336 [00:01<00:00, 300.62it/s]\n", "Testing: : 100%|██████████| 38/38 [00:00<00:00, 270.53it/s]\n", "Testing: : 100%|██████████| 172/172 [00:00<00:00, 295.33it/s]\n", "Testing: : 100%|██████████| 125/125 [00:00<00:00, 293.11it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 287.29it/s]\n", "Testing: : 100%|██████████| 54/54 [00:00<00:00, 279.16it/s]\n", "Testing: : 100%|██████████| 1071/1071 [00:03<00:00, 280.62it/s]\n", "Testing: : 100%|██████████| 1560/1560 [00:05<00:00, 287.09it/s]\n", "Testing: : 100%|██████████| 61/61 [00:00<00:00, 288.61it/s]\n", "Testing: : 100%|██████████| 46/46 [00:00<00:00, 283.23it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 297.16it/s]\n", "Testing: : 100%|██████████| 61/61 [00:00<00:00, 286.41it/s]\n", "Testing: : 100%|██████████| 616/616 [00:02<00:00, 282.70it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 288.60it/s]\n", "Testing: : 100%|██████████| 196/196 [00:00<00:00, 284.62it/s]\n", "Testing: : 100%|██████████| 235/235 [00:00<00:00, 276.95it/s]\n", "Testing: : 100%|██████████| 68/68 [00:00<00:00, 273.47it/s]\n", "Testing: : 100%|██████████| 37/37 [00:00<00:00, 273.68it/s]\n", "Testing: : 100%|██████████| 302/302 [00:01<00:00, 281.49it/s]\n", "Testing: : 100%|██████████| 350/350 [00:01<00:00, 268.18it/s]\n", "Testing: : 100%|██████████| 333/333 [00:01<00:00, 278.35it/s]\n", "Testing: : 100%|██████████| 39/39 [00:00<00:00, 268.79it/s]\n", "Testing: : 100%|██████████| 62/62 [00:00<00:00, 274.33it/s]\n", "Testing: : 100%|██████████| 26/26 [00:00<00:00, 278.95it/s]\n", "Testing: : 100%|██████████| 39/39 [00:00<00:00, 267.15it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 273.06it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 270.05it/s]\n", "Testing: : 100%|██████████| 68/68 [00:00<00:00, 275.50it/s]\n", "Testing: : 100%|██████████| 38/38 [00:00<00:00, 267.67it/s]\n", "Testing: : 100%|██████████| 289/289 [00:01<00:00, 273.03it/s]\n", "Testing: : 100%|██████████| 63/63 [00:00<00:00, 278.69it/s]\n", "Testing: : 100%|██████████| 196/196 [00:00<00:00, 263.97it/s]\n", "Testing: : 100%|██████████| 318/318 [00:01<00:00, 281.65it/s]\n", "Testing: : 100%|██████████| 782/782 [00:02<00:00, 262.34it/s]\n", "Testing: : 100%|██████████| 37/37 [00:00<00:00, 273.01it/s]\n", "Testing: : 100%|██████████| 62/62 [00:00<00:00, 263.35it/s]\n", "Testing: : 100%|██████████| 350/350 [00:01<00:00, 263.54it/s]\n", "Testing: : 100%|██████████| 223/223 [00:00<00:00, 267.41it/s]\n", "Testing: : 100%|██████████| 250/250 [00:00<00:00, 272.46it/s]\n", "Testing: : 100%|██████████| 46/46 [00:00<00:00, 264.00it/s]\n", "Testing: : 100%|██████████| 27/27 [00:00<00:00, 211.16it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 259.97it/s]\n", "Testing: : 100%|██████████| 164/164 [00:00<00:00, 271.13it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 282.58it/s]\n", "Testing: : 100%|██████████| 336/336 [00:01<00:00, 278.49it/s]\n", "Testing: : 100%|██████████| 62/62 [00:00<00:00, 279.25it/s]\n", "Testing: : 100%|██████████| 165/165 [00:00<00:00, 265.71it/s]\n", "Testing: : 100%|██████████| 1329/1329 [00:04<00:00, 271.06it/s]\n", "Testing: : 100%|██████████| 336/336 [00:01<00:00, 263.92it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 267.51it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 265.60it/s]\n", "Testing: : 100%|██████████| 179/179 [00:00<00:00, 254.26it/s]\n", "Testing: : 100%|██████████| 317/317 [00:01<00:00, 266.79it/s]\n", "Testing: : 100%|██████████| 33/33 [00:00<00:00, 261.35it/s]\n", "Testing: : 100%|██████████| 55/55 [00:00<00:00, 271.65it/s]\n", "Testing: : 100%|██████████| 350/350 [00:01<00:00, 272.78it/s]\n", "Testing: : 100%|██████████| 41/41 [00:00<00:00, 270.03it/s]\n", "Testing: : 100%|██████████| 844/844 [00:03<00:00, 263.24it/s]\n", "Testing: : 100%|██████████| 469/469 [00:01<00:00, 279.54it/s]\n", "Testing: : 100%|██████████| 94/94 [00:00<00:00, 276.88it/s]\n", "Testing: : 100%|██████████| 352/352 [00:01<00:00, 265.88it/s]\n", "Testing: : 100%|██████████| 27/27 [00:00<00:00, 247.80it/s]\n", "Testing: : 100%|██████████| 979/979 [00:03<00:00, 252.39it/s]\n", "Testing: : 100%|██████████| 179/179 [00:00<00:00, 270.46it/s]\n", "Testing: : 100%|██████████| 46/46 [00:00<00:00, 260.98it/s]\n", "Testing: : 100%|██████████| 36/36 [00:00<00:00, 251.63it/s]\n", "Testing: : 100%|██████████| 336/336 [00:01<00:00, 247.16it/s]\n", "Testing: : 100%|██████████| 54/54 [00:00<00:00, 263.34it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 254.56it/s]\n", "Testing: : 100%|██████████| 235/235 [00:00<00:00, 258.34it/s]\n", "Testing: : 100%|██████████| 700/700 [00:02<00:00, 255.20it/s]\n", "Testing: : 100%|██████████| 1227/1227 [00:05<00:00, 245.05it/s]\n", "Testing: : 100%|██████████| 50/50 [00:00<00:00, 251.60it/s]\n", "Testing: : 100%|██████████| 356/356 [00:01<00:00, 250.05it/s]\n", "Testing: : 100%|██████████| 223/223 [00:00<00:00, 255.85it/s]\n", "Testing: : 100%|██████████| 115/115 [00:00<00:00, 248.22it/s]\n"]}, {"ename": "ModuleNotFoundError", "evalue": "No module named 'statsmodels'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 37\u001b[39m\n\u001b[32m     29\u001b[39m     gctrl.run_exps(\n\u001b[32m     30\u001b[39m         method=method,\n\u001b[32m     31\u001b[39m         training_schema=training_schema\n\u001b[32m     32\u001b[39m     )\n\u001b[32m     35\u001b[39m \u001b[33;03m\"\"\"============= [EVALUATION SETTINGS] =============\"\"\"\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m37\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mEasyTSAD\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mEvaluations\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mProtocols\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m EventF1PA, PointF1PA\n\u001b[32m     38\u001b[39m \u001b[38;5;66;03m# Specifying evaluation protocols\u001b[39;00m\n\u001b[32m     39\u001b[39m gctrl.set_evals(\n\u001b[32m     40\u001b[39m     [\n\u001b[32m     41\u001b[39m         PointF1PA(),\n\u001b[32m   (...)\u001b[39m\u001b[32m     44\u001b[39m     ]\n\u001b[32m     45\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_baseline/../../EasyTSAD/Evaluations/Protocols/__init__.py:14\u001b[39m\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mPointPrc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m PointPrc\n\u001b[32m     13\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mPointRoc\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m PointRoc\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01mVUS\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m VUS_ROC, VUS_PR, R_AP, R_AUC\n", "\u001b[36mFile \u001b[39m\u001b[32m/data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/EasyTSAD-bench/Examples/run_baseline/../../EasyTSAD/Evaluations/Protocols/VUS.py:8\u001b[39m\n\u001b[32m      6\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mscipy\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01msignal\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m argrelextrema\n\u001b[32m      7\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01msklearn\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m metrics\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mstatsmodels\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mtsa\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mstattools\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m acf\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mEasyTSAD\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mEvaluations\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m MetricInterface\n\u001b[32m     11\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01m.\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m MetricInterface, EvalInterface\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'statsmodels'"]}], "source": ["# Create a global controller\n", "gctrl = TSADController()\n", "\n", "\"\"\"============= [DATASET SETTINGS] =============\"\"\"\n", "# Specifying datasets\n", "datasets = [\"TODS\", \"UCR\", \"AIOPS\", \"NAB\", \"Yahoo\", \"WSD\"]\n", "dirname = \"../../../datasets\"\n", "# set datasets path, dirname is the absolute/relative path of dataset.\n", "\n", "# Use all curves in datasets:\n", "gctrl.set_dataset(\n", "    dataset_type=\"UTS\",\n", "    dirname=dirname,\n", "    datasets=datasets,\n", ")\n", "\n", "gctrl.spilt_dataset_for_zero_shot_cross(src=[\"AIOPS\"], dst=[\"UCR\"])\n", "\n", "\n", "\"\"\"============= [EXPERIMENTAL SETTINGS] =============\"\"\"\n", "# Specifying methods and training schemas\n", "from EasyTSAD.Methods import AE, Donut, AR\n", "\n", "methods = [\"AR\"]\n", "training_schema = \"zero_shot_cross_ds\"\n", "\n", "for method in methods:\n", "    # run models\n", "    gctrl.run_exps(\n", "        method=method,\n", "        training_schema=training_schema\n", "    )\n", "    \n", "    \n"]}, {"cell_type": "code", "execution_count": 3, "id": "990beac2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["(2025-07-28 16:44:57,049) [INFO]: Register evaluations\n", "(2025-07-28 16:44:57,050) [INFO]: Perform evaluations. Method[AR], Schema[zero_shot_cross_ds].\n", "(2025-07-28 16:44:57,051) [INFO]:     [Load Data (All)] DataSets: TODS,UCR,AIOPS,NAB,Yahoo,WSD \n", "(2025-07-28 16:44:57,539) [INFO]:     [AR] Eval dataset UCR <<<\n", "(2025-07-28 16:44:57,540) [INFO]:         [UCR] Using margins (0, 50)\n", "(2025-07-28 16:45:09,907) [WARNING]: [AR] UCR: resperation2     All test labels are normal. SKIP this curve. <<<\n", "(2025-07-28 16:45:26,754) [WARNING]: [AR] UCR: NOISEresperation2     All test labels are normal. SKIP this curve. <<<\n", "(2025-07-28 16:45:49,042) [INFO]: Plotting. Method[AR], <PERSON><PERSON><PERSON>[zero_shot_cross_ds].\n", "(2025-07-28 16:45:49,043) [INFO]:     [Load Data (All)] DataSets: TODS,UCR,AIOPS,NAB,Yahoo,WSD \n", "(2025-07-28 16:45:49,516) [INFO]:     [AR] Plot dataset UCR score only \n"]}], "source": ["\"\"\"============= [EVALUATION SETTINGS] =============\"\"\"\n", "\n", "from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA\n", "# Specifying evaluation protocols\n", "gctrl.set_evals(\n", "    [\n", "        PointF1PA(),\n", "        EventF1PA(),\n", "        EventF1PA(mode=\"squeeze\")\n", "    ]\n", ")\n", "\n", "for method in methods:\n", "    gctrl.do_evals(\n", "        method=method,\n", "        training_schema=training_schema\n", "    )\n", "    \n", "    \n", "\"\"\"============= [PLOTTING SETTINGS] =============\"\"\"\n", "\n", "# plot anomaly scores for each curve\n", "for method in methods:\n", "    gctrl.plots(\n", "        method=method,\n", "        training_schema=training_schema\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "AlphaEvolve-for-Anomaly-Detector-Synthesis (3.12.11)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}