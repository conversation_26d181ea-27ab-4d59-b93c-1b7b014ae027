[Data_Params]
# use which method to preprocess original data.
# Option:
#   - z-score(Standardlization),
#   - min-max(Normalization),
#   - raw (original curves)
 preprocess = "raw"  # cos_ai_service handles its own normalization

 # Use `diff_order` differencing orders. Default: 0
 diff_order = 0


# Define CosAIService algorithm parameters - EXACT copy from cos_ai_service/model/utils/default.py
# Default Settings
[Model_Params.Default]
 # These are the exact default parameters from cos_ai_service
 [Model_Params.Default.param_list]
  param_sparse = 0.80
  param_percent = 0.15
  param_smooth = 8
  param_dtw = 0.18
  param_downsample = 360
  param_dtw_low = 1.2
  param_dtw_high = 2.4
  param_sigma = 3
  param_cos_sim = 0.35
  time_window_focus = 3
  time_window_dtw = 240
  time_window_nsigma = 45

 [Model_Params.Default.param_valid_interval]
  param_lolmt = 10
  param_hglmt = 9007199254740000

 [Model_Params.Default.param_list_sparse]
  param_sparse = 0.80
  param_percent = 0.15
  param_smooth = 8
  param_dtw = 0.18
  param_downsample = 360
  param_dtw_low = 1.2
  param_dtw_high = 2.4
  param_sigma = 4.5
  param_cos_sim = 0.35
  time_window_focus = 3
  time_window_dtw = 240
  time_window_nsigma = -1