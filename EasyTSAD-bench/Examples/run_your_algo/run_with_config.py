from typing import Dict
import numpy as np
import sys
sys.path.append("../../")
sys.path.append("../../../cos_ai_service")  # Add root directory to path
from EasyTSAD.Controller import TSADController

# Import cos_ai_service modules - use exact imports from the original code
from model.data_preprocess import smooth_ewma, downsample
from model.utils.waste.similarity_prod import analysis_DTW, analysis_D2
from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse
from model.utils.default import default
from vis_data_preprocess import data_preprocess
print("Successfully imported cos_ai_service modules")


if __name__ == "__main__":

    # Create a global controller
    gctrl = TSADController()

    """============= [DATASET SETTINGS] ============="""
    # Or specify certain curves in one dataset,
    # e.g. AIOPS 0efb375b-b902-3661-ab23-9a0bb799f4e3 and ab216663-dcc2-3a24-b1ee-2c3e550e06c9
    gctrl.set_dataset(
        dataset_type="UTS",
        dirname="../../../datasets",
        datasets="AIOPS",
        specify_curves=True,
        curve_names=[
            "0efb375b-b902-3661-ab23-9a0bb799f4e3",
            "ab216663-dcc2-3a24-b1ee-2c3e550e06c9"
        ]
    )
    
    """============= Implement CosAIService Algorithm ============="""
    from EasyTSAD.Methods import BaseMethod
    from EasyTSAD.DataFactory import TSData

    class CosAIServiceAlgo(BaseMethod):
        def __init__(self, params: dict) -> None:
            super().__init__()
            self.__anomaly_score = None
            self.name = 'CosAIServiceAlgo'

            # Use default parameters from cos_ai_service
            self.param_config = default.copy()

            # Override with any custom parameters from config
            if "param_list" in params:
                self.param_config["param_list"].update(params["param_list"])
            if "param_list_sparse" in params:
                self.param_config["param_list_sparse"].update(params["param_list_sparse"])

        def _create_temporal_context(self, data_array, timestamps=None):
            """
            Transform EasyTSAD single time series into cos_ai_service format exactly as expected.
            Based on the data structure from cos_ai_service/vis_data_preprocess.py

            Args:
                data_array: 1D numpy array of time series data
                timestamps: Optional timestamp array

            Returns:
                dict: cos_ai_service compatible data structure
            """
            data_len = len(data_array)

            # Split data into three roughly equal parts for today, yesterday, lastweek
            # This mimics having temporal context as required by cos_ai_service
            if data_len < 3:
                # Handle edge case for very short series
                today = data_array.tolist()
                yesterday = data_array.tolist()
                lastweek = data_array.tolist()
            else:
                # Split data into three parts
                part_size = data_len // 3
                today = data_array[-part_size:].tolist()  # Most recent part
                yesterday = data_array[-2*part_size:-part_size].tolist()  # Middle part
                lastweek = data_array[-3*part_size:-2*part_size].tolist()  # Earliest part

            # Create timestamps if not provided
            if timestamps is None:
                timestamps = list(range(len(today)))

            # Create the exact data structure expected by cos_ai_service data_preprocess function
            # This structure matches what's expected in vis_data_preprocess.py
            input_data = {
                "timestamps": timestamps,
                "metric_data": {
                    "change_period": True,
                    "series": [
                        {
                            "metric_val": today,
                            "val_status": [0] * len(today)  # 0 means data is reported/valid
                        },
                        {
                            "metric_val": yesterday,
                            "val_status": [0] * len(yesterday)  # 0 means data is reported/valid
                        },
                        {
                            "metric_val": lastweek,
                            "val_status": [0] * len(lastweek)  # 0 means data is reported/valid
                        }
                    ]
                },
                "param": self.param_config
            }

            return input_data

        def _run_cos_ai_algorithm(self, input_data):
            """
            Run the exact cos_ai_service anomaly detection algorithm.
            This follows the exact flow from cos_ai_service/vis_prod_data.py

            Args:
                input_data: cos_ai_service compatible data structure

            Returns:
                dict: Updated data with anomaly detection results
            """
            
            # Step 1: Data preprocessing - exact call from vis_prod_data.py
            metric_data = data_preprocess(input_data)

            # Step 2: Run the exact algorithm flow from change_analysis() function
            # Check if data is sparse (from algo_sparse vs algo_no_sparse)
            is_sparse = metric_data.get("log", {}).get("is_sparse", False)

            if is_sparse:
                # algo_sparse() implementation
                param_list = metric_data["param"]["param_list_sparse"]
                metric_data = smooth_ewma(metric_data, param_smooth=param_list["param_smooth"])
                metric_data = analysis_nsigma_sparse(metric_data,
                                                    param_sigma=param_list["param_sigma"],
                                                    time_window=int(param_list["time_window_nsigma"]))
                metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
                metric_data = analysis_D2(metric_data)
            else:
                # algo_no_sparse() implementation - exact sequence from vis_prod_data.py
                param_list = metric_data["param"]["param_list"]
                metric_data = smooth_ewma(metric_data, param_smooth=param_list["param_smooth"])
                metric_data = analysis_DTW(metric_data)
                metric_data = analysis_nsigma(metric_data,
                                            param_sigma=param_list["param_sigma"],
                                            time_window=int(param_list["time_window_nsigma"]))
                metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
                metric_data = analysis_D2(metric_data)

            return metric_data


        def _extract_anomaly_scores(self, metric_data, original_length):
            """
            Extract the exact anomaly scores from cos_ai_service results.
            Use the is_change_err boolean output directly as the anomaly score.

            Args:
                metric_data: cos_ai_service results
                original_length: Length of original EasyTSAD test data

            Returns:
                np.ndarray: Boolean anomaly indicators converted to float scores
            """
            # Get the exact output from cos_ai_service: is_change_err boolean array
            is_change_err = metric_data.get("is_change_err", [])

            # Convert boolean indicators to float scores (0.0 or 1.0)
            # This is the exact output format from cos_ai_service
            if len(is_change_err) == 0:
                raise ValueError("No anomaly scores detected. Check cos_ai_service implementation.")

            # Use the exact boolean output from cos_ai_service
            anomaly_scores = np.array([float(err) for err in is_change_err], dtype=np.float64)

            # Map back to original test data length if needed
            if original_length != len(anomaly_scores):
                if original_length > len(anomaly_scores) and len(anomaly_scores) > 0:
                    # Interpolate to match original length
                    indices = np.linspace(0, len(anomaly_scores)-1, original_length)
                    anomaly_scores = np.interp(indices, np.arange(len(anomaly_scores)), anomaly_scores)
                elif original_length < len(anomaly_scores):
                    # Sample scores to match shorter length
                    indices = np.linspace(0, len(anomaly_scores)-1, original_length, dtype=int)
                    anomaly_scores = anomaly_scores[indices]
                else:
                    # Edge case: return zeros if we can't map properly
                    anomaly_scores = np.zeros(original_length, dtype=np.float64)

            return anomaly_scores

        def train_valid_phase(self, tsTrain: TSData):
            '''
            Define train and valid phase for naive mode.
            For cos_ai_service algorithm, we don't need explicit training as it's based on
            statistical analysis and pattern matching with historical data.
            '''
            # cos_ai_service is an unsupervised algorithm that doesn't require training
            # It analyzes patterns in real-time using today vs yesterday vs lastweek comparisons
            print("CosAIService algorithm: No explicit training required (unsupervised)")
            return

        def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):
            '''
            Define train and valid phase for all-in-one mode.
            cos_ai_service doesn't require training across multiple time series.
            '''
            print("CosAIService algorithm: No explicit training required (unsupervised)")
            return

        def test_phase(self, tsData: TSData):
            '''
            Define test phase for each time series using the exact cos_ai_service algorithm.
            This follows the exact flow from cos_ai_service/vis_prod_data.py change_analysis()
            '''
            test_len = tsData.test.shape[0]

            # Step 1: Transform EasyTSAD data to cos_ai_service input format
            input_data = self._create_temporal_context(tsData.test)

            # Step 2: Run the exact cos_ai_service algorithm
            result_data = self._run_cos_ai_algorithm(input_data)

            # Step 3: Extract the exact boolean anomaly indicators from cos_ai_service
            anomaly_scores = self._extract_anomaly_scores(result_data, test_len)

            # Store the anomaly scores (boolean indicators as floats: 0.0 or 1.0)
            self.__anomaly_score = anomaly_scores


        def anomaly_score(self) -> np.ndarray:
            return self.__anomaly_score

        def param_statistic(self, save_file):
            """Save parameter statistics following EasyTSAD conventions"""
            param_info = f"""CosAIService Algorithm Parameters:

Algorithm: {self.name}
Type: Temporal Pattern Analysis with DTW and N-Sigma Detection
Processing: EWMA Smoothing + Downsampling + Statistical Analysis

Parameters Used:
- param_smooth: {self.param_config['param_list']['param_smooth']}
- param_dtw: {self.param_config['param_list']['param_dtw']}
- param_sigma: {self.param_config['param_list']['param_sigma']}
- time_window_dtw: {self.param_config['param_list']['time_window_dtw']}
- time_window_nsigma: {self.param_config['param_list']['time_window_nsigma']}
- time_window_focus: {self.param_config['param_list']['time_window_focus']}

Sparse Parameters:
- param_sigma_sparse: {self.param_config['param_list_sparse']['param_sigma']}
- time_window_nsigma_sparse: {self.param_config['param_list_sparse']['time_window_nsigma']}

Algorithm Flow:
1. Data preprocessing and temporal context creation
2. EWMA smoothing for noise reduction
3. DTW analysis for pattern similarity detection
4. N-sigma statistical outlier detection
5. Downsampling and final anomaly scoring

Output: Boolean anomaly indicators (0.0/1.0) from cos_ai_service
"""
            with open(save_file, 'w') as f:
                f.write(param_info)
    
    """============= Run CosAIService Algorithm ============="""
    # Specifying methods and training schemas

    training_schema = "naive"
    method = "CosAIServiceAlgo"  # string of our cos_ai_service algorithm class

    # run models
    gctrl.run_exps(
        method=method,
        training_schema=training_schema,
        cfg_path="YourAlgo.toml" # path/to/config
    )
       
        
    """============= [EVALUATION SETTINGS] ============="""
    
    from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA
    # Specifying evaluation protocols
    gctrl.set_evals(
        [
            PointF1PA(),
            EventF1PA(),
            EventF1PA(mode="squeeze")
        ]
    )

    gctrl.do_evals(
        method=method,
        training_schema=training_schema
    )
        
        
    """============= [PLOTTING SETTINGS] ============="""
    
    # plot anomaly scores for each curve
    gctrl.plots(
        method=method,
        training_schema=training_schema
    )
