from typing import Dict
import numpy as np
import sys
import os
import json
import math
sys.path.append("../../")
sys.path.append("../../../")  # Add root directory to path
from EasyTSAD.Controller import TSADController

# Import cos_ai_service modules
try:
    from cos_ai_service.model.data_preprocess import smooth_ewma, downsample
    from cos_ai_service.model.utils.waste.similarity_prod import analysis_DTW, analysis_D2
    from cos_ai_service.model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse
    from cos_ai_service.model.utils.default import default
    print("Successfully imported cos_ai_service modules")
except ImportError as e:
    print(f"Warning: Could not import cos_ai_service modules: {e}")
    print("Using enhanced fallback implementations...")

    # Enhanced fallback implementations with actual anomaly detection logic
    def smooth_ewma(data, param_smooth=8):
        """Apply exponential weighted moving average smoothing"""
        import pandas as pd

        # Create smoothed versions
        smoothed_data = data.copy()
        for key in ['metric_today', 'metric_yesterday', 'metric_lastweek']:
            if key in data:
                series = pd.Series(data[key])
                smoothed = series.ewm(span=param_smooth).mean().tolist()
                if 'smooth' not in smoothed_data:
                    smoothed_data['smooth'] = {'ewma': {}}
                smoothed_data['smooth']['ewma'][key] = smoothed

        return smoothed_data

    def downsample(data, target_len):
        """Downsample data to target length"""
        def ds(d, target_len):
            if target_len >= len(d):
                return d

            res = []
            len_interval = len(d) / target_len
            for i in range(target_len):
                start, end = int(i * len_interval), int((i + 1) * len_interval)
                if i == target_len - 1:
                    end = len(d)
                res.append(max(d[start:end]))  # Use max for downsampling
            return res

        data["downsample"] = {
            "metric_today": ds(data["metric_today"], target_len),
            "metric_yesterday": ds(data["metric_yesterday"], target_len),
            "metric_lastweek": ds(data["metric_lastweek"], target_len)
        }
        return data

    def analysis_DTW(data):
        """Simple DTW-like analysis using correlation"""
        try:
            from scipy.stats import pearsonr

            smooth_data = data.get('smooth', {}).get('ewma', data)
            today = smooth_data['metric_today']
            yesterday = smooth_data['metric_yesterday']
            lastweek = smooth_data['metric_lastweek']

            # Calculate correlations as DTW approximation
            min_len = min(len(today), len(yesterday), len(lastweek))
            if min_len > 1:
                corr_yesterday, _ = pearsonr(today[-min_len:], yesterday[-min_len:])
                corr_lastweek, _ = pearsonr(today[-min_len:], lastweek[-min_len:])

                # Lower correlation means higher anomaly
                dtw_score = 1.0 - max(corr_yesterday, corr_lastweek)

                if 'log' not in data:
                    data['log'] = {}
                data['log']['DTW'] = [dtw_score] * len(today)

                # Mark as anomaly if DTW score is high
                threshold = data.get('param', {}).get('param_list', {}).get('param_dtw', 0.18)
                if dtw_score > threshold:
                    data['is_change_err'][-1] = 1

        except ImportError:
            # Fallback without scipy
            if 'log' not in data:
                data['log'] = {}
            data['log']['DTW'] = [0.0] * len(data['metric_today'])

        return data

    def analysis_D2(data):
        """Simple distance analysis"""
        try:
            downsample_data = data.get('downsample', data)
            today = downsample_data['metric_today']
            yesterday = downsample_data['metric_yesterday']
            lastweek = downsample_data['metric_lastweek']

            # Calculate simple distance metrics
            if len(today) > 0 and len(yesterday) > 0 and len(lastweek) > 0:
                dist_yesterday = sum(abs(a - b) for a, b in zip(today, yesterday)) / len(today)
                dist_lastweek = sum(abs(a - b) for a, b in zip(today, lastweek)) / len(today)

                dist_all = min(dist_yesterday, dist_lastweek)

                param_list = data.get('param', {}).get('param_list', {})
                thre_low = param_list.get('param_dtw_low', 1.2)
                thre_high = param_list.get('param_dtw_high', 2.4)

                if dist_all > thre_high:
                    data['is_change_err'][-1] = 1
                elif dist_all < thre_low:
                    data['is_change_err'][-1] = 0

                if 'log' not in data:
                    data['log'] = {}
                data['log']['DTW_all'] = f"distance: {dist_all:.3f}"

        except Exception:
            pass

        return data

    def analysis_nsigma(data, param_sigma=3, time_window=45):
        """N-sigma outlier detection"""
        today = data['metric_today']
        yesterday = data['metric_yesterday']
        lastweek = data['metric_lastweek']

        if len(today) > 0:
            # Calculate statistics from historical data
            historical = yesterday + lastweek
            if len(historical) > 1:
                mean_hist = sum(historical) / len(historical)
                var_hist = sum((x - mean_hist) ** 2 for x in historical) / len(historical)
                std_hist = var_hist ** 0.5

                if 'log' not in data:
                    data['log'] = {}
                data['log']['nsigma'] = []

                # Check each point in today's data
                for i, val in enumerate(today):
                    if std_hist > 0:
                        z_score = abs(val - mean_hist) / std_hist
                        data['log']['nsigma'].append(z_score)

                        if z_score > param_sigma:
                            data['is_change_err'][i] = 1
                    else:
                        data['log']['nsigma'].append(0.0)

        return data

    def analysis_nsigma_sparse(data, param_sigma=4.5, time_window=-1):
        """N-sigma analysis for sparse data"""
        return analysis_nsigma(data, param_sigma, time_window)

    default = {
        "param_list": {
            "param_sparse": 0.80,
            "param_percent": 0.15,
            "param_smooth": 8,
            "param_dtw": 0.18,
            "param_downsample": 360,
            "param_dtw_low": 1.2,
            "param_dtw_high": 2.4,
            "param_sigma": 3,
            "param_cos_sim": 0.35,
            "time_window_focus": 3,
            "time_window_dtw": 240,
            "time_window_nsigma": 45
        },
        "param_valid_interval": {
            "param_lolmt": 10,
            "param_hglmt": 9007199254740000
        },
        "param_list_sparse": {
            "param_sparse": 0.80,
            "param_percent": 0.15,
            "param_smooth": 8,
            "param_dtw": 0.18,
            "param_downsample": 360,
            "param_dtw_low": 1.2,
            "param_dtw_high": 2.4,
            "param_sigma": 4.5,
            "param_cos_sim": 0.35,
            "time_window_focus": 3,
            "time_window_dtw": 240,
            "time_window_nsigma": -1
        }
    }

if __name__ == "__main__":

    # Create a global controller
    gctrl = TSADController()

    """============= [DATASET SETTINGS] ============="""
    # Or specify certain curves in one dataset,
    # e.g. AIOPS 0efb375b-b902-3661-ab23-9a0bb799f4e3 and ab216663-dcc2-3a24-b1ee-2c3e550e06c9
    gctrl.set_dataset(
        dataset_type="UTS",
        dirname="../../../datasets",
        datasets="AIOPS",
        specify_curves=True,
        curve_names=[
            "0efb375b-b902-3661-ab23-9a0bb799f4e3",
            "ab216663-dcc2-3a24-b1ee-2c3e550e06c9"
        ]
    )
    
    """============= Implement CosAIService Algorithm ============="""
    from EasyTSAD.Methods import BaseMethod
    from EasyTSAD.DataFactory import TSData

    class CosAIServiceAlgo(BaseMethod):
        def __init__(self, hparams) -> None:
            super().__init__()
            self.__anomaly_score = None
            # Use default parameters from cos_ai_service
            self.param_config = default.copy()
            # Override with any custom parameters from hparams
            if "param_list" in hparams:
                self.param_config["param_list"].update(hparams["param_list"])

        def _create_temporal_context(self, data_array, timestamps=None):
            """
            Transform EasyTSAD single time series into cos_ai_service format
            with today, yesterday, and lastweek components.

            Args:
                data_array: 1D numpy array of time series data
                timestamps: Optional timestamp array

            Returns:
                dict: cos_ai_service compatible data structure
            """
            data_len = len(data_array)

            # Assume data represents multiple days worth of data
            # Split into roughly equal parts for today, yesterday, lastweek
            # This is a simplified approach - in real scenarios, you'd use actual timestamps

            if data_len < 3:
                # Handle edge case for very short series
                today = data_array.copy()
                yesterday = data_array.copy()
                lastweek = data_array.copy()
            else:
                # Split data into three roughly equal parts
                part_size = data_len // 3
                today = data_array[-part_size:].tolist()  # Most recent part
                yesterday = data_array[-2*part_size:-part_size].tolist()  # Middle part
                lastweek = data_array[-3*part_size:-2*part_size].tolist()  # Earliest part

                # Ensure all parts have the same length by padding if necessary
                max_len = max(len(today), len(yesterday), len(lastweek))

                # Pad shorter sequences by repeating the last value
                while len(today) < max_len:
                    today.append(today[-1] if today else 0.0)
                while len(yesterday) < max_len:
                    yesterday.append(yesterday[-1] if yesterday else 0.0)
                while len(lastweek) < max_len:
                    lastweek.append(lastweek[-1] if lastweek else 0.0)

            # Normalize data to [0, 1] range as expected by cos_ai_service
            all_data = today + yesterday + lastweek
            min_val, max_val = min(all_data), max(all_data)

            if max_val > min_val:
                today = [(val - min_val) / (max_val - min_val) for val in today]
                yesterday = [(val - min_val) / (max_val - min_val) for val in yesterday]
                lastweek = [(val - min_val) / (max_val - min_val) for val in lastweek]

            # Create timestamps if not provided
            if timestamps is None:
                timestamps = list(range(len(today)))

            # Create cos_ai_service compatible data structure
            metric_data = {
                "timestamps": timestamps,
                "change_period": True,  # Assume we're in change period
                "metric_today": today,
                "metric_yesterday": yesterday,
                "metric_lastweek": lastweek,
                "origin_data": {
                    "metric_today": today.copy(),
                    "metric_today_report_status": [0] * len(today),  # All data reported
                    "metric_yesterday": yesterday.copy(),
                    "metric_yesterday_report_status": [0] * len(yesterday),
                    "metric_lastweek": lastweek.copy(),
                    "metric_lastweek_report_status": [0] * len(lastweek)
                },
                "interval": {},
                "curve": {"high": {}, "low": {}},
                "is_change_err": [0] * len(today),  # Initialize as no errors
                "log": {
                    "is_sparse": False,
                    "report_ratio": 1.0,
                    "in_valid_interval": True
                },
                "param": self.param_config
            }

            return metric_data

        def _run_cos_ai_algorithm(self, metric_data):
            """
            Run the cos_ai_service anomaly detection algorithm

            Args:
                metric_data: cos_ai_service compatible data structure

            Returns:
                dict: Updated metric_data with anomaly detection results
            """
            try:
                # Check if data is sparse
                if metric_data["log"]["is_sparse"]:
                    # Run sparse algorithm
                    param_list = metric_data["param"]["param_list_sparse"]
                    metric_data = smooth_ewma(metric_data, param_smooth=param_list["param_smooth"])
                    metric_data = analysis_nsigma_sparse(metric_data,
                                                       param_sigma=param_list["param_sigma"],
                                                       time_window=int(param_list["time_window_nsigma"]))
                    metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
                    metric_data = analysis_D2(metric_data)
                else:
                    # Run normal algorithm
                    param_list = metric_data["param"]["param_list"]
                    metric_data = smooth_ewma(metric_data, param_smooth=param_list["param_smooth"])
                    metric_data = analysis_DTW(metric_data)
                    metric_data = analysis_nsigma(metric_data,
                                                param_sigma=param_list["param_sigma"],
                                                time_window=int(param_list["time_window_nsigma"]))
                    metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
                    metric_data = analysis_D2(metric_data)

            except Exception as e:
                print(f"Error in cos_ai_service algorithm: {e}")
                # Fallback: mark all points as normal
                metric_data["is_change_err"] = [0] * len(metric_data["metric_today"])

            return metric_data

        def _extract_anomaly_scores(self, metric_data, original_length):
            """
            Extract anomaly scores from cos_ai_service results and map back to original data length

            Args:
                metric_data: cos_ai_service results
                original_length: Length of original EasyTSAD test data

            Returns:
                np.ndarray: Anomaly scores for each point in original test data
            """
            # Get anomaly indicators from cos_ai_service
            is_change_err = metric_data.get("is_change_err", [])

            # Get additional scoring information if available
            dtw_scores = metric_data.get("log", {}).get("DTW", [])
            nsigma_scores = metric_data.get("log", {}).get("nsigma", [])

            # Create composite anomaly score
            today_length = len(metric_data["metric_today"])
            anomaly_scores = []

            for i in range(today_length):
                score = 0.0

                # Base score from change error indicator
                if i < len(is_change_err):
                    score += is_change_err[i] * 1.0

                # Add DTW contribution if available
                if i < len(dtw_scores) and dtw_scores[i] > 0:
                    score += min(dtw_scores[i], 1.0)  # Normalize DTW score

                # Add n-sigma contribution if available
                if i < len(nsigma_scores) and nsigma_scores[i] > 0:
                    score += min(nsigma_scores[i] / 3.0, 1.0)  # Normalize n-sigma score

                anomaly_scores.append(score)

            # Map back to original test data length
            if original_length != today_length:
                # Interpolate or repeat scores to match original length
                if original_length > today_length:
                    # Repeat each score multiple times
                    repeat_factor = original_length // today_length
                    remainder = original_length % today_length

                    extended_scores = []
                    for i, score in enumerate(anomaly_scores):
                        extended_scores.extend([score] * repeat_factor)
                        if i < remainder:
                            extended_scores.append(score)
                    anomaly_scores = extended_scores[:original_length]
                else:
                    # Sample scores to match shorter length
                    indices = np.linspace(0, today_length-1, original_length, dtype=int)
                    anomaly_scores = [anomaly_scores[i] for i in indices]

            return np.array(anomaly_scores)

        def train_valid_phase(self, tsTrain: TSData):
            '''
            Define train and valid phase for naive mode.
            For cos_ai_service algorithm, we don't need explicit training as it's based on
            statistical analysis and pattern matching with historical data.
            '''
            # cos_ai_service is an unsupervised algorithm that doesn't require training
            # It analyzes patterns in real-time using today vs yesterday vs lastweek comparisons
            print("CosAIService algorithm: No explicit training required (unsupervised)")
            return

        def train_valid_phase_all_in_one(self, tsTrains: Dict[str, TSData]):
            '''
            Define train and valid phase for all-in-one mode.
            cos_ai_service doesn't require training across multiple time series.
            '''
            print("CosAIService algorithm: No explicit training required (unsupervised)")
            return

        def test_phase(self, tsData: TSData):
            '''
            Define test phase for each time series using cos_ai_service algorithm.
            '''
            print(f"Running CosAIService algorithm on test data with shape: {tsData.test.shape}")

            try:
                # Transform EasyTSAD data to cos_ai_service format
                metric_data = self._create_temporal_context(tsData.test)

                # Run cos_ai_service anomaly detection algorithm
                metric_data = self._run_cos_ai_algorithm(metric_data)

                # Extract anomaly scores and map back to original data length
                anomaly_scores = self._extract_anomaly_scores(metric_data, len(tsData.test))

                # Store the anomaly scores
                self.__anomaly_score = anomaly_scores

                print(f"CosAIService algorithm completed. Generated {len(anomaly_scores)} anomaly scores")
                print(f"Anomaly score range: [{np.min(anomaly_scores):.3f}, {np.max(anomaly_scores):.3f}]")
                print(f"Number of detected anomalies: {np.sum(anomaly_scores > 0.5)}")

            except Exception as e:
                print(f"Error in CosAIService test phase: {e}")
                import traceback
                traceback.print_exc()
                # Fallback: return zero scores
                self.__anomaly_score = np.zeros(len(tsData.test))

        def anomaly_score(self) -> np.ndarray:
            return self.__anomaly_score

        def param_statistic(self, save_file):
            param_info = f"""CosAIService Algorithm Parameters:

Algorithm Type: Temporal Pattern Analysis with DTW and N-Sigma
Data Processing: EWMA Smoothing + Downsampling
Anomaly Detection: DTW similarity analysis + Statistical outlier detection

Parameters Used:
- param_smooth: {self.param_config['param_list']['param_smooth']}
- param_dtw: {self.param_config['param_list']['param_dtw']}
- param_sigma: {self.param_config['param_list']['param_sigma']}
- time_window_dtw: {self.param_config['param_list']['time_window_dtw']}
- time_window_nsigma: {self.param_config['param_list']['time_window_nsigma']}
- time_window_focus: {self.param_config['param_list']['time_window_focus']}

Algorithm Description:
1. Transform single time series into temporal context (today, yesterday, lastweek)
2. Apply EWMA smoothing for noise reduction
3. Perform DTW analysis to detect pattern deviations
4. Apply N-sigma statistical analysis for outlier detection
5. Combine results to generate final anomaly scores

Data Transformation:
- Input: Single continuous time series from EasyTSAD
- Output: Temporal context with today/yesterday/lastweek components
- Normalization: Min-max scaling to [0,1] range
"""
            with open(save_file, 'w') as f:
                f.write(param_info)
    
    """============= Run CosAIService Algorithm ============="""
    # Specifying methods and training schemas

    training_schema = "naive"
    method = "CosAIServiceAlgo"  # string of our cos_ai_service algorithm class

    # run models
    gctrl.run_exps(
        method=method,
        training_schema=training_schema,
        cfg_path="YourAlgo.toml" # path/to/config
    )
       
        
    """============= [EVALUATION SETTINGS] ============="""
    
    from EasyTSAD.Evaluations.Protocols import EventF1PA, PointF1PA
    # Specifying evaluation protocols
    gctrl.set_evals(
        [
            PointF1PA(),
            EventF1PA(),
            EventF1PA(mode="squeeze")
        ]
    )

    gctrl.do_evals(
        method=method,
        training_schema=training_schema
    )
        
        
    """============= [PLOTTING SETTINGS] ============="""
    
    # plot anomaly scores for each curve
    gctrl.plots(
        method=method,
        training_schema=training_schema
    )
