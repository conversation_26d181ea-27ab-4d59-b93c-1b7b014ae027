
[build-system]
requires = ["setuptools >= 61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "trace-opt"
dynamic = ["version", "authors", "license", "requires-python", "dependencies", "description"]
readme = "README.md"
keywords = ["trace", "opto", "AutoDiff"]
classifiers = [
  "Development Status :: 4 - Beta",
  "Intended Audience :: Developers",
  "License :: OSI Approved :: MIT License",
  "Programming Language :: Python :: 3.9",
]

[project.optional-dependencies]
autogen = ["autogen-agentchat==0.2.40"]


[project.urls]
Homepage = "https://microsoft.github.io/Trace/"
Documentation = "https://microsoft.github.io/Trace/intro.html"
Repository = "https://github.com/microsoft/Trace.git"
