{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# Greeting Agent\n", "\n", "## Introduction\n", "\n", "This notebook will focus on using `trace` to optimize a sales agent that greets a customer.\n", "\n", "## Setup and Installation\n", "\n", "Let's start by importing the necessary libraries."], "id": "a5a83b8093fae334"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "%pip install trace-opt", "id": "af6a991e6fa8e083"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["%%capture\n", "!pip install openai==1.55.3 httpx==0.27.2 --force-reinstall --quiet"], "id": "500ce27b656605ea"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from opto import trace\n", "from opto.trace import node, bundle, model, ExecutionError\n", "from opto.optimizers import OptoPrime"], "id": "72b76d44a5423795"}, {"metadata": {}, "cell_type": "markdown", "source": "Add API keys for LLM calls. Run the code below:", "id": "88243c6b69d0c2ad"}, {"metadata": {"ExecuteTime": {"end_time": "2024-12-10T00:10:08.564966Z", "start_time": "2024-12-10T00:10:08.520705Z"}}, "cell_type": "code", "source": ["import os\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# Function to save the environment variable and API key\n", "def save_env_variable(env_name, api_key):\n", "    # Validate inputs\n", "    if not env_name.strip():\n", "        print(\"⚠️ Environment variable name cannot be empty.\")\n", "        return\n", "    if not api_key.strip():\n", "        print(\"⚠️ API key cannot be empty.\")\n", "        return\n", "    \n", "    # Store the API key as an environment variable\n", "    os.environ[env_name] = api_key\n", "    globals()[env_name] = api_key  # Set it as a global variable\n", "    print(f\"✅ API key has been set for environment variable: {env_name}\")\n", "\n", "# Create the input widgets\n", "env_name_input = widgets.Text(\n", "    value=\"OPENAI_API_KEY\",  # Default value\n", "    description=\"Env Name:\",\n", "    placeholder=\"Enter env variable name (e.g., MY_API_KEY)\",\n", ")\n", "\n", "api_key_input = widgets.Password(\n", "    description=\"API Key:\",\n", "    placeholder=\"Enter your API key\",\n", ")\n", "\n", "# Create the button to submit the inputs\n", "submit_button = widgets.Button(description=\"Set API Key\")\n", "\n", "# Display the widgets\n", "display(env_name_input, api_key_input, submit_button)\n", "\n", "# Callback function for the button click\n", "def on_button_click(b):\n", "    env_name = env_name_input.value\n", "    api_key = api_key_input.value\n", "    save_env_variable(env_name, api_key)\n", "\n", "# Attach the callback to the button\n", "submit_button.on_click(on_button_click)"], "id": "3242fb533b7cb3f4", "outputs": [{"data": {"text/plain": ["Text(value='OPENAI_API_KEY', description='Env Name:', placeholder='Enter env variable name (e.g., MY_API_KEY)'…"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1bd6aa77089941b6bf1387d59df773d2"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["Password(description='API Key:', placeholder='Enter your API key')"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2c985d3f3ddd439bb6366c58833af31c"}}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["Button(description='Set API Key', style=ButtonStyle())"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "29026f7b286643a7bd31f4b2ac0533ff"}}, "metadata": {}, "output_type": "display_data"}], "execution_count": 1}, {"metadata": {}, "cell_type": "markdown", "source": ["## Define an Agent\n", "\n", "In here, we use `@trace.bundle` to wrap functions so that they show up in TraceGraph. We use `trace.node` to wrap system prompts. `@trace.model` does not do much, except to provide us some convenience to grab all the trainable parameters. |"], "id": "753dc6c3e24a0899"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["@trace.model\n", "class Agent:\n", "\n", "    def __init__(self, system_prompt):\n", "        self.system_prompt = system_prompt\n", "        self.instruct1 = trace.node(\"Decide the language\", trainable=True)\n", "        self.instruct2 = trace.node(\"Extract name if it's there\", trainable=True)\n", "\n", "    def __call__(self, user_query):\n", "        response = trace.operators.call_llm(self.system_prompt,\n", "                                            self.instruct1, user_query)\n", "        en_or_es = self.decide_lang(response)\n", "\n", "        user_name = trace.operators.call_llm(self.system_prompt,\n", "                                             self.instruct2, user_query)\n", "        greeting = self.greet(en_or_es, user_name)\n", "\n", "        return greeting\n", "\n", "    @trace.bundle(trainable=True)\n", "    def decide_lang(self, response):\n", "        \"\"\"Map the language into a variable\"\"\"\n", "        return\n", "\n", "    @trace.bundle(trainable=True)\n", "    def greet(self, lang, user_name):\n", "        \"\"\"Produce a greeting based on the language\"\"\"\n", "        greeting = \"<PERSON><PERSON>\"\n", "        return f\"{greeting}, {user_name}!\""], "id": "26064f7dfbd2ac2e"}, {"metadata": {}, "cell_type": "markdown", "source": "## Define Feedback and Training", "id": "4d45873f3379d594"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def feedback_fn(generated_response, gold_label='en'):\n", "    if  gold_label == 'en' and 'Hello' in generated_response:\n", "        return \"Correct\"\n", "    elif gold_label == 'es' and 'Hola' in generated_response:\n", "        return \"Correct\"\n", "    else:\n", "        return \"Incorrect\"\n", "\n", "\n", "def train():\n", "    epoch = 3\n", "    agent = Agent(\"You are a sales assistant.\")\n", "    optimizer = OptoPrime(agent.parameters())\n", "\n", "    for i in range(epoch):\n", "        print(f\"Training Epoch {i}\")\n", "        try:\n", "            greeting = agent(\"Hello, I'm <PERSON>.\")\n", "            feedback = feedback_fn(greeting.data, 'en')\n", "        except ExecutionError as e:\n", "            greeting = e.exception_node\n", "            feedback, terminal, reward = greeting.data, False, 0\n", "\n", "        optimizer.zero_feedback()\n", "        optimizer.backward(greeting, feedback)\n", "        optimizer.step(verbose=True)\n", "\n", "        if feedback == 'Correct':\n", "            break\n", "\n", "    return agent"], "id": "43f743d5c27936c8"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "agent = train()", "id": "ab2cb1b0c8a4f4b0"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}