{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BigBench-Hard\n", "\n", "## Introduction\n", "\n", "In this notebook, we will demonstrate how to use the `trace` package to optimize prompts and code for natural language processing tasks using the BigBench-Hard benchmark. SotA approaches on this benchmark only optimize prompts, while relying on hand-written code to extract answers from LLM responses. By leveraging the LLM-based optimizers provided in `trace`, we aim to enhance the performance of a workflow calling LLMs and post-processing their responses in generating accurate and relevant answers.\n", "\n", "## Setup\n", "\n", "First, we'll import the necessary packages and set up our environment. We will use a copy of the BigBench-Hard benchmark hosted on [HuggingFace](https://huggingface.co/datasets/maveriq/bigbenchhard). To use HuggingFace datasets, ensure that you have the `datasets` package installed:\n", "\n", "```{note}\n", "To replicate our experiment in the paper, run the script here:\n", "https://github.com/microsoft/Trace/blob/main/examples/bbh/run_prompt_bigbench_trace.py\n", "```"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install datasets\n", "%pip install trace-opt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2024-07-19T21:19:03.229950Z", "start_time": "2024-07-19T21:18:59.801910Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["# Import necessary libraries\n", "import autogen\n", "from opto.trace.nodes import node, GRAPH, ParameterNode\n", "from opto.optimizers import OptoPrime\n", "from datasets import load_dataset\n", "from textwrap import dedent\n", "from opto.trace.bundle import bundle\n", "from opto.trace.modules import model\n", "from opto.trace.errors import ExecutionError\n", "from opto.trace.nodes import ExceptionNode\n", "from typing import List\n", "import re"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import os\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# Function to save the environment variable and API key\n", "def save_env_variable(env_name, api_key):\n", "    # Validate inputs\n", "    if not env_name.strip():\n", "        print(\"⚠️ Environment variable name cannot be empty.\")\n", "        return\n", "    if not api_key.strip():\n", "        print(\"⚠️ API key cannot be empty.\")\n", "        return\n", "    \n", "    # Store the API key as an environment variable\n", "    os.environ[env_name] = api_key\n", "    globals()[env_name] = api_key  # Set it as a global variable\n", "    print(f\"✅ API key has been set for environment variable: {env_name}\")\n", "\n", "# Create the input widgets\n", "env_name_input = widgets.Text(\n", "    value=\"OPENAI_API_KEY\",  # Default value\n", "    description=\"Env Name:\",\n", "    placeholder=\"Enter env variable name (e.g., MY_API_KEY)\",\n", ")\n", "\n", "api_key_input = widgets.Password(\n", "    description=\"API Key:\",\n", "    placeholder=\"Enter your API key\",\n", ")\n", "\n", "# Create the button to submit the inputs\n", "submit_button = widgets.Button(description=\"Set API Key\")\n", "\n", "# Display the widgets\n", "display(env_name_input, api_key_input, submit_button)\n", "\n", "# Callback function for the button click\n", "def on_button_click(b):\n", "    env_name = env_name_input.value\n", "    api_key = api_key_input.value\n", "    save_env_variable(env_name, api_key)\n", "\n", "# Attach the callback to the button\n", "submit_button.on_click(on_button_click)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the Evaluation Function\n", "\n", "Next, we'll define the utility function for evaluating answers obtained by prompting an LLM."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2024-07-19T21:19:03.230792Z", "start_time": "2024-07-19T21:19:03.223927Z"}}, "outputs": [], "source": ["def eval_metric(true, prediction):\n", "    matches = re.findall(r\"\\([A-Z]\\)\", true)\n", "    if matches:\n", "        pred = prediction\n", "        matches = re.findall(r\"\\([A-Z]\\)\", pred)\n", "        parsed_answer = matches[-1] if matches else \"\"\n", "        return parsed_answer == true\n", "    else:\n", "        return prediction == true"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Helper Function\n", "\n", "We'll create a helper class called `LLMCallable` to interact with the LLM API."]}, {"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2024-07-19T21:19:03.880915Z", "start_time": "2024-07-19T21:19:03.657068Z"}}, "outputs": [], "source": ["class LLMCallable:\n", "    def __init__(self, config_list=None, max_tokens=1024, verbose=False):\n", "        if config_list is None:\n", "            config_list = autogen.config_list_from_json(\"OAI_CONFIG_LIST\")\n", "        self.llm = autogen.OpenAIWrapper(config_list=config_list)\n", "        self.max_tokens = max_tokens\n", "        self.verbose = verbose\n", "\n", "    @bundle(catch_execution_error=True)\n", "    def call_llm(self, user_prompt):\n", "        system_prompt = \"You are a helpful assistant.\\n\"\n", "        messages = [{\"role\": \"system\", \"content\": system_prompt}, {\"role\": \"user\", \"content\": user_prompt}]\n", "        response = self.llm.create(messages=messages, max_tokens=self.max_tokens)\n", "        response = response.choices[0].message.content\n", "\n", "        if self.verbose:\n", "            print(\"LLM response:\\n\", response)\n", "        return response\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define a Traced Class\n", "\n", "We will define a Predict class to generate predictions using LLM. Note that we use a module provided by `trace` called `Model` which can wrap a python class to enable tracing. "]}, {"cell_type": "code", "execution_count": 5, "metadata": {"ExecuteTime": {"end_time": "2024-07-19T21:19:04.970205Z", "start_time": "2024-07-19T21:19:04.933904Z"}}, "outputs": [], "source": ["@model\n", "class Predict(LLMCallable):\n", "    def __init__(self):\n", "        super().__init__()\n", "\n", "        self.demos = []\n", "        self. prompt_template = dedent(\n", "        \"\"\"\n", "        Given the fields `question`, produce the fields `answer`.\n", "\n", "        ---\n", "\n", "        Follow the following format.\n", "\n", "        Question: \n", "        Answer: \n", "\n", "        ---\n", "        Question: {}\n", "        Answer:\n", "        \"\"\"\n", "        )\n", "        self.prompt_template = ParameterNode(self.prompt_template, trainable=True,\n", "                                             description=\"This is the Prompt Template to the LLM. \" + \\\n", "                                                         \"Need to include information about what the format of answers LLM should output. \" + \\\n", "                                                         \"They can be (A)/(B), a number like 8, or a string, or Yes/No.\")\n", "\n", "    @bundle(trainable=True, catch_execution_error=True, allow_external_dependencies=True)\n", "    def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "\n", "    @bundle(trainable=True, catch_execution_error=True, allow_external_dependencies=True)\n", "    def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "\n", "    def forward(self, question):\n", "        user_prompt = self.create_prompt(self.prompt_template, question)\n", "        response = self.call_llm(user_prompt)\n", "        answer = self.extract_answer(self.prompt_template, question, response)\n", "        return answer"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the optimizer\n", "\n", "Note that the `prompt_template` is a `ParameterNode` as well as the `extract_answer` is a trainable function. `trace` handles the optimization of heterogenous parameters seamlessly."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2024-07-19T21:19:06.148198Z", "start_time": "2024-07-19T21:19:06.128481Z"}}, "outputs": [], "source": ["def train(dp, optimizer, examples):\n", "    for step, example in enumerate(examples):\n", "        try:\n", "            response = dp.forward(example['question'])\n", "            correctness = eval_metric(example['answer'], response)\n", "            feedback = \"The answer is correct! No need to change anything.\" if correctness else f\"The answer is wrong. We expect the output of your answer to be \\\"{example['answer']}\\\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer.\"\n", "        except ExecutionError as e:\n", "            response = e.exception_node\n", "            feedback = response.data\n", "            correctness = False\n", "            \n", "        print(\"Question:\", example[\"question\"])\n", "        print(\"Expected answer:\", example[\"answer\"])\n", "        print(\"Answer:\", response)\n", "\n", "        if correctness:\n", "            continue\n", "\n", "        optimizer.zero_feedback()\n", "        optimizer.backward(response, feedback)\n", "\n", "        print(f\"Output: {response}, Feedback: {feedback}, Variables:\")  # Logging\n", "        for p in optimizer.parameters:\n", "            print(p.name, p.data)\n", "        optimizer.step(verbose=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Putting it all together\n", "\n", "Finally, we use the optimizer to find better prompts using a small training set as follows."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2024-07-19T21:19:21.867979Z", "start_time": "2024-07-19T21:19:07.382684Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Generating train split: 100%|██████████| 250/250 [00:00<00:00, 40783.17 examples/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Training on a few examples:\n", "Question: Is the following sentence plausible? \"<PERSON> beat the buzzer.\"\n", "Expected answer: no\n", "Answer: MessageNode: (eval:1, dtype=<class 'str'>, data=Yes, the sentence \"<PERSON> beat the buzzer\" is plausible. It is commonly used in sports contexts to describe a scenario where a player, such as <PERSON> in ice hockey, scores just before the time runs out in a period or game.)\n", "Output: MessageNode: (eval:1, dtype=<class 'str'>, data=Yes, the sentence \"<PERSON> beat the buzzer\" is plausible. It is commonly used in sports contexts to describe a scenario where a player, such as <PERSON> in ice hockey, scores just before the time runs out in a period or game.), Feedback: The answer is wrong. We expect the output of your answer to be \"no\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer., Variables:\n", "__code:1 def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "__code:0 def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "str:0 \n", "Given the fields `question`, produce the fields `answer`.\n", "\n", "---\n", "\n", "Follow the following format.\n", "\n", "Question: \n", "Answer: \n", "\n", "---\n", "Question: {}\n", "Answer:\n", "\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval0 = eval(self=self0, prompt_template=str0, question=question0, __code=__code1)\n", "LLMCallable.call_llm0 = LLMCallable.call_llm(self=self1, user_prompt=eval0)\n", "eval1 = eval(self=self2, prompt_template=str0, question=question1, response=LLMCallable.call_llm0, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[LLMCallable.call_llm] .\n", "\n", "#Variables\n", "(str) str0=\n", "Given the fields `question`, produce the fields `answer`.\n", "\n", "---\n", "\n", "Follow the following format.\n", "\n", "Question: \n", "Answer: \n", "\n", "---\n", "Question: {}\n", "Answer:\n", "\n", "(code) __code1:def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "(code) __code0:def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def create_prompt(self, prompt_template, question):\n", "(code) __code0: The code should start with:\n", "def extract_answer(self, prompt_template, question, response):\n", "\n", "#Inputs\n", "(ModelWrapper) self2=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question1=Is the following sentence plausible? \"<PERSON> beat the buzzer.\"\n", "(ModelWrapper) self1=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(ModelWrapper) self0=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question0=Is the following sentence plausible? \"<PERSON> beat the buzzer.\"\n", "\n", "#Others\n", "(str) eval0=\n", "Given the fields `question`, produce the fields `answer`.\n", "\n", "---\n", "\n", "Follow the following format.\n", "\n", "Question: \n", "Answer: \n", "\n", "---\n", "Question: Is the following sentence plausible? \"<PERSON> beat the buzzer.\"\n", "Answer:\n", "\n", "(str) LLMCallable.call_llm0=Question: Is the following sentence plausible? \"<PERSON> beat the buzzer.\"\n", "Answer: Yes, the sentence \"<PERSON> beat the buzzer\" is plausible. It is commonly used in sports contexts to describe a scenario where a player, such as <PERSON> in ice hockey, scores just before the time runs out in a period or game.\n", "\n", "#Outputs\n", "(str) eval1=Yes, the sentence \"<PERSON> beat the buzzer\" is plausible. It is commonly used in sports contexts to describe a scenario where a player, such as <PERSON> in ice hockey, scores just before the time runs out in a period or game.\n", "\n", "#Feedback\n", "The answer is wrong. We expect the output of your answer to be \"no\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "  \"reasoning\": \"The objective is to modify the prompt template (str0) such that the Language Model (LLM) produces an answer that aligns with the expected answer 'no' as opposed to 'yes'. The function 'create_prompt' as defined by '__code1', uses the prompt template (str0) to format a question. The prompt template instructs how the question should be posed to the LLM. Currently, the prompt template is generic and does not guide the LLM towards evaluating the plausibility of the statement correctly. Since the feedback demands a specific answer ('no'), the prompt template itself should be tailored to facilitate evaluating the plausibility specifically, possibly by providing a context or criteria under which the statement might be deemed implausible. The function 'extract_answer' as defined by '__code0' correctly identifies the answer segment from the LLM's output, and hence does not require changes.\",\n", "  \"suggestion\": {\n", "    \"str0\": \"Given the grammatical fields `question`, produce the fields `answer`.\\n\\n---\\n\\nPlease analyze the grammatical plausibility of the question provided:\\n\\nQuestion: {}\\nAnswer:\"\n", "  }\n", "}\n", "Question: Is the following sentence plausible? \"<PERSON> scored in the third period.\"\n", "Expected answer: yes\n", "Answer: MessageNode: (exception_eval:0, dtype=<class 'str'>, data=(IndexError) list index out of range)\n", "Output: MessageNode: (exception_eval:0, dtype=<class 'str'>, data=(IndexError) list index out of range), Feedback: (IndexError) list index out of range, Variables:\n", "__code:1 def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "__code:0 def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "str:0 Given the grammatical fields `question`, produce the fields `answer`.\n", "\n", "---\n", "\n", "Please analyze the grammatical plausibility of the question provided:\n", "\n", "Question: {}\n", "Answer:\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval2 = eval(self=self3, prompt_template=str0, question=question2, __code=__code1)\n", "LLMCallable.call_llm1 = LLMCallable.call_llm(self=self4, user_prompt=eval2)\n", "exception_eval0 = eval(self=self5, prompt_template=str0, question=question3, response=LLMCallable.call_llm1, __code=__code0)\n", "\n", "#Documentation\n", "[exception] The operator eval raises an exception.\n", "[LLMCallable.call_llm] .\n", "\n", "#Variables\n", "(str) str0=Given the grammatical fields `question`, produce the fields `answer`.\n", "\n", "---\n", "\n", "Please analyze the grammatical plausibility of the question provided:\n", "\n", "Question: {}\n", "Answer:\n", "(code) __code1:def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "(code) __code0:def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def create_prompt(self, prompt_template, question):\n", "(code) __code0: The code should start with:\n", "def extract_answer(self, prompt_template, question, response):\n", "\n", "#Inputs\n", "(ModelWrapper) self5=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question3=Is the following sentence plausible? \"<PERSON> scored in the third period.\"\n", "(ModelWrapper) self4=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(ModelWrapper) self3=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question2=Is the following sentence plausible? \"<PERSON> scored in the third period.\"\n", "\n", "#Others\n", "(str) eval2=Given the grammatical fields `question`, produce the fields `answer`.\n", "\n", "---\n", "\n", "Please analyze the grammatical plausibility of the question provided:\n", "\n", "Question: Is the following sentence plausible? \"<PERSON> scored in the third period.\"\n", "Answer:\n", "(str) LLMCallable.call_llm1=Yes, the sentence \"<PERSON> scored in the third period.\" is grammatically plausible. It follows the standard subject-verb-object format and provides clear information about who did what and when. The subject is \"<PERSON>,\" the verb is \"scored,\" and the phrase \"in the third period\" provides additional context about the timing of the action.\n", "\n", "#Outputs\n", "(str) exception_eval0=(IndexError) list index out of range\n", "\n", "#Feedback\n", "(IndexError) list index out of range\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "    \"reasoning\": \"The IndexError 'list index out of range' usually results from trying to access an index in a list or string that doesn't exist. From the provided feedback, we see that the exception occurs when executing `exception_eval0`. Specifically, the exception arises in the `extract_answer` function defined in `__code0`. The assignment `answer = response.split(\\\"Answer:\\\")[1].strip()` attempts to access the second element (index 1) after splitting `response` by the substring 'Answer:'. If 'Answer:' isn't found or there is no information following 'Answer:', the index [1] will be out of range, leading to the IndexError. In the string `LLMCallable.call_llm1`, the sentence 'Answer:' does not immediately follow a colon but merely appears in the string, hence it doesn't split as expected. \\nThe fix involves ensuring that 'Answer:' appears correctly formatted so when split, it follows with enough content to access index [1]. Hence, the structure of `str0` should correctly prepare the `response` to include 'Answer:' followed by the answer content.\",\n", "    \"suggestion\": {\n", "        \"str0\": \"Given the grammatical fields `question`, produce the fields `answer`: Answer: {}\"\n", "    }\n", "}\n", "Question: Is the following sentence plausible? \"<PERSON><PERSON> got on the end of a through ball.\"\n", "Expected answer: yes\n", "Answer: MessageNode: (eval:4, dtype=<class 'str'>, data=Yes, the sentence \"<PERSON><PERSON> got on the end of a through ball\" is plausible. It is commonly used in sports commentary, especially in soccer (football), to describe a player receiving a pass that goes through the defense.)\n", "Output: MessageNode: (eval:4, dtype=<class 'str'>, data=Yes, the sentence \"<PERSON><PERSON> got on the end of a through ball\" is plausible. It is commonly used in sports commentary, especially in soccer (football), to describe a player receiving a pass that goes through the defense.), Feedback: The answer is wrong. We expect the output of your answer to be \"yes\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer., Variables:\n", "__code:1 def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "__code:0 def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "str:0 Given the grammatical fields `question`, produce the fields `answer`: Answer: {}\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval3 = eval(self=self6, prompt_template=str0, question=question4, __code=__code1)\n", "LLMCallable.call_llm2 = LLMCallable.call_llm(self=self7, user_prompt=eval3)\n", "eval4 = eval(self=self8, prompt_template=str0, question=question5, response=LLMCallable.call_llm2, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[LLMCallable.call_llm] .\n", "\n", "#Variables\n", "(str) str0=Given the grammatical fields `question`, produce the fields `answer`: Answer: {}\n", "(code) __code1:def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "(code) __code0:def extract_answer(self, prompt_template, question, response):\n", "        answer = response.split(\"Answer:\")[1].strip()\n", "        return answer\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def create_prompt(self, prompt_template, question):\n", "(code) __code0: The code should start with:\n", "def extract_answer(self, prompt_template, question, response):\n", "\n", "#Inputs\n", "(ModelWrapper) self8=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question5=Is the following sentence plausible? \"<PERSON><PERSON> got on the end of a through ball.\"\n", "(ModelWrapper) self7=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(ModelWrapper) self6=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question4=Is the following sentence plausible? \"<PERSON><PERSON> got on the end of a through ball.\"\n", "\n", "#Others\n", "(str) eval3=Given the grammatical fields `question`, produce the fields `answer`: Answer: Is the following sentence plausible? \"<PERSON><PERSON> got on the end of a through ball.\"\n", "(str) LLMCallable.call_llm2=Answer: Yes, the sentence \"<PERSON><PERSON> got on the end of a through ball\" is plausible. It is commonly used in sports commentary, especially in soccer (football), to describe a player receiving a pass that goes through the defense.\n", "\n", "#Outputs\n", "(str) eval4=Yes, the sentence \"<PERSON><PERSON> got on the end of a through ball\" is plausible. It is commonly used in sports commentary, especially in soccer (football), to describe a player receiving a pass that goes through the defense.\n", "\n", "#Feedback\n", "The answer is wrong. We expect the output of your answer to be \"yes\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "    \"reasoning\": \"The goal is to adjust the necessary variables so the final output (`eval4`) from the language model (LLM) is 'yes'. Analyzing the processes in the code reveals that `str0`, `__code1` and `__code0` are instrumental in crafting the prompt passed into the LLM and formatting its response to extract an answer. The original format of the string template `str0` used to generate the prompt seems to influence how detailed the LLM answers. The function `create_prompt` builds the prompt using `str0`, but the prompt explicitly includes an instruction to 'produce the fields' which might result in more elaborate answers from the LLM, not just a simple 'yes' or 'no'. To change this, we can tweak `str0` to request a concise response. The output is essentially derived from how the LLM responds and due to how we extract the `answer` using `__code0`, it takes everything following 'Answer:'. Given the feedback is seeking a precise 'yes' answer, adjustments to both the request template (`str0`) and the extraction method can be made to align outputs with expectations.\",\n", "    \"answer\": \"\",\n", "    \"suggestion\": {\n", "        \"str0\": \"Given the grammatical fields `question`, specify whether the statement is plausible in one word (yes/no):\",\n", "        \"__code0\": \"def extract_answer(self, prompt_template, question, response):\\n answer = response.split(' ')[1].strip()\\n return answer\"\n", "    }\n", "}\n", "Question: Is the following sentence plausible? \"<PERSON><PERSON><PERSON> was called for the goal tend in the Eastern Conference Finals.\"\n", "Expected answer: no\n", "Answer: MessageNode: (eval:6, dtype=<class 'str'>, data=provide)\n", "Output: MessageNode: (eval:6, dtype=<class 'str'>, data=provide), Feedback: The answer is wrong. We expect the output of your answer to be \"no\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer., Variables:\n", "__code:1 def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "__code:0 def extract_answer(self, prompt_template, question, response):\n", " answer = response.split(' ')[1].strip()\n", " return answer\n", "str:0 Given the grammatical fields `question`, specify whether the statement is plausible in one word (yes/no):\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval5 = eval(self=self9, prompt_template=str0, question=question6, __code=__code1)\n", "LLMCallable.call_llm3 = LLMCallable.call_llm(self=self10, user_prompt=eval5)\n", "eval6 = eval(self=self11, prompt_template=str0, question=question7, response=LLMCallable.call_llm3, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[LLMCallable.call_llm] .\n", "\n", "#Variables\n", "(str) str0=Given the grammatical fields `question`, specify whether the statement is plausible in one word (yes/no):\n", "(code) __code1:def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "(code) __code0:def extract_answer(self, prompt_template, question, response):\n", " answer = response.split(' ')[1].strip()\n", " return answer\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def create_prompt(self, prompt_template, question):\n", "(code) __code0: The code should start with:\n", "def extract_answer(self, prompt_template, question, response):\n", "\n", "#Inputs\n", "(ModelWrapper) self11=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question7=Is the following sentence plausible? \"<PERSON><PERSON><PERSON> was called for the goal tend in the Eastern Conference Finals.\"\n", "(ModelWrapper) self10=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(ModelWrapper) self9=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question6=Is the following sentence plausible? \"<PERSON><PERSON><PERSON> was called for the goal tend in the Eastern Conference Finals.\"\n", "\n", "#Others\n", "(str) eval5=Given the grammatical fields `question`, specify whether the statement is plausible in one word (yes/no):\n", "(str) LLMCallable.call_llm3=Please provide the statement for evaluation.\n", "\n", "#Outputs\n", "(str) eval6=provide\n", "\n", "#Feedback\n", "The answer is wrong. We expect the output of your answer to be \"no\". Please modify the prompt and relevant parts of the program to help LLM produce the right answer.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "  \"reasoning\": \"The code block executes a sequence of operations involving assessment and response generation based on the question of the plausibility of a statement. The problem lies in the provided string in str0 and possibly the formulation of calls and return statements in __code0. The variable str0 is meant to construct a prompt template, and __code1 formats this to include the question. However, str0 fails to communicate clearly to the LLM that it needs to evaluate the truth of the statement, rather than asking for an explanation or any other output format. This unclear prompt might lead to LLMCallable.call_llm3 generating an unhelpful response like 'Please provide the statement for evaluation.' Subsequently, __code0 is expected to extract the answer, but its logic needs refinement to reliably parse the response from LLM. The variable eval6 results in 'provide' which indicates the LLM response was not correctly parsed and also shows it might not have been guided correctly by the prompt to give a yes/no answer. To address the issue, we need to reformulate the prompt in str0 and adjust the response parsing logic in __code0 to ensure a correct and clear extraction of 'yes' or 'no'.\",\n", "  \"suggestion\": {\n", "    \"str0\": \"Is the statement \\\"{question}\\\" plausible? Answer 'yes' or 'no'.\",\n", "    \"__code0\": \"def extract_answer(self, prompt_template, question, response):\\n answer = response.strip().lower()\\n return answer\"\n", "  }\n", "}\n", "Question: Is the following sentence plausible? \"<PERSON><PERSON><PERSON> skated behind the net.\"\n", "Expected answer: no\n", "Answer: MessageNode: (exception_eval:1, dtype=<class 'str'>, data=(KeyError) 'question')\n", "Output: MessageNode: (exception_eval:1, dtype=<class 'str'>, data=(KeyError) 'question'), Feedback: (KeyError) 'question', Variables:\n", "__code:1 def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "__code:0 def extract_answer(self, prompt_template, question, response):\n", " answer = response.strip().lower()\n", " return answer\n", "str:0 Is the statement \"{question}\" plausible? Answer 'yes' or 'no'.\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "exception_eval1 = eval(self=self12, prompt_template=str0, question=question8, __code=__code1)\n", "\n", "#Documentation\n", "[exception] The operator eval raises an exception.\n", "\n", "#Variables\n", "(str) str0=Is the statement \"{question}\" plausible? Answer 'yes' or 'no'.\n", "(code) __code1:def create_prompt(self, prompt_template, question):\n", "        return prompt_template.format(question)\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def create_prompt(self, prompt_template, question):\n", "\n", "#Inputs\n", "(ModelWrapper) self12=<opto.trace.modules.model.<locals>.ModelWrapper object at 0x7f4bd428a410>\n", "(str) question8=Is the following sentence plausible? \"<PERSON><PERSON><PERSON> skated behind the net.\"\n", "\n", "#Others\n", "\n", "\n", "#Outputs\n", "(str) exception_eval1=(KeyError) 'question'\n", "\n", "#Feedback\n", "(KeyErro<PERSON>) 'question'\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "  \"reasoning\": \"The operation fails because it is trying to use the Python str.format method in the __code1 function, and is looking for a key named 'question' in the format string. However, in the original format string 'str0', this key is represented as {question} inside double quotes, which Python cannot recognize as the names of the placeholders for the format method. As a result, the KeyError means that the template string 'str0' does not match the expected format string requirements where placeholder names are used directly without additional quotes or alterations.\",\n", "  \"answer\": \"\",\n", "  \"suggestion\": {\n", "    \"str0\": \"Is the statement {question} plausible? Answer 'yes' or 'no'.\"\n", "  }\n", "}\n", "\n", "Testing on new examples:\n", "Accuracy:  0.0\n"]}], "source": ["task = \"sports_understanding\"\n", "train_set = load_dataset(\"maveriq/bigbenchhard\", task)[\"train\"]\n", "examples = [{\"question\": r[\"input\"], \"answer\": r[\"target\"]} for r in train_set]\n", "\n", "dp = Predict()\n", "optimizer = OptoPrime(dp.parameters(),\n", "                                    config_list=autogen.config_list_from_json(\"OAI_CONFIG_LIST\"))\n", "\n", "print(\"Training on a few examples:\")\n", "train(dp, optimizer, examples[:5])\n", "\n", "test_accuracy = []\n", "print(\"\\nTesting on new examples:\")\n", "for example in examples[5:10]:\n", "    try:\n", "        response = dp.forward(example[\"question\"])\n", "        correctness = eval_metric(example[\"answer\"], response.data)\n", "    except ExecutionError as e:\n", "        correctness = 0\n", "\n", "    test_accuracy.append(correctness)\n", "\n", "print(\"Accuracy: \", sum(test_accuracy) / len(test_accuracy))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, you can run each cell in this notebook step by step to walk through the process of setting up and optimizing prompts. Happy optimizing!"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}