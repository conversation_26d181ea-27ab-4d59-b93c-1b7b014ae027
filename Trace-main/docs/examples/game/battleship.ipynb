{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Single Agent: Battleship\n", "\n", "## Introduction\n", "\n", "This notebook will focus on using `trace` to optimize multiple pieces of code automatically in the context of a Battleship game. \n", "\n", "## Setup and Installation\n", "\n", "Let's start by importing the necessary libraries."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install trace-opt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import random\n", "import numpy as np\n", "from opto.trace import bundle, node, Module, GRAPH\n", "from opto.trace.errors import ExecutionError\n", "from opto.trace.bundle import ExceptionNode\n", "from opto.optimizers import OptoPrime\n", "from autogen import config_list_from_json"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import os\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# Function to save the environment variable and API key\n", "def save_env_variable(env_name, api_key):\n", "    # Validate inputs\n", "    if not env_name.strip():\n", "        print(\"⚠️ Environment variable name cannot be empty.\")\n", "        return\n", "    if not api_key.strip():\n", "        print(\"⚠️ API key cannot be empty.\")\n", "        return\n", "    \n", "    # Store the API key as an environment variable\n", "    os.environ[env_name] = api_key\n", "    globals()[env_name] = api_key  # Set it as a global variable\n", "    print(f\"✅ API key has been set for environment variable: {env_name}\")\n", "\n", "# Create the input widgets\n", "env_name_input = widgets.Text(\n", "    value=\"OPENAI_API_KEY\",  # Default value\n", "    description=\"Env Name:\",\n", "    placeholder=\"Enter env variable name (e.g., MY_API_KEY)\",\n", ")\n", "\n", "api_key_input = widgets.Password(\n", "    description=\"API Key:\",\n", "    placeholder=\"Enter your API key\",\n", ")\n", "\n", "# Create the button to submit the inputs\n", "submit_button = widgets.Button(description=\"Set API Key\")\n", "\n", "# Display the widgets\n", "display(env_name_input, api_key_input, submit_button)\n", "\n", "# Callback function for the button click\n", "def on_button_click(b):\n", "    env_name = env_name_input.value\n", "    api_key = api_key_input.value\n", "    save_env_variable(env_name, api_key)\n", "\n", "# Attach the callback to the button\n", "submit_button.on_click(on_button_click)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Environment Setup\n", "\n", "Define the Battleship board creation and environment handling functions."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def create_battleship_board(width, height):\n", "    board = [['.' for _ in range(width)] for _ in range(height)]\n", "    return board\n", "\n", "def can_place_ship(board, row, col, size, is_vertical):\n", "    if is_vertical:\n", "        if row + size > len(board):\n", "            return False\n", "        for i in range(size):\n", "            if board[row + i][col] != '.':\n", "                return False\n", "    else:\n", "        if col + size > len(board[0]):\n", "            return False\n", "        for i in range(size):\n", "            if board[row][col + i] != '.':\n", "                return False\n", "    return True\n", "\n", "def place_ship(board, row, col, size, is_vertical, ship_symbol):\n", "    if is_vertical:\n", "        for i in range(size):\n", "            board[row + i][col] = ship_symbol\n", "    else:\n", "        for i in range(size):\n", "            board[row][col + i] = ship_symbol\n", "\n", "def create_and_fill_battleship_board(width, height, ships, num_each_type=2):\n", "    board = [['.' for _ in range(width)] for _ in range(height)]\n", "    for ship_symbol, size in ships.items():\n", "        for num in range(1, num_each_type + 1):\n", "            placed = False\n", "            while not placed:\n", "                row = random.randint(0, height - 1)\n", "                col = random.randint(0, width - 1)\n", "                is_vertical = random.choice([True, False])\n", "                if can_place_ship(board, row, col, size, is_vertical):\n", "                    place_ship(board, row, col, size, is_vertical, ship_symbol)\n", "                    placed = True\n", "    return board\n", "\n", "def check_hit(board, row, col):\n", "    if 0 <= row < len(board) and 0 <= col < len(board[0]):\n", "        if board[row][col] not in ['.', 'O', 'X']:\n", "            board[row][col] = 'X'\n", "            return True\n", "        else:\n", "            if board[row][col] == '.':\n", "                board[row][col] = 'O'\n", "    return False\n", "\n", "# Ships to be placed on the board\n", "ships = {\n", "    'C': 5,  # Carrier\n", "    'B': 4,  # Battleship\n", "    'R': 3,  # Cruiser\n", "    'S': 3,  # Submarine\n", "    'D': 2  # Destroyer\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Wrap the environment into a BattleshipBoard class"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Define BattleshipBoard class\n", "class BattleshipBoard:\n", "    def __init__(self, width, height, num_each_type=2, exclude_ships=[], init_with_one_hit=False):\n", "        self.width = width\n", "        self.height = height\n", "        self.ships = {s: ships[s] for s in ships if s not in exclude_ships}\n", "        self.board = create_and_fill_battleship_board(width, height, self.ships, num_each_type=num_each_type)\n", "        self.shots = [['.' for _ in range(width)] for _ in range(height)]\n", "        self.hits = 0\n", "        self.misses = 0\n", "\n", "        if init_with_one_hit:\n", "            initialized = False\n", "            for row in range(height):\n", "                for col in range(width):\n", "                    if self.board[row][col] != '.':\n", "                        self.check_shot(row, col)\n", "                        initialized = True\n", "                        break\n", "                if initialized:\n", "                    break\n", "\n", "    def get_life_points(self):\n", "        return sum(self.ships.values())\n", "\n", "    def check_shot(self, row, col):\n", "        is_hit = check_hit(self.board, row, col)\n", "        if is_hit:\n", "            self.hits += 1\n", "            self.shots[row][col] = 'X'\n", "        else:\n", "            self.misses += 1\n", "            if self.shots[row][col] == '.':\n", "                self.shots[row][col] = 'O'\n", "        return is_hit\n", "\n", "    def check_terminate(self):\n", "        return (self.hits >= sum(self.ships.values())) or (self.misses + self.hits >= self.width * self.height)\n", "\n", "    def get_board(self):\n", "        return self.board\n", "\n", "    def get_shots(self):\n", "        return self.shots\n", "\n", "    def get_shots_overlay_board(self):\n", "        shots_overlay_board = [[self.board[row][col] if self.shots[row][col] == '.' else self.shots[row][col] for col in range(self.width)] for row in range(self.height)]\n", "        return shots_overlay_board\n", "\n", "    def get_hits(self):\n", "        return self.hits\n", "\n", "    def get_misses(self):\n", "        return self.misses\n", "\n", "    def get_game_status(self):\n", "        if self.hits == sum(self.ships.values()):\n", "            return 'Game Over: All ships sunk!'\n", "        return 'Game in progress'\n", "\n", "    def visualize_board(self):\n", "        str_rep = ''\n", "        for row in self.board:\n", "            str_rep += ' '.join(row) + '\\n'\n", "        print(str_rep)\n", "\n", "    def visualize_own_board(self):\n", "        str_rep = ''\n", "        board = self.get_shots_overlay_board()\n", "        for row in board:\n", "            str_rep += ' '.join(row) + '\\n'\n", "        print(str_rep)\n", "\n", "    def visualize_shots(self):\n", "        str_rep = ''\n", "        for row in self.shots:\n", "            str_rep += ' '.join(row) + '\\n'\n", "        print(str_rep)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define a Policy class with multiple trainable functions"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Define Policy class\n", "class Policy(Module):\n", "    def init(self, width, height):\n", "        pass\n", "\n", "    def __call__(self, map):\n", "        return self.select_coordinate(map).data\n", "\n", "    def select_coordinate(self, map):\n", "        plan = self.reason(map)\n", "        output = self.act(map, plan)\n", "        return output\n", "\n", "    @bundle(trainable=True)\n", "    def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        return\n", "\n", "    @bundle(trainable=True)\n", "    def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        return\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Helper Functions to rollout and evaluate the policy\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Function to get user feedback for placing shot\n", "def user_fb_for_placing_shot(board, coords):\n", "    try:\n", "        reward = board.check_shot(coords[0], coords[1])\n", "        new_map = board.get_shots()\n", "        terminal = board.check_terminate()\n", "        return new_map, reward, terminal, f\"Got {int(reward)} reward.\"\n", "    except Exception as e:\n", "        board.misses += 1\n", "        return board.get_shots(), 0, <PERSON>alse, str(e)\n", "    \n", "# Function to rollout policy\n", "def rollout(policy, board):\n", "    rewards = []\n", "    obs = board.get_shots()\n", "    while not board.check_terminate():\n", "        output = policy(obs)\n", "        obs, reward, terminal, feedback = user_fb_for_placing_shot(board, output)\n", "        if terminal:\n", "            break\n", "        rewards.append(reward)\n", "    rewards = np.array(rewards)\n", "    return rewards\n", "\n", "# Function to evaluate policy\n", "def eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes):\n", "    scores = []\n", "    for _ in range(n_eval_episodes):\n", "        board = BattleshipBoard(board_size, board_size, num_each_type=num_each_type, exclude_ships=exclude_ships)\n", "        rewards = rollout(policy, board)\n", "        scores.append(rewards.mean())\n", "    scores = np.array(scores)\n", "    print(f\"Scores: {scores.mean()} ({scores.std()})\")\n", "    return scores"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Creating the initial policy\n", "\n", "Finally, create a `Policy` object and evaluate the performance of the initial code."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Scores: 0.0 (0.0)\n", "Initial scores: [0. 0. 0.]\n"]}], "source": ["# Set parameters\n", "board_size = 5\n", "num_each_type = 1\n", "exclude_ships = ['C']\n", "n_eval_episodes = 3\n", "\n", "# Create policy and evaluate\n", "policy = Policy()\n", "init_scores = eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes)\n", "print(\"Initial scores:\", init_scores)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Putting it all together\n", "\n", "Create an optimizer and evaluate the online optimization of the produced codes."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval0 = eval(self=self0, map=list0, __code=__code1)\n", "eval1 = eval(self=self1, map=list0, plan=eval0, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "\n", "#Variables\n", "(code) __code1:def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        return\n", "(code) __code0:def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        return\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "(code) __code0: The code should start with:\n", "def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "\n", "#Inputs\n", "(Policy) self1=<__main__.Policy object at 0x7f6d1d53d810>\n", "(Policy) self0=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) list0=[['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "\n", "#Others\n", "(NoneType) eval0=None\n", "\n", "#Outputs\n", "(NoneType) eval1=None\n", "\n", "#Feedback\n", "'NoneType' object is not subscriptable\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "  \"reasoning\": \"In this problem, two evaluation functions (`eval0` and `eval1`) are defined using provided code strings (`__code1` and `__code0`). The feedback stating 'NoneType' object is not subscriptable indicates an issue where a `None` object is being treated as if it can be indexed like a list or a dictionary. This issue arises because both `__code1` and `__code0` functions do not return any value, i.e., they implicitly return `None`. When `eval1` expects to receive a 'plan' that can be accessed using indices, it instead receives `None` from `eval0`, which causes the error. To resolve this issue, both `__code1` and `__code0` functions need to be updated to return meaningful values to prevent the subscriptability error.\",\n", "  \"suggestion\": {\n", "    \"__code1\": \"def reason(self, map):\\n        \\\"\\\"\\\"\\n        Given a map, analyze the board in a game.\\n        X denotes hits, O denotes misses, and . denotes unknown positions.\\n        \\\"\\\"\\\"\\n        analysis_result = {'hits': 0, 'misses': 0, 'unknowns': 0}\\n        for row in map:\\n            for cell in row:\\n                if cell == 'X':\\n                    analysis_result['hits'] += 1\\n                elif cell == 'O':\\n                    analysis_result['misses'] += 1\\n                elif cell == '.':\\n                    analysis_result['unknowns'] += 1\\n        return analysis_result\",\n", "    \"__code0\": \"def act(self, map, plan):\\n        \\\"\\\"\\\"\\n        Given a map and a plan, select a target coordinate in a game.\\n        X denotes hits, O denotes misses, and . denotes unknown positions.\\n        \\\"\\\"\\\"\\n        x, y = None, None\\n        for i, row in enumerate(map):\\n            for j, cell in enumerate(row):\\n                if cell == '.' and plan['unknowns'] > 0:  # check for unknown spots as a sample strategy\\n                    x, y = i, j\\n                    break\\n            if x is not None:\\n                break\\n        return (x, y)\"\n", "  }\n", "}\n", "Scores: 0.4788647342995169 (0.01701570386908862)\n", "Iteration 0 returns: [0.5        0.47826087 0.45833333]\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval0 = eval(self=self0, map=map0, __code=__code1)\n", "eval1 = eval(self=self1, map=map1, plan=eval0, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "\n", "#Variables\n", "(code) __code1:def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        analysis_result = {'hits': 0, 'misses': 0, 'unknowns': 0}\n", "        for row in map:\n", "            for cell in row:\n", "                if cell == 'X':\n", "                    analysis_result['hits'] += 1\n", "                elif cell == 'O':\n", "                    analysis_result['misses'] += 1\n", "                elif cell == '.':\n", "                    analysis_result['unknowns'] += 1\n", "        return analysis_result\n", "(code) __code0:def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map and a plan, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        x, y = None, None\n", "        for i, row in enumerate(map):\n", "            for j, cell in enumerate(row):\n", "                if cell == '.' and plan['unknowns'] > 0:  # check for unknown spots as a sample strategy\n", "                    x, y = i, j\n", "                    break\n", "            if x is not None:\n", "                break\n", "        return (x, y)\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "(code) __code0: The code should start with:\n", "def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "\n", "#Inputs\n", "(Policy) self1=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) map1=[['O', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "(Policy) self0=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) map0=[['O', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "\n", "#Others\n", "(dict) eval0={'hits': 0, 'misses': 0, 'unknowns': 25}\n", "\n", "#Outputs\n", "(tuple) eval1=(0, 0)\n", "\n", "#Feedback\n", "Got 0 reward.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "  \"reasoning\": \"The code provided involves two primary functions defined under __code1 and __code0. The first function (__code1) is designed to analyze a game board represented by a map and identify hits ('X'), misses ('O'), and unknown positions ('.'). According to the execution results, this function performs correctly as it counts 24 unknown positions. The second function (__code0) is intended to act based on the map and the analysis provided by __code1 to select a target coordinate on the board, most likely for making a move in the game. It selects the first unknown position on the map it encounters, which is (0,1). However, the 'Got 0 reward' feedback implies that either the target selection was not optimal for receiving a reward or another aspect of the game strategy needs adjustment. The issue seems to revolve around the response strategy to the game's situation rather than the implementation of target selection because the function indeed selected a legitimate unknown position on the board. The decision-making aspect, especially considering strategic placement or selection beyond the first viable option, could be where improvements are needed. Nevertheless, the code explicitly targets the first unknown cell when unknowns are present, which, as per current instructions and the functioning of __code0, is performed as expected. Thus, no changes seem necessary to the selections made by __code0.\",\n", "  \"answer\": \"\",\n", "  \"suggestion\": {}\n", "}\n", "Scores: 0.6344540280467054 (0.15523541800098087)\n", "Iteration 1 returns: [0.84615385 0.47826087 0.57894737]\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval0 = eval(self=self0, map=map0, __code=__code1)\n", "eval1 = eval(self=self1, map=map1, plan=eval0, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "\n", "#Variables\n", "(code) __code1:def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        analysis_result = {'hits': 0, 'misses': 0, 'unknowns': 0}\n", "        for row in map:\n", "            for cell in row:\n", "                if cell == 'X':\n", "                    analysis_result['hits'] += 1\n", "                elif cell == 'O':\n", "                    analysis_result['misses'] += 1\n", "                elif cell == '.':\n", "                    analysis_result['unknowns'] += 1\n", "        return analysis_result\n", "(code) __code0:def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map and a plan, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        x, y = None, None\n", "        for i, row in enumerate(map):\n", "            for j, cell in enumerate(row):\n", "                if cell == '.' and plan['unknowns'] > 0:  # check for unknown spots as a sample strategy\n", "                    x, y = i, j\n", "                    break\n", "            if x is not None:\n", "                break\n", "        return (x, y)\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "(code) __code0: The code should start with:\n", "def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "\n", "#Inputs\n", "(Policy) self1=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) map1=[['O', 'O', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "(Policy) self0=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) map0=[['O', 'O', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "\n", "#Others\n", "(dict) eval0={'hits': 0, 'misses': 1, 'unknowns': 24}\n", "\n", "#Outputs\n", "(tuple) eval1=(0, 1)\n", "\n", "#Feedback\n", "Got 0 reward.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "    \"reasoning\": \"The feedback 'Got 0 reward' implies that the output from the code, which calculates a move, could potentially not advance the player significantly in the game objective. Generally, actions that are significant, such as hitting a target that changes the game state, would elicit a positive reward. Given the code, we evaluate the game map and make decisions based on that evaluation. The function '__code1' is designed to count the hits, misses, and unknown positions on a game board. This evaluation was given in 'Others' and it appears to be correctly implemented with the output '{'hits': 0, 'misses': 1, 'unknowns': 24}'. Therefore, the reasoning part isn't necessarily flawed. However, '__code0' that intends to specify the next action based on the evaluation could be problematic. The current strategy is to select the first unknown position found. In a strategy game, often the placement or choice of next move should be strategic and may not just rely on finding the first available unknown position but perhaps on a more sophisticated decision-making process. Given this, the algorithm lacks a deeper game strategy and changing the logic in '__code0' might lead to better gaming outcomes or rewards.\",\n", "    \"suggestion\": {\n", "        \"__code0\": \"def act(self, map, plan):\\n        \\\"\\\"\\\"\\n        Given a map and a plan, select a target coordinate in a game.\\n        X denotes hits, O denotes misses, and . denotes unknown positions.\\n        This version prioritizes targeting unknown cells near known misses as they are likely next to hits in strategic game scenarios like Battleship.\\n        \\\"\\\"\\\"\\n        best_option = None\\n        best_score = -1\\n\\n        for i, row in enumerate(map):\\n            for j, cell in enumerate(row):\\n                if cell == '.' and (i > 0 and map[i-1][j] == 'O' or\\n                                  i < len(map) - 1 and map[i+1][j] == 'O' or\\n                                  j > 0 and map[i][j-1] == 'O' or\\n                                  j < len(row) - 1 and map[i][j+1] == 'O'):\\n                    score = 1  # increase score based on proximity to a miss which might edge near a hit\\n                    if score > best_score:\\n                        best_score = score\\n                        best_option = (i, j)\\n\\n        if best_option is not None:\\n            return best_option\\n        else:\\n            # fallback to the first unknown if no strategic selections are found\\n            for i, row in enumerate(map):\\n                for j, cell in enumerate(row):\\n                    if cell == '.':\\n                        return (i, j)\\n        return None\"\n", "    }\n", "}\n", "Scores: 0.4888888888888889 (0.04321208107251127)\n", "Iteration 2 returns: [0.45833333 0.55       0.45833333]\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval0 = eval(self=self0, map=map0, __code=__code1)\n", "eval1 = eval(self=self1, map=map1, plan=eval0, __code=__code0)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "\n", "#Variables\n", "(code) __code1:def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "        analysis_result = {'hits': 0, 'misses': 0, 'unknowns': 0}\n", "        for row in map:\n", "            for cell in row:\n", "                if cell == 'X':\n", "                    analysis_result['hits'] += 1\n", "                elif cell == 'O':\n", "                    analysis_result['misses'] += 1\n", "                elif cell == '.':\n", "                    analysis_result['unknowns'] += 1\n", "        return analysis_result\n", "(code) __code0:def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map and a plan, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        This version prioritizes targeting unknown cells near known misses as they are likely next to hits in strategic game scenarios like Battleship.\n", "        \"\"\"\n", "        best_option = None\n", "        best_score = -1\n", "\n", "        for i, row in enumerate(map):\n", "            for j, cell in enumerate(row):\n", "                if cell == '.' and (i > 0 and map[i-1][j] == 'O' or\n", "                                  i < len(map) - 1 and map[i+1][j] == 'O' or\n", "                                  j > 0 and map[i][j-1] == 'O' or\n", "                                  j < len(row) - 1 and map[i][j+1] == 'O'):\n", "                    score = 1  # increase score based on proximity to a miss which might edge near a hit\n", "                    if score > best_score:\n", "                        best_score = score\n", "                        best_option = (i, j)\n", "\n", "        if best_option is not None:\n", "            return best_option\n", "        else:\n", "            # fallback to the first unknown if no strategic selections are found\n", "            for i, row in enumerate(map):\n", "                for j, cell in enumerate(row):\n", "                    if cell == '.':\n", "                        return (i, j)\n", "        return None\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def reason(self, map):\n", "        \"\"\"\n", "        Given a map, analyze the board in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "(code) __code0: The code should start with:\n", "def act(self, map, plan):\n", "        \"\"\"\n", "        Given a map, select a target coordinate in a game.\n", "        X denotes hits, O denotes misses, and . denotes unknown positions.\n", "        \"\"\"\n", "\n", "#Inputs\n", "(Policy) self1=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) map1=[['O', 'O', 'X', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "(Policy) self0=<__main__.Policy object at 0x7f6d1d53d810>\n", "(list) map0=[['O', 'O', 'X', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.'], ['.', '.', '.', '.', '.']]\n", "\n", "#Others\n", "(dict) eval0={'hits': 0, 'misses': 2, 'unknowns': 23}\n", "\n", "#Outputs\n", "(tuple) eval1=(0, 2)\n", "\n", "#Feedback\n", "Got 1 reward.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "    \"reasoning\": \"Looking at the code and the constraints, the __code1 function which is defined for analyzing the map correctly identified only two misses instead of three, and no hits recognized. The __code0 function for acting on the map is supposed to prioritize targeting unknown cells near misses. However, there seems to be no issues with the __code0 as the output (0, 2) correctly attempts to target a cell adjacent to a known miss for likely strategic advantage in games like Battleship, as the 'X' which denotes a hit is nearby. Both functions have constraints on their structure but the definition of how they process elements is not restricted. Thus, the main issue lies in __code1 where the hits count is zero, which implies the loop might be missing cases or not progressing properly through every cell.\",\n", "    \"answer\": null,\n", "    \"suggestion\": {\n", "        \"__code1\": \"def reason(self, map):\\n        \\\"\\\"\\\"\\n        Given a map, analyze the board in a game.\\n        X denotes hits, O denotes misses, and . denotes unknown positions.\\n        \\\"\\\"\\\"\\n        analysis_result = {'hits': 0, 'misses': 0, 'unknowns': 0}\\n        for row in map:\\n            for cell in row:\\n                if cell == 'X':\\n                    analysis_result['hits'] += 1\\n                elif cell == 'O':\\n                    analysis_result['misses'] += 1\\n                elif cell == '.':\\n                    analysis_result['unknowns'] += 1\\n        return analysis_result\"\n", "    }\n", "}\n", "Scores: 0.4788647342995169 (0.01701570386908862)\n", "Iteration 3 returns: [0.47826087 0.5        0.45833333]\n"]}], "source": ["optimizer = OptoPrime(policy.parameters(), memory_size=0)\n", "\n", "\n", "feedback = \"\"\n", "# This is an online optimization problem. we have the opportunity to\n", "# keep changing the function with each round of interaction\n", "board = BattleshipBoard(board_size, board_size, num_each_type=num_each_type, exclude_ships=exclude_ships)\n", "obs = node(board.get_shots())  # init observation\n", "i = 0\n", "while i < 4:\n", "    GRAPH.clear()\n", "\n", "    try:\n", "        output = policy.select_coordinate(obs)\n", "        obs, reward, terminal, feedback = user_fb_for_placing_shot(board, output.data)  # not traced\n", "    except ExecutionError as e:  # this is a retry\n", "        output = e.exception_node\n", "        feedback = output.data\n", "        reward, terminal = 0, False\n", "\n", "    if terminal:\n", "        board = BattleshipBoard(board_size, board_size, num_each_type=num_each_type, exclude_ships=exclude_ships)\n", "        obs = node(board.get_shots())  # init observation\n", "\n", "    # Update\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(output, feedback)\n", "    optimizer.step(verbose=True)\n", "\n", "    # Logging\n", "    if not isinstance(output, ExceptionNode):\n", "        try:\n", "            returns = eval_policy(policy, board_size, num_each_type, exclude_ships, n_eval_episodes)\n", "            print(\"Iteration\", i, \"returns:\", returns)\n", "        except Exception:\n", "            pass\n", "\n", "        i += 1\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["This completes the tutorial on using the Trace package for optimizing multiple codes in an episodic setting. Happy optimizing!"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}