{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Multi-Agent: Negotiation Arena\n", "\n", "## Introduction\n", "\n", "This notebook will guide you through the process of setting up and optimizing prompts for a trading game between two players named <PERSON> and <PERSON>. The goal is to maximize the overall value of resources in both players' inventories.\n", "\n", "## Setup\n", "\n", "First, we'll import the necessary packages and set up our environment."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import necessary libraries\n", "import os\n", "from openai import OpenAI\n", "import json\n", "\n", "import opto.trace as trace\n", "from opto.optimizers import OptoPrime\n", "from autogen import config_list_from_json\n", "\n", "config = config_list_from_json(\"OAI_CONFIG_LIST\")\n", "key = None\n", "for c in config:\n", "    if c['model'] == 'gpt-4-0125-preview':\n", "        key = c['api_key']\n", "        break\n", "if key is None:\n", "    raise Exception(\"No key found for gpt-4-0125-preview in the provided config file\")\n", "\n", "client = OpenAI(api_key=key)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Game Components\n", "\n", "Next, we'll define the nodes and system prompts used in the game."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Define nodes for player names and prompts\n", "p1_name = trace.node('<PERSON>', trainable=False)\n", "p2_name = trace.node('Bob', trainable=False)\n", "p1_prompt = trace.node('STOCKPILE THESE RESOURCES: N/A', trainable=True)\n", "p2_prompt = trace.node('STOCKPILE THESE RESOURCES: N/A', trainable=True)\n", "\n", "# Define system prompt\n", "system_prompt = f\"\"\"\n", "RULES of the TRADING GAME between two players named {p1_name.data} and {p2_name.data}.\n", "\n", "Each player's inventory is private and consists of three resources, WOOD, STONE, and GOLD.\n", "The higher the quantity of a resource a player has, the higher the value of that resource.\n", "The value of a resource is determined by a scale that increases exponentially with quantity.\n", "The goal of the game is to maximize the total value of the resources in all players' inventories (OVERALL SCORE).\n", "\n", "The game is played in turns, with each player taking one action per turn.\n", "Turns alternate between the two players, starting with {p1_name.data}.\n", "Trading is the only way to exchange resources between players.\n", "Players can choose to end the game when they think subsequent trades will be rejected or not beneficial.\n", "\n", "Each player can do one of 4 actions: propose a trade, accept a trade, reject a trade or end the game.\n", "Propose a trade: A proposed trade must barter the same quantity of one resource for another.\n", "A player can only propose a trade if they have sufficient quantity of the resource they are trading away.\n", "Accept a trade: A player can only accept a trade if they have sufficient quantity of the resource that they in turn are trading away.\n", "Reject a trade: A player can reject a trade if they do not have sufficient quantity of the resource, or if it would lead to lower OVERALL SCORE.\n", "End game: If both players select the end game action, the game ends and the overall value of both players' inventories are tallied up to produce the OVERALL SCORE.\n", "NOTE: BOTH players must select the end game action during their respective turns for the game to end.\n", "\n", "Each of the four actions must be formatted as a valid json object that can be parsed by python json.loads:\n", "Example of proposing a trade = {{'action': 'TRADE', 'sell_resource': 'WOOD', 'buy_resource': 'STONE', 'quantity': 5}}\n", "Example of accepting a trade = {{'action': 'ACCEPT'}}\n", "Example of rejecting a trade = {{'action': 'REJECT'}}\n", "Example of ending the game = {{'action': 'END'}}\n", "\"\"\"\n", "\n", "# Initialize game state variables\n", "p1_inventory = {'WOOD': 4, 'STONE': 3, 'GOLD': 2}\n", "p2_inventory = {'WOOD': 1, 'STONE': 5, 'GOLD': 2}\n", "proposed_trade = None\n", "proposed_end = False\n", "conversation = []\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Using bundle to wrap helper functions\n", "\n", "We'll create functions to handle message formatting, parsing, and game actions."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Function to create a message for the LLM\n", "@trace.bundle(trainable=False)\n", "def create_message(player, prompt, previous_message=None):\n", "    global p1_inventory\n", "    global p2_inventory\n", "\n", "    player_prompt = f'In the trading game, you are named {player}.\\n'\n", "    messages = [{'role': 'system', 'content': player_prompt}, {'role': 'system', 'content': prompt}]\n", "    \n", "    current_inventory = p1_inventory if player == \"Alice\" else p2_inventory\n", "    inventory_message = f'Your inventory consists of {current_inventory[\"WOOD\"]} WOOD, {current_inventory[\"STONE\"]} STONE, and {current_inventory[\"GOLD\"]} GOLD.'\n", "    messages.append({'role': 'user', 'content': inventory_message})\n", "\n", "    return messages\n", "\n", "# Function to parse responses\n", "def parse(player, response_json):\n", "    global p1_inventory\n", "    global p2_inventory\n", "\n", "    sell_resource = response_json['sell_resource']\n", "    buy_resource = response_json['buy_resource']\n", "    quantity = response_json['quantity']\n", "    if player == \"Alice\":\n", "        if p1_inventory[sell_resource] < quantity:\n", "            return None\n", "        if p2_inventory[buy_resource] < quantity:\n", "            return None\n", "        return {\"Alice\": {sell_resource: -quantity, buy_resource: quantity}, \n", "                \"Bob\": {sell_resource: quantity, buy_resource: -quantity}}\n", "    else:\n", "        if p2_inventory[sell_resource] < quantity:\n", "            return None\n", "        if p1_inventory[buy_resource] < quantity:\n", "            return None\n", "        return {\"Alice\": {sell_resource: quantity, buy_resource: -quantity}, \n", "                \"Bob\": {sell_resource: -quantity, buy_resource: quantity}}\n", "\n", "# Function to accept a trade\n", "def accept_trade():\n", "    global proposed_trade\n", "    global p1_inventory\n", "    global p2_inventory\n", "\n", "    current_dict = proposed_trade[\"<PERSON>\"]\n", "    for key in current_dict:\n", "        p1_inventory[key] += current_dict[key]\n", "    \n", "    current_dict = proposed_trade[\"<PERSON>\"]\n", "    for key in current_dict:\n", "        p2_inventory[key] += current_dict[key]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Chat Function\n", "\n", "We define a function to handle the chat between the different players."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["@trace.bundle(trainable=False)\n", "def chat(player, message):\n", "    global system_prompt\n", "    global conversation\n", "    global proposed_trade\n", "    global proposed_end\n", "    \n", "    current_message = [{'role': 'system', 'content': system_prompt}] + message\n", "\n", "    if len(conversation) > 0:\n", "        current_message.append({'role': 'user', 'content': 'This is the transcript of the conversation so far.'})\n", "        conversation_history = \"\"\n", "        for i in conversation:\n", "            conversation_history += f'{i[\"role\"]} said: {i[\"content\"]}\\n'\n", "        current_message.append({'role': 'user', 'content': conversation_history})\n", "\n", "    chat = client.chat.completions.create(\n", "            model='gpt-4-0125-preview',\n", "            messages=current_message,\n", "            temperature=0,\n", "            max_tokens=200,\n", "            seed=42,\n", "            response_format={ \"type\": \"json_object\" }\n", "        )\n", "    \n", "    response = chat.choices[0].message.content\n", "    response_json = json.loads(response)\n", "    \n", "    action = response_json['action']\n", "    \n", "    if action == 'END':\n", "        if proposed_end:\n", "            return 'TERMINATE'\n", "        else:\n", "            proposed_end = True\n", "    elif action == 'REJECT':\n", "        proposed_trade = None\n", "        if proposed_end:\n", "            proposed_end = False\n", "    elif action == 'ACCEPT':\n", "        if proposed_trade is not None:\n", "            accept_trade()\n", "        elif proposed_end:\n", "            return 'TERMINATE'\n", "    elif action == 'TRADE':\n", "        proposed_trade = parse(player,response_json)\n", "        if proposed_end:\n", "            proposed_end = False\n", "    \n", "    return response\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define the end_game function\n", "\n", "This function calculates the final score based on the players' inventories."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def end_game():\n", "    global p1_inventory\n", "    global p2_inventory\n", "    \n", "    value_scale = [1, 2, 4, 7, 12, 20, 33, 54, 88, 143, 250]\n", "\n", "    p1_value = 0\n", "    if p1_inventory['WOOD'] > 0:\n", "        p1_value += value_scale[p1_inventory['WOOD']-1 if p1_inventory['WOOD'] <= 11 else 10]\n", "    if p1_inventory['STONE'] > 0:\n", "        p1_value += value_scale[p1_inventory['STONE']-1 if p1_inventory['STONE'] <= 11 else 10]\n", "    if p1_inventory['GOLD'] > 0:\n", "        p1_value += value_scale[p1_inventory['GOLD']-1 if p1_inventory['GOLD'] <= 11 else 10]\n", "\n", "    p2_value = 0\n", "    if p2_inventory['WOOD'] > 0:\n", "        p2_value += value_scale[p2_inventory['WOOD']-1 if p2_inventory['WOOD'] <= 11 else 10]\n", "    if p2_inventory['STONE'] > 0:\n", "        p2_value += value_scale[p2_inventory['STONE']-1 if p2_inventory['STONE'] <= 11 else 10]\n", "    if p2_inventory['GOLD'] > 0:\n", "        p2_value += value_scale[p2_inventory['GOLD']-1 if p2_inventory['GOLD'] <= 11 else 10]\n", "\n", "    return p1_value + p2_value, p1_value, p2_value\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Optimize Prompts\n", "\n", "Finally, we use the optimizer to find better prompts for the players over multiple iterations."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ITERATION 1\n", "Alice STOCKPILE THESE RESOURCES: N/A\n", "Bob STOCKPILE THESE RESOURCES: N/A\n", "The game has ended. <PERSON> has inventory with value of 13 and <PERSON> has inventory with value of 15.\n", "OVERALL SCORE: 28\n", "OVERALL SCORE is less than optimal. Find better trades to increase the OVERALL SCORE.\n", "ITERATION 2\n", "Alice STOCKPILE THESE RESOURCES: GOLD\n", "<PERSON> STOCKPILE THESE RESOURCES: STONE\n", "The game has ended. <PERSON> has inventory with value of 13 and <PERSON> has inventory with value of 22.\n", "OVERALL SCORE: 35\n", "OVERALL SCORE is less than optimal. Find better trades to increase the OVERALL SCORE.\n", "ITERATION 3\n", "Alice STOCKPILE THESE RESOURCES: WOOD\n", "<PERSON> STOCKPILE THESE RESOURCES: GOLD\n", "The game has ended. <PERSON> has inventory with value of 13 and <PERSON> has inventory with value of 22.\n", "OVERALL SCORE: 35\n", "OVERALL SCORE is less than optimal. Find better trades to increase the OVERALL SCORE.\n", "ITERATION 4\n", "Alice GOLD\n", "<PERSON>\n", "The game has ended. <PERSON> has inventory with value of 13 and <PERSON> has inventory with value of 22.\n", "OVERALL SCORE: 35\n", "OVERALL SCORE is less than optimal. Find better trades to increase the OVERALL SCORE.\n", "ITERATION 5\n", "Alice STONE\n", "<PERSON>\n", "The game has ended. <PERSON> has inventory with value of 13 and <PERSON> has inventory with value of 15.\n", "OVERALL SCORE: 28\n", "OVERALL SCORE is less than optimal. Find better trades to increase the OVERALL SCORE.\n"]}], "source": ["# Initialize optimizer\n", "optimizer = OptoPrime(\n", "                [p1_prompt, p2_prompt], memory_size=0, config_list=config_list_from_json(\"OAI_CONFIG_LIST\")\n", "            )\n", "\n", "# Run optimization loop\n", "for i in range(5):\n", "    p1_inventory = {'WOOD': 4, 'STONE': 3, 'GOLD': 2}\n", "    p2_inventory = {'WOOD': 1, 'STONE': 5, 'GOLD': 2}\n", "    proposed_trade = None\n", "    proposed_end = False\n", "    conversation = []\n", "\n", "    current_message = None\n", "    current_player = p2_name\n", "    while (current_message is None) or (current_message.data != 'TERMINATE'):\n", "        current_player = p1_name if current_player == p2_name else p2_name\n", "        current_prompt = p1_prompt if current_player == p1_name else p2_prompt\n", "        message_prompt = create_message(current_player, current_prompt, current_message)\n", "        current_message = chat(current_player, message_prompt)\n", "        if current_message.data != 'TERMINATE':\n", "            conversation.append({'role': current_player.data, 'content': current_message.data})\n", "        \n", "    result_value, p1_value, p2_value = end_game()\n", "    feedback = 'The game has ended. ' + \\\n", "                p1_name.data + f' has inventory with value of {p1_value} and ' + \\\n", "                p2_name.data + f' has inventory with value of {p2_value}.\\n'\n", "    feedback += 'OVERALL SCORE: ' + str(result_value)\n", "    if result_value < 73:\n", "        feedback += '\\nOVERALL SCORE is less than optimal. Find better trades to increase the OVERALL SCORE.'\n", "\n", "    print(\"ITERATION\", i+1)\n", "    print(p1_name.data, p1_prompt.data)\n", "    print(p2_name.data, p2_prompt.data)\n", "    print(feedback)\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(current_message, feedback, visualize=False)\n", "    optimizer.step(verbose=False)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, you can run each cell in this notebook step by step to walk through the process of setting up and optimizing prompts for the trading game. Happy optimizing!"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}