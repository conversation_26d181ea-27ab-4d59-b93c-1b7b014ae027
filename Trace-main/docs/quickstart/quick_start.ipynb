{"cells": [{"cell_type": "markdown", "id": "a035458a05292e04", "metadata": {"collapsed": false}, "source": ["# ⚡️ First: 5-Minute Basics\n", "\n", "Writing a Trace program involves three simple steps.\n", "\n", "1. Write a normal python program.\n", "2. Decorate the program with Trace primitives: `node` and `@bundle`.\n", "3. Use a Trace-graph-compatible optimizer to optimize the program.\n", "\n", "In this short tutorial, we will look at a simple example of how Trace allows code optimization on runnable Python code.\n", "Then, we show how you can use <PERSON> to create a **code generation and verifier** framework by alternating between optimizing the code and the unit tests with a dozen lines of code.\n"]}, {"cell_type": "code", "execution_count": null, "id": "91f226d14015a1ec", "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install trace-opt"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import os\n", "import ipywidgets as widgets\n", "from IPython.display import display\n", "\n", "# Function to save the environment variable and API key\n", "def save_env_variable(env_name, api_key):\n", "    # Validate inputs\n", "    if not env_name.strip():\n", "        print(\"⚠️ Environment variable name cannot be empty.\")\n", "        return\n", "    if not api_key.strip():\n", "        print(\"⚠️ API key cannot be empty.\")\n", "        return\n", "    \n", "    # Store the API key as an environment variable\n", "    os.environ[env_name] = api_key\n", "    globals()[env_name] = api_key  # Set it as a global variable\n", "    print(f\"✅ API key has been set for environment variable: {env_name}\")\n", "\n", "# Create the input widgets\n", "env_name_input = widgets.Text(\n", "    value=\"OPENAI_API_KEY\",  # Default value\n", "    description=\"Env Name:\",\n", "    placeholder=\"Enter env variable name (e.g., MY_API_KEY)\",\n", ")\n", "\n", "api_key_input = widgets.Password(\n", "    description=\"API Key:\",\n", "    placeholder=\"Enter your API key\",\n", ")\n", "\n", "# Create the button to submit the inputs\n", "submit_button = widgets.Button(description=\"Set API Key\")\n", "\n", "# Display the widgets\n", "display(env_name_input, api_key_input, submit_button)\n", "\n", "# Callback function for the button click\n", "def on_button_click(b):\n", "    env_name = env_name_input.value\n", "    api_key = api_key_input.value\n", "    save_env_variable(env_name, api_key)\n", "\n", "# Attach the callback to the button\n", "submit_button.on_click(on_button_click)"], "id": "150ebe0c019eb767"}, {"cell_type": "markdown", "id": "1c6eec8b500e7966", "metadata": {"collapsed": false}, "source": ["## Start with a Normal Python Program\n", "\n", "To illustrate the fundamental difference between Trace and many other LLM libraries, we can look at a coding problem from OpenAI's Human-Eval dataset. "]}, {"cell_type": "code", "execution_count": 2, "id": "b36585d6-e814-4753-aa7b-eea4956fac9f", "metadata": {}, "outputs": [], "source": ["def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst"]}, {"cell_type": "code", "execution_count": 3, "id": "1cab71b5-13f5-4395-9944-84f204c12fb2", "metadata": {}, "outputs": [{"data": {"text/plain": ["[1, 2, 3, 4]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["strange_sort_list([1, 2, 3, 4])"]}, {"cell_type": "markdown", "id": "96778239-7346-4482-a07a-30b92d513f2a", "metadata": {}, "source": ["Our first attempt is not very successful -- we tried one of the example input and the output is not correct. Can we leverage an LLM to help us automatically figure out the right solution?\n", "\n", "## Trace this Program\n", "\n", "We use decorators like `@bundle` to wrap over Python functions. A **bundled** function behaves like any other Python functions."]}, {"cell_type": "code", "execution_count": 4, "id": "e9d5cfa1-209e-4cea-b832-f12e9f4eb324", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (eval:0, dtype=<class 'list'>, data=[1, 2, 3, 4])\n"]}], "source": ["from opto.trace import node, bundle\n", "\n", "@bundle(trainable=True)\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "test_input = [1, 2, 3, 4]\n", "test_output = strange_sort_list(test_input)\n", "print(test_output)"]}, {"cell_type": "markdown", "id": "e507886c-7bfa-4ae1-af99-8814adcf4123", "metadata": {}, "source": ["Note that even though `strange_sort_list` is now bundled, it still executes like a normal Python function. The returned value however, is a `MessageNode`. Trace automatically converts input to `Node` and start tracing operations. We can see the output is still the same as we had before."]}, {"cell_type": "code", "execution_count": 5, "id": "22dd3048-1ac8-41f3-8290-18afee21081c", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"701pt\" height=\"305pt\"\n", " viewBox=\"0.00 0.00 701.04 304.86\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 300.86)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-300.86 697.04,-300.86 697.04,4 -4,4\"/>\n", "<!-- eval0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>eval0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"449.54\" cy=\"-148.43\" rx=\"144.5\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">eval0</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[eval] This operator eval...</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2, 3, 4]</text>\n", "</g>\n", "<!-- eq0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>eq0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"296.54\" cy=\"-37.48\" rx=\"143.59\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"296.54\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">eq0</text>\n", "<text text-anchor=\"middle\" x=\"296.54\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[eq] This is an eq operat...</text>\n", "<text text-anchor=\"middle\" x=\"296.54\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">False</text>\n", "</g>\n", "<!-- eval0&#45;&gt;eq0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval0&#45;&gt;eq0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M401.13,-112.96C385.99,-102.17 369.14,-90.18 353.47,-79.01\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"355.08,-75.86 344.9,-72.91 351.02,-81.56 355.08,-75.86\"/>\n", "</g>\n", "<!-- list0 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>list0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"143.54\" cy=\"-148.43\" rx=\"143.59\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"143.54\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">list0</text>\n", "<text text-anchor=\"middle\" x=\"143.54\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in ...</text>\n", "<text text-anchor=\"middle\" x=\"143.54\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 4, 2, 3]</text>\n", "</g>\n", "<!-- list0&#45;&gt;eq0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>list0&#45;&gt;eq0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M191.95,-112.96C207.1,-102.17 223.94,-90.18 239.62,-79.01\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"242.07,-81.56 248.19,-72.91 238.01,-75.86 242.07,-81.56\"/>\n", "</g>\n", "<!-- lst0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>lst0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"314.54\" cy=\"-259.38\" rx=\"143.59\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"314.54\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">lst0</text>\n", "<text text-anchor=\"middle\" x=\"314.54\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in ...</text>\n", "<text text-anchor=\"middle\" x=\"314.54\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2, 3, 4]</text>\n", "</g>\n", "<!-- lst0&#45;&gt;eval0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>lst0&#45;&gt;eval0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M357.63,-223.61C370.66,-213.09 385.09,-201.45 398.58,-190.56\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"400.82,-193.25 406.4,-184.25 396.42,-187.8 400.82,-193.25\"/>\n", "</g>\n", "<!-- __code0 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>__code0</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"693.04,-285.88 476.04,-285.88 476.04,-232.88 693.04,-232.88 693.04,-285.88\"/>\n", "<text text-anchor=\"middle\" x=\"584.54\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">__code0</text>\n", "<text text-anchor=\"middle\" x=\"584.54\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[ParameterNode] This is a...</text>\n", "<text text-anchor=\"middle\" x=\"584.54\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">def strange_sort_list(lst...</text>\n", "</g>\n", "<!-- __code0&#45;&gt;eval0 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>__code0&#45;&gt;eval0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M552.89,-232.84C537.35,-220.29 518.23,-204.86 500.68,-190.7\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"502.73,-187.86 492.75,-184.3 498.33,-193.3 502.73,-187.86\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f9a8055c7c0>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["correctness = test_output.eq([1, 4, 2, 3])\n", "correctness.backward(\"test failed\", visualize=True, print_limit=25)"]}, {"cell_type": "markdown", "id": "75424461-8780-4900-8cf4-7cef22cde76e", "metadata": {}, "source": ["We are not just tracing how the program is run -- we are tracing how we verified the correctness of the output too, all without any manual intervention. Our design philosophy is to support un-interrupted Python programming.\n", "\n", "```{note}\n", "You may have noticed that we used `test_output.eq([1, 4, 2, 3])` instead of the more conventional `test_output == [1, 4, 2, 3]`. \n", "You can try it! Actually, both statements will execute without an error. However, `test_output == [1, 4, 2, 3]` returns a Python boolean object `False`, while `test_output.eq([1, 4, 2, 3])` returns a Trace `MessageNode`. We have taken great effor to make Node objects work just like any Python objects, but there are some corner cases to watch out for.\n", "```"]}, {"cell_type": "markdown", "id": "05e708d4-8139-46f1-8cec-02f5206510e6", "metadata": {}, "source": ["## Optimize with <PERSON><PERSON><PERSON>\n", "\n", "We can use Trace and the optimizer defined in `opto.optimizers` to learn a program that can pass this test! But first, we need to provide some signal to the optimizer. We call this signal **feedback**. The more intelligent the optimizer is, the less guidance we need to give in the feedback. Think of this as us providing instructions to help LLM-based optimizers to come up with better solutions.\n", "\n", "```{note}\n", "You might be thinking -- `correctness` is already a boolean variable and has a name `correctness`. Why do we need to explicitly provide a feedback string to the optimizer? This is because just by `False` or `True` -- it is hard to know whether the test has passed or not. We could be testing on a positive or a negative target. Therefore, it is important to pass in an explicit feedback.\n", "```\n", "\n", "```{tip}\n", "What do we have to write without <PERSON>? If you are using a ChatGPT/Bard/Claude interface, you will copy paste the code into the interface, ask it to write code for you. Copy/paste the code back into a code editor that has Python runtime. Execute the Python code. Compare the output with the ground truth, and then write in the chat interface `test case failed!`. Trace simplified all of these steps.\n", "```"]}, {"cell_type": "code", "execution_count": 6, "id": "d38793a1-03ba-4897-b9f2-aee40ece76ed", "metadata": {}, "outputs": [], "source": ["def get_feedback(predict, target):\n", "    if predict == target:\n", "        return \"test case passed!\"\n", "    else:\n", "        return \"test case failed!\""]}, {"cell_type": "markdown", "id": "5936bb57-b0f1-419a-a1a4-9d3bcd1f5c23", "metadata": {}, "source": ["The code below looks like any PyTorch code. We can break it down to a few steps:\n", "1. We first import an optimizer from `opto.optimizers`.\n", "2. The bundled function `strange_sort_list` has a convenience method `.parameters()` for us to grab all the trainable parameters in it. In this case, the trainable parameter is just the function code itself.\n", "3. We need to perform `backward` pass on a `MessageNode` object, which is `correctness`. We also need to pass in the `feedback` into the optimizer as well.\n", "4. We call `optimizer.step()` to perform the actual optimization"]}, {"cell_type": "code", "execution_count": 7, "id": "81d783e1-462f-4938-9f2c-389b4b546a74", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Epoch 0\n", "Training Epoch 1\n"]}], "source": ["import autogen\n", "from opto.optimizers import OptoPrime\n", "from opto import trace\n", "\n", "test_ground_truth = [1, 4, 2, 3]\n", "test_input = [1, 2, 3, 4]\n", "\n", "epoch = 2\n", "\n", "optimizer = OptoPrime(strange_sort_list.parameters())\n", "\n", "for i in range(epoch):\n", "    print(f\"Training Epoch {i}\")\n", "    try:\n", "        test_output = strange_sort_list(test_input)\n", "        feedback = get_feedback(test_output, test_ground_truth)\n", "    except trace.ExecutionError as e:\n", "        feedback = e.exception_node.data\n", "        test_output = e.exception_node\n", "    \n", "    correctness = test_output.eq(test_ground_truth)\n", "    \n", "    if correctness:\n", "        break\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(correctness, feedback)\n", "    optimizer.step()"]}, {"cell_type": "markdown", "id": "6a9e33d7-9cba-4ce5-bd5d-05e3c8c5c671", "metadata": {}, "source": ["We can visualize what the optimizer is doing. <PERSON> first constructs a tuple `(g0, f0)`, which corresponds to `(Trace graph, feedback)`. Trace graph is a representation of what happened in the execution. Feedback is provided by the `get_feedback` function we defined above. \n", "\n", "The LLM-based optimizer is then asked to generate a rationale/reasoning `r1`, and then an improvement `a1` is proposed.\n", "\n", "```{note}\n", "Trace graph contains a `#Code` section. However, this does not look like the `strange_sort_list` function we wrote. The actual function is in the `#Variables` section. This is because we chose to use a specific optimizer `OptoPrime`, which frames the optimization problem as a code \"debugging\" problem, and uses the `#Code` section to represent the computation flow.\n", "```"]}, {"cell_type": "code", "execution_count": 8, "id": "84bf4993-074b-4853-b8fe-2520ee24a48d", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin-bottom: 10px;\">\n", "            <!-- First set of blocks -->\n", "    \n", "            <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "                <div style=\"flex-grow: 1; background-color: #E0E0E0; border: 2px solid #9E9E9E; padding: 10px; border-radius: 5px; width: 550px;\">\n", "                    <p><b><PERSON></b></p><pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word;\">\n", "\n", "#Code\n", "eval1 = eval(lst=lst1, __code=__code0)\n", "eq1 = eq(x=eval1, y=list1)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[eq] This is an eq operator of x and y. .\n", "\n", "#Variables\n", "(code) __code0:def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "#Constraints\n", "(code) __code0: The code should start with:\n", "def strange_sort_list(lst):\n", "\n", "#Inputs\n", "(list) lst1=[]\n", "(list) list1=[1, 4, 2, 3]\n", "\n", "#Outputs\n", "(bool) eq1=False\n", "\n", "</pre>\n", "                </div>\n", "                <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #9E9E9E;\">\n", "                    g<sub>0</sub>\n", "                </div>\n", "            </div>\n", "        \n", "            <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "                <div style=\"flex-grow: 1; background-color: #FFB3BA; border: 2px solid #FF6B6B; padding: 10px; border-radius: 5px;\">\n", "                    <p style=\"margin: 0;\"><b>Feedback: </b>test case failed!</p>\n", "                </div>\n", "                <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #FF6B6B;\">\n", "                    f<sub>0</sub>\n", "                </div>\n", "            </div>\n", "\n", "            <div style=\"display: flex; align-items: stretch; margin-bottom: 10px;\">\n", "                <div style=\"flex-grow: 1; background-color: #BAFFC9; border: 2px solid #4CAF50; padding: 10px; border-radius: 5px; width: 550px;\">\n", "                    <p style=\"margin: 0;\"><b>Reasoning: </b>The objective in the code is to utilize a function defined in __code0, which is supposed to perform a 'strange sort' on a given list. The 'strange_sort_list' should alternate between the minimum and maximum elements of the list. The expectation is that the function will convert the input 'lst1' which is [1, 2, 3, 4] to a list sorted in a 'strange' order as [1, 4, 2, 3] as per the definition in the code documentation linked to __code0. However, the current implementation of the function in __code0 simply sorts the list in ascending order and returns it, as indicated by the output list [1, 2, 3, 4], which does not match what the 'strange_sort_list' function is supposed to implement. This discordance has led to the 'eq1' result being False as the expected [1, 4, 2, 3] does not equal [1, 2, 3, 4].</p>\n", "                </div>\n", "                <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #4CAF50;\">\n", "                    r<sub>1</sub>\n", "                </div>\n", "            </div>\n", "        \n", "                <div style=\"display: flex; align-items: stretch; margin-bottom: 20px;\">\n", "                <div style=\"flex-grow: 1; background-color: 'white'; border: 2px solid #4D9DE0; padding: 10px; border-radius: 5px;\">\n", "                    <p><b>Improvement</b></p>\n", "                    <pre style=\"margin: 0; white-space: pre-wrap; word-wrap: break-word; font-family: monospace; background-color: 'white';\">__code0:\n", "\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    result = []\n", "    ascending = True\n", "    while lst:\n", "        if ascending:\n", "            value = min(lst)\n", "            lst.remove(value)\n", "        else:\n", "            value = max(lst)\n", "            lst.remove(value)\n", "        result.append(value)\n", "        ascending = not ascending\n", "    return result\n", "\n", "</pre>\n", "                </div>\n", "                <div style=\"width: 40px; display: flex; align-items: center; justify-content: center; font-size: 24px; color: #4D9DE0;\">\n", "                    a<sub>1</sub>\n", "                </div>\n", "            </div>\n", "        </div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from opto.trace.utils import render_opt_step\n", "\n", "render_opt_step(0, optimizer)"]}, {"cell_type": "markdown", "id": "15cae6bf-abe7-4931-ab7d-412aa7ec4cac", "metadata": {}, "source": ["```{note}\n", "We can examine what the learned function look like by printing out its content below. Calling `.data` on a node returns the wrapped data inside the node.\n", "```"]}, {"cell_type": "code", "execution_count": 9, "id": "b036523a-d440-49e4-abb9-91970ffc68ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Examples:\n", "    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n", "    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n", "    strange_sort_list([]) == []\n", "    '''\n", "    result = []\n", "    ascending = True\n", "    while lst:\n", "        if ascending:\n", "            value = min(lst)\n", "            lst.remove(value)\n", "        else:\n", "            value = max(lst)\n", "            lst.remove(value)\n", "        result.append(value)\n", "        ascending = not ascending\n", "    return result\n"]}], "source": ["print(strange_sort_list.parameters()[0].data)"]}, {"cell_type": "markdown", "id": "d336635f-24c9-4342-a5c1-65447d941ce5", "metadata": {}, "source": ["## The Optimized Function is Runnable\n", "\n", "You might wonder -- oh, is this function actually changed? Yes it is. In fact, we can pass in a different test example and see if it worked."]}, {"cell_type": "code", "execution_count": 10, "id": "723e1b08-3031-4158-947f-862c596f09d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (eval:3, dtype=<class 'list'>, data=[2, 5, 3, 5])\n"]}], "source": ["new_test_input = [5, 3, 2, 5]\n", "new_test_output = strange_sort_list(new_test_input)\n", "print(new_test_output)"]}, {"cell_type": "markdown", "id": "afd8d1b8-7d88-40c3-b1eb-6abd85edfad7", "metadata": {}, "source": ["**Now the answer is correct!** (alternating numbers between min, max, min, max!)"]}, {"cell_type": "markdown", "id": "a58756c0-c740-4d8e-850b-4c14a5f80486", "metadata": {}, "source": ["## Save and Load the Optimized Function\n", "\n", "In fact, just like PyTorch, you can easily save and load this function too. We will discuss more details in the next section."]}, {"cell_type": "code", "execution_count": 11, "id": "0da4d986-1f78-4a77-b9ec-ade9edc2191b", "metadata": {}, "outputs": [], "source": ["strange_sort_list.save(\"strange_sort_list.pkl\")"]}, {"cell_type": "markdown", "id": "d02e52cb-9d69-4bf3-b67e-d0bcf7019366", "metadata": {}, "source": ["## Coder-Verifier Framework using Trace\n", "\n", "The functionalities provided by <PERSON> seem simple and straightforward right now. But using these as basic building blocks, we can construct LLM-based workflow that is complex and mind-blowing. \n", "\n", "Earlier, we used a unit test provided by the dataset. What if we don't have any unit test, or if we wonder the tests are exhaustive or not? <PERSON> takes an optimization perspective that can allow you to write multiple functions -- we can write a function that proposes unit tests for another function. Let's call this function a `verifier`.\n", "\n", "```{tip}\n", "<PERSON> does not force users to wrap Python functions into a string in order to send to an optimizer. This allows natural Python programming flow. <PERSON> manages the function body under the hood.\n", "```\n", "\n", "```{note}\n", "Note that we removed the examples in the function docstring below!\n", "```"]}, {"cell_type": "code", "execution_count": 12, "id": "de225077-2fc7-42a1-be8e-ecf87a1d2277", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (eval:1, dtype=<class 'list'>, data=[1, 2, 3])\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"907pt\" height=\"535pt\"\n", " viewBox=\"0.00 0.00 907.09 534.81\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 530.81)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-530.81 903.09,-530.81 903.09,4 -4,4\"/>\n", "<!-- eval1 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>eval1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"449.54\" cy=\"-148.43\" rx=\"144.5\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">eval1</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[eval] This operator eval...</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2, 3]</text>\n", "</g>\n", "<!-- eq0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>eq0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"535.54\" cy=\"-37.48\" rx=\"143.59\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"535.54\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">eq0</text>\n", "<text text-anchor=\"middle\" x=\"535.54\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[eq] This is an eq operat...</text>\n", "<text text-anchor=\"middle\" x=\"535.54\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">True</text>\n", "</g>\n", "<!-- eval1&#45;&gt;eq0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>eval1&#45;&gt;eq0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M477.94,-111.45C485.28,-102.16 493.26,-92.04 500.88,-82.39\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"503.73,-84.43 507.17,-74.42 498.23,-80.1 503.73,-84.43\"/>\n", "</g>\n", "<!-- getitem1 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>getitem1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"716.54\" cy=\"-259.38\" rx=\"140.01\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"716.54\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">getitem1</text>\n", "<text text-anchor=\"middle\" x=\"716.54\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[getitem] This is a getit...</text>\n", "<text text-anchor=\"middle\" x=\"716.54\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2, 3]</text>\n", "</g>\n", "<!-- getitem1&#45;&gt;eq0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>getitem1&#45;&gt;eq0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M690.22,-222.54C667.64,-192.25 633.89,-148 602.54,-110.95 594.33,-101.25 585.26,-91.09 576.5,-81.54\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"578.93,-79.01 569.58,-74.04 573.79,-83.76 578.93,-79.01\"/>\n", "</g>\n", "<!-- getitem0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>getitem0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"182.54\" cy=\"-259.38\" rx=\"140.01\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"182.54\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">getitem0</text>\n", "<text text-anchor=\"middle\" x=\"182.54\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[getitem] This is a getit...</text>\n", "<text text-anchor=\"middle\" x=\"182.54\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2, 3]</text>\n", "</g>\n", "<!-- getitem0&#45;&gt;eval1 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>getitem0&#45;&gt;eval1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M257.63,-227.74C290.76,-214.22 329.87,-198.26 364.11,-184.29\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"365.7,-187.43 373.63,-180.41 363.05,-180.94 365.7,-187.43\"/>\n", "</g>\n", "<!-- __code0 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>__code0</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"558.04,-285.88 341.04,-285.88 341.04,-232.88 558.04,-232.88 558.04,-285.88\"/>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">__code0</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[ParameterNode] This is a...</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">def strange_sort_list(lst...</text>\n", "</g>\n", "<!-- __code0&#45;&gt;eval1 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>__code0&#45;&gt;eval1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M449.54,-232.84C449.54,-221.93 449.54,-208.84 449.54,-196.3\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"453.04,-195.95 449.54,-185.95 446.04,-195.95 453.04,-195.95\"/>\n", "</g>\n", "<!-- eval0 -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>eval0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"449.54\" cy=\"-370.34\" rx=\"144.5\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-381.64\" font-family=\"Times,serif\" font-size=\"14.00\">eval0</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-366.64\" font-family=\"Times,serif\" font-size=\"14.00\">[eval] This operator eval...</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-351.64\" font-family=\"Times,serif\" font-size=\"14.00\">([1, 2, 3], [1, 2, 3])</text>\n", "</g>\n", "<!-- eval0&#45;&gt;getitem1 -->\n", "<g id=\"edge7\" class=\"edge\">\n", "<title>eval0&#45;&gt;getitem1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M525.34,-338.41C558.46,-324.89 597.44,-308.98 631.54,-295.07\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"633.08,-298.22 641.02,-291.2 630.44,-291.74 633.08,-298.22\"/>\n", "</g>\n", "<!-- eval0&#45;&gt;getitem0 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>eval0&#45;&gt;getitem0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M373.75,-338.41C340.63,-324.89 301.64,-308.98 267.55,-295.07\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"268.65,-291.74 258.07,-291.2 266,-298.22 268.65,-291.74\"/>\n", "</g>\n", "<!-- int0 -->\n", "<g id=\"node7\" class=\"node\">\n", "<title>int0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"143.54\" cy=\"-370.34\" rx=\"143.59\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"143.54\" y=\"-381.64\" font-family=\"Times,serif\" font-size=\"14.00\">int0</text>\n", "<text text-anchor=\"middle\" x=\"143.54\" y=\"-366.64\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in ...</text>\n", "<text text-anchor=\"middle\" x=\"143.54\" y=\"-351.64\" font-family=\"Times,serif\" font-size=\"14.00\">0</text>\n", "</g>\n", "<!-- int0&#45;&gt;getitem0 -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>int0&#45;&gt;getitem0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M156.64,-332.75C159.63,-324.4 162.84,-315.41 165.96,-306.71\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"169.34,-307.65 169.42,-297.06 162.75,-305.29 169.34,-307.65\"/>\n", "</g>\n", "<!-- int1 -->\n", "<g id=\"node8\" class=\"node\">\n", "<title>int1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"755.54\" cy=\"-370.34\" rx=\"143.59\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"755.54\" y=\"-381.64\" font-family=\"Times,serif\" font-size=\"14.00\">int1</text>\n", "<text text-anchor=\"middle\" x=\"755.54\" y=\"-366.64\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in ...</text>\n", "<text text-anchor=\"middle\" x=\"755.54\" y=\"-351.64\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- int1&#45;&gt;getitem1 -->\n", "<g id=\"edge8\" class=\"edge\">\n", "<title>int1&#45;&gt;getitem1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M742.45,-332.75C739.46,-324.4 736.24,-315.41 733.13,-306.71\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"736.33,-305.29 729.67,-297.06 729.74,-307.65 736.33,-305.29\"/>\n", "</g>\n", "<!-- __code1 -->\n", "<g id=\"node9\" class=\"node\">\n", "<title>__code1</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"558.04,-526.81 341.04,-526.81 341.04,-443.81 558.04,-443.81 558.04,-526.81\"/>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-511.61\" font-family=\"Times,serif\" font-size=\"14.00\">__code1</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-496.61\" font-family=\"Times,serif\" font-size=\"14.00\">[ParameterNode] This is a...</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-481.61\" font-family=\"Times,serif\" font-size=\"14.00\">def verifier():</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-466.61\" font-family=\"Times,serif\" font-size=\"14.00\"> &#160;&#160;&#160;&quot;&quot;&quot;</text>\n", "<text text-anchor=\"middle\" x=\"449.54\" y=\"-451.61\" font-family=\"Times,serif\" font-size=\"14.00\"> ...</text>\n", "</g>\n", "<!-- __code1&#45;&gt;eval0 -->\n", "<g id=\"edge9\" class=\"edge\">\n", "<title>__code1&#45;&gt;eval0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M449.54,-443.53C449.54,-435.26 449.54,-426.5 449.54,-418.05\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"453.04,-417.94 449.54,-407.94 446.04,-417.94 453.04,-417.94\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f9a4e048880>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from opto.trace import node, bundle, GRAPH\n", "GRAPH.clear()\n", "\n", "@bundle(trainable=True)\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "    '''\n", "    return sorted(lst)\n", "\n", "@bundle(trainable=True)\n", "def verifier():\n", "    \"\"\"\n", "    For a coding problem described below:\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Return a test input and an expected output to verify if the function is implemented correctly.\n", "    \"\"\"\n", "    test_input = [1,2,3]\n", "    expected_output = [1,2,3]\n", "    return test_input, expected_output\n", "\n", "test_input, expected_output =  verifier()\n", "test_output = strange_sort_list(test_input)\n", "print(test_output)\n", "verifier_passed = test_output.eq(expected_output)\n", "verifier_passed.backward(\"synthetic test result\", visualize=True, print_limit=25)"]}, {"cell_type": "markdown", "id": "61e2290f-2a72-4511-bb0e-7ad49b85e7a0", "metadata": {}, "source": ["Wow. It looks like we passed the synthetic test! Encouraging! Except, the proposed unit test is wrong. This is when the optimization perspective becomes important. Both the unit test function and the code writing function need to be optimized. \n", "\n"]}, {"cell_type": "markdown", "id": "eb8e98b8-ce83-4190-ab2b-3d887a8f44cc", "metadata": {}, "source": ["Let's come up with a game that can help the code function to write better code.\n", "\n", "Given a pair of ground truth input and expected output, we define the following procedure:\n", "\n", "1. Create two functions: `coder` and `verifier`.\n", "2. `coder` will keep **optimizing** its code unless the `verifier` agrees to let it pass.\n", "3. Now the `coder` will take the ground truth input and be checked against ground truth output.\n", "4. If the `coder` fails, `verifier` will **optimize** its internal tests.\n", "\n", "```{tip}\n", "In the Trace framework, feedback is similar to how we understand `loss` or `metric` in classical machine learning. We need to use it to guide the optimizer to change the the function's behavior.\n", "```"]}, {"cell_type": "code", "execution_count": 13, "id": "db1ea71c-9a4e-4e14-9584-b19bff6ae1ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Epoch 0\n", "Code function update\n", "Verifier update\n", "Training Epoch 1\n", "Code function update\n", "Code function update\n", "Code function update\n", "Code function update\n"]}], "source": ["import autogen\n", "from opto.optimizers import OptoPrime\n", "\n", "GRAPH.clear()\n", "\n", "test_ground_truth = [1, 4, 2, 3]\n", "test_ground_truth_input = [1, 2, 3, 4]\n", "\n", "epoch = 2\n", "\n", "code_optimizer = OptoPrime(strange_sort_list.parameters(),\n", "                          ignore_extraction_error=True)\n", "verifier_optimizer = OptoPrime(verifier.parameters(),\n", "                              ignore_extraction_error=True)\n", "\n", "for i in range(epoch):\n", "    print(f\"Training Epoch {i}\")\n", "\n", "    # Step 2: Coder optimizes till it passes the verifier\n", "    verifier_passed = False\n", "    while not verifier_passed:\n", "        print(f\"Code function update\")\n", "        try:\n", "            test_input, expected_output =  verifier()\n", "            test_output = strange_sort_list(test_input)\n", "            # note that we use the same feedback function as before\n", "            feedback = get_feedback(test_output.data, expected_output.data)\n", "        except trace.ExecutionError as e:\n", "            feedback = e.exception_node.data\n", "            test_output = e.exception_node\n", "            expected_output = None\n", "        verifier_passed = test_output.eq(expected_output)\n", "        \n", "        code_optimizer.zero_feedback()\n", "        code_optimizer.backward(verifier_passed, feedback, retain_graph=True)\n", "        code_optimizer.step()\n", "\n", "    # Step 3: Coder is checked by ground truth\n", "    try:\n", "        test_output = strange_sort_list(test_ground_truth_input)\n", "        feedback = get_feedback(test_output, test_ground_truth)\n", "    except trace.ExecutionError as e:\n", "        feedback = e.exception_node.data\n", "        test_output = e.exception_node\n", "    correctness = test_output.eq(test_ground_truth)\n", "    \n", "    if correctness:\n", "        break\n", "\n", "    # Step 4: If the Coder fails, Verifier needs to propose better unit tests\n", "    print(f\"Verifier update\")\n", "    feedback = \"Verifier proposed wrong test_input, expected_output that do not validate the implementation of the function.\"\n", "    verifier_optimizer.zero_feedback()\n", "    verifier_optimizer.backward(verifier_passed, feedback)\n", "    verifier_optimizer.step()"]}, {"cell_type": "code", "execution_count": 14, "id": "60e74ff3-56d5-4a98-a770-ebf60214e174", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterNode: (__code:0, dtype=<class 'str'>, data=def strange_sort_list(lst):\n", "    result = []\n", "    min_index = 0\n", "    max_index = len(lst) - 1\n", "    sorted_lst = sorted(lst)\n", "    while min_index <= max_index:\n", "        result.append(sorted_lst[min_index])\n", "        min_index += 1\n", "        if min_index <= max_index:\n", "            result.append(sorted_lst[max_index])\n", "            max_index -= 1\n", "    return result)\n"]}], "source": ["print(strange_sort_list.parameters()[0])"]}, {"cell_type": "code", "execution_count": 15, "id": "8a07b65f-3fc0-4368-8c19-a6cb985ef9e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterNode: (__code:1, dtype=<class 'str'>, data=def verifier():\n", "    \"\"\"\n", "    For a coding problem described below:\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "\n", "    Return a test input and an expected output to verify if the function is implemented correctly.\n", "    \"\"\"\n", "    test_input = [3, 1, 2]\n", "    expected_output = [1, 3, 2]\n", "    return test_input, expected_output)\n"]}], "source": ["print(verifier.parameters()[0])"]}, {"cell_type": "markdown", "id": "ec75ffbf-956f-430e-b28d-62ab9218b35e", "metadata": {}, "source": ["```{note}\n", "`[3, 1, 2]` came up by the verifier is not in the function docstring examples.\n", "```\n", "\n", "## What's Next?\n", "\n", "We can see that the verifier can come up with good examples to test if the code function is correct, and the code function itself does not need ground-truth answers to update. All of these can be achieved within the optimization framework of Trace.\n", "\n", "This is not the end of what <PERSON> can do. Next, we will demonstrate how we can write a Python **class** object that can be optimized by <PERSON> using what we learned here. "]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}