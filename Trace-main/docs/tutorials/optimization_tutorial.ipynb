{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Optimization\n", "\n", "Here we show a small example of how to apply `trace` to optimize python objects based on language feedback. Here we want to change the input to function `foobar` such that output is large enough. `foobar` is a function that is composed of `foo` based on built-in operators and `bar` which is a blackbox function, whose information is only given via the docstring.  \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install trace-opt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import opto\n", "from opto.trace import bundle, node\n", "from opto.optimizers import OptoPrime\n", "from opto.trace.nodes import GRAPH\n", "\n", "\n", "def blackbox(x):\n", "    return -x * 2\n", "\n", "\n", "@bundle()\n", "def bar(x):\n", "    \"This is a test function, which does negative scaling.\"\n", "    return blackbox(x)\n", "\n", "\n", "def foo(x):\n", "    y = x + 1\n", "    return x * y\n", "\n", "\n", "# foobar is a composition of custom function and built-in functions\n", "\n", "\n", "def foobar(x):\n", "    return foo(bar(x))\n", "\n", "\n", "def user(x):\n", "    if x < 50:\n", "        return \"The number needs to be larger.\"\n", "    else:\n", "        return \"Success.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Backpropagation \n", "\n", "We apply `FunctionOptimizer` to change the input to the function `foobar` such that the simulated user is satisfied. To this end, we backpropagated the user's language feedback about the output, through the graph that connects the input to the output.\n", "\n", "We use helper functions from [AutoGen](https://github.com/microsoft/autogen) to call LLMs to interpret the user's language feedback. Before running the cell below, please copy `OAI_CONFIG_LIST_sample` from the root folder of this repository to the current folder, rename it to `OAI_CONFIG_LIST`, and set the correct configuration for LLMs in there."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"1134pt\" height=\"394pt\"\n", " viewBox=\"0.00 0.00 1134.37 393.86\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 389.86)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-389.86 1130.37,-389.86 1130.37,4 -4,4\"/>\n", "<!-- bar0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>bar0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"823.73\" cy=\"-259.38\" rx=\"302.78\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"823.73\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">bar0</text>\n", "<text text-anchor=\"middle\" x=\"823.73\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[bar] This is a test function, which does negative scaling..</text>\n", "<text text-anchor=\"middle\" x=\"823.73\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">2.0</text>\n", "</g>\n", "<!-- multiply0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>multiply0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"700.73\" cy=\"-37.48\" rx=\"258.19\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"700.73\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">multiply0</text>\n", "<text text-anchor=\"middle\" x=\"700.73\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[multiply] This is a multiply operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"700.73\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">6.0</text>\n", "</g>\n", "<!-- bar0&#45;&gt;multiply0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>bar0&#45;&gt;multiply0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M826.94,-221.76C827.96,-190.18 825.07,-144.49 804.73,-110.95 797.61,-99.22 787.79,-88.9 777.01,-80\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"779.05,-77.16 769.01,-73.78 774.75,-82.68 779.05,-77.16\"/>\n", "</g>\n", "<!-- add0 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>add0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"577.73\" cy=\"-148.43\" rx=\"217.58\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"577.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">add0</text>\n", "<text text-anchor=\"middle\" x=\"577.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[add] This is an add operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"577.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">3.0</text>\n", "</g>\n", "<!-- bar0&#45;&gt;add0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>bar0&#45;&gt;add0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M744.2,-223.16C718.7,-211.86 690.33,-199.3 664.34,-187.79\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"665.45,-184.45 654.89,-183.6 662.61,-190.85 665.45,-184.45\"/>\n", "</g>\n", "<!-- add0&#45;&gt;multiply0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>add0&#45;&gt;multiply0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M618.35,-111.45C629.15,-101.88 640.93,-91.45 652.11,-81.55\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"654.57,-84.04 659.74,-74.79 649.93,-78.8 654.57,-84.04\"/>\n", "</g>\n", "<!-- int0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>int0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"251.73\" cy=\"-259.38\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">int0</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- int0&#45;&gt;add0 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>int0&#45;&gt;add0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M351.76,-224.95C389.37,-212.38 432.27,-198.04 470.52,-185.26\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"471.76,-188.54 480.13,-182.05 469.54,-181.9 471.76,-188.54\"/>\n", "</g>\n", "<!-- float0 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>float0</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"1078.23,-385.86 569.23,-385.86 569.23,-332.86 1078.23,-332.86 1078.23,-385.86\"/>\n", "<text text-anchor=\"middle\" x=\"823.73\" y=\"-370.66\" font-family=\"Times,serif\" font-size=\"14.00\">float0</text>\n", "<text text-anchor=\"middle\" x=\"823.73\" y=\"-355.66\" font-family=\"Times,serif\" font-size=\"14.00\">[ParameterNode] This is a ParameterNode in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"823.73\" y=\"-340.66\" font-family=\"Times,serif\" font-size=\"14.00\">&#45;1.0</text>\n", "</g>\n", "<!-- float0&#45;&gt;bar0 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>float0&#45;&gt;bar0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M823.73,-332.62C823.73,-324.79 823.73,-315.94 823.73,-307.18\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"827.23,-307.01 823.73,-297.01 820.23,-307.01 827.23,-307.01\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f94010ba320>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import autogen\n", "\n", "# One-step optimization example\n", "x = node(-1.0, trainable=True)\n", "optimizer = OptoPrime([x])\n", "output = foobar(x)\n", "feedback = user(output.data)\n", "optimizer.zero_feedback()\n", "optimizer.backward(output, feedback, visualize=True)  # this is equivalent to the line below\n", "# output.backward(feedback, propagator=optimizer.propagator, visualize=visualize)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The propagated feedback contains graph structure, data of the nodes in the graph, and the transformation used in the graph. They are presented in a python-like syntax."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Function Feedback\n", "Graph:\n", "  1: bar0 = bar(x=float0)\n", "  2: add0 = add(x=bar0, y=int0)\n", "  3: multiply0 = multiply(x=bar0, y=add0)\n", "Roots:\n", "  float0: (-1.0, None)\n", "  int0: (1, None)\n", "Others:\n", "  bar0: (2.0, None)\n", "  add0: (3.0, None)\n", "Documentation:\n", "  bar: [bar] This is a test function, which does negative scaling..\n", "  add: [add] This is an add operator of x and y. .\n", "  multiply: [multiply] This is a multiply operator of x and y. .\n", "Output:\n", "  multiply0: (6.0, None)\n", "User Feedback:\n", "  The number needs to be larger.\n"]}], "source": ["from opto.optimizers.optoprime import node_to_function_feedback\n", "\n", "print(\"Function Feedback\")\n", "for k, v in x.feedback.items():\n", "    v = v[0]\n", "    f_feedback = node_to_function_feedback(v)\n", "    print(\"Graph:\")\n", "    for kk, vv in f_feedback.graph:\n", "        print(f\"  {kk}: {vv}\")\n", "    print(\"Roots:\")\n", "    for kk, vv in f_feedback.roots.items():\n", "        print(f\"  {kk}: {vv}\")\n", "    print(\"Others:\")\n", "    for kk, vv in f_feedback.others.items():\n", "        print(f\"  {kk}: {vv}\")\n", "    print(\"Documentation:\")\n", "    for kk, vv in f_feedback.documentation.items():\n", "        print(f\"  {kk}: {vv}\")\n", "    print(\"Output:\")\n", "    for kk, vv in f_feedback.output.items():\n", "        print(f\"  {kk}: {vv}\")\n", "    print(\"User Feedback:\")\n", "    print(f\"  {f_feedback.user_feedback}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once the feedback is propagated, we can call the optimizer to change the variable based on the feedback."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "bar0 = bar(x=float0)\n", "add0 = add(x=bar0, y=int0)\n", "multiply0 = multiply(x=bar0, y=add0)\n", "\n", "#Documentation\n", "[bar] This is a test function, which does negative scaling..\n", "[add] This is an add operator of x and y. .\n", "[multiply] This is a multiply operator of x and y. .\n", "\n", "#Variables\n", "(float) float0=-1.0\n", "\n", "#Constraints\n", "\n", "\n", "#Inputs\n", "(int) int0=1\n", "\n", "#Others\n", "(float) bar0=2.0\n", "(float) add0=3.0\n", "\n", "#Outputs\n", "(float) multiply0=6.0\n", "\n", "#Feedback\n", "The number needs to be larger.\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "    \"reasoning\": \"From the provided code, the procedures are as follows:\\n1. A function 'bar' performs a transformation on 'float0' and returns 'bar0'.\\n2. A function 'add' sums 'bar0' with 'int0' resulting in 'add0'.\\n3. Finally, 'multiply' takes the products of 'bar0' and 'add0' resulting in 'multiply0'.\\nGiven the feedback to make the output ('multiply0') larger, we need to analyze how changes in 'float0' impact 'bar0' and accordingly 'add0' and 'multiply0'. The exact operation performed by 'bar' is not explicitly stated, but given that the output is positive when the input is negative, it suggests a negation or similar transformation with a scaling. Adjusting 'float0' to a more negative value might lead to an equivalence in 'bar0' being more positive if the function is a simple negation or -x transformation. Increasing this magnitude should, in turn, increase 'add0' and therefore 'multiply0'. Changing 'float0' from -1.0 to a more negative value such as -2.0 would likely achieve a larger result for 'multiply0'.\",\n", "    \"suggestion\": {\n", "        \"float0\": -2.0\n", "    }\n", "}\n", "\n", "After step\n", "old variable -1.0\n", "new variable -2.0\n"]}], "source": ["old_variable = x.data\n", "optimizer.step(verbose=True)\n", "\n", "print(\"\\nAfter step\")\n", "print(\"old variable\", old_variable)\n", "print(\"new variable\", x.data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example of Full Optimization Loop\n", "\n", "We can apply the steps above repeatedly to create a training loop to optimize the variable according to the user. Notice because of the way `foobar` works, the optimizer actually needs to change the input to be lower in order to make the output to be larger (which is what the user suggests). \n", "\n", "This is a non-trivial problem, becasue the optimizer sees only\n", "\n", "```\n", "output = blackbox(x) * (blackbox(x)+1)\n", "```\n", "\n", "and the hint/docstring `\"This is a test function, which does scaling and negation.\"` about how `blackbox` works. The optimizer needs to figure out how to change the input based on this vague information.\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["variable=-1.0, output=6.0, feedback=The number needs to be larger.\n", "variable=-2.0, output=20.0, feedback=The number needs to be larger.\n", "variable=-1.0, output=6.0, feedback=The number needs to be larger.\n", "variable=-2.0, output=20.0, feedback=The number needs to be larger.\n", "variable=-4.0, output=72.0, feedback=Success.\n", "History\n", "  0: -1.0\n", "  1: -2.0\n", "  2: -1.0\n", "  3: -2.0\n", "  4: -4.0\n", "  5: -4.0\n"]}], "source": ["# A small example of how to use the optimizer in a loop\n", "GRAPH.clear()\n", "x = node(-1.0, trainable=True)\n", "optimizer = OptoPrime([x])\n", "\n", "history = [x.data]\n", "feedback = \"\"\n", "while feedback.lower() != \"Success.\".lower():\n", "    output = foobar(x)\n", "    feedback = user(output.data)\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(output, feedback)\n", "    print(f\"variable={x.data}, output={output.data}, feedback={feedback}\")  # logging\n", "    optimizer.step()\n", "    history.append(x.data)  # logging\n", "\n", "print(\"History\")\n", "for i, v in enumerate(history):\n", "    print(f\"  {i}: {v}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Adding constraints\n", "\n", "We can add constraints to parameter nodes to guide the optimizer. In this small example, the constraint info helps save one optimization step."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["variable=-1.0, output=6.0, feedback=The number needs to be larger.\n", "variable=3.0, output=30.0, feedback=The number needs to be larger.\n", "variable=4.0, output=56.0, feedback=Success.\n", "History\n", "  0: -1.0\n", "  1: 3.0\n", "  2: 4.0\n", "  3: 4.0\n"]}], "source": ["# A small example of how to include constraints on parameters\n", "GRAPH.clear()\n", "x = node(-1.0, trainable=True, constraint=\"The value should be greater than 2.0\")\n", "optimizer = OptoPrime([x])\n", "\n", "history = [x.data]\n", "feedback = \"\"\n", "while feedback.lower() != \"Success.\".lower():\n", "    output = foobar(x)\n", "    feedback = user(output.data)\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(output, feedback)\n", "    print(f\"variable={x.data}, output={output.data}, feedback={feedback}\")  # logging\n", "    optimizer.step()\n", "    history.append(x.data)  # logging\n", "\n", "print(\"History\")\n", "for i, v in enumerate(history):\n", "    print(f\"  {i}: {v}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example of optimizing strings\n", "\n", "Below is a similar example, except the variable is written in text and is converted by a poor converter to numbers before inputting to `foo` and `bar`."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["@bundle()\n", "def convert_english_to_numbers(x):\n", "    \"\"\"This is a function that converts English to numbers. This function has limited ability.\"\"\"\n", "    # remove speical characters, like, \", &, etc.\n", "    x = x.replace('\"', \"\")\n", "    try:  # Convert string to integer\n", "        return int(x)\n", "    except:\n", "        pass\n", "    # Convert intergers written in Engligsh in [-10, 10] to numbers\n", "    if x == \"negative ten\":\n", "        return -10\n", "    if x == \"negative nine\":\n", "        return -9\n", "    if x == \"negative eight\":\n", "        return -8\n", "    if x == \"negative seven\":\n", "        return -7\n", "    if x == \"negative six\":\n", "        return -6\n", "    if x == \"negative five\":\n", "        return -5\n", "    if x == \"negative four\":\n", "        return -4\n", "    if x == \"negative three\":\n", "        return -3\n", "    if x == \"negative two\":\n", "        return -2\n", "    if x == \"negative one\":\n", "        return -1\n", "    if x == \"zero\":\n", "        return 0\n", "    if x == \"one\":\n", "        return 1\n", "    if x == \"two\":\n", "        return 2\n", "    if x == \"three\":\n", "        return 3\n", "    if x == \"four\":\n", "        return 4\n", "    if x == \"five\":\n", "        return 5\n", "    if x == \"six\":\n", "        return 6\n", "    if x == \"seven\":\n", "        return 7\n", "    if x == \"eight\":\n", "        return 8\n", "    if x == \"nine\":\n", "        return 9\n", "    if x == \"ten\":\n", "        return 10\n", "    return \"FAIL\"\n", "\n", "\n", "def user(x):\n", "    if x == \"FAIL\":\n", "        return \"The text cannot be converted to a number.\"\n", "    if x < 50:\n", "        return \"The number needs to be larger.\"\n", "    else:\n", "        return \"Success.\"\n", "\n", "\n", "def foobar_text(x):\n", "    output = convert_english_to_numbers(x)\n", "    if output.data == \"FAIL\":  # This is not traced\n", "        return output\n", "    else:\n", "        return foo(bar(output))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["variable=negative point one, output=FAIL, feedback=The text cannot be converted to a number.\n", "variable=minus one tenth, output=FAIL, feedback=The text cannot be converted to a number.\n", "variable=-0.1, output=FAIL, feedback=The text cannot be converted to a number.\n", "variable=one, output=2, feedback=The number needs to be larger.\n", "variable=three, output=30, feedback=The number needs to be larger.\n", "variable=four, output=56, feedback=Success.\n", "History\n", "  0: negative point one\n", "  1: minus one tenth\n", "  2: -0.1\n", "  3: one\n", "  4: three\n", "  5: four\n", "  6: four\n"]}], "source": ["GRAPH.clear()\n", "x = node(\"negative point one\", trainable=True)\n", "optimizer = OptoPrime([x])\n", "\n", "history = [x.data]\n", "feedback = \"\"\n", "while feedback.lower() != \"Success.\".lower():\n", "    output = foobar_text(x)\n", "    feedback = user(output.data)\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(output, feedback)\n", "    print(f\"variable={x.data}, output={output.data}, feedback={feedback}\")  # logging\n", "    optimizer.step()\n", "    history.append(x.data)  # logging\n", "\n", "print(\"History\")\n", "for i, v in enumerate(history):\n", "    print(f\"  {i}: {v}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example of optimizing functions\n", "\n", "We can use `trace` to optimize python function code directly. This can be achieved by setting `trainable=True` when decorating a custom function by `@bundle`. This would create a `ParameterNode` in the operator, which can be accessed by the `parameter` attribute of the decorated function. It can be used like any other parameters and sent to the optimizer."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["output=2, feedback=Try again. The output should be negative, variables=\n", "\n", "__code:0 def my_fun(x):\n", "    \"\"\"Test function\"\"\"\n", "    return x**2 + 1\n", "output=0, feedback=Try again. The output should be negative, variables=\n", "\n", "__code:0 def my_fun(x):\n", "    \"\"\"Test function\"\"\"\n", "    return x**3 + 1\n", "output=-3, feedback=Success., variables=\n", "\n", "__code:0 def my_fun(x):\n", "    \"\"\"Test function\"\"\"\n", "    return x**3 - 2\n"]}], "source": ["GRAPH.clear()\n", "\n", "\n", "def user(output):\n", "    if output < 0:\n", "        return \"Success.\"\n", "    else:\n", "        return \"Try again. The output should be negative\"\n", "\n", "\n", "# We make this function as a parameter that can be optimized.\n", "\n", "\n", "@bundle(trainable=True)\n", "def my_fun(x):\n", "    \"\"\"Test function\"\"\"\n", "    return x**2 + 1\n", "\n", "\n", "x = node(-1, trainable=False)\n", "optimizer = OptoPrime([my_fun.parameter])\n", "\n", "feedback = \"\"\n", "while feedback != \"Success.\":\n", "    output = my_fun(x)\n", "    feedback = user(output.data)\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(output, feedback)\n", "\n", "    print(f\"output={output.data}, feedback={feedback}, variables=\\n\")  # logging\n", "    for p in optimizer.parameters:\n", "        print(p.name, p.data)\n", "    optimizer.step(verbose=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Example of hyper-parameter optimization for ML models\n", "\n", "We can use `trace` to optimize the hyper-parameters of a machine learning model using language feedbacks. This example requires `scikit-learn`. Before running the cell below, please ensure that it is installed using:\n", "\n", "    pip install scikit-learn"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install scikit-learn\n", "%pip install pandas"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from sklearn.datasets import fetch_openml\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.utils import check_random_state\n", "import numpy as np\n", "\n", "\n", "train_samples = 10000\n", "X, y = fetch_openml(\"mnist_784\", version=1, return_X_y=True, as_frame=False)\n", "\n", "random_state = check_random_state(0)\n", "permutation = random_state.permutation(X.shape[0])\n", "X = X[permutation]\n", "y = y[permutation]\n", "X = X.reshape((X.shape[0], -1))\n", "\n", "X_train, X_validation, y_train, y_validation = train_test_split(X, y, train_size=train_samples, test_size=20000)\n", "\n", "scaler = StandardScaler()\n", "X_train = scaler.fit_transform(X_train)\n", "X_validation = scaler.transform(X_validation)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The language feedback consists of a text representation of the validation accuracy:"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["def scorer(classifier, guess, history):\n", "    score = classifier.score(X_validation, y_validation) * 100\n", "    sparsity = np.mean(classifier.coef_ == 0) * 100\n", "    return_feedback = f\"\\nScore is the accuracy of the classifier on the validation set, and should be maximized.\"\n", "    return_feedback += f\"\\nSparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.\"\n", "    return_feedback += f\"By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\"\n", "    return_feedback += f\"\\n\\nMost recent guess: \\nRegularization Parameter: {guess:.4f}, Score: {score:.2f}%, Sparsity: {sparsity:.2f}%\"\n", "    if len(history) > 0:\n", "        return_feedback += f\"\\n\\nHistory of guesses:\"\n", "        for item in history:\n", "            return_feedback += (\n", "                f\"\\nRegularization Parameter: {item[0]:.4f}, Score: {item[1]:.2f}%, Sparsity: {item[2]:.2f}%\"\n", "            )\n", "    return return_feedback, score, sparsity\n", "\n", "\n", "@bundle(trainable=False)\n", "def train_classifier(regularization_parameter):\n", "    \"\"\"regularization_parameter is a positive number that controls the sparsity of the classifier. Lower values will increase sparsity, and higher values will decrease sparsity.\"\"\"\n", "    classifier = LogisticRegression(C=regularization_parameter, penalty=\"l1\", solver=\"saga\", tol=0.1)\n", "    classifier.fit(X_train, y_train)\n", "    return classifier"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["variable=0.005, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "variable=0.01, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "variable=0.05, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "variable=0.07, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "variable=0.1, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.1000, Score: 86.95%, Sparsity: 24.81%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "variable=0.075, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0750, Score: 86.92%, Sparsity: 27.07%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "Regularization Parameter: 0.1000, Score: 86.95%, Sparsity: 24.81%\n", "variable=0.07, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0700, Score: 86.86%, Sparsity: 24.62%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "Regularization Parameter: 0.1000, Score: 86.95%, Sparsity: 24.81%\n", "Regularization Parameter: 0.0750, Score: 86.92%, Sparsity: 27.07%\n", "variable=0.065, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0650, Score: 86.97%, Sparsity: 28.20%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "Regularization Parameter: 0.1000, Score: 86.95%, Sparsity: 24.81%\n", "Regularization Parameter: 0.0750, Score: 86.92%, Sparsity: 27.07%\n", "Regularization Parameter: 0.0700, Score: 86.86%, Sparsity: 24.62%\n", "variable=0.07, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0700, Score: 86.94%, Sparsity: 27.12%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "Regularization Parameter: 0.1000, Score: 86.95%, Sparsity: 24.81%\n", "Regularization Parameter: 0.0750, Score: 86.92%, Sparsity: 27.07%\n", "Regularization Parameter: 0.0700, Score: 86.86%, Sparsity: 24.62%\n", "Regularization Parameter: 0.0650, Score: 86.97%, Sparsity: 28.20%\n", "variable=0.072, feedback=\n", "Score is the accuracy of the classifier on the validation set, and should be maximized.\n", "Sparsity is the percentage of zero coefficients in the classifier. If the classifier is overfit, a higher sparsity will yield a better score. If the classifier is underfit however, a lower sparsity will yield a better score.By lowering the regularization parameter (must always be positive), the sparsity will increase. By increasing the regularization parameter, the sparsity will decrease.\n", "\n", "Most recent guess: \n", "Regularization Parameter: 0.0720, Score: 86.88%, Sparsity: 28.07%\n", "\n", "History of guesses:\n", "Regularization Parameter: 0.0050, Score: 83.17%, Sparsity: 76.88%\n", "Regularization Parameter: 0.0100, Score: 85.51%, Sparsity: 64.73%\n", "Regularization Parameter: 0.0500, Score: 86.84%, Sparsity: 28.30%\n", "Regularization Parameter: 0.0700, Score: 87.00%, Sparsity: 27.95%\n", "Regularization Parameter: 0.1000, Score: 86.95%, Sparsity: 24.81%\n", "Regularization Parameter: 0.0750, Score: 86.92%, Sparsity: 27.07%\n", "Regularization Parameter: 0.0700, Score: 86.86%, Sparsity: 24.62%\n", "Regularization Parameter: 0.0650, Score: 86.97%, Sparsity: 28.20%\n", "Regularization Parameter: 0.0700, Score: 86.94%, Sparsity: 27.12%\n", "Best regularization parameter: 0.07\n", "Best score: 87.005\n"]}], "source": ["x = node(0.005, trainable=True)\n", "optimizer = OptoPrime([x])\n", "\n", "history = []\n", "bestScore = None\n", "bestRegularization = None\n", "for i in range(10):\n", "    classifier = train_classifier(x)\n", "    fb, score, sparsity = scorer(classifier.data, x.data, history)\n", "    history.append((x.data, score, sparsity))\n", "    print(f\"variable={x.data}, feedback={fb}\")  # logging\n", "    if bestScore is None or score > bestScore:\n", "        bestScore = score\n", "        bestRegularization = x.data\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(classifier, fb)\n", "    optimizer.step()\n", "\n", "print(\"Best regularization parameter:\", bestRegularization)\n", "print(\"Best score:\", bestScore)"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}