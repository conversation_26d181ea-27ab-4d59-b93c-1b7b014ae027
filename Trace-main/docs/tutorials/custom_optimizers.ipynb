{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Building Custom Optimizer\n", "\n", "We give a tutorial on how to build custom optimizers in Trace. We will demonstrate how the classical back-propagation and gradient descent algorithms can be implemented in Trace as an optimizer. We will show two ways to do this. The first is through implementing the back-propagation algorithm within the Trace optimzier, which operates on Trace graph. The second is to overload the propagator to propagate gradeints directly in Trace, instead of Trace graph. This example shows the flexibilty of the Trace framework."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic back-propagation and gradient descent with PyTorch\n", "\n", "To start, let's define a simple objective and run vanilla gradient descent to optimize the variable in pytorch. This code will be used as the reference of desired behaviors. We make the code below transparent for tutorial purppose, so we use the `torch.autograd.grad` api and write down the gradient descent update rule manually."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install trace-opt\n", "%pip install torch"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vanilla gradient descent implementation using PyTorch\n", "  Loss at iter 0: 1.5\n", "  Loss at iter 1: 1.1200000047683716\n", "  Loss at iter 2: 0.8122000098228455\n", "  Loss at iter 3: 0.5628820061683655\n", "  Loss at iter 4: 0.36093443632125854\n", "  Loss at iter 5: 0.19735687971115112\n", "  Loss at iter 6: 0.0648590698838234\n", "  Loss at iter 7: 0.04434824362397194\n", "  Loss at iter 8: 0.06279093772172928\n", "  Loss at iter 9: 0.046178679913282394\n"]}], "source": ["import torch\n", "\n", "stepsize = 0.1\n", "print('Vanilla gradient descent implementation using PyTorch')\n", "param  = torch.tensor(1.0, requires_grad=True)  # this is the param we optimize\n", "def forward():\n", "    x = param\n", "    return torch.abs(x) + torch.square(x) * torch.tensor(0.5, requires_grad=True)\n", "for i in range(10):\n", "    y = forward()\n", "    g = torch.autograd.grad(y, [param], torch.tensor(1.0))\n", "    param = param - stepsize * g[0]\n", "    print(f'  Loss at iter {i}: {y.data}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Set up the objective in Trace\n", "\n", "After seeing how ideally basic gradient descent + back-propagation behaves, next we show how it can be implemented it in Trace. To this end, we need to turn each math ops used in the above loss as a `bundle`, and define the parameter as a `node`. In this way, <PERSON> can create a computational graph (DAG) of the workflow of computing the objective. We visualize the DAG below."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"1179pt\" height=\"394pt\"\n", " viewBox=\"0.00 0.00 1179.50 393.86\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 389.86)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-389.86 1175.5,-389.86 1175.5,4 -4,4\"/>\n", "<!-- abs0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>abs0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"205.77\" cy=\"-148.43\" rx=\"205.54\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"205.77\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">abs0</text>\n", "<text text-anchor=\"middle\" x=\"205.77\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[abs] .</text>\n", "<text text-anchor=\"middle\" x=\"205.77\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">tensor(1., grad_fn=&lt;AbsBackward0&gt;)</text>\n", "</g>\n", "<!-- add0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>add0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"446.77\" cy=\"-37.48\" rx=\"232.78\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"446.77\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">add0</text>\n", "<text text-anchor=\"middle\" x=\"446.77\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[add] This is an add operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"446.77\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">tensor(1.5000, grad_fn=&lt;AddBackward0&gt;)</text>\n", "</g>\n", "<!-- abs0&#45;&gt;add0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>abs0&#45;&gt;add0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M281.03,-113.4C306.48,-101.9 335.08,-88.97 361.27,-77.13\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"362.75,-80.3 370.42,-72.99 359.86,-73.92 362.75,-80.3\"/>\n", "</g>\n", "<!-- multiply0 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>multiply0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"687.77\" cy=\"-148.43\" rx=\"258.19\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"687.77\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">multiply0</text>\n", "<text text-anchor=\"middle\" x=\"687.77\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[multiply] This is a multiply operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"687.77\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">tensor(0.5000, grad_fn=&lt;MulBackward0&gt;)</text>\n", "</g>\n", "<!-- multiply0&#45;&gt;add0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>multiply0&#45;&gt;add0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M610.52,-112.51C585.63,-101.25 557.9,-88.72 532.44,-77.21\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"533.72,-73.95 523.17,-73.02 530.84,-80.32 533.72,-73.95\"/>\n", "</g>\n", "<!-- square0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>square0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"441.77\" cy=\"-259.38\" rx=\"207.78\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"441.77\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">square0</text>\n", "<text text-anchor=\"middle\" x=\"441.77\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[square] .</text>\n", "<text text-anchor=\"middle\" x=\"441.77\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">tensor(1., grad_fn=&lt;PowBackward0&gt;)</text>\n", "</g>\n", "<!-- square0&#45;&gt;multiply0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>square0&#45;&gt;multiply0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M518.26,-224.51C544.12,-213.05 573.2,-200.17 599.88,-188.36\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"601.48,-191.48 609.21,-184.23 598.64,-185.07 601.48,-191.48\"/>\n", "</g>\n", "<!-- Tensor1 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>Tensor1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"919.77\" cy=\"-259.38\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"919.77\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">Tensor1</text>\n", "<text text-anchor=\"middle\" x=\"919.77\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"919.77\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">tensor(0.5000, requires_grad=True)</text>\n", "</g>\n", "<!-- Tensor1&#45;&gt;multiply0 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>Tensor1&#45;&gt;multiply0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M845.4,-223.46C821.78,-212.37 795.5,-200.03 771.28,-188.65\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"772.65,-185.43 762.11,-184.34 769.67,-191.76 772.65,-185.43\"/>\n", "</g>\n", "<!-- Tensor0 -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>Tensor0</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"578.27,-385.86 69.27,-385.86 69.27,-332.86 578.27,-332.86 578.27,-385.86\"/>\n", "<text text-anchor=\"middle\" x=\"323.77\" y=\"-370.66\" font-family=\"Times,serif\" font-size=\"14.00\">Tensor0</text>\n", "<text text-anchor=\"middle\" x=\"323.77\" y=\"-355.66\" font-family=\"Times,serif\" font-size=\"14.00\">[ParameterNode] This is a ParameterNode in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"323.77\" y=\"-340.66\" font-family=\"Times,serif\" font-size=\"14.00\">tensor(1., requires_grad=True)</text>\n", "</g>\n", "<!-- Tensor0&#45;&gt;abs0 -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>Tensor0&#45;&gt;abs0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M264.01,-332.86C248.94,-323.65 234.35,-311.75 224.77,-296.86 205.64,-267.12 201.55,-226.98 201.87,-196.1\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"205.37,-196.1 202.13,-186.01 198.37,-195.91 205.37,-196.1\"/>\n", "</g>\n", "<!-- Tensor0&#45;&gt;square0 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>Tensor0&#45;&gt;square0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M354.77,-332.62C365.75,-323.5 378.41,-312.99 390.62,-302.85\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"393.09,-305.35 398.55,-296.27 388.62,-299.96 393.09,-305.35\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7fd99dcc3f70>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from opto.trace import bundle, node\n", "from opto.trace.propagators.propagators import Propagator\n", "\n", "@bundle()\n", "def abs(x):\n", "    return torch.abs(x)\n", "\n", "@bundle()\n", "def square(x):\n", "    return torch.square(x)\n", "\n", "param  = node(torch.tensor(1.0, requires_grad=True), trainable=True)\n", "def forward():\n", "    x = param\n", "    return abs(x) + square(x) * torch.tensor(0.5, requires_grad=True)\n", "\n", "forward().backward(visualize=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Version 1 Trace Implementation based on Optimizer\n", "\n", "The first way is to implement the back-propagation algorithm as part of the optimizer in Trace. By default, optimzers in Trace receive the propagated Trace graph at the parameter nodes. Trace graph is a generalization of gradient. Here we show how we can implement back-propagation on the Trace graph to recover the propagated gradient and use it for gradient descent. We can see the loss sequence here matches what we had above implemented by PyTorch.\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Version 1 gradient descent implementation using Trace\n", "  Loss at iter 0: 1.5\n", "  Loss at iter 1: 1.1200000047683716\n", "  Loss at iter 2: 0.8122000098228455\n", "  Loss at iter 3: 0.5628820061683655\n", "  Loss at iter 4: 0.36093443632125854\n", "  Loss at iter 5: 0.19735687971115112\n", "  Loss at iter 6: 0.0648590698838234\n", "  Loss at iter 7: 0.04434824362397194\n", "  Loss at iter 8: 0.06279093772172928\n", "  Loss at iter 9: 0.046178679913282394\n"]}], "source": ["from opto.optimizers.optimizer import Optimizer\n", "from collections import defaultdict\n", "\n", "\n", "class BackPropagationGradientDescent(Optimizer):\n", "\n", "    def __init__(self, parameters, stepsize, *args, **kwargs):\n", "        super().__init__(parameters, *args, **kwargs)\n", "        self.stepsize = stepsize\n", "\n", "    def _step(self, *args, **kwargs):\n", "        \"\"\"Return the new data of parameter nodes based on the feedback.\"\"\"\n", "        trace_graph = self.trace_graph   # aggregate the trace graphes into one.\n", "        grads = defaultdict(lambda: torch.tensor(0.0))\n", "        # trace_graph.graph is a list of nodes sorted according to the topological order\n", "        for i, ( _, x) in enumerate(reversed(trace_graph.graph)):  # back-propagation starts from the last node\n", "            if len(x.parents) == 0:\n", "                continue\n", "            g = trace_graph.user_feedback if i == 0 else grads[x]\n", "            propagated_grads = torch.autograd.grad(x.data,  [p.data for p in x.parents], g)  # propagate the gradient\n", "            for p, pg in zip(x.parents, propagated_grads):\n", "                grads[p] += pg  #  accumulate gradient\n", "        return {p: p.data - self.stepsize * grads[p] for p in self.parameters}  # propose new update\n", "\n", "\n", "\n", "bp = BackPropagationGradientDescent([param], stepsize=stepsize)\n", "print('Version 1 gradient descent implementation using Trace')\n", "\n", "for i in range(10):\n", "    y = forward()\n", "    bp.zero_feedback()\n", "    bp.backward(y, torch.tensor(1.0))\n", "    bp.step()\n", "    print(f'  Loss at iter {i}: {y.data}')\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Version 2 Trace Implementation based on Propagator + Optimizer\n", "\n", "Another way is to override the what's propagated in the `backward` call of <PERSON>. Trace has a generic backward routine performed on the computational graph that can support designing new end-to-end optimization algorithms. While by default Trace propagates Trace graphes in `backward` for generality, for the differentiable problems here we can override the behavior and let it directly propagate gradients. In this way, the optimizer would receive directly the propagted gradient instead of Trace graphs.\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Implementation by Propagator\n", "Version 2 gradient descent implementation using Trace\n", "  Loss at iter 0: 1.5\n", "  Loss at iter 1: 1.1200000047683716\n", "  Loss at iter 2: 0.8122000098228455\n", "  Loss at iter 3: 0.5628820061683655\n", "  Loss at iter 4: 0.36093443632125854\n", "  Loss at iter 5: 0.19735687971115112\n", "  Loss at iter 6: 0.0648590698838234\n", "  Loss at iter 7: 0.04434824362397194\n", "  Loss at iter 8: 0.06279093772172928\n", "  Loss at iter 9: 0.046178679913282394\n"]}], "source": ["\n", "print('Implementation by <PERSON><PERSON><PERSON><PERSON>')\n", "\n", "\n", "# We create a custom propagator that back-propagates the gradient\n", "class BackPropagator(Propagator):\n", "\n", "    def init_feedback(self, node, feedback):\n", "        return feedback\n", "\n", "    def _propagate(self, child):\n", "        grad = sum(sum(v) for v in child.feedback.values())\n", "        propagated_grads = torch.autograd.grad(child.data,  [p.data for p in child.parents], grad)\n", "        return {p: pg for p, pg in zip(child.parents, propagated_grads)}\n", "\n", "\n", "class GradientDescent(Optimizer):\n", "\n", "    def __init__(self, parameters, stepsize, *args, **kwargs):\n", "        super().__init__(parameters, *args, **kwargs)\n", "        self.stepsize = stepsize\n", "\n", "    def default_propagator(self):\n", "        # use the custom propagator instead of the default one, which propagates Trace graph\n", "        return BackPropagator()\n", "\n", "    def _step(self, *args, **kwargs):\n", "        # simpel gradient descent\n", "        return {p: p.data - self.stepsize * sum(sum(v) for v in p.feedback.values()) for p in self.parameters}  # propose new update\n", "\n", "\n", "\n", "param  = node(torch.tensor(1.0, requires_grad=True), trainable=True)  # reset\n", "bp = GradientDescent([param], stepsize=stepsize)\n", "print('Version 2 gradient descent implementation using Trace')\n", "\n", "for i in range(10):\n", "    y = forward()\n", "    bp.zero_feedback()\n", "    bp.backward(y, torch.tensor(1.0))\n", "    bp.step()\n", "    print(f'  Loss at iter {i}: {y.data}')\n"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}