{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Basics\n", "\n", "## Node and MessageNode\n", "\n", "`trace` is a computational graph framework for tracing and optimizing codes. Its core data structure is the \"node\" container of python objects. To create a node, use `node` method, which creates a `Node` object. To access the content of a node, use the `data` attribute."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install trace-opt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"ExecuteTime": {"end_time": "2024-08-01T21:49:21.474982Z", "start_time": "2024-08-01T21:49:21.469742Z"}, "collapsed": false}, "outputs": [], "source": ["from opto.trace import node, GRAPH\n", "\n", "def print_node(node):\n", "    print(node)\n", "    print(f\"parents: {[p.name for p in node.parents]}\")\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"ExecuteTime": {"end_time": "2024-08-01T21:41:21.651217Z", "start_time": "2024-08-01T21:41:21.624547Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["node of int 1\n", "string\n", "[1, 2, 3]\n", "{'a': 1, 'b': 2}\n", "<__main__.Foo object at 0x7f99341e7760>\n"]}], "source": ["x = node(1)  # node of int\n", "print(\"node of int\", x.data)\n", "x = node(\"string\")  # node of str\n", "print(x.data)\n", "x = node([1, 2, 3])  # node of list\n", "print(x.data)\n", "x = node({\"a\": 1, \"b\": 2})  # node of dict\n", "print(x.data)\n", "\n", "\n", "class Foo:\n", "    def __init__(self, x):\n", "        self.x = x\n", "        self.secret = \"secret\"\n", "\n", "    def print(self, val):\n", "        print(val)\n", "\n", "\n", "x = node(Foo(\"foo\"))  # node of a class instance\n", "print(x.data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["When a computation is performed using the contents of nodes, the result is also a node. This allows for the creation of a computation graph. The computation graph is a directed acyclic graph where the edges indicate the data dependencies.\n", "\n", "Nodes that are defined manually can be marked as trainable by setting their `trainable` attribute to True; such nodes are a subclass of Node called `ParameterNode`.\n", "Nodes that are created automatically as a result of computations are a different subclass of Node called `MessageNode`.\n", "\n", "Nodes can be copied. This can be done in two ways with `clone` or `detach`"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<__main__.Foo object at 0x7f99340f9cc0>\n"]}], "source": ["# clone returns a MessageNode whose parent is the original node\n", "x_clone = x.clone()\n", "assert x in x_clone.parents\n", "assert x_clone.data != x.data\n", "assert x_clone.data.x == x.data.x\n", "print(x_clone.data)\n", "# detach returns a new Node which is not connected to the original node\n", "x_detach = x.detach()\n", "assert len(x_detach.parents) == 0\n", "assert x_detach.data != x.data\n", "assert x_detach.data.x == x.data.x"]}, {"cell_type": "markdown", "metadata": {}, "source": ["`trace` overloads python's magic methods that gives return value explicitly (such as `__add__`), except logical operations such as `__bool__` and setters. (The comparison magic methods instead compare nodes according to the computation graph that will be explained later, rather than comparing the data.) \n", "\n", "When nodes are used with these magic methods, the output would be a `MessageNode`, which is a subclass of `Node` that has the inputs of the method as the parents. The attribute `description` of a `MessageNode` documents the method's function."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (divide:0, dtype=<class 'float'>, data=0.3333333333333333)\n", "MessageNode: (divide:0, dtype=<class 'float'>, data=0.3333333333333333)\n", "parents: ['node_x:0', 'node_y:0']\n", "\n", "\n", "MessageNode: (getitem:0, dtype=<class 'int'>, data=1)\n", "parents: ['dict_node:0', 'str:1']\n", "len(dict_node) = MessageNode: (len_:0, dtype=<class 'int'>, data=2)\n", "\n", "\n", "Node: (str:3, dtype=<class 'str'>, data=hello world)\n", "MessageNode: (node_getattr:1, dtype=<class 'str'>, data=secret)\n", "parents: ['Foo:1', 'str:4']\n"]}], "source": ["# Basic arithmetic operations\n", "x = node(1, name=\"node_x\")\n", "y = node(3, name=\"node_y\")\n", "z = x / y\n", "z2 = x / 3  # the int 3 would be converted to a node automatically\n", "print(z)\n", "print_node(z)\n", "print(\"\\n\")\n", "\n", "# Index a node\n", "dict_node = node({\"a\": 1, \"b\": 2}, name=\"dict_node\")\n", "a = dict_node[\"a\"]\n", "print_node(a)\n", "print(\"len(dict_node) =\", dict_node.len())\n", "\n", "print(\"\\n\")\n", "\n", "# Getting class attribute and calling class method\n", "x = node(Foo(\"foo\"))\n", "x.call(\"print\", \"hello world\")\n", "print_node(x.getattr(\"secret\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Operations on Nodes\n", "For equivalence relations between nodes, we follow the PyTorch convention. \n", "\n", "In order to work with Python's control flow statements like `if` and `while`, the result of the comparison (a boolean value) is not a node and therefore is not traced."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"ExecuteTime": {"end_time": "2024-08-01T23:22:27.324646Z", "start_time": "2024-08-01T23:22:26.483003Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True\n", "True\n", "result is not a node, therefore, we cannot call backward() on it, but we can use `x.eq(1)` function to trace the comparison.\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"406pt\" height=\"194pt\"\n", " viewBox=\"0.00 0.00 405.50 193.91\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 189.91)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-189.91 401.5,-189.91 401.5,4 -4,4\"/>\n", "<!-- list1 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>list1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"94.75\" cy=\"-148.43\" rx=\"94.51\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"94.75\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">list1</text>\n", "<text text-anchor=\"middle\" x=\"94.75\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is ...</text>\n", "<text text-anchor=\"middle\" x=\"94.75\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2, 3]</text>\n", "</g>\n", "<!-- eq0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>eq0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"198.75\" cy=\"-37.48\" rx=\"92.76\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"198.75\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">eq0</text>\n", "<text text-anchor=\"middle\" x=\"198.75\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[eq] This is an...</text>\n", "<text text-anchor=\"middle\" x=\"198.75\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">False</text>\n", "</g>\n", "<!-- list1&#45;&gt;eq0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>list1&#45;&gt;eq0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M127.66,-112.96C137.62,-102.52 148.66,-90.95 159.02,-80.1\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"161.69,-82.38 166.06,-72.73 156.62,-77.54 161.69,-82.38\"/>\n", "</g>\n", "<!-- int4 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>int4</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"302.75\" cy=\"-148.43\" rx=\"94.51\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"302.75\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">int4</text>\n", "<text text-anchor=\"middle\" x=\"302.75\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is ...</text>\n", "<text text-anchor=\"middle\" x=\"302.75\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- int4&#45;&gt;eq0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>int4&#45;&gt;eq0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M269.85,-112.96C259.89,-102.52 248.84,-90.95 238.48,-80.1\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"240.88,-77.54 231.44,-72.73 235.82,-82.38 240.88,-77.54\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f99340e6a10>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["x = node(True)\n", "if x:\n", "    print(\"True\")\n", "\n", "x = node([1, 2, 3])\n", "print(1 in x)\n", "\n", "result = 1 in x # result is not a node\n", "try:\n", "    result.backward()\n", "except:\n", "    print(\"result is not a node, therefore, we cannot call backward() on it, but we can use `x.eq(1)` function to trace the comparison.\")\n", "\n", "# In order to trace the comparison, we need to use `.eq` method\n", "result = x.eq(1)\n", "result.backward(visualize=True, print_limit=15)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["If the two nodes contain the same value, they are considered equal when we use `in` operator. \n", "If you want to check if a node object is inside a list, use the `contain` function."]}, {"cell_type": "code", "execution_count": 7, "metadata": {"ExecuteTime": {"end_time": "2024-08-01T22:55:43.983229Z", "start_time": "2024-08-01T22:55:43.977436Z"}, "collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x is in y True\n", "x is not in y2 False\n", "When we use `in` operator, x is in y True\n", "When we use `in` operator, x is also in y2 True\n"]}], "source": ["from opto.trace.utils import contain\n", "\n", "x = node(1)\n", "y = [x, node(2), node(3)]\n", "y2 = [node(1), node(2), node(3)]\n", "\n", "print(\"x is in y\", contain(y, x))\n", "print(\"x is not in y2\", contain(y2, x))\n", "\n", "# x is in y and y2 if we use `in` operator\n", "print(\"When we use `in` operator, x is in y\", x in y)\n", "print(\"When we use `in` operator, x is also in y2\", x in y2)"]}, {"cell_type": "markdown", "metadata": {"collapsed": false}, "source": ["```{warning}\n", "When using a node with a logical operator like `and`, `or`, `not`, the output does not always have the same behavior -- since the result is dependent on how Python evvaluates the expression.\n", "``` "]}, {"cell_type": "code", "execution_count": 8, "metadata": {"ExecuteTime": {"end_time": "2024-08-01T23:15:44.011634Z", "start_time": "2024-08-01T23:15:44.004190Z"}, "collapsed": false, "editable": true, "slideshow": {"slide_type": ""}, "tags": ["hide-cell"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["True and x: Node: (bool:1, dtype=<class 'bool'>, data=True)\n", "x and True: True\n", "node(True) and x: Node: (bool:1, dtype=<class 'bool'>, data=True)\n", "x and node(True): Node: (bool:3, dtype=<class 'bool'>, data=True)\n", "\n", "\n", "False and x: False\n", "x and False: False\n", "node(False) and x: Node: (bool:4, dtype=<class 'bool'>, data=False)\n", "x and node(False): Node: (bool:5, dtype=<class 'bool'>, data=False)\n", "\n", "\n", "True and x: Node: (bool:6, dtype=<class 'bool'>, data=False)\n", "x and True: Node: (bool:6, dtype=<class 'bool'>, data=False)\n", "node(True) and x: Node: (bool:6, dtype=<class 'bool'>, data=False)\n", "x and node(True): Node: (bool:6, dtype=<class 'bool'>, data=False)\n", "\n", "\n", "False and x: False\n", "x and False: Node: (bool:6, dtype=<class 'bool'>, data=False)\n", "node(False) and x: Node: (bool:8, dtype=<class 'bool'>, data=False)\n", "x and node(False): Node: (bool:6, dtype=<class 'bool'>, data=False)\n"]}], "source": ["x = node(True)\n", "\n", "y = True and x  # Node\n", "print(\"True and x:\", y)\n", "y = x and True  # True\n", "print(\"x and True:\", y)\n", "y = node(True) and x  # Node\n", "print(\"node(True) and x:\", y)\n", "y = x and node(True)  # Node\n", "print(\"x and node(True):\", y)\n", "\n", "print('\\n')\n", "\n", "y = False and x  # False\n", "print(\"False and x:\", y)\n", "y = x and False  # False\n", "print(\"x and False:\", y)\n", "y = node(False) and x  # Node\n", "print(\"node(False) and x:\", y)\n", "y = x and node(False)  # Node\n", "print(\"x and node(False):\", y)\n", "\n", "print('\\n')\n", "\n", "x = node(False)\n", "\n", "y = True and x  # Node\n", "print(\"True and x:\", y)\n", "y = x and True  # Node\n", "print(\"x and True:\", y)\n", "y = node(True) and x  # Node\n", "print(\"node(True) and x:\", y)\n", "y = x and node(True)  # Node\n", "print(\"x and node(True):\", y)\n", "\n", "print('\\n')\n", "\n", "y = False and x  # False\n", "print(\"False and x:\", y)\n", "y = x and False  # Node\n", "print(\"x and False:\", y)\n", "y = node(False) and x  # Node\n", "print(\"node(False) and x:\", y)\n", "y = x and node(False)  # Node\n", "print(\"x and node(False):\", y)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Nodes can be used to encapsulate any python object, including functions. Here are a few examples."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["output: MessageNode: (call:1, dtype=<class 'int'>, data=2)\n", "parents [('function:0', <function fun at 0x7f991e677d00>), ('int:11', 1)]\n", "\n", "\n", "\n", "The attribute of the wrapped object cannot be directly accessed. Instead use getattr() or call()\n", "foo_node: MessageNode: (node_getattr:2, dtype=<class 'int'>, data=1)\n", "parents [('Foo:2', <__main__.Foo object at 0x7f99340fa770>), ('str:6', 'node')]\n", "non_node: MessageNode: (node_getattr:3, dtype=<class 'int'>, data=2)\n", "parents [('Foo:2', <__main__.Foo object at 0x7f99340fa770>), ('str:7', 'non_node')]\n", "output: MessageNode: (call:2, dtype=<class 'int'>, data=4)\n", "parents [('node_getattr:4', <bound method Foo.non_trace_fun of <__main__.Foo object at 0x7f99340fa770>>)]\n", "output: MessageNode: (call:4, dtype=<class 'int'>, data=4)\n", "parents [('node_getattr:6', <bound method Foo.non_trace_fun of <__main__.Foo object at 0x7f99340fa770>>)]\n"]}], "source": ["def fun(x):\n", "    return x + 1\n", "\n", "\n", "fun_node = node(fun)\n", "y = fun_node(node(1))\n", "print(f\"output: {y}\\nparents {[(p.name, p.data) for p in y.parents]}\")\n", "print(\"\\n\\n\")\n", "\n", "\n", "class Foo:\n", "\n", "    def __init__(self):\n", "        self.node = node(1)\n", "        self.non_node = 2\n", "\n", "    def trace_fun(self):\n", "        return self.node * 2\n", "\n", "    def non_trace_fun(self):\n", "        return self.non_node * 2\n", "\n", "\n", "foo = node(Foo())\n", "\n", "try:\n", "    foo.node\n", "    foo.trace_fun()\n", "except AttributeError:\n", "    print(\"The attribute of the wrapped object cannot be directly accessed. Instead use getattr() or call()\")\n", "\n", "\n", "attr = foo.getattr(\"node\")\n", "print(f\"foo_node: {attr}\\nparents {[(p.name, p.data) for p in attr.parents]}\")\n", "\n", "\n", "attr = foo.getattr(\"non_node\")\n", "print(f\"non_node: {attr}\\nparents {[(p.name, p.data) for p in attr.parents]}\")\n", "\n", "\n", "fun = foo.getattr(\"non_trace_fun\")\n", "y = fun()\n", "print(f\"output: {y}\\nparents {[(p.name, p.data) for p in y.parents]}\")\n", "\n", "try:\n", "    fun = foo.getattr(\"trace_fun\")\n", "    y = fun()\n", "except AssertionError as e:\n", "    print(e)\n", "\n", "y = foo.call(\"non_trace_fun\")\n", "print(f\"output: {y}\\nparents {[(p.name, p.data) for p in y.parents]}\")\n", "\n", "try:\n", "    y = foo.call(\"trace_fun\")\n", "except AssertionError as e:\n", "    print(e)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Use Bundle to Writing Custom Node Operators\n", "\n", "In addition to magic methods, we can use `bundle` to write custom methods that are traceable. When decorating a method with `bundle`, it needs a description of the method. It has a format of `[method_name] description`. `bundle` will automatically add all nodes whose `data` attribute is used within the function as the parents of the output `MessageNode`.\n", "\n", "Given a function `fun`, the decorated function by default will unpack all the inputs (i.e. it unpacks all the data inside nodes), send them to `fun`, and then creates a `MessageNode` to wrap the output of `fun` which has parents containing all the nodes used in this operation. "]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (add:0, dtype=<class 'int'>, data=2)\n", "parents: ['node_x:0']\n", "\n", "\n", "MessageNode: (add:1, dtype=<class 'int'>, data=3)\n", "parents: ['node_x:1', 'node_y:0']\n", "\n", "\n", "MessageNode: (pass_through:0, dtype=<class 'tuple'>, data=(1, 2))\n", "\n", "\n"]}], "source": ["from opto.trace import bundle, GRAPH\n", "from opto.trace.nodes import Node\n", "\n", "GRAPH.clear()\n", "\n", "@bundle()\n", "def add(x):\n", "    \"\"\"\n", "    Add 1 to input x\n", "    \"\"\"\n", "    return x + 1\n", "\n", "\n", "x = node(1, name=\"node_x\")\n", "z = add(x)\n", "print_node(z)\n", "print(\"\\n\")\n", "\n", "\n", "@bundle()\n", "def add(x, y):\n", "    \"\"\"\n", "    Add input x and input y\n", "    \"\"\"\n", "    return x + y\n", "\n", "\n", "x = node(1, name=\"node_x\")\n", "y = node(2, name=\"node_y\")\n", "z = add(x, y)\n", "print_node(z)\n", "print(\"\\n\")\n", "\n", "# The output is a node of a tuple of two nodes\n", "\n", "\n", "@bundle()\n", "def pass_through(x, y):\n", "    \"\"\"\n", "    No operation, just return inputs\n", "    \"\"\"\n", "    return x, y\n", "\n", "\n", "x = node(1, name=\"node_x\")\n", "y = node(2, name=\"node_y\")\n", "z = pass_through(x, y)\n", "print(z)\n", "\n", "assert isinstance(z, Node)\n", "assert isinstance(z.data, tuple)\n", "assert len(z.data) == 2\n", "print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Graph\n", "\n", "The graph of nodes can be visualized by calling `backward` method of a node. (Later we will cover how `backward` also sends feedback across the graph). "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"1553pt\" height=\"305pt\"\n", " viewBox=\"0.00 0.00 1553.46 304.86\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 300.86)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-300.86 1549.46,-300.86 1549.46,4 -4,4\"/>\n", "<!-- add0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>add0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"545.73\" cy=\"-148.43\" rx=\"217.58\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"545.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">add0</text>\n", "<text text-anchor=\"middle\" x=\"545.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[add] This is an add operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"545.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">3</text>\n", "</g>\n", "<!-- add2 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>add2</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"772.73\" cy=\"-37.48\" rx=\"217.58\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">add2</text>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[add] This is an add operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">5</text>\n", "</g>\n", "<!-- add0&#45;&gt;add2 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>add0&#45;&gt;add2</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M617.55,-112.96C641.17,-101.62 667.57,-88.95 691.81,-77.32\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"693.48,-80.4 700.98,-72.91 690.45,-74.08 693.48,-80.4\"/>\n", "</g>\n", "<!-- add1 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>add1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"999.73\" cy=\"-148.43\" rx=\"217.58\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"999.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">add1</text>\n", "<text text-anchor=\"middle\" x=\"999.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[add] This is an add operator of x and y. .</text>\n", "<text text-anchor=\"middle\" x=\"999.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">2</text>\n", "</g>\n", "<!-- add1&#45;&gt;add2 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>add1&#45;&gt;add2</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M927.91,-112.96C904.29,-101.62 877.89,-88.95 853.65,-77.32\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"855.01,-74.08 844.48,-72.91 851.98,-80.4 855.01,-74.08\"/>\n", "</g>\n", "<!-- node_x0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>node_x0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"772.73\" cy=\"-259.38\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">node_x0</text>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- node_x0&#45;&gt;add0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>node_x0&#45;&gt;add0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M699.97,-223.46C676.63,-212.26 650.63,-199.78 626.74,-188.31\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"628.22,-185.14 617.69,-183.97 625.19,-191.45 628.22,-185.14\"/>\n", "</g>\n", "<!-- node_x0&#45;&gt;add1 -->\n", "<g id=\"edge5\" class=\"edge\">\n", "<title>node_x0&#45;&gt;add1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M845.49,-223.46C868.83,-212.26 894.83,-199.78 918.72,-188.31\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"920.27,-191.45 927.77,-183.97 917.24,-185.14 920.27,-191.45\"/>\n", "</g>\n", "<!-- node_y0 -->\n", "<g id=\"node5\" class=\"node\">\n", "<title>node_y0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"251.73\" cy=\"-259.38\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">node_y0</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">2</text>\n", "</g>\n", "<!-- node_y0&#45;&gt;add0 -->\n", "<g id=\"edge4\" class=\"edge\">\n", "<title>node_y0&#45;&gt;add0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M343.54,-224.36C376.21,-212.25 413.12,-198.57 446.39,-186.25\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"447.96,-189.39 456.12,-182.64 445.53,-182.83 447.96,-189.39\"/>\n", "</g>\n", "<!-- int0 -->\n", "<g id=\"node6\" class=\"node\">\n", "<title>int0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"1293.73\" cy=\"-259.38\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"1293.73\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">int0</text>\n", "<text text-anchor=\"middle\" x=\"1293.73\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"1293.73\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- int0&#45;&gt;add1 -->\n", "<g id=\"edge6\" class=\"edge\">\n", "<title>int0&#45;&gt;add1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M1201.92,-224.36C1169.25,-212.25 1132.34,-198.57 1099.07,-186.25\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"1099.93,-182.83 1089.34,-182.64 1097.5,-189.39 1099.93,-182.83\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f99340e71f0>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from opto.trace.nodes import GRAPH\n", "\n", "GRAPH.clear()  # to remove all the nodes\n", "x = node(1, name=\"node_x\")\n", "y = node(2, name=\"node_y\")\n", "a = x + y\n", "b = x + 1\n", "final = a + b\n", "final.backward(visualize=True)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Node: (bool:0, dtype=<class 'bool'>, data=True) Node: (int:0, dtype=<class 'int'>, data=1) Node: (int:1, dtype=<class 'int'>, data=0)\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"511pt\" height=\"83pt\"\n", " viewBox=\"0.00 0.00 511.46 82.95\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 78.95)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-78.95 507.46,-78.95 507.46,4 -4,4\"/>\n", "<!-- int0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>int0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"251.73\" cy=\"-37.48\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">int0</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f99340e5960>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["GRAPH.clear()\n", "x = node(True)\n", "one = node(1)\n", "zero = node(0)\n", "print(x, one, zero)\n", "# Logical operations are not traceable\n", "y = one if x.data else zero\n", "y.backward(visualize=True)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"612pt\" height=\"194pt\"\n", " viewBox=\"0.00 0.00 611.87 193.91\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 189.91)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-189.91 607.87,-189.91 607.87,4 -4,4\"/>\n", "<!-- bool0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>bool0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"301.93\" cy=\"-148.43\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"301.93\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">bool0</text>\n", "<text text-anchor=\"middle\" x=\"301.93\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"301.93\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">True</text>\n", "</g>\n", "<!-- fun0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>fun0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"301.93\" cy=\"-37.48\" rx=\"301.87\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"301.93\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">fun0</text>\n", "<text text-anchor=\"middle\" x=\"301.93\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[fun] Return one if input x is True, otherwise return zero.</text>\n", "<text text-anchor=\"middle\" x=\"301.93\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- bool0&#45;&gt;fun0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>bool0&#45;&gt;fun0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M301.93,-110.85C301.93,-102.67 301.93,-93.89 301.93,-85.37\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"305.43,-85.15 301.93,-75.15 298.43,-85.15 305.43,-85.15\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f99340e64d0>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# This is traceable\n", "@bundle(allow_external_dependencies=True)\n", "def fun(x):\n", "    \"\"\"\n", "    Return one if input x is True, otherwise return zero\n", "    \"\"\"\n", "    return one.data if x else zero.data\n", "\n", "\n", "y = fun(x)\n", "y.backward(visualize=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Broadcasting\n", "Using `apply_op`, we can broadcast node operators to a container of nodes. A container of nodes are either `list`, `tuple`, `dict`, or subclass of an abstract class `BaseModule`. `apply_op` recursively applies the operator to all nodes in the container. "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x [1, 2, 1]\n", "y [3, 4, 2]\n", "Elements in z should be added, except for the last one. Value:  [4, 6, 1]\n", "1+3=4\n", "0==0==0\n", "x_plus_y.x should be added. Value:  xy\n", "x_plus_y.y should be added. Value:  [2, 4]\n", "x_plus_y.z should be not added, just 1. Value:  1\n"]}], "source": ["from opto.trace import apply_op, node, NodeContainer\n", "from opto.trace import operators as ops\n", "\n", "import copy\n", "\n", "# Using list as a node container\n", "x = [node(1), node(2), 1]\n", "y = [node(3), node(4), 2]\n", "z = copy.deepcopy(x)\n", "z = apply_op(ops.add, z, x, y)\n", "print(\"x\", [x[0].data, x[1].data, x[2]])\n", "print(\"y\", [y[0].data, y[1].data, y[2]])\n", "print(\"Elements in z should be added, except for the last one. Value: \", [z[0].data, z[1].data, z[2]])\n", "\n", "\n", "# Using list as a node container\n", "x = dict(a=node(1), b=0)\n", "y = dict(a=node(3), b=0)\n", "z = copy.deepcopy(x)\n", "z = apply_op(ops.add, z, x, y)\n", "print(f\"{x['a'].data}+{y['a'].data}={z['a'].data}\")\n", "print(f\"{x['b']}=={y['b']}=={z['b']}\")\n", "\n", "# Using a custom class as a node container\n", "\n", "\n", "class Foo(NodeContainer):\n", "    def __init__(self, x):\n", "        self.x = node(x)\n", "        self.y = [node(1), node(2)]\n", "        self.z = 1\n", "\n", "\n", "x = Foo(\"x\")\n", "y = Foo(\"y\")\n", "x_plus_y = Foo(\"template\")\n", "x_plus_y = apply_op(ops.add, x_plus_y, x, y)\n", "print(\"x_plus_y.x should be added. Value: \", x_plus_y.x.data)\n", "print(\"x_plus_y.y should be added. Value: \", [n.data for n in x_plus_y.y])\n", "print(\"x_plus_y.z should be not added, just 1. Value: \", x_plus_y.z)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Nodes and Python Data Structure\n", "\n", "We can create a `node` over Python data structure like dictionary, tuple, set, or list. We automatically handle the iteration and you can wrap a node around any data structure and use them like normal python objects."]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (getitem:0, dtype=<class 'str'>, data=arg2)\n", "MessageNode: (getitem:1, dtype=<class 'str'>, data=arg1)\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"965pt\" height=\"305pt\"\n", " viewBox=\"0.00 0.00 965.46 304.86\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 300.86)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-300.86 961.46,-300.86 961.46,4 -4,4\"/>\n", "<!-- to_list0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>to_list0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"705.73\" cy=\"-148.43\" rx=\"183.7\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"705.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">to_list0</text>\n", "<text text-anchor=\"middle\" x=\"705.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[to_list] This converts x to a list. &#160;.</text>\n", "<text text-anchor=\"middle\" x=\"705.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">[&#39;arg2&#39;, &#39;arg1&#39;]</text>\n", "</g>\n", "<!-- getitem1 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>getitem1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"478.73\" cy=\"-37.48\" rx=\"302.78\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"478.73\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">getitem1</text>\n", "<text text-anchor=\"middle\" x=\"478.73\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[getitem] This is a getitem operator of x based on index. .</text>\n", "<text text-anchor=\"middle\" x=\"478.73\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">arg1</text>\n", "</g>\n", "<!-- to_list0&#45;&gt;getitem1 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>to_list0&#45;&gt;getitem1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M635.46,-113.7C612.14,-102.51 585.95,-89.94 561.76,-78.33\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"563.13,-75.1 552.6,-73.93 560.1,-81.41 563.13,-75.1\"/>\n", "</g>\n", "<!-- int15 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>int15</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"251.73\" cy=\"-148.43\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">int15</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- int15&#45;&gt;getitem1 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>int15&#45;&gt;getitem1</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M324.49,-112.51C347.16,-101.63 372.33,-89.55 395.64,-78.36\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"397.32,-81.43 404.82,-73.95 394.29,-75.12 397.32,-81.43\"/>\n", "</g>\n", "<!-- set0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>set0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"705.73\" cy=\"-259.38\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"705.73\" y=\"-270.68\" font-family=\"Times,serif\" font-size=\"14.00\">set0</text>\n", "<text text-anchor=\"middle\" x=\"705.73\" y=\"-255.68\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"705.73\" y=\"-240.68\" font-family=\"Times,serif\" font-size=\"14.00\">{&#39;arg2&#39;, &#39;arg1&#39;}</text>\n", "</g>\n", "<!-- set0&#45;&gt;to_list0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>set0&#45;&gt;to_list0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M705.73,-221.8C705.73,-213.63 705.73,-204.85 705.73,-196.32\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"709.23,-196.1 705.73,-186.1 702.23,-196.1 709.23,-196.1\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f99340e6e90>"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["from opto.trace import node\n", "\n", "args = node({\"arg1\", \"arg2\"}, trainable=False)\n", "for a in args:\n", "    print(a)\n", "\n", "a.backward(visualize=True)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MessageNode: (getitem:2, dtype=<class 'str'>, data=arg1) MessageNode: (getitem:3, dtype=<class 'int'>, data=1)\n", "MessageNode: (getitem:4, dtype=<class 'str'>, data=arg2) MessageNode: (getitem:5, dtype=<class 'int'>, data=2)\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.50.0 (0)\n", " -->\n", "<!-- Pages: 1 -->\n", "<svg width=\"1032pt\" height=\"194pt\"\n", " viewBox=\"0.00 0.00 1032.46 193.91\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 189.91)\">\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-189.91 1028.46,-189.91 1028.46,4 -4,4\"/>\n", "<!-- list0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>list0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"251.73\" cy=\"-148.43\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">list0</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"251.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">[1, 2]</text>\n", "</g>\n", "<!-- getitem5 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>getitem5</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"511.73\" cy=\"-37.48\" rx=\"302.78\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"511.73\" y=\"-48.78\" font-family=\"Times,serif\" font-size=\"14.00\">getitem5</text>\n", "<text text-anchor=\"middle\" x=\"511.73\" y=\"-33.78\" font-family=\"Times,serif\" font-size=\"14.00\">[getitem] This is a getitem operator of x based on index. .</text>\n", "<text text-anchor=\"middle\" x=\"511.73\" y=\"-18.78\" font-family=\"Times,serif\" font-size=\"14.00\">2</text>\n", "</g>\n", "<!-- list0&#45;&gt;getitem5 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>list0&#45;&gt;getitem5</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M334,-112.96C360.86,-101.7 390.89,-89.12 418.5,-77.54\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"419.89,-80.76 427.76,-73.66 417.19,-74.3 419.89,-80.76\"/>\n", "</g>\n", "<!-- int19 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>int19</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"772.73\" cy=\"-148.43\" rx=\"251.96\" ry=\"37.45\"/>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-159.73\" font-family=\"Times,serif\" font-size=\"14.00\">int19</text>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-144.73\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"772.73\" y=\"-129.73\" font-family=\"Times,serif\" font-size=\"14.00\">1</text>\n", "</g>\n", "<!-- int19&#45;&gt;getitem5 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>int19&#45;&gt;getitem5</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M690.15,-112.96C663.18,-101.7 633.04,-89.12 605.32,-77.54\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"606.6,-74.29 596.02,-73.66 603.9,-80.74 606.6,-74.29\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f99340e6c20>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["parms = node([1, 2], trainable=False)\n", "args = node([\"arg1\", \"arg2\"], trainable=False)\n", "\n", "for a, p in zip(args, parms):\n", "    print(a, p)\n", "\n", "p.backward(visualize=True)"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 4}