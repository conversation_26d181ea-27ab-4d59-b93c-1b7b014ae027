{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Error Handling\n", "\n", "We show how the optimizer in Trace can be used to resolve execution error duing optimization. When an exception is thrown during the execution of a bundled method, a special MessageNode, called the ExceptionNode, is created and a new Python exception trace.ExecutionError is thrown. The trace.ExecutionError is a wrapper of the original exception and contains the created ExceptionNode as its attribute `exception_node`. The ExceptionNode's parents are the inputs to the bundled method triggering the exception. Therefore, to resolve the error, we can simply use the created ExceptionNode as the target and its data can be used as feedback. \n", "\n", "\n", "Below we show a basic example of how Trace deal with execptions."]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false}, "outputs": [], "source": ["%pip install trace-opt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Iter 0, Failed, Parameter -1.0\n", "\n", "Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "exception_func_with_input_checking0 = func_with_input_checking(a=float0)\n", "\n", "#Documentation\n", "[exception] The operator func_with_input_checking raises an exception.\n", "\n", "#Variables\n", "(float) float0=-1.0\n", "\n", "#Constraints\n", "\n", "\n", "#Inputs\n", "\n", "\n", "#Others\n", "\n", "\n", "#Outputs\n", "(str) exception_func_with_input_checking0=(ValueError) Input must be greater than 0.1\n", "\n", "#Feedback\n", "(ValueError) Input must be greater than 0.1\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "  \"reasoning\": \"The instruction indicates that we need to modify the value of 'float0' to improve the output and address the feedback provided. The feedback, which is a ValueError stating 'Input must be greater than 0.1', suggests that the current value of 'float0' (-1.0) is not acceptable for the function 'func_with_input_checking'. The function expects a number greater than 0.1. Hence, setting 'float0' to a value greater than 0.1 should resolve the issue and prevent the exception from being raised.\",\n", "  \"answer\": \"\",\n", "  \"suggestion\": {\n", "    \"float0\": 0.2\n", "  }\n", "}\n", "\n", "Success, Parameter: 0.2\n"]}], "source": ["from opto import trace\n", "from opto.optimizers import OptoPrime\n", "\n", "\n", "def check_input(a):\n", "    if a <0.1:\n", "        raise ValueError(\"Input must be greater than 0.1\")\n", "\n", "@trace.bundle()\n", "def func_with_input_checking(a):\n", "    check_input(a)\n", "    return True\n", "\n", "\n", "param = trace.node(-1., trainable=True)  # Note; setting the initial value to -1. makes it a float;\n", "optimizer = OptoPrime([param], memory_size=5)\n", "\n", "for _ in range(5):\n", "    try:\n", "        success = func_with_input_checking(param)\n", "        print(f'\\nSuccess, Parameter: {param.data}')\n", "        break\n", "    except trace.ExecutionError as e:\n", "        print(f'\\nIter {_}, Failed, Parameter {param.data}\\n')\n", "        target = e.exception_node\n", "        optimizer.zero_feedback()\n", "        optimizer.backward(target, target.create_feedback())\n", "        optimizer.step(verbose=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next we extend this basic example to create an example of constrained optimization. This example shows how optimization and constrained satisfication can be approached in the same way."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Iter 0, Not satisfying constraint, Parameter -1.0\n", "\n", "\n", "Iter 1, Objective 1.44, Parameter 0.2\n", "\n", "\n", "Iter 2, Objective 1.2100000000000002, Parameter 0.1\n", "\n", "\n", "Iter 3, Objective 1.2100000000000002, Parameter 0.1\n", "\n", "\n", "Iter 4, Objective 1.2321000000000002, Parameter 0.11\n", "\n", "\n", "Iter 5, Objective 1.2321000000000002, Parameter 0.11\n", "\n", "\n", "Iter 6, Objective 1.2122009999999999, Parameter 0.101\n", "\n", "\n", "Iter 7, Not satisfying constraint, Parameter -1.0\n", "\n", "\n", "Iter 8, Objective 1.2321000000000002, Parameter 0.11\n", "\n", "\n", "Iter 9, Objective 1.2544000000000002, Parameter 0.12\n", "\n"]}], "source": ["from opto import trace\n", "from opto.optimizers import OptoPrime\n", "\n", "trace.GRAPH.clear()\n", "\n", "def check_input(a):\n", "    if a <0.1:\n", "        raise ValueError(\"Input must be greater than 0.1\")\n", "\n", "@trace.bundle()\n", "def objective(a):\n", "    \"\"\" Computes (a+1)**2. \"\"\"\n", "    check_input(a)\n", "    return (a+1)**2\n", "\n", "\n", "param = trace.node(-1., trainable=True)  # Note; setting the initial value to -1. makes it a float;\n", "optimizer = OptoPrime([param], memory_size=5)\n", "\n", "for _ in range(10):\n", "    try:\n", "        target = objective(param)\n", "        feedback = 'Minimize the objective.'\n", "        print(f'\\nIter {_}, Objective {target.data}, Parameter {param.data}\\n')\n", "\n", "    except trace.ExecutionError as e:\n", "        print(f'\\nIter {_}, Not satisfying constraint, Parameter {param.data}\\n')\n", "        target = e.exception_node\n", "        feedback = e.exception_node.create_feedback()\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(target, feedback)\n", "    optimizer.step()\n"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}