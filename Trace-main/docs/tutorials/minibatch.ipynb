{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Batch Optimization\n", "\n", "We provide an example of how to update parameters on a batch of data. In these toy examples, we show different ways to update parameters of functions on data containing multiple inputs. For simplicity, we consider batch update without random sampling."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.netflix.net/simple\n", "Requirement already satisfied: trace-opt in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (0.1.1)\n", "Requirement already satisfied: autogen-agentchat~=0.2 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from trace-opt) (0.2.37)\n", "Requirement already satisfied: graphviz>=0.20.1 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from trace-opt) (0.20.3)\n", "Requirement already satisfied: scikit-learn in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from trace-opt) (1.5.1)\n", "Requirement already satisfied: xgboost in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from trace-opt) (2.1.1)\n", "Requirement already satisfied: diskcache in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (5.6.3)\n", "Requirement already satisfied: docker in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (7.1.0)\n", "Requirement already satisfied: flaml in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (2.3.1)\n", "Requirement already satisfied: numpy<2,>=1.17.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (1.26.4)\n", "Requirement already satisfied: openai>=1.3 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (1.52.2)\n", "Requirement already satisfied: packaging in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (24.1)\n", "Requirement already satisfied: pydantic!=2.6.0,<3,>=1.10 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (2.9.2)\n", "Requirement already satisfied: python-dotenv in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (1.0.1)\n", "Requirement already satisfied: termcolor in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (2.5.0)\n", "Requirement already satisfied: tiktoken in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from autogen-agentchat~=0.2->trace-opt) (0.8.0)\n", "Requirement already satisfied: scipy>=1.6.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from scikit-learn->trace-opt) (1.13.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from scikit-learn->trace-opt) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from scikit-learn->trace-opt) (3.5.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (4.6.2.post1)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (0.27.2)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (0.6.1)\n", "Requirement already satisfied: sniffio in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (4.66.6)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from openai>=1.3->autogen-agentchat~=0.2->trace-opt) (4.12.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from pydantic!=2.6.0,<3,>=1.10->autogen-agentchat~=0.2->trace-opt) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.23.4 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from pydantic!=2.6.0,<3,>=1.10->autogen-agentchat~=0.2->trace-opt) (2.23.4)\n", "Requirement already satisfied: requests>=2.26.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from docker->autogen-agentchat~=0.2->trace-opt) (2.32.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from docker->autogen-agentchat~=0.2->trace-opt) (2.2.3)\n", "Requirement already satisfied: regex>=2022.1.18 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from tiktoken->autogen-agentchat~=0.2->trace-opt) (2024.9.11)\n", "Requirement already satisfied: idna>=2.8 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from anyio<5,>=3.5.0->openai>=1.3->autogen-agentchat~=0.2->trace-opt) (3.7)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from anyio<5,>=3.5.0->openai>=1.3->autogen-agentchat~=0.2->trace-opt) (1.2.2)\n", "Requirement already satisfied: certifi in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from httpx<1,>=0.23.0->openai>=1.3->autogen-agentchat~=0.2->trace-opt) (2024.8.30)\n", "Requirement already satisfied: httpcore==1.* in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from httpx<1,>=0.23.0->openai>=1.3->autogen-agentchat~=0.2->trace-opt) (1.0.6)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai>=1.3->autogen-agentchat~=0.2->trace-opt) (0.14.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/miniconda3/envs/trace/lib/python3.10/site-packages (from requests>=2.26.0->docker->autogen-agentchat~=0.2->trace-opt) (3.3.2)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install trace-opt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we consider a small linear regression problem. To perform updates on multiple inputs at a time, here we just compute the loss for each input and then sum it up, and perform one `backward` call to tell the optimizer to minimize the loss. Since the optimizer is capable of seeing the graph, it can understand how different inputs and labels are paired and evaluated by the loss function."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import random\n", "import numpy as np\n", "\n", "random.seed(0)\n", "np.random.seed(0)\n", "\n", "from opto import trace\n", "from opto.optimizers import OptoPrime\n", "\n", "\n", "def true_fun(x):\n", "    return 2*x  - 3\n", "\n", "inputs = [3, 2, 1, 5, 4]\n", "outputs = [true_fun(x) for x in inputs]\n", "N = len(inputs)\n", "\n", "\n", "@trace.bundle()\n", "def loss(y_hat, y):\n", "    \"\"\" A least squares loss function. \"\"\"\n", "    return (y_hat - y) ** 2\n", "\n", "\n", "def compute_loss(inputs, outputs):\n", "    l = 0\n", "    for x,y in zip(inputs, outputs):\n", "        y_hat = fun(x)\n", "        l += loss(y_hat, y)\n", "    return l\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Iteration 0 Loss: 85\n", "Iteration 1 Loss: 10\n", "Iteration 2 Loss: 10\n", "Iteration 3 Loss: 7.5\n", "Iteration 4 Loss: 122.8125\n", "Iteration 5 Loss: 80.3125\n", "Iteration 6 Loss: 12.8125\n", "Iteration 7 Loss: 10.0\n", "Iteration 8 Loss: 7.5\n", "Iteration 9 Loss: 8.150000000000002\n", "Iteration 10 Loss: 6.449999999999999\n", "Iteration 11 Loss: 8.150000000000002\n", "Iteration 12 Loss: 9.037500000000001\n", "Iteration 13 Loss: 9.427\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjsAAAGwCAYAAABPSaTdAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAABLiUlEQVR4nO3de3iU5Z0//vczx2Qmk8k5QyAkgUSCnAW14oH4Feh6rMtlreJx7W9bv6CVWuuh9JC1Nal0pbSwaulapVpW+92qdbvbCngAkbqcBBURBEIIkElISCYzk2SOz++PmefJhCSQhJl5nnnm/bquXMLMkHyImrxz35/PfQuiKIogIiIi0iid0gUQERERJRLDDhEREWkaww4RERFpGsMOERERaRrDDhEREWkaww4RERFpGsMOERERaZpB6QLUIBwO4+TJk7DZbBAEQelyiIiIaBhEUYTb7UZJSQl0uqHXbxh2AJw8eRKlpaVKl0FERESj0NTUhHHjxg35PMMOAJvNBiDyycrOzla4GiIiIhqOrq4ulJaWyt/Hh8KwA8hbV9nZ2Qw7REREKeZcLShsUCYiIiJNY9ghIiIiTWPYISIiIk1j2CEiIiJNY9ghIiIiTWPYISIiIk1j2CEiIiJNY9ghIiIiTWPYISIiIk1j2CEiIiJNY9ghIiIiTWPYISIiIk1j2CFSiWAorHQJRESaxLBDpALffW0PLql7B82uHqVLISLSHIYdIhV4/0ArTnv9+OunTqVLISLSHIYdIoX1BkLo6A4AADYfPKVwNURE2sOwQ6Sw1i6f/OuPjrSjNxBSsBoiIu1h2CFSWIu7V/61LxjGR0faFayGiEh7FA07W7ZswY033oiSkhIIgoA333xTfi4QCOCxxx7DtGnTYLVaUVJSgrvvvhsnT57s9z58Ph8efPBBFBQUwGq14qabbsLx48eT/DchGj2nq7ff77mVRUQUX4qGHa/XixkzZmDNmjUDnuvu7sbu3bvxox/9CLt378brr7+OgwcP4qabbur3umXLluGNN97Aq6++iq1bt8Lj8eCGG25AKMStAEoNLV2RsGPLMAAANh9g2CEiiieDkh/82muvxbXXXjvoc3a7HRs3buz32OrVq3HJJZfg2LFjGD9+PFwuF1544QW8/PLLmD9/PgDglVdeQWlpKTZt2oSvfvWrCf87EJ0vKexcN3UM/rT7OI60eXGsvRvj8y0KV0ZEpA0p1bPjcrkgCAJycnIAALt27UIgEMDChQvl15SUlGDq1KnYtm3bkO/H5/Ohq6ur3xuRUpzRBuXKoixcVJYLANj8JVd3iIjiJWXCTm9vLx5//HEsXrwY2dnZAACn0wmTyYTc3Nx+ry0uLobTOfR5JfX19bDb7fJbaWlpQmsnOpuWaM9OsT0D8y4oBABsPtCqZElERJqSEmEnEAjgtttuQzgcxrPPPnvO14uiCEEQhnz+iSeegMvlkt+ampriWS7RiEjTWI7sDNRMioSdbYfb4Quy74yIKB5UH3YCgQBuvfVWNDQ0YOPGjfKqDgA4HA74/X50dHT0+zOtra0oLi4e8n2azWZkZ2f3eyNSgiiK8jRWcbYZF47JRqHNjG5/CLuOdpzjTxMR0XCoOuxIQefLL7/Epk2bkJ+f3+/52bNnw2g09mtkbm5uxmeffYa5c+cmu1yiEXP1BOALRi4ALc7OgCAI8lbW+xxBJyKKC0XDjsfjwZ49e7Bnzx4AQENDA/bs2YNjx44hGAzilltuwc6dO/GHP/wBoVAITqcTTqcTfr8fQGRi65vf/Ca+973v4Z133sHHH3+MO++8E9OmTZOns4jUrCXanJxjMSLDqAeAmL4dhh0ionhQdPR8586duPrqq+XfP/zwwwCAe+65B7W1tXjrrbcAADNnzuz359577z3U1NQAAH75y1/CYDDg1ltvRU9PD6655hq89NJL0Ov1Sfk7EJ0PZ3TsvNiWIT92ZVUBdAJwoMWNk509KMnJVKo8IiJNUDTs1NTUQBTFIZ8/23OSjIwMrF69GqtXr45naURJETuJJcmxmDCzNAe7j3Viy8FTuO2S8UqVR0SkCaru2SHSOulAQUe2ud/j8y4oAsCrI4iI4oFhh0hB8jZWdka/x+dFR9C3ftmGQCic9LqIiLSEYYdIQS1DhJ3pY+3Is5rg9gXx8bFOBSojItIOhh0iBUnTWI4zwo5OJ+DKqgIAwOaDPE2ZiOh8MOwQKWiobSygbwT9fY6gExGdF4YdIoUEQmG0eSIrO8V284Dnr4qGnX0nu9AavVKCiIhGjmGHSCFtHh9EEdDrBBRYB4adgiwzpo21AwA+ONiW7PKIiDSDYYdIIdKdWEU2M3S6wS+u5dURRETnj2GHSCFDTWLFkm5B/+DLUwiFz33IJhERDcSwQ6SQoSaxYs0szUF2hgGd3QHsPd6ZpMqIiLSFYYdIIX2TWAP7dSQGvQ5XVvFiUCKi88GwQ6SQwe7FGox8Czr7doiIRoVhh0ghLW7pXqyzhx1pBH3v8U6c9voTXhcRkdYw7BApRJrGOluDMgA47BmodtggipFGZSIiGhmGHSKFSA3K5wo7QN/FoNzKIiIaOYYdIgV4fEF4fEEAkZWbc5H6drYcPIUwR9CJiEaEYYdIAdIZO1aTHllmwzlfP6csD1aTHm0ePz5v7kp0eUREmsKwQ6SA4U5iSUwGHeZWSregcyuLiGgkGHaIFDDcSaxYfbegtyakJiIirWLYIVKA0zX85mSJFHZ2H+uEqyeQkLqIiLSIYYdIAcO5F+tMpXkWTCy0IhQWse0Qb0EnIhouhh0iBUhhx3GWqyIGM++CIgDA+7w6goho2Bh2iBTgHMXKDtB3C/rmg6cgihxBJyIaDoYdIgWMdBpLcklFHjKMOji7enGgxZ2I0oiINIdhhyjJwmERre5Ig/JIprEAIMOox2UT8gHwFnQiouFi2CFKsnavH8GwCEEACm0j69kBeAs6EdFIMewQJZnUnJxvNcOoH/n/gvMmRZqUdxw9LV85QUREQ2PYIUoyeRLLPvJVHQCoKLCiLN+CQEjE3w+3x7M0IiJNYtghSjJ5Ess2sn6dWH1bWTxNmYjoXBh2iJJstJNYsfqujuAIOhHRuTDsECVZS9foJrFiXTYxHya9Dsc7enCkzRuv0oiINIlhhyjJ+g4UHF3PDgBYTAZcUpEHgCPoRETnwrBDlGSjuRdrMPJWFkfQiYjOimGHKMn6prHOL+xIV0f875F29AZC510XEZFWMewQJVFvIISO7gCA85vGAoDKoiyU2DPgC4bx9yMcQSciGgrDDlEStUabk00GHXIsxvN6X4IgYJ50MSj7doiIhsSwQ5RELe7oFlZ2BgRBOO/3N++CyGnKW9i3Q0Q0JIYdoiRyus5/EivW5ZX5MOgEHGnz4lh7d1zeJxGR1jDsECVRvCaxJLYMI2aX5QLgacpERENh2CFKInkSK05hB0Bf3w63soiIBsWwQ5REzmiDcrxWdoC+83a2HW6HL8gRdCKiMzHsECVRPO7FOtOFY7JRaDOj2x/CzqMdcXu/RERawbBDlESx01jxIghCzC3o3MoiIjoTww5RkoiiKE9jxTPsALG3oLNJmYjoTAw7REni6gnAFwwDAIriNHouubKqADoBONjiwcnOnri+byKiVMewQ5QkLdHm5ByLERlGfVzfd47FhJmlOQC4lUVEdCaGHaIkcSZg7DyWdJoyr44gIupP0bCzZcsW3HjjjSgpKYEgCHjzzTf7PS+KImpra1FSUoLMzEzU1NRg3759/V7j8/nw4IMPoqCgAFarFTfddBOOHz+exL8F0fBIk1hFCQo70i3oHx5qQyAUTsjHICJKRYqGHa/XixkzZmDNmjWDPr9ixQqsXLkSa9aswY4dO+BwOLBgwQK43W75NcuWLcMbb7yBV199FVu3boXH48ENN9yAUIjnjZC69B0oGN9+Hcm0sXbkWU1w+4LY3cgRdCIiiaJh59prr8XPfvYzLFq0aMBzoihi1apVWL58ORYtWoSpU6di3bp16O7uxvr16wEALpcLL7zwAp555hnMnz8fs2bNwiuvvIJPP/0UmzZtSvZfh+isEr2NpdMJuLKqAAD7doiIYqm2Z6ehoQFOpxMLFy6UHzObzZg3bx62bdsGANi1axcCgUC/15SUlGDq1Knyawbj8/nQ1dXV740o0aSVnURtYwF9W1kMO0REfVQbdpxOJwCguLi43+PFxcXyc06nEyaTCbm5uUO+ZjD19fWw2+3yW2lpaZyrJxpImsZK1MoOAFxZFQk7+052oTV6gCERUbpTbdiRCILQ7/eiKA547Eznes0TTzwBl8slvzU1NcWlVqKzkbex4nhVxJkKssyYPs4OANhysC1hH4eIKJWoNuw4HA4AGLBC09raKq/2OBwO+P1+dHR0DPmawZjNZmRnZ/d7I0qkQCiMNk9kZSfeBwqeiVdHEBH1p9qwU1FRAYfDgY0bN8qP+f1+bN68GXPnzgUAzJ49G0ajsd9rmpub8dlnn8mvIVKDNo8PoggYdAIKrMkJOx98eQqhsJjQj0VElAoMSn5wj8eDQ4cOyb9vaGjAnj17kJeXh/Hjx2PZsmWoq6tDVVUVqqqqUFdXB4vFgsWLFwMA7HY7vvnNb+J73/se8vPzkZeXh0ceeQTTpk3D/PnzlfprEQ0g3YlVZDNDpzv7Nuz5mlmag+wMAzq7A9h7vBMXjc899x8iItIwRcPOzp07cfXVV8u/f/jhhwEA99xzD1566SU8+uij6OnpwZIlS9DR0YFLL70UGzZsgM1mk//ML3/5SxgMBtx6663o6enBNddcg5deegl6fXyP4yc6H8mYxJIY9DpcWVWI//60GZsPnGLYIaK0J4iimPbr3F1dXbDb7XC5XOzfoYRYt+0ofvLWPvzDFAeev2t2wj/eH3c04dE/fYIZpTn489LLE/7xiIiUMNzv36rt2SHSkmRMYsWaFz1v55PjnTjt9SflYxIRqRXDDlES9N2LldjmZElxdgaqHTaIYqRRmYgonTHsECVBizuxV0UMRlrd4S3oRJTuGHaIkkCaxkpm2Km5oAgAsOXLUwhzBJ2I0hjDDlESSFdFJGMaSzK7LBdWkx5tHj/2neT9b0SUvhh2iBLM4wvC4wsCSF6DMgCYDDrMrZRuQW9N2sclIlIbhh2iBJPO2MkyG5BlTu7RVrwFnYiIYYco4ZI9iRVLujpi97FOuHoCSf/4RERqwLBDlGBKTGJJxuVaUFmUhVBYxIeHeAs6EaUnhh2iBHO6Is3JSoQdIOYWdI6gE1GaYtghSrBk3os1GDnsHDwF3g5DROmIYYcowaSw41CgZwcALqnIQ4ZRB2dXLw60uBWpgYhISQw7RAmW7HuxzpRh1OOyCfkAgPe5lUVEaYhhhyjB+qaxlAk7APt2iCi9MewQJVA4LKLVrWyDMgDUTIpcHbGz8bR8wCERUbpg2CFKoHavH8GwCEEACm3K9OwAQHmBFWX5FgRCIrZxBJ2I0gzDDlECSc3J+VYzjHpl/3eLncoiIkonDDtECSRPYtmVW9WRxF4dwRF0IkonDDtECSRPYinYryP5yoR8mPQ6HO/oweFTXqXLISJKGoYdogRSwySWxGIy4JKKPADcyiKi9MKwQ5RALV3KT2LF4i3oRJSOGHaIEkhN21hAX5PyR0fa0eMPKVwNEVFyMOwQJVDfvVjKNygDQGVRFsbmZMIfDOOjhnalyyEiSgqGHaIEalH4qogzCYKAq3iaMhGlGYYdogTpDYTQ0R0AoJ5tLIDn7RBR+mHYIUqQ1mhzssmggz3TqHA1fS6vzIdBJ6ChzYvGdo6gE5H2MewQJUiLu685WRAEhavpY8swYnZZLgCu7hBRemDYIUoQp0tdk1ix5k1i3w4RpQ+GHaIEUdskVqyaCyK3oG873A5fkCPoRKRtDDtECdKisjN2Yk0eY0OhzYyeQAg7GjqULoeIKKEYdogSxCmdnqySsfNYgiDETGW1KlwNEVFiMewQJYia7sUaDK+OIKJ0wbBDlCCx01hqdEVlAXQCcLDFg5OdPUqXQ0SUMAw7RAkgiqKqp7EAIMdiwszSHABc3SEibWPYSbBwWIQ/GFa6DEoyV08Avui/dzVOY0lqJkWmsjiCTkRaxrCTQLVv7cOUn7yNP+5sUroUSrKWaHNyjsWIDKNe4WqGJjUpf3ioDYEQQzkRaRPDTgIZ9QJ6AiEcavUoXQolmVPFY+expo21I89qgtsXxO5GjqATkTYx7CRQZVEWAODwKYaddKP2SSyJTifgqqoCAMD77NshIo1i2EkgKexwZSf99B0oqN5+HQmvjiAirWPYSaDKQhsAoNnVC48vqHA1lEypso0FAFdVFUIQgM+bu9AarZuISEsYdhLIbjGiICvyk/1hru6klb57sdQfdvKzzJg21g6AI+hEpE0MOwlWWWQFwK2sdCNNY6XCyg6AmKsjGHaISHsYdhJM7tthk3JakbexVHgv1mCkqyM++LINobCocDVERPHFsJNglYVsUk43gVAYbZ7Iyo6aDxSMNWNcDrIzDHD1BLCnqVPpcoiI4ophJ8EqiyJNyuzZSR9tHh9EETDoBBRYUyPsGPQ6XFnFrSwi0iaGnQSTtrEaT3fz2og0Id2JVWQzQ6cTFK5m+ObxFnQi0iiGnQQrzjYjy2xAKCziaLtX6XIoCVJpEiuW1KT8yfFOtEe34YiItEDVYScYDOKHP/whKioqkJmZiQkTJuDJJ59EONy3QiKKImpra1FSUoLMzEzU1NRg3759ClbdnyAImMjDBdNKqk1iSYqzM1DtsEEUga2H2pQuh4goblQddp5++mk8//zzWLNmDfbv348VK1bgF7/4BVavXi2/ZsWKFVi5ciXWrFmDHTt2wOFwYMGCBXC73QpW3h+blNNLqk1ixeIt6ESkRaoOO3//+9/xta99Dddffz3Ky8txyy23YOHChdi5cyeAyKrOqlWrsHz5cixatAhTp07FunXr0N3djfXr1ytcfR9eG5Fe+u7FSo3m5FjSVhZXdohIS1Qddq644gq88847OHjwIABg79692Lp1K6677joAQENDA5xOJxYuXCj/GbPZjHnz5mHbtm1Dvl+fz4eurq5+b4nEsJNeWtypc1XEmWaU2iEIQKvbJ4/PExGlOoPSBZzNY489BpfLherqauj1eoRCITz11FO4/fbbAQBOpxMAUFxc3O/PFRcXo7Gxccj3W19fj3/5l39JXOFnkMLOkTYPwmExpSZ0aOSkaaxUDDsWkwFleRYcbe/GAacbBZWptzpFRHQmVa/svPbaa3jllVewfv167N69G+vWrcO//uu/Yt26df1eJwj9w4MoigMei/XEE0/A5XLJb01NTQmpX1KamwmTXofeQBgnOnsS+rFIeVKDcqpNY0mqHdkAgC+c6ul7IyI6H6pe2fn+97+Pxx9/HLfddhsAYNq0aWhsbER9fT3uueceOBwOAJEVnjFjxsh/rrW1dcBqTyyz2QyzOXk/sRr0OlQUWHGgxY1DrR6U5lmS9rEpuTy+oHzDfSo2KAPAJIcNf9vnxBfNid3eJSJKFlWv7HR3d0On61+iXq+XR88rKirgcDiwceNG+Xm/34/Nmzdj7ty5Sa31XNi3kx6kM3ayzAZkmVX9s8SQJo+JnPp9oIUrO0SkDar+anzjjTfiqaeewvjx4zFlyhR8/PHHWLlyJe677z4Ake2rZcuWoa6uDlVVVaiqqkJdXR0sFgsWL16scPX98ayd9JDKk1gSaRvrgNONUFiEnj1mRJTiVB12Vq9ejR/96EdYsmQJWltbUVJSgm9/+9v48Y9/LL/m0UcfRU9PD5YsWYKOjg5ceuml2LBhA2w2m4KVD8Tbz9NDKk9iScbnWZBp1KMnEEJjuxcToudEERGlKlWHHZvNhlWrVmHVqlVDvkYQBNTW1qK2tjZpdY1G7MGC52qgptTldKXm6cmxdDoBFzhs2NvUiS+cboYdIkp5qu7Z0ZIJhVYIAuDqCaDN41e6HEqQVL0X60zVxZGVUTYpE5EWMOwkSYZRj9LcyBQW+3a0Swo7jhTu2QGA6miTMsfPiUgLGHaSiH072pfK92LFmuRg2CEi7WDYSSIp7Bzmyo5m9U1jpXbYkSayjp3uhjd6bhARUapi2Eki3n6ubeGwiFZ36jcoA0Ce1YQiW2QrjuftEFGqY9hJIp61o23tXj+CYRGCABTaUrtnBwCqx/Sdt0NElMoYdpJI2sZydvXC3RtQuBqKN6k5Od9qhlGf+v9rTXZwIouItCH1vyKnEHumUf6J//Apr8LVULzJk1j21F/VAdikTETawbCTZOzb0S55EivF+3Uksbefi6KocDVERKPHsJNkvBBUu7QyiSWZWGSFXifA1ROQgxwRUSpi2Ekyhh3taunSxiSWxGzQY2KhFQC3sogotTHsJJl81g4PFtQcrW1jAcAkaSurmWGHiFIXw06SSWGnsd0LXzCkcDUUT333YmmjQRkAqqNNygecnMgiotTFsJNkRTYzbGYDwiJwtK1b6XIojlo0clVErGpOZBGRBjDsJJkgCDxcUIN6AyF0dEfOTtLSNpZ0sODhUx74g2GFqyEiGh2GHQWwSVl7WqPNySaDDvZMo8LVxE+JPQO2DAMCIRFH2vjfKxGlJoYdBfD2c+1pcfc1JwuCoHA18SMIQkzfDreyiCg1MewogAcLao/Tpb1JLIl0uOB+TmQRUYpi2FGAtLJz5JQHoTBPptUCLU5iSfqujeBEFhGlJoYdBZTmWWAy6OALhnGio0fpcigOWjR4xo5k8hhuYxFRamPYUYBeJ2BCQeRk2kOn+A1EC5zS6ckaGjuXXFAcCTvNrl64ohNnRESphGFHIRw/1xat3YsVy5ZhxLjcTADcyiKi1MSwoxA2KWtL7DSWFvFwQSJKZQw7CuFZO9ohiqKmp7GAvokshh0iSkUMOwqJDTuiyImsVObqCcAXPV1Yi9NYAFA9hhNZRJS6GHYUUlFghU4AunqDOOXxKV0OnYeWaHNyjsWIDKNe4WoSI/ZgwTCPSyCiFDOqsNPU1ITjx4/Lv9++fTuWLVuGtWvXxq0wrcsw6lGaZwHAraxU59Tw2LmkPN8Kk0GHbn8Ix3lcAhGlmFGFncWLF+O9994DADidTixYsADbt2/HD37wAzz55JNxLVDLpCblwww7KU3Lk1gSg16HqujW635uZRFRihlV2Pnss89wySWXAAD++Mc/YurUqdi2bRvWr1+Pl156KZ71aRqblLWh70BBbfbrSKQmZR4uSESpZlRhJxAIwGyOfGHftGkTbrrpJgBAdXU1mpub41edxk3khaCakA7bWEDs+DlXdogotYwq7EyZMgXPP/88PvjgA2zcuBH/8A//AAA4efIk8vPz41qglnFlRxv67sXSeNgZw7N2iCg1jSrsPP300/jNb36Dmpoa3H777ZgxYwYA4K233pK3t+jcpLDT0uVDVy+P4U9V0jSW1ld2pAtBj7Z50RsIKVwNEdHwGUbzh2pqatDW1oauri7k5ubKj3/rW9+CxWKJW3Fal51hRJHNjFa3D4dbPZg1Pvfcf4hUR97G0uC9WLEKs8zIt5rQ7vXjyxYPpo2zK10SEdGwjGplp6enBz6fTw46jY2NWLVqFQ4cOICioqK4Fqh13MpKbYFQGG3Rc5K0eqCgRBAEeSuLE1lElEpGFXa+9rWv4fe//z0AoLOzE5deeimeeeYZ3HzzzXjuuefiWqDWVbJJOaW1eXwQRcCgE1Bg1XbYAYBJxdFrI5rZt0NEqWNUYWf37t248sorAQD/+Z//ieLiYjQ2NuL3v/89fv3rX8e1QK2Twg7P2klN0p1YRTYzdDpB4WoST1rZOdDClR0iSh2jCjvd3d2w2SJf9DZs2IBFixZBp9PhK1/5ChobG+NaoNbx9vPUli6TWBJ5/JwrO0SUQkYVdiorK/Hmm2+iqakJb7/9NhYuXAgAaG1tRXZ2dlwL1DppZefY6W5OuKSgdJnEklQV2aATgHavH6fcvNONiFLDqMLOj3/8YzzyyCMoLy/HJZdcgssuuwxAZJVn1qxZcS1Q6wptZtgyDAiLwNF2r9Ll0AilyySWJNOkR3m+FQAPFySi1DGqsHPLLbfg2LFj2LlzJ95++2358WuuuQa//OUv41ZcOhAEgRNZKazvXiztNydL5L4dHi5IRCliVGEHABwOB2bNmoWTJ0/ixIkTAIBLLrkE1dXVcSsuXbBvJ3W1uNPjqohY0kTWfvbtEFGKGFXYCYfDePLJJ2G321FWVobx48cjJycHP/3pTxEOh+Ndo+ZxZSd1SdNY6RR2OJFFRKlmVCcoL1++HC+88AJ+/vOf4/LLL4coivjwww9RW1uL3t5ePPXUU/GuU9MYdlKX1KCcLtNYADA5evv5wRYPgqEwDPpRLxATESXFqMLOunXr8O///u/ybecAMGPGDIwdOxZLlixh2BkhKewcafMiFBahT4PzWrTA4wvC4wsCSJ8GZQAYl5sJi0mPbn8IR9u9qCyyKV0SEdFZjepHstOnTw/am1NdXY3Tp0+fd1HpZlyuBSaDDv5gGMc7upUuh4ZJOmMny2xAlnlUPzekJJ1OkC8F5Q3oRJQKRhV2ZsyYgTVr1gx4fM2aNZg+ffp5F5Vu9DoBEwoi47zcykod6TiJJeHhgkSUSkb14+iKFStw/fXXY9OmTbjssssgCAK2bduGpqYm/M///E+8a0wLlUVZ+MLpxqFWD66ZXKx0OTQM6TiJJamO9u1wZYeIUsGoVnbmzZuHgwcP4h//8R/R2dmJ06dPY9GiRdi3bx9efPHFuBZ44sQJ3HnnncjPz4fFYsHMmTOxa9cu+XlRFFFbW4uSkhJkZmaipqYG+/bti2sNycAm5dTjdKXX6cmx+raxOJFFROo36kaDkpKSAY3Ie/fuxbp16/C73/3uvAsDgI6ODlx++eW4+uqr8de//hVFRUU4fPgwcnJy5NesWLECK1euxEsvvYQLLrgAP/vZz7BgwQIcOHBAvr8rFfD289STbvdixZK2sY539MDdG4Atw6hwRUREQ1N1V+XTTz+N0tLSfqtF5eXl8q9FUcSqVauwfPlyLFq0CEBkUqy4uBjr16/Ht7/97UHfr8/ng8/Xd69PV5fyP53GruyIoghB4ESW2klhx5GGPTs5FhMc2RlwdvXiYIsbs8vylC6JiGhIqj4g46233sKcOXPw9a9/HUVFRZg1axZ++9vfys83NDTA6XTKF5ECgNlsxrx587Bt27Yh3299fT3sdrv8VlpamtC/x3BUFFihEwB3b5AXLKaIdLsX60zS4YLs2yEitVN12Dly5Aiee+45VFVV4e2338b999+P73znO/j9738PAHA6nQCA4uL+Db3FxcXyc4N54okn4HK55LempqbE/SWGyWzQY3yeBQD7dlJF3zRWmoYdqUmZE1lEpHIj2saStoqG0tnZeT61DBAOhzFnzhzU1dUBAGbNmoV9+/bhueeew9133y2/7swtn3NtA5nNZpjN6tt6qCzKwtH2bhw65cHcygKly6GzCIdFtLrTt0EZiBk/Z5MyEanciFZ2Yrd+BnsrKyvrF0LO15gxY3DhhRf2e2zy5Mk4duwYgMhlpAAGrOK0trYOWO1JBRM5kZUy2r1+BMMiBAEotKkvOCdD7DaWKIoKV0NENLQRrezEe6z8XC6//HIcOHCg32MHDx5EWVkZAKCiogIOhwMbN27ErFmzAAB+vx+bN2/G008/ndRa44G3n6cOqTk532qGMU3vhppQkAWDToC7N4iTrl6MzclUuiQiokGp+qv0d7/7XXz00Ueoq6vDoUOHsH79eqxduxZLly4FENm+WrZsGerq6vDGG2/gs88+w7333guLxYLFixcrXP3I8ayd1CFPYtnTc1UHAEwGnfzf7AFuZRGRiql69Pziiy/GG2+8gSeeeAJPPvkkKioqsGrVKtxxxx3yax599FH09PRgyZIl6OjowKWXXooNGzak1Bk7Emkbq9XtQ1dvANk8u0S15EmsNO3XkUxy2PCF0439zW78n+rU2zomovSg6rADADfccANuuOGGIZ8XBAG1tbWora1NXlEJkp1hRHG2GS1dPhxq9eCi8blKl0RDSPdJLEm1Ixt/xkkc4Pg5EamYqrex0hG3slJDS1d6T2JJOJFFRKmAYUdlpCblwww7qsZtrAhpIuvwKS98wZDC1RARDY5hR2W4spMa+u7FSt8GZSAS9uyZRoTCIg63epUuh4hoUAw7KjORF4KmhJY0vypCIggCb0AnItVj2FEZaWWn6XQ3egPcFlCj3kAIHd0BANzGAoDJ0bDDJmUiUiuGHZUpzDIjO8OAsAg0tHFbQI1ao83JJoMO9kweDzApekfWfoYdIlIphh2VEQSBfTsq1+Lua04+2x1s6UJqUubBgkSkVgw7KsSwo25OFyexYl1QHAk7LV0+dHj9CldDRDQQw44KVbJJWdWk5uTiNG9OlmSZDRifZwEQuRSUiEhtGHZUSAo7PGtHneSwk6a3nQ+GE1lEpGYMOypUWRj5xnGkzYtQWFS4GjqTUzo9mSs7Mmki64tmruwQkfow7KjQ2NxMmA06+INhNJ3uVrocOoN0L1Yxe3Zk1WMiE1lftDDsEJH6MOyokF4nYEIhm5TVSprGYtjpI21jHXS6EeZqJBGpDMOOSrFJWZ1EUeQ01iDK860wG3ToCYRwjKuRRKQyDDsqVcmVHVVy9QTgC4YB8F6sWHqdII+gs0mZiNSGYUeleNaOOrVEm5NzLEZkGPUKV6Mu1fJEFvt2iEhdGHZUKnb8XBTZA6EWzi5uYQ1lEieyiEilGHZUqrzAAp0AuH1BtLp9SpdDUZzEGtrk6ETWAU5kEZHKMOyolNmgR1m+FQC3stREPlCQ/ToDSCs7R9u96PYHFa6GiKgPw46KTWSTsupwG2toBVlmFGSZIYrAwRb+N0tE6sGwo2JsUlYf3ot1dpN5AzoRqRDDjoox7KiPNI1VbGPYGcyk6Pj5fjYpE5GKMOyoGA8WVB95G4srO4OSro04wPFzIlIRhh0Vm1gYaVA+5fbB1RNQuBoKhMJo80RXdtizM6jqmNvPeWQCEakFw46K2TKMciMst7KU1+bxQRQBg05AvtWkdDmqVFmUBZ0AdHQHcIpHJhCRSjDsqFzs4YKkLOlOrCKbGTqdoHA16pRh1KOiILIiuZ9bWUSkEgw7Kse+HfXgJNbwSH07XzRzIouI1IFhR+UmciJLNTiJNTzVxdL4OVd2iEgdGHZUjrefqwcnsYZHWtnhNhYRqQXDjspJ21hNHd3oDYQUria98V6s4ZEmsg63ehAIhRWuhoiIYUf1CrJMsGcaIYrAkVNepctJay1u3os1HONyM5FlNsAfCqOhjf/NEpHyGHZUThAENimrhDSNxXuxzk4QBPlS0C+4lUVEKsCwkwLYt6MOcoMye3bOSQ47nMgiIhVg2EkBPGtHeR5fEB5fEAB7doZjsoMTWUSkHgw7KYAXgipPOmMny2xAltmgcDXqN8kRPWuHYYeIVIBhJwVIYaehzYsgp1sU0TeJxebk4ZC2sU509vBeNyJSHMNOChibk4kMow7+UBhNHT1Kl5OW+iaxuIU1HPZMI0qivU0HW7i6Q0TKYthJATqdgAkF3MpSktMVaU7mJNbw8doIIlILhp0Uwb4dZfFerJGr5vg5EakEw06KYNhRlhx2bOzZGS6etUNEasGwkyJ4sKCyeC/WyE2ObmMdcLohiqLC1RBROmPYSRGxZ+3wG0fy8V6skasosMKoF+DxBXGcjfVEpCCGnRRRnm+FXhf5xiGd5EvJEQ6LaHVHT09m2Bk2o16HyiIeLkhEymPYSREmgw5leRYA7NtJtnavH8GwCEEACtmzMyJ9TcqcyCIi5TDspJCJcpMyf0pOJqk5uSDLDKOe/8uMhBR29nNlh4gUxK/cKYRNysqQJ7F4evKITeIdWUSkAikVdurr6yEIApYtWyY/JooiamtrUVJSgszMTNTU1GDfvn3KFZlAvP1cGfIkFvt1RkyayGpo86I3EFK4GiJKVykTdnbs2IG1a9di+vTp/R5fsWIFVq5ciTVr1mDHjh1wOBxYsGAB3G7t/STZd9aOV+FK0gsnsUavyGZGrsWIUFhkSCcixaRE2PF4PLjjjjvw29/+Frm5ufLjoihi1apVWL58ORYtWoSpU6di3bp16O7uxvr16xWsODGknp02jw+ubl6umCzS9BvDzsgJgsDDBYlIcSkRdpYuXYrrr78e8+fP7/d4Q0MDnE4nFi5cKD9mNpsxb948bNu2bcj35/P50NXV1e8tFWSZDRgTPdTu0Cl+40gWbmOdn2qHdLhgavx/RkTao/qw8+qrr2L37t2or68f8JzT6QQAFBcX93u8uLhYfm4w9fX1sNvt8ltpaWl8i04gXhuRfLwX6/zwjiwiUpqqw05TUxMeeughvPLKK8jIGPobjSAI/X4viuKAx2I98cQTcLlc8ltTU1Pcak60iWxSTjpOY50f+fZzhh0iUohB6QLOZteuXWhtbcXs2bPlx0KhELZs2YI1a9bgwIEDACIrPGPGjJFf09raOmC1J5bZbIbZnJrfuLiyk1y9gRA6ov1R3MYanQuKsyAIwCm3D20eHwqyUvP/PSJKXape2bnmmmvw6aefYs+ePfLbnDlzcMcdd2DPnj2YMGECHA4HNm7cKP8Zv9+PzZs3Y+7cuQpWnjg8aye5WqPNyWaDDvZMo8LVpCaLySCf/s3zdohICape2bHZbJg6dWq/x6xWK/Lz8+XHly1bhrq6OlRVVaGqqgp1dXWwWCxYvHixEiUnnBR2jnf0oDcQQoZRr3BF2tbi7hs7P9vWKJ3dJIcNR9u78YXTjcsrC5Quh4jSjKrDznA8+uij6OnpwZIlS9DR0YFLL70UGzZsgM1mU7q0hMi3mpBjMaKzO4DDpzyYUmJXuiRNc7o4iRUP1Y5svL2vBV80cyKLiJIv5cLO+++/3+/3giCgtrYWtbW1itSTbIIgoLIwCzsbO3ColWEn0TiJFR+Tx0SvjWjhNhYRJZ+qe3ZocNJW1mE2KSecHHZ42/l5mSSfteNGKCwqXA0RpRuGnRTEJuXkcUYblB1c2Tkv4/MsyDTq4QuG0djO606IKLkYdlLQRI6fJw3vxYoPvU7ABcWR/2553g4RJRvDTgqSbj9vaPMiGAorXI22xU5j0fmRro1gkzIRJRvDTgoam5OJTKMegZCIY6e7lS5Hs0RR5DRWHPFCUCJSCsNOCtLpBEwotALgVlYiuXoC8AUjK2dFvCrivFWPYdghImUw7KQoNiknXku0OTnHYuThjXEgbWMdO90Nry+ocDVElE4YdlJUJS8ETThnF7ew4inPakJRdISf5+0QUTIx7KQonrWTeJzEij/pBnTekUVEycSwk6LksHPKC1HkIW2JIB8oyH6duKmWmpQ5kUVEScSwk6LK8q3Q6wR4fEF5u4Xii9tY8VfNiSwiUgDDTooyGXQoy7cAYN9OovBerPiLHT/niiQRJQvDTgqTmpS/bGHYSQRpGqvYxrATL5VFWdDrBLh6AlyRJKKkYdhJYRw/Tyx5G4srO3FjNugxoSByRhS3sogoWRh2Ulgl78hKmEAojDZPdGWHPTtxJU1kfdHMsENEycGwk8I4fp44bR4fRBEw6ATkW01Kl6MpUpPyAScnsogoORh2UtjEaM9Ou9ePDq9f4Wq0RboTq8hmhk4nKFyNtnAii4iSjWEnhVnNBpRE+0nYtxNfnMRKHGkb6/ApD/zRu8eIiBKJYSfFTWTfTkJwEitxSuwZsGUYEAiJONLG/26JKPEYdlIcm5QTg5NYiSMIQkzfDreyiCjxGHZSHMNOYvBerMSSDhfcz4ksIkoChp0Ux9vPE6PFzXuxEqnaER0/50QWESUBw06Kk1Z2TnT2oNsfVLga7ZCmsXgvVmJwG4uIkolhJ8XlZ5mRazECAI6c8ipcjXbIDcrs2UmIC6Jhp9nVC1d3QOFqiEjrGHY0gH078eXxBeHxRVbJ2LOTGNkZRozNyQTArSwiSjyGHQ1g2Ikv6YydLLMBWWaDwtVo1+QxPFyQiJKDYUcDJrJJOa76JrHYnJxIfU3KDDtElFgMOxrA28/jq28Si1tYiTRJvjaC21hElFgMOxoghZ2jbV4EQjx+/3w5XZHmZE5iJZa0jXXA6UY4LCpcDRFpGcOOBpTYM5Fp1CMYFtHY3q10OSmP92IlR3m+FSaDDt3+EI539ChdDhFpGMOOBuh0AiYWWQGwbyce5LBjY89OIhn0OlRFVyX3cyuLiBKIYUcjpJOUD7Nv57zxXqzkmcTDBYkoCRh2NILj5/HDe7GSZzKvjSCiJGDY0QiGnfgIh0W0uqOnJzPsJFzfRBZXdogocRh2NEIKO4dPeTjZch7avX4EwyIEAShkz07CVUcnso62edEbCClcDRFpFcOORpTlW2HQCej2h9Ac7TmhkZOakwuyzDDq+b9HohVmmZFvNSEsAl+2cFWSiBKDX801wqjXoSzfAoBbWedDnsTi6clJIQiCvJXFiSwiShSGHQ1h3875kyex2K+TNPK1Ec3s2yGixGDY0RCGnfPHSazkq5bGz1u4skNEicGwoyFykzLDzqi1dHESK9mkJmWu7BBRojDsaEhlYeSbBi8EHT1uYyVfVZENghCZhDsVHfsnIoonhh0Nka6MOO3147TXr3A1qYn3YiVfpkmPivzIf7s8XJCIEoFhR0MsJgPG5mQCYN/OaHEaSxm8NoKIEolhR2Mmskl51HoDIXR0BwBwGyvZpIms/ezbIaIEYNjRGOlCUIadkWuNNiebDTrYM40KV5NepCZlTmQRUSIw7GiMPH7OJuURa3H3jZ0LgqBwNelFGj8/2OJBMBRWuBoi0hqGHY3h+PnoOV2cxFJKaa4FFpMe/mAYR9u9SpdDRBqj6rBTX1+Piy++GDabDUVFRbj55ptx4MCBfq8RRRG1tbUoKSlBZmYmampqsG/fPoUqVp4Udk509sDrCypcTWrhJJZydDoBFxTzBnQiSgxVh53Nmzdj6dKl+Oijj7Bx40YEg0EsXLgQXm/fT34rVqzAypUrsWbNGuzYsQMOhwMLFiyA252eXzDzrCbkWU0AgCOn+BPySMhhh7edK2IyDxckogQxKF3A2fztb3/r9/sXX3wRRUVF2LVrF6666iqIoohVq1Zh+fLlWLRoEQBg3bp1KC4uxvr16/Htb39bibIVV1mYhe3e0zh0yo1p4+xKl5MynNEGZQdXdhQxiSs7RJQgql7ZOZPL5QIA5OXlAQAaGhrgdDqxcOFC+TVmsxnz5s3Dtm3bhnw/Pp8PXV1d/d60hOPno8N7sZRVPSZ6ISgPFiSiOEuZsCOKIh5++GFcccUVmDp1KgDA6XQCAIqLi/u9tri4WH5uMPX19bDb7fJbaWlp4gpXAC8EHZ3YaSxKPmki63hHD9y9AYWrISItSZmw88ADD+CTTz7Bf/zHfwx47swxYVEUzzo6/MQTT8DlcslvTU1Nca9XSQw7IyeKIqexFJZjMcmf+4Mt3MoiovhJibDz4IMP4q233sJ7772HcePGyY87HA4AGLCK09raOmC1J5bZbEZ2dna/Ny2Rwk5jezcCPLNkWFw9AfiCkc9VEa+KUIx8Azr7dogojlQddkRRxAMPPIDXX38d7777LioqKvo9X1FRAYfDgY0bN8qP+f1+bN68GXPnzk12uapRYs+AxaRHMCyikWeWDEtLtDk5x2JEhlGvcDXpS7ojixNZRBRPqg47S5cuxSuvvIL169fDZrPB6XTC6XSip6cHQGT7atmyZairq8Mbb7yBzz77DPfeey8sFgsWL16scPXKEQQBE3ltxIg4u7iFpQaTHWxSJqL4U/Xo+XPPPQcAqKmp6ff4iy++iHvvvRcA8Oijj6KnpwdLlixBR0cHLr30UmzYsAE2my3J1apLZVEWPj3hYtgZJk5iqYO8suN0n7P3johouFQddkRRPOdrBEFAbW0tamtrE19QCmGT8sjIBwqyX0dREwuzYNAJcPcGcdLVi7E5mUqXREQaoOptLBo9eRuLF4IOC7ex1MFk0Mn/7R7gVhYRxQnDjkb1XQjqRTh87hWydMd7sdRDmsjazyZlIooThh2NKsu3wKAT0BMI4aSrR+lyVE+axiq2MewoTerbOcDxcyKKE4YdjTLqdSgvsAJg385wyNtYXNlRHCeyiCjeGHY0rJLj58MSCIXR5omu7LBnR3HSNtbhU174giGFqyEiLWDY0TC5b4dNymfV5vFBFAGDTkC+1aR0OWnPkZ2B7AwDQmERh1t5KCYRnT+GHQ3j+PnwSHdiFdnM0Ol4rovSBEHgDehEFFcMOxrGsDM8nMRSn2o2KRNRHDHsaNiEwkiDckd3AO3RnhQaSFrZ4SSWelRHm5T3M+wQURww7GiYxWSQT6Dl6s7QWtyRIMhJLPXoGz/nNhYRnT+GHY2Tt7LYpDwk3oulPlLYaenyocPrV7gaIkp1DDsax76dc3PyXizVyTIbUJoXWZX8gltZRHSeGHY0jmHn3Fp4L5YqSX07m/a3cHWHiM6Lqm89p/PXd0cWw85Q5Ksi2LOjKlNKsrHx8xa8sLUBL2xtQFVRFuaU52FOWS4uLs9DaV4mBIFHBRDRuTHsaJx0ivJJVy+8viCsZv4rj+XxBeHxBQGwZ0dt7vpKGU65ffjoSDsOn/Liy1YPvmz14D+2HwMQORfp4vI8zCmPhJ9qhw0GPReriWggfufTuFyrCflWE9q9fhw+5cH0cTlKl6Qq0hZWltmALAZBVcnPMuOpf5wGAGj3+LCrsQM7Gzuw4+hpfHbChVa3D//9aTP++9NmAIDVpMdFZbmYU5aHi8tzMXN8Diwm/jslIoadtDCxKAvtDadxqJVh50x9k1hsTlaz/CwzFk5xYOEUBwCgxx/C3uOd2Hn0NHYc7cDuxg64fUF88GUbPviyDQCg1wmYWpKNOeWR8DO7LA+FNv57JkpHDDtpoLIoC9ujYYf665vE4hZWKsk06fGVCfn4yoR8AEAoLOJgi1sOPzuOnkazqxd7j7uw97gLL2xtAABUFFjlnp855bmoKLCy74coDTDspAHefj40qTmZk1ipTa8TMHlMNiaPycZdl5UDAE509kTDz2nsPNqBAy1uNLR50dDmxf/bdRwAkG81yT0/c8rzMKUkG0b2/RBpDsNOGuDBgkPjvVjaNTYnE2NnjsXXZo4FALi6A9h9rEMOP3uOd6Ld68fb+1rw9r4WAECGUYdZpbm4uDwXc8rzMGt8DmwZRiX/GkQUBww7aUAKO43t3fh/O5tUsWxfkpOBaWPtin8j6bsXi70cWme3GHF1dRGuri4CAPiCIXx2okve+trZeBqd3QH8/Ug7/n6kHQCgE4DJY7IxpywXs8vzMHNcDkfeiVIQw04aGGPPgM1sgNsXxPf/8xOly5EJAjCxMAvTx9kxY1wOZpTmYPIYG8wGfdJqaHFHDxTkyk7aMRv0mF2Wi9llufj2PCAcFnGkzSP3/Ow82oFjp7ux72QX9p3swrq/NwIAci1GTB+Xgxnj7Jg+LgfTS+0o4iWyRKomiKIoKl2E0rq6umC32+FyuZCdna10OQnxn7uO47/2nlS6DABAWBRx5JQXJzp7Bjxn1AuodmRHAlBpDmaMy0FlURb0usT8JD23/h2cdPXijSVzMWt8bkI+BqWulq5e7Iyu+uxu7MD+Zjf8ofCA15XYM+TgM3NcDqaOsyOb21+UgkJhEb5gCL5AGL3Rf/qCYfQGQvAFw+d8rjcQfU3s49HHvnlFBWomFcW13uF+/2bYQXqEHTU65fbhk+Od2HvchU+Od+KT4y6cHuRaAItJj6lj7fJP0jPitJUQDou44Id/RTAsYtvj/wcl0RviiYbiC4ZwwOmOTHk1deKT4534stWDwb6KTii0RlYsx9kxvTQHF47JRoYxeauWpH3hsAh3bxAd3X50dPvR2R2I/jqAzujvewJnBo8QeqP/9AcHhpVAKHGR4Gc3T8WdXymL6/tk2BkBhh11EEURxzt6sDcafPY2deLTEy50+0MDXhu7lTCjNAfTx+WM+AyVU24fLn5qEwQBOPizazmFQ6Pi8QXx2QlXv+DedHrgqqVBJ2CSwxZdsYwE96qiLJ76TACA3kCoX2Dp909vX4CJfc7VE0A4gd/BjXoBZoMeZoMOZoMOGUY9TAYdzEY9MqL/lB6PfU3k13pkGCOPmY2RX88Yl4MJ0engeGHYGQGGHfUKhUUcPuWJ/hTtwt7jndjf3DXoTx/SVoL0zeRcWwmfnXDhhtVbUWgzY8fy+Yn8a1Caaff48MkJFz5pckXDeyfaPANXLTONekwdmx3ZAhtnx8zSHIzPs6RMA3QwFGZYO0M4LMLVM3CFZbAgExtgegMDt0eHy2LSI9diQo7FOOCfFpMhGjh0yDDoYTbGBpEzwkrMa0x6XUr8u2XYGQGGndTiC4bwRbMbnxzvxJ6myE/Sh04NvpUwMbqVIPUATY7ZStj0eQv+v9/vxNSx2fjLg1cm+W9B6UQURZx09WJvU2ck/DS58OkJl3wvW6wcixHTxvY17c8YZ0dRHM+BCodFdAdC8PQG4fEF4O6N3A/nkf4Z82v3EI9Lv/aHwsizmlCWb0FFvhVl+VaUF1hQlm9FRb4Vdos2+5ZCYREnO3vQ2N6No+1eNLZ70dDWjcZ2LxpPd8MfHF1w0esE5GQaY8KKCbkWI3KtfQEm12KMPh55LMdiTOpQh9ow7IwAw07q8/iC+DSm92fv8U4c7xh8K6F6jA3Tx+XA6wviz3tOYv7kIvz7PRcrUDWlM2n6a29T3xbY5ye7Bm2AdmRn9Gvaz7OaoqHj7GHF4wv2e97rC8LjDw76g0Ei5FiMkQCUb0F5TBAqz7ci12JU9QpWMBTGyc7eAWHmaLsXTad7Bv33FCvLbJDDSGxwkQOMJTbAmJBjNcJmNqj6c6JGDDsjwLCjTe0enxx8pB6g9kEaoO+4dLx84SSRkvzBcLQBulPeuv2y1Z2Qvgy9ToAtwyBfgptlNiAr+vu+x43Rx/Qxv+573mzQwdnVi6NtfSscR9u7cbTNi1a376wfPzvDgPIC66BhKN9qSso3/UAojBMdPWho96KxLVK79HdoOt2N4Fk+8Sa9DqV5mSiPWdEqjwY5hz0DJoP6t4C0gGFnBBh20oMoijjR2SMHoL1NnWjt8qF+0TRcGr1jiUhtvL4g9p3skrfAPj3hQo8/1C+cSL+2mQ2wxvw68rxRDihWc19YMRt0CQ0U3f4gGmPCw9E2bzQQdaM5epjnUGxmA8rkVaDotliBFWX5FhRmmUdUtz8YRlNHtI7o6kxDtK7jHT0InS3QGHQoy5M+ft+qVFm+BSU5mQk7EoOGj2FnBBh2iIiSpzcQ6tfvIoWhxvZunHT1nHWbzWLSnxE+Iv/MtZjQdDryPqVQdbTdixMdPWddGcsw6uQAI6/S5FtQXmCFIzsDOgYaVRvu92+eoExEREmVYdRjksOGSQ7bgOd6AyEc7+iWt8bODC7d/hD2N3dhf3PXsD+eFJCkECMFpPJ8K4psZgaaNMCwQ0REqpFh1KOyyIbKooFByBcM4XhHz6BbUh1eP0rzBoaZ8oKRb32R9jDsEBFRSjAb9JhYmIWJcT6YjrSP7eJERESkaQw7REREpGkMO0RERKRpDDtERESkaQw7REREpGkMO0RERKRpDDtERESkaQw7REREpGkMO0RERKRpDDtERESkaQw7REREpGkMO0RERKRpDDtERESkaQw7REREpGkGpQtQA1EUAQBdXV0KV0JERETDJX3flr6PD4VhB4Db7QYAlJaWKlwJERERjZTb7Ybdbh/yeUE8VxxKA+FwGCdPnoTNZoMgCHF7v11dXSgtLUVTUxOys7Pj9n5THT8vA/FzMjh+Xgbi52Qgfk4Glw6fF1EU4Xa7UVJSAp1u6M4cruwA0Ol0GDduXMLef3Z2tmb/Qzsf/LwMxM/J4Ph5GYifk4H4ORmc1j8vZ1vRkbBBmYiIiDSNYYeIiIg0jWEngcxmM37yk5/AbDYrXYqq8PMyED8ng+PnZSB+Tgbi52Rw/Lz0YYMyERERaRpXdoiIiEjTGHaIiIhI0xh2iIiISNMYdoiIiEjTGHYS6Nlnn0VFRQUyMjIwe/ZsfPDBB0qXpJj6+npcfPHFsNlsKCoqws0334wDBw4oXZaq1NfXQxAELFu2TOlSFHfixAnceeedyM/Ph8ViwcyZM7Fr1y6ly1JMMBjED3/4Q1RUVCAzMxMTJkzAk08+iXA4rHRpSbVlyxbceOONKCkpgSAIePPNN/s9L4oiamtrUVJSgszMTNTU1GDfvn3KFJskZ/ucBAIBPPbYY5g2bRqsVitKSkpw99134+TJk8oVrBCGnQR57bXXsGzZMixfvhwff/wxrrzySlx77bU4duyY0qUpYvPmzVi6dCk++ugjbNy4EcFgEAsXLoTX61W6NFXYsWMH1q5di+nTpytdiuI6Ojpw+eWXw2g04q9//Ss+//xzPPPMM8jJyVG6NMU8/fTTeP7557FmzRrs378fK1aswC9+8QusXr1a6dKSyuv1YsaMGVizZs2gz69YsQIrV67EmjVrsGPHDjgcDixYsEC+/1CLzvY56e7uxu7du/GjH/0Iu3fvxuuvv46DBw/ipptuUqBShYmUEJdccol4//3393usurpafPzxxxWqSF1aW1tFAOLmzZuVLkVxbrdbrKqqEjdu3CjOmzdPfOihh5QuSVGPPfaYeMUVVyhdhqpcf/314n333dfvsUWLFol33nmnQhUpD4D4xhtvyL8Ph8Oiw+EQf/7zn8uP9fb2ina7XXz++ecVqDD5zvycDGb79u0iALGxsTE5RakEV3YSwO/3Y9euXVi4cGG/xxcuXIht27YpVJW6uFwuAEBeXp7ClShv6dKluP766zF//nylS1GFt956C3PmzMHXv/51FBUVYdasWfjtb3+rdFmKuuKKK/DOO+/g4MGDAIC9e/di69atuO666xSuTD0aGhrgdDr7fd01m82YN28ev+7GcLlcEAQh7VZKeRFoArS1tSEUCqG4uLjf48XFxXA6nQpVpR6iKOLhhx/GFVdcgalTpypdjqJeffVV7N69Gzt27FC6FNU4cuQInnvuOTz88MP4wQ9+gO3bt+M73/kOzGYz7r77bqXLU8Rjjz0Gl8uF6upq6PV6hEIhPPXUU7j99tuVLk01pK+tg33dbWxsVKIk1ent7cXjjz+OxYsXa/pi0MEw7CSQIAj9fi+K4oDH0tEDDzyATz75BFu3blW6FEU1NTXhoYcewoYNG5CRkaF0OaoRDocxZ84c1NXVAQBmzZqFffv24bnnnkvbsPPaa6/hlVdewfr16zFlyhTs2bMHy5YtQ0lJCe655x6ly1MVft0dXCAQwG233YZwOIxnn31W6XKSjmEnAQoKCqDX6wes4rS2tg74qSPdPPjgg3jrrbewZcsWjBs3TulyFLVr1y60trZi9uzZ8mOhUAhbtmzBmjVr4PP5oNfrFaxQGWPGjMGFF17Y77HJkyfjT3/6k0IVKe/73/8+Hn/8cdx2220AgGnTpqGxsRH19fUMO1EOhwNAZIVnzJgx8uP8uhsJOrfeeisaGhrw7rvvpt2qDsBprIQwmUyYPXs2Nm7c2O/xjRs3Yu7cuQpVpSxRFPHAAw/g9ddfx7vvvouKigqlS1LcNddcg08//RR79uyR3+bMmYM77rgDe/bsScugAwCXX375gGMJDh48iLKyMoUqUl53dzd0uv5frvV6fdqNnp9NRUUFHA5Hv6+7fr8fmzdvTtuvu0Bf0Pnyyy+xadMm5OfnK12SIriykyAPP/ww7rrrLsyZMweXXXYZ1q5di2PHjuH+++9XujRFLF26FOvXr8ef//xn2Gw2edXLbrcjMzNT4eqUYbPZBvQsWa1W5Ofnp3Uv03e/+13MnTsXdXV1uPXWW7F9+3asXbsWa9euVbo0xdx444146qmnMH78eEyZMgUff/wxVq5cifvuu0/p0pLK4/Hg0KFD8u8bGhqwZ88e5OXlYfz48Vi2bBnq6upQVVWFqqoq1NXVwWKxYPHixQpWnVhn+5yUlJTglltuwe7du/GXv/wFoVBI/tqbl5cHk8mkVNnJp+wwmLb927/9m1hWViaaTCbxoosuSusxawCDvr344otKl6YqHD2P+K//+i9x6tSpotlsFqurq8W1a9cqXZKiurq6xIceekgcP368mJGRIU6YMEFcvny56PP5lC4tqd57771Bv47cc889oihGxs9/8pOfiA6HQzSbzeJVV10lfvrpp8oWnWBn+5w0NDQM+bX3vffeU7r0pBJEURSTGa6IiIiIkok9O0RERKRpDDtERESkaQw7REREpGkMO0RERKRpDDtERESkaQw7REREpGkMO0RERKRpDDtERESkaQw7REQAysvLsWrVKqXLIKIEYNghoqS79957cfPNNwMAampqsGzZsqR97Jdeegk5OTkDHt+xYwe+9a1vJa0OIkoeXgRKRJrg9/vP62LDwsLCOFZDRGrClR0iUsy9996LzZs341e/+hUEQYAgCDh69CgA4PPPP8d1112HrKwsFBcX46677kJbW5v8Z2tqavDAAw/g4YcfRkFBARYsWAAAWLlyJaZNmwar1YrS0lIsWbIEHo8HAPD+++/jn/7pn+ByueSPV1tbC2DgNtaxY8fwta99DVlZWcjOzsatt96KlpYW+fna2lrMnDkTL7/8MsrLy2G323HbbbfB7XYn9pNGRCPGsENEivnVr36Fyy67DP/8z/+M5uZmNDc3o7S0FM3NzZg3bx5mzpyJnTt34m9/+xtaWlpw66239vvz69atg8FgwIcffojf/OY3AACdTodf//rX+Oyzz7Bu3Tq8++67ePTRRwEAc+fOxapVq5CdnS1/vEceeWRAXaIo4uabb8bp06exefNmbNy4EYcPH8Y3vvGNfq87fPgw3nzzTfzlL3/BX/7yF2zevBk///nPE/TZIqLR4jYWESnGbrfDZDLBYrHA4XDIjz/33HO46KKLUFdXJz/2u9/9DqWlpTh48CAuuOACAEBlZSVWrFjR733G9v9UVFTgpz/9Kf7v//2/ePbZZ2EymWC32yEIQr+Pd6ZNmzbhk08+QUNDA0pLSwEAL7/8MqZMmYIdO3bg4osvBgCEw2G89NJLsNlsAIC77roL77zzDp566qnz+8QQUVxxZYeIVGfXrl147733kJWVJb9VV1cDiKymSObMmTPgz7733ntYsGABxo4dC5vNhrvvvhvt7e3wer3D/vj79+9HaWmpHHQA4MILL0ROTg72798vP1ZeXi4HHQAYM2YMWltbR/R3JaLE48oOEalOOBzGjTfeiKeffnrAc2PGjJF/bbVa+z3X2NiI6667Dvfffz9++tOfIi8vD1u3bsU3v/lNBAKBYX98URQhCMI5Hzcajf2eFwQB4XB42B+HiJKDYYeIFGUymRAKhfo9dtFFF+FPf/oTysvLYTAM/8vUzp07EQwG8cwzz0Cniyxc//GPfzznxzvThRdeiGPHjqGpqUle3fn888/hcrkwefLkYddDROrAbSwiUlR5eTn+93//F0ePHkVbWxvC4TCWLl2K06dP4/bbb8f27dtx5MgRbNiwAffdd99Zg8rEiRMRDAaxevVqHDlyBC+//DKef/75AR/P4/HgnXfeQVtbG7q7uwe8n/nz52P69Om44447sHv3bmzfvh1333035s2bN+jWGRGpG8MOESnqkUcegV6vx4UXXojCwkIcO3YMJSUl+PDDDxEKhfDVr34VU6dOxUMPPQS73S6v2Axm5syZWLlyJZ5++mlMnToVf/jDH1BfX9/vNXPnzsX999+Pb3zjGygsLBzQ4AxEtqPefPNN5Obm4qqrrsL8+fMxYcIEvPbaa3H/+xNR4gmiKIpKF0FERESUKFzZISIiIk1j2CEiIiJNY9ghIiIiTWPYISIiIk1j2CEiIiJNY9ghIiIiTWPYISIiIk1j2CEiIiJNY9ghIiIiTWPYISIiIk1j2CEiIiJN+/8Bb4UU3ujdoe4AAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["trace.GRAPH.clear()\n", "\n", "@trace.bundle(trainable=True)\n", "def fun(x):\n", "    \"\"\" A linear predictor function \"\"\"\n", "    return 0\n", "\n", "optimizer = OptoPrime(fun.parameters())\n", "\n", "ls = []\n", "for i in range(15):\n", "    try:\n", "        l = compute_loss(inputs, outputs)\n", "        target = l\n", "        feedback = 'Minimize loss'\n", "        print(f'Iteration {i} Loss: {l.data}')\n", "        ls.append(l.data)\n", "    except trace.ExecutionError as e:\n", "        target = e.exception_node\n", "        feedback = str(e.exception_node.data)\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(target, feedback)\n", "    optimizer.step()\n", "\n", "# plot ls\n", "import matplotlib.pyplot as plt\n", "plt.plot(ls)\n", "plt.xlabel('Iteration')\n", "plt.ylabel('Loss')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In contrast, if we update the parameter without batching but in a purely online fashion one by one, then the optimization results can be more noisy sometimes."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Iteration 0 Loss: 85\n", "Iteration 1 Loss: 15\n", "Iteration 2 Loss: 10\n", "Iteration 4 Loss: 10\n", "Iteration 5 Loss: 6\n", "Iteration 6 Loss: 6\n", "Iteration 7 Loss: 5\n", "Iteration 8 Loss: 5\n", "Iteration 9 Loss: 1\n", "Iteration 10 Loss: 0\n", "Iteration 11 Loss: 0\n", "Iteration 12 Loss: 0\n", "Iteration 13 Loss: 9\n", "Iteration 14 Loss: 120\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["\n", "\n", "trace.GRAPH.clear()\n", "\n", "@trace.bundle(trainable=True)\n", "def fun(x):\n", "    \"\"\" A linear predictor function \"\"\"\n", "    return 0\n", "\n", "optimizer = OptoPrime(fun.parameters())\n", "\n", "ls = []\n", "for i in range(15):\n", "    try:\n", "        l_eval = compute_loss(inputs, outputs)\n", "        print(f'Iteration {i} Loss: {l_eval.data}')\n", "        ls.append(l_eval.data)\n", "\n", "        ind = np.random.randint(0, N) % N\n", "        target = compute_loss([inputs[ind]], [outputs[ind]])\n", "        feedback = 'Minimize loss'\n", "    except trace.ExecutionError as e:\n", "        target = e.exception_node\n", "        feedback = str(e.exception_node.data)\n", "\n", "    optimizer.zero_feedback()\n", "    optimizer.backward(target, feedback)\n", "    optimizer.step()\n", "\n", "\n", "\n", "# plot ls\n", "import matplotlib.pyplot as plt\n", "plt.plot(ls)\n", "plt.xlabel('Iteration')\n", "plt.ylabel('Loss')\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Batching Non-Commutative Feedbacks\n", "\n", "In the earlier numerical example, the loss function was commutative so that we can do  `batch_loss += loss(each_input)`. What if the feedbacks received are not commutative? This can happen often with non-numeric (e.g. text) feedbacks. Here we will see a simple design pattern for using `trace` and `OptoPrime` for batch optimization in such cases."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from opto.trace import bundle\n", "\n", "@bundle(trainable=False)\n", "def concat(*items):\n", "    \"\"\" Concatenate the items into a single string \"\"\"\n", "    output = ''\n", "    for i, item in enumerate(items):\n", "        output += f'ID {[i]}: {item}\\n'\n", "    return output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note that the `concat` function when called with a list of feedbacks will concatenate them all with an identifier for each element. This way, the optimizer when given a batch of outputs and a corresponding batch of feedbacks can disambiguate which feedback corresponds to which output."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt\n", " \n", "You're tasked to solve a coding/algorithm problem. You will see the instruction, the code, the documentation of each function used in the code, and the feedback about the execution result.\n", "\n", "Specifically, a problem will be composed of the following parts:\n", "- #Instruction: the instruction which describes the things you need to do or the question you should answer.\n", "- #Code: the code defined in the problem.\n", "- #Documentation: the documentation of each function used in #Code. The explanation might be incomplete and just contain high-level description. You can use the values in #Others to help infer how those functions work.\n", "- #Variables: the input variables that you can change.\n", "- #Constraints: the constraints or descriptions of the variables in #Variables.\n", "- #Inputs: the values of other inputs to the code, which are not changeable.\n", "- #Others: the intermediate values created through the code execution.\n", "- #Outputs: the result of the code output.\n", "- #Feedback: the feedback about the code's execution result.\n", "\n", "In #Variables, #Inputs, #Outputs, and #Others, the format is:\n", "\n", "<data_type> <variable_name> = <value>\n", "\n", "If <type> is (code), it means <value> is the source code of a python code, which may include docstring and definitions.\n", "\n", "Output_format: Your output should be in the following json format, satisfying the json syntax:\n", "\n", "{{\n", "\"reasoning\": <Your reasoning>,\n", "\"answer\": <Your answer>,\n", "\"suggestion\": {{\n", "    <variable_1>: <suggested_value_1>,\n", "    <variable_2>: <suggested_value_2>,\n", "}}\n", "}}\n", "\n", "In \"reasoning\", explain the problem: 1. what the #Instruction means 2. what the #Feedback on #Output means to #Variables considering how #Variables are used in #Code and other values in #Documentation, #Inputs, #Others. 3. Reasoning about the suggested changes in #Variables (if needed) and the expected result.\n", "\n", "If #Instruction asks for an answer, write it down in \"answer\".\n", "\n", "If you need to suggest a change in the values of #Variables, write down the suggested values in \"suggestion\". Remember you can change only the values in #Variables, not others. When <type> of a variable is (code), you should write the new definition in the format of python code without syntax errors, and you should not change the function name or the function signature.\n", "\n", "If no changes or answer are needed, just output TERMINATE.\n", "\n", "Now you see problem instance:\n", "\n", "================================\n", "\n", "#Instruction\n", "You need to change the <value> of the variables in #Variables to improve the output in accordance to #Feedback.\n", "\n", "#Code\n", "eval84 = eval(lst=lst0, __code=__code1)\n", "eval85 = eval(lst=lst1, __code=__code1)\n", "eval86 = eval(lst=lst2, __code=__code1)\n", "eval87 = eval(lst=lst3, __code=__code1)\n", "eq0 = eq(x=eval84, y=list0)\n", "eq1 = eq(x=eval85, y=list1)\n", "eq2 = eq(x=eval86, y=list2)\n", "eq3 = eq(x=eval87, y=list3)\n", "concat1 = concat(args_0=eq0, args_1=eq1, args_2=eq2, args_3=eq3)\n", "\n", "#Documentation\n", "[eval] This operator eval(__code, *args, **kwargs) evaluates the code block, where __code is the code (str) and *args and **kwargs are the arguments of the function. The output is the result of the evaluation, i.e., __code(*args, **kwargs).\n", "[eq] This is an eq operator of x and y. .\n", "[concat] Concatenate the items into a single string .\n", "\n", "#Variables\n", "(code) __code1:def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "#Constraints\n", "(code) __code1: The code should start with:\n", "def strange_sort_list(lst):\n", "\n", "#Inputs\n", "(list) lst1=[5, 5, 5, 5]\n", "(list) lst2=[]\n", "(list) lst3=[9, 8, 7, 6, 5, 4]\n", "(list) lst0=[1, 2, 3, 4]\n", "(list) list1=[5, 5, 5, 5]\n", "(list) list2=[]\n", "(list) list3=[4, 9, 5, 8, 6, 7]\n", "(list) list0=[1, 4, 2, 3]\n", "\n", "#Others\n", "(list) eval85=[5, 5, 5, 5]\n", "(list) eval86=[]\n", "(list) eval87=[4, 5, 6, 7, 8, 9]\n", "(list) eval84=[1, 2, 3, 4]\n", "(bool) eq0=False\n", "(bool) eq1=True\n", "(bool) eq2=True\n", "(bool) eq3=False\n", "\n", "#Outputs\n", "(str) concat1=ID [0]: False\n", "ID [1]: True\n", "ID [2]: True\n", "ID [3]: False\n", "\n", "\n", "#Feedback\n", "ID [0]: test case failed!\n", "ID [1]: test case passed!\n", "ID [2]: test case passed!\n", "ID [3]: test case failed!\n", "\n", "\n", "================================\n", "\n", "\n", "Your response:\n", "\n", "LLM response:\n", " {\n", "    \"reasoning\": \"The feedback indicates that the eval functions are used to generate sorted lists through a given sort function defined in __code1. The eq functions compare the output of these sort functions with predefined lists (list0, list1, list2, list3). The tests for lst0 and lst3 have failed, indicating that the output from the strange_sort_list function did not match the expected pattern given in list0 and list3. From the documentation and the given list patterns, 'strange_sort_list' should alternate between the smallest and largest remaining elements in the list, but the function in __code1 currently only sorts the list in ascending order. Thus, lst0 and lst3 are not sorting correctly into their expected 'strange' order.\",\n", "    \"answer\": \"Change __code1 to implement the strange sorting pattern by alternating between selecting minimum and maximum elements.\",\n", "    \"suggestion\": {\n", "        \"__code1\": \"def strange_sort_list(lst):\\n    lst = sorted(lst)\\n    result = []\\n    while lst:\\n        result.append(lst.pop(0))  # append and remove the first (minimum)\\n        if lst:  # check to avoid popping from an empty list\\n            result.append(lst.pop()) # append and remove the last (maximum)\\n    return result\\n\"\n", "    }\n", "}\n"]}], "source": ["@bundle(trainable=True)\n", "def strange_sort_list(lst):\n", "    '''\n", "    Given list of integers, return list in strange order.\n", "    Strange sorting, is when you start with the minimum value,\n", "    then maximum of the remaining integers, then minimum and so on.\n", "    '''\n", "    lst = sorted(lst)\n", "    return lst\n", "\n", "def get_feedback(predict, target):\n", "    if predict == target:\n", "        return \"test case passed!\"\n", "    else:\n", "        return \"test case failed!\"\n", "    \n", "from opto.optimizers import OptoPrime\n", "\n", "test_ground_truths = [[1, 4, 2, 3], [5, 5, 5, 5], [], [4, 9, 5, 8, 6, 7]]\n", "test_inputs = [[1, 2, 3, 4], [5, 5, 5, 5], [], [9, 8, 7, 6, 5, 4]]\n", "\n", "optimizer = OptoPrime(strange_sort_list.parameters())\n", "\n", "outputs = []\n", "feedbacks = []\n", "for i in range(len(test_inputs)):\n", "    try:\n", "        test_output = strange_sort_list(test_inputs[i])\n", "        feedback = get_feedback(test_output, test_ground_truths[i])\n", "    except trace.ExecutionError as e:\n", "        feedback = e.exception_node.data\n", "        test_output = e.exception_node\n", "    feedbacks.append(feedback)\n", "    \n", "    correctness = test_output.eq(test_ground_truths[i])\n", "    outputs.append(correctness)\n", "\n", "batched_feedback = concat(*feedbacks)\n", "batched_outputs = concat(*outputs)\n", "optimizer.zero_feedback()\n", "optimizer.backward(batched_outputs, batched_feedback.data)\n", "optimizer.step(verbose=True)"]}], "metadata": {"kernelspec": {"display_name": "trace", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}