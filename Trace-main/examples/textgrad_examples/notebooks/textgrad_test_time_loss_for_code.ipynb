{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from opto import trace\n", "from opto.optimizers import OptoPrime\n", "\n", "import random\n", "import time"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# We'll use below utilities to run a python function.\n", "from IPython.core.interactiveshell import InteractiveShell\n", "\n", "def run_function_in_interpreter(func_code):\n", "    # raise Exception(\"This function will run the code returned by GPT-4o. Remove this if you'd like to run the code!\")\n", "    interpreter = InteractiveShell.instance()\n", "\n", "    interpreter.run_cell(func_code, store_history=False, silent=True)\n", "\n", "    func_name = func_code.split(\"def \")[1].split(\"(\")[0].strip()\n", "    func = interpreter.user_ns[func_name]\n", "\n", "    return func\n", "\n", "\n", "\n", "def test_longest_increasing_subsequence(fn):\n", "    nums = [10, 22, 9, 33, 21, 50, 41, 60]\n", "    assert fn(nums) == 5\n", "\n", "    nums = [7, 2, 1, 3, 8, 4, 9, 6, 5]\n", "    assert fn(nums) == 4\n", "\n", "    nums = [5, 4, 3, 2, 1]\n", "    assert fn(nums) == 1\n", "\n", "    nums = [1, 2, 3, 4, 5]\n", "    assert fn(nums) == 5\n", "\n", "    nums = [3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5]\n", "    assert fn(nums) == 4\n", "\n", "    nums = [10, 9, 2, 5, 3, 7, 101, 18]\n", "    assert fn(nums) == 4\n", "\n", "    nums = [0, 8, 4, 12, 2, 10, 6, 14, 1, 9, 5, 13, 3, 11, 7, 15]\n", "    assert fn(nums) == 6\n", "\n", "    nums = [7, 7, 7, 7, 7, 7, 7]\n", "    assert fn(nums) == 1\n", "\n", "    nums = [20, 25, 47, 35, 56, 68, 98, 101, 212, 301, 415, 500]\n", "    assert fn(nums) == 11\n", "\n", "    nums = [9, 8, 7, 6, 5, 4, 3, 2, 1, 0]\n", "    assert fn(nums) == 1\n", "\n", "    print(\"All test cases passed!\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["problem_text = \"\"\"Longest Increasing Subsequence (LIS)\n", "\n", "Problem Statement:\n", "Given a sequence of integers, find the length of the longest subsequence that is strictly increasing. A subsequence is a sequence that can be derived from another sequence by deleting some or no elements without changing the order of the remaining elements.\n", "\n", "Input:\n", "The input consists of a list of integers representing the sequence.\n", "\n", "Output:\n", "The output should be an integer representing the length of the longest increasing subsequence.\"\"\"\n", "\n", "initial_solution = \"\"\"\n", "def longest_increasing_subsequence(nums):\n", "    n = len(nums)\n", "    dp = [1] * n\n", "\n", "    for i in range(1, n):\n", "        for j in range(i):\n", "            if nums[i] > nums[j]:\n", "                dp[i] = max(dp[i], dp[j] + 1)\n", "\n", "    max_length = max(dp)\n", "    lis = []\n", "\n", "    for i in range(n - 1, -1, -1):\n", "        if dp[i] == max_length:\n", "            lis.append(nums[i])\n", "            max_length -= 1\n", "\n", "    return len(lis[::-1])\n", "\"\"\"\n", "\n", "# Generate a random test case\n", "def generate_random_test_case(size, min_value, max_value):\n", "    return [random.randint(min_value, max_value) for _ in range(size)]\n", "\n", "# Test the function with a random test case\n", "size = 10000  # Adjust the size as needed\n", "min_value = 1\n", "max_value = 1000\n", "\n", "nums = generate_random_test_case(size, min_value, max_value)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test Case Size: 10000\n", "Longest Increasing Subsequence Length: 180\n", "Runtime: 5.81503 seconds\n", "All test cases passed!\n"]}], "source": ["longest_increasing_subsequence = run_function_in_interpreter(initial_solution)\n", "\n", "start_time = time.time()\n", "lis = longest_increasing_subsequence(nums)\n", "end_time = time.time()\n", "\n", "print(f\"Test Case Size: {size}\")\n", "print(f\"Longest Increasing Subsequence Length: {lis}\")\n", "print(f\"Runtime: {end_time - start_time:.5f} seconds\")\n", "\n", "# Test for all test cases\n", "test_longest_increasing_subsequence(longest_increasing_subsequence)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Trace code"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.40.1 (20161225.0304)\n", " -->\n", "<!-- Title: %3 Pages: 1 -->\n", "<svg width=\"1227pt\" height=\"324pt\"\n", " viewBox=\"0.00 0.00 1226.78 324.01\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 320.0143)\">\n", "<title>%3</title>\n", "<polygon fill=\"#ffffff\" stroke=\"transparent\" points=\"-4,4 -4,-320.0143 1222.7828,-320.0143 1222.7828,4 -4,4\"/>\n", "<!-- str1 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>str1</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"197.2828\" cy=\"-246.0107\" rx=\"197.0658\" ry=\"48.1667\"/>\n", "<text text-anchor=\"middle\" x=\"197.2828\" y=\"-264.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">str1</text>\n", "<text text-anchor=\"middle\" x=\"197.2828\" y=\"-249.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"197.2828\" y=\"-234.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Problem: {problem_text}</text>\n", "<text text-anchor=\"middle\" x=\"197.2828\" y=\"-219.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Current Code: {solution}</text>\n", "</g>\n", "<!-- format0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>format0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"610.2828\" cy=\"-70.0036\" rx=\"249.7174\" ry=\"70.0071\"/>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-104.3036\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">format0</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-89.3036\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">[format] Fills in a string template with content, str.format(). .</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-74.3036\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Problem: Longest Increasing Subsequence (LIS)</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-43.3036\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Problem Statement:</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-28.3036\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Given a sequence of integers, find...</text>\n", "</g>\n", "<!-- str1&#45;&gt;format0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>str1&#45;&gt;format0</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M295.4198,-204.1879C345.7006,-182.7599 407.9411,-156.235 463.7743,-132.4407\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"465.2091,-135.6339 473.0363,-128.4936 462.4647,-129.1943 465.2091,-135.6339\"/>\n", "</g>\n", "<!-- str2 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>str2</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"610.2828\" cy=\"-246.0107\" rx=\"197.0658\" ry=\"70.0071\"/>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-280.3107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">str2</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-265.3107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-250.3107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Longest Increasing Subsequence (LIS)</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-219.3107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Problem Statement:</text>\n", "<text text-anchor=\"middle\" x=\"610.2828\" y=\"-204.3107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">Given a sequence of integers, find the leng...</text>\n", "</g>\n", "<!-- str2&#45;&gt;format0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>str2&#45;&gt;format0</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M610.2828,-175.9423C610.2828,-167.4673 610.2828,-158.7605 610.2828,-150.1546\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"613.7829,-150.1184 610.2828,-140.1184 606.7829,-150.1185 613.7829,-150.1184\"/>\n", "</g>\n", "<!-- str0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>str0</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"1218.7828,-311.0107 825.7828,-311.0107 825.7828,-181.0107 1218.7828,-181.0107 1218.7828,-311.0107\"/>\n", "<text text-anchor=\"middle\" x=\"1022.2828\" y=\"-295.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">str0</text>\n", "<text text-anchor=\"middle\" x=\"1022.2828\" y=\"-280.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">[ParameterNode] This is a ParameterNode in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"1022.2828\" y=\"-249.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\">def longest_increasing_subsequence(nums):</text>\n", "<text text-anchor=\"middle\" x=\"1022.2828\" y=\"-234.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\"> &#160;&#160;&#160;n = len(nums)</text>\n", "<text text-anchor=\"middle\" x=\"1022.2828\" y=\"-219.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\"> &#160;&#160;&#160;dp = [1] * n</text>\n", "<text text-anchor=\"middle\" x=\"1022.2828\" y=\"-188.8107\" font-family=\"Times,serif\" font-size=\"14.00\" fill=\"#000000\"> &#160;&#160;&#160;for i in range(1,...</text>\n", "</g>\n", "<!-- str0&#45;&gt;format0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>str0&#45;&gt;format0</title>\n", "<path fill=\"none\" stroke=\"#000000\" d=\"M870.0398,-180.9722C832.9536,-165.1289 793.3681,-148.2179 756.566,-132.496\"/>\n", "<polygon fill=\"#000000\" stroke=\"#000000\" points=\"757.8437,-129.2359 747.2727,-128.5259 755.0937,-135.6731 757.8437,-129.2359\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7f74cfc08280>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "code = trace.node(initial_solution, trainable=True)\n", "opt = OptoPrime([code])\n", "\n", "feedback = \"Think about the problem and the code snippet. Does the code solve the problem? What is the runtime complexity? Improve the runtime complexity of the code.\"\n", "format_string = \"Problem: {problem_text}\\nCurrent Code: {solution}\"\n", "\n", "from opto.trace import operators as ops\n", "problem = ops.format(format_string, problem_text=problem_text, solution=code)\n", "opt.zero_feedback()\n", "\n", "# Let's visualize our computation graph.\n", "problem.backward(feedback, visualize=True)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["LLM response:\n", " {\n", "\"reasoning\": \"The instruction requires improving the output based on the feedback, which suggests analyzing and optimizing the code snippet for solving the Longest Increasing Subsequence (LIS) problem. The current code employs a dynamic programming approach with a time complexity of O(n^2), where 'n' is the length of the input list. To enhance this, the code can be optimized using a combination of dynamic programming and binary search, which reduces the time complexity to O(n log n). This involves maintaining a list to track the smallest possible tail for increasing subsequences of varying lengths and utilizing binary search to find the appropriate position to update the list.\",\n", "\"answer\": null,\n", "\"suggestion\": {\n", "    \"str0\": \"def longest_increasing_subsequence(nums):\\n    if not nums:\\n        return 0\\n    tails = []\\n    for num in nums:\\n        left, right = 0, len(tails)\\n        while left < right:\\n            mid = (left + right) // 2\\n            if tails[mid] < num:\\n                left = mid + 1\\n            else:\\n                right = mid\\n        if left == len(tails):\\n            tails.append(num)\\n        else:\\n            tails[left] = num\\n    return len(tails)\"\n", "}\n", "}\n"]}], "source": ["# Let's update the code\n", "opt.step(verbose='output')\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Longest Increasing Subsequence Length: 180\n", "Runtime: 0.01259 seconds\n", "All test cases passed!\n"]}], "source": ["# Hopefully, we should get much better runtime!\n", "longest_increasing_subsequence = run_function_in_interpreter(code.data)\n", "\n", "start_time = time.time()\n", "lis = longest_increasing_subsequence(nums)\n", "end_time = time.time()\n", "\n", "print(f\"Longest Increasing Subsequence Length: {lis}\")\n", "print(f\"Runtime: {end_time - start_time:.5f} seconds\")\n", "\n", "test_longest_increasing_subsequence(longest_increasing_subsequence)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["At this point, OptoPrime in Trace solves the problem. There's no need to further iterate. "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def longest_increasing_subsequence(nums):\n", "    if not nums:\n", "        return 0\n", "    tails = []\n", "    for num in nums:\n", "        left, right = 0, len(tails)\n", "        while left < right:\n", "            mid = (left + right) // 2\n", "            if tails[mid] < num:\n", "                left = mid + 1\n", "            else:\n", "                right = mid\n", "        if left == len(tails):\n", "            tails.append(num)\n", "        else:\n", "            tails[left] = num\n", "    return len(tails)\n"]}], "source": ["print(code.data)"]}], "metadata": {"kernelspec": {"display_name": "trace-3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}