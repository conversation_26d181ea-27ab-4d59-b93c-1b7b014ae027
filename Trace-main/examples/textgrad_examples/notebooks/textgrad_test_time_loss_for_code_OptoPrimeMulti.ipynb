{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-WGqLq5vb7Jm", "outputId": "f18bba80-41ae-4473-f426-4fb8f5746082"}, "outputs": [], "source": ["%pip install textgrad\n", "%pip install git+https://github.com/microsoft/Trace.git\n", "%pip install dask[dataframe]\n", "%pip install autogen"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "7MTXRbDhcHAP"}, "outputs": [], "source": ["import os\n", "import openai\n", "\n", "os.environ['OPENAI_API_KEY'] = \"\"\n", "\n", "OAI_CONFIG_LIST = [ { \"model\": \"gpt-4o-mini\", \"api_key\": os.environ['OPENAI_API_KEY'],}]\n", "\n", "import json; config_file_path = \"/content/config_list.json\"; json.dump(OAI_CONFIG_LIST, open(config_file_path, \"w\")); os.environ['OAI_CONFIG_LIST'] = config_file_path"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "JftiVF1eb0rH"}, "outputs": [], "source": ["from opto import trace\n", "from opto.optimizers import OptoPrime, OptoPrimeMulti\n", "\n", "import random\n", "import time"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "cO6nE2LPb0rH"}, "outputs": [], "source": ["# We'll use below utilities to run a python function.\n", "from IPython.core.interactiveshell import InteractiveShell\n", "\n", "def run_function_in_interpreter(func_code):\n", "    # raise Exception(\"This function will run the code returned by GPT-4o. Remove this if you'd like to run the code!\")\n", "    interpreter = InteractiveShell.instance()\n", "\n", "    interpreter.run_cell(func_code, store_history=False, silent=True)\n", "\n", "    func_name = func_code.split(\"def \")[1].split(\"(\")[0].strip()\n", "    func = interpreter.user_ns[func_name]\n", "\n", "    return func\n", "\n", "\n", "\n", "def test_longest_increasing_subsequence(fn):\n", "    nums = [10, 22, 9, 33, 21, 50, 41, 60]\n", "    assert fn(nums) == 5\n", "\n", "    nums = [7, 2, 1, 3, 8, 4, 9, 6, 5]\n", "    assert fn(nums) == 4\n", "\n", "    nums = [5, 4, 3, 2, 1]\n", "    assert fn(nums) == 1\n", "\n", "    nums = [1, 2, 3, 4, 5]\n", "    assert fn(nums) == 5\n", "\n", "    nums = [3, 1, 4, 1, 5, 9, 2, 6, 5, 3, 5]\n", "    assert fn(nums) == 4\n", "\n", "    nums = [10, 9, 2, 5, 3, 7, 101, 18]\n", "    assert fn(nums) == 4\n", "\n", "    nums = [0, 8, 4, 12, 2, 10, 6, 14, 1, 9, 5, 13, 3, 11, 7, 15]\n", "    assert fn(nums) == 6\n", "\n", "    nums = [7, 7, 7, 7, 7, 7, 7]\n", "    assert fn(nums) == 1\n", "\n", "    nums = [20, 25, 47, 35, 56, 68, 98, 101, 212, 301, 415, 500]\n", "    assert fn(nums) == 11\n", "\n", "    nums = [9, 8, 7, 6, 5, 4, 3, 2, 1, 0]\n", "    assert fn(nums) == 1\n", "\n", "    print(\"All test cases passed!\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "OYkVBYtkb0rH"}, "outputs": [], "source": ["problem_text = \"\"\"Longest Increasing Subsequence (LIS)\n", "\n", "Problem Statement:\n", "Given a sequence of integers, find the length of the longest subsequence that is strictly increasing. A subsequence is a sequence that can be derived from another sequence by deleting some or no elements without changing the order of the remaining elements.\n", "\n", "Input:\n", "The input consists of a list of integers representing the sequence.\n", "\n", "Output:\n", "The output should be an integer representing the length of the longest increasing subsequence.\"\"\"\n", "\n", "initial_solution = \"\"\"\n", "def longest_increasing_subsequence(nums):\n", "    n = len(nums)\n", "    dp = [1] * n\n", "\n", "    for i in range(1, n):\n", "        for j in range(i):\n", "            if nums[i] > nums[j]:\n", "                dp[i] = max(dp[i], dp[j] + 1)\n", "\n", "    max_length = max(dp)\n", "    lis = []\n", "\n", "    for i in range(n - 1, -1, -1):\n", "        if dp[i] == max_length:\n", "            lis.append(nums[i])\n", "            max_length -= 1\n", "\n", "    return len(lis[::-1])\n", "\"\"\"\n", "\n", "# Generate a random test case\n", "def generate_random_test_case(size, min_value, max_value):\n", "    return [random.randint(min_value, max_value) for _ in range(size)]\n", "\n", "# Test the function with a random test case\n", "size = 10000  # Adjust the size as needed\n", "min_value = 1\n", "max_value = 1000\n", "\n", "nums = generate_random_test_case(size, min_value, max_value)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "fqTOVqftb0rI", "outputId": "a7506eb0-7cc0-47c7-facc-76f93541117e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test Case Size: 10000\n", "Longest Increasing Subsequence Length: 176\n", "Runtime: 12.51021 seconds\n", "All test cases passed!\n"]}], "source": ["longest_increasing_subsequence = run_function_in_interpreter(initial_solution)\n", "\n", "start_time = time.time()\n", "lis = longest_increasing_subsequence(nums)\n", "end_time = time.time()\n", "\n", "print(f\"Test Case Size: {size}\")\n", "print(f\"Longest Increasing Subsequence Length: {lis}\")\n", "print(f\"Runtime: {end_time - start_time:.5f} seconds\")\n", "\n", "# Test for all test cases\n", "test_longest_increasing_subsequence(longest_increasing_subsequence)"]}, {"cell_type": "markdown", "metadata": {"id": "Tj_xi5Jib0rI"}, "source": ["# Trace code"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 645}, "id": "wSgBz-Amb0rI", "outputId": "299e048c-d0db-4b9c-c557-f72fd962d4a2"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\n", "PYDEV DEBUGGER WARNING:\n", "sys.settrace() should not be used when the debugger is being used.\n", "This may cause the debugger to stop working correctly.\n", "If this is needed, please check: \n", "http://pydev.blogspot.com/2007/06/why-cant-pydev-debugger-work-with.html\n", "to see how to restore the debug tracing back correctly.\n", "Call Location:\n", "  File \"/usr/local/lib/python3.10/dist-packages/opto/trace/bundle.py\", line 359, in sync_call_fun\n", "    sys.settrace(oldtracer)\n", "\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", " \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<!-- Generated by graphviz version 2.43.0 (0)\n", " -->\n", "<!-- Title: %3 Pages: 1 -->\n", "<svg width=\"1223pt\" height=\"324pt\"\n", " viewBox=\"0.00 0.00 1222.58 324.01\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\">\n", "<g id=\"graph0\" class=\"graph\" transform=\"scale(1 1) rotate(0) translate(4 320.01)\">\n", "<title>%3</title>\n", "<polygon fill=\"white\" stroke=\"transparent\" points=\"-4,4 -4,-320.01 1218.58,-320.01 1218.58,4 -4,4\"/>\n", "<!-- x0 -->\n", "<g id=\"node1\" class=\"node\">\n", "<title>x0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"196.58\" cy=\"-246.01\" rx=\"196.65\" ry=\"48.17\"/>\n", "<text text-anchor=\"middle\" x=\"196.58\" y=\"-264.81\" font-family=\"Times,serif\" font-size=\"14.00\">x0</text>\n", "<text text-anchor=\"middle\" x=\"196.58\" y=\"-249.81\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"196.58\" y=\"-234.81\" font-family=\"Times,serif\" font-size=\"14.00\">Problem: {problem_text}</text>\n", "<text text-anchor=\"middle\" x=\"196.58\" y=\"-219.81\" font-family=\"Times,serif\" font-size=\"14.00\">Current Code: {solution}</text>\n", "</g>\n", "<!-- format0 -->\n", "<g id=\"node2\" class=\"node\">\n", "<title>format0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"607.58\" cy=\"-70\" rx=\"248.8\" ry=\"70.01\"/>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-104.3\" font-family=\"Times,serif\" font-size=\"14.00\">format0</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-89.3\" font-family=\"Times,serif\" font-size=\"14.00\">[format] Fills in a string template with content, str.format(). .</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-74.3\" font-family=\"Times,serif\" font-size=\"14.00\">Problem: Longest Increasing Subsequence (LIS)</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-43.3\" font-family=\"Times,serif\" font-size=\"14.00\">Problem Statement:</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-28.3\" font-family=\"Times,serif\" font-size=\"14.00\">Given a sequence of integers, find...</text>\n", "</g>\n", "<!-- x0&#45;&gt;format0 -->\n", "<g id=\"edge1\" class=\"edge\">\n", "<title>x0&#45;&gt;format0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M293.2,-204.1C343.44,-182.83 405.88,-156.4 461.85,-132.7\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"463.29,-135.89 471.13,-128.77 460.56,-129.45 463.29,-135.89\"/>\n", "</g>\n", "<!-- problem_text0 -->\n", "<g id=\"node3\" class=\"node\">\n", "<title>problem_text0</title>\n", "<ellipse fill=\"#deebf6\" stroke=\"#5c9bd5\" stroke-width=\"1.2\" cx=\"607.58\" cy=\"-246.01\" rx=\"196.65\" ry=\"70.01\"/>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-280.31\" font-family=\"Times,serif\" font-size=\"14.00\">problem_text0</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-265.31\" font-family=\"Times,serif\" font-size=\"14.00\">[Node] This is a node in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-250.31\" font-family=\"Times,serif\" font-size=\"14.00\">Longest Increasing Subsequence (LIS)</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-219.31\" font-family=\"Times,serif\" font-size=\"14.00\">Problem Statement:</text>\n", "<text text-anchor=\"middle\" x=\"607.58\" y=\"-204.31\" font-family=\"Times,serif\" font-size=\"14.00\">Given a sequence of integers, find the leng...</text>\n", "</g>\n", "<!-- problem_text0&#45;&gt;format0 -->\n", "<g id=\"edge2\" class=\"edge\">\n", "<title>problem_text0&#45;&gt;format0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M607.58,-175.99C607.58,-167.53 607.58,-158.84 607.58,-150.25\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"611.08,-150.23 607.58,-140.23 604.08,-150.23 611.08,-150.23\"/>\n", "</g>\n", "<!-- str0 -->\n", "<g id=\"node4\" class=\"node\">\n", "<title>str0</title>\n", "<polygon fill=\"#ffe5e5\" stroke=\"#ff7e79\" stroke-width=\"1.2\" points=\"1214.58,-311.01 822.58,-311.01 822.58,-181.01 1214.58,-181.01 1214.58,-311.01\"/>\n", "<text text-anchor=\"middle\" x=\"1018.58\" y=\"-295.81\" font-family=\"Times,serif\" font-size=\"14.00\">str0</text>\n", "<text text-anchor=\"middle\" x=\"1018.58\" y=\"-280.81\" font-family=\"Times,serif\" font-size=\"14.00\">[ParameterNode] This is a ParameterNode in a computational graph.</text>\n", "<text text-anchor=\"middle\" x=\"1018.58\" y=\"-249.81\" font-family=\"Times,serif\" font-size=\"14.00\">def longest_increasing_subsequence(nums):</text>\n", "<text text-anchor=\"middle\" x=\"1018.58\" y=\"-234.81\" font-family=\"Times,serif\" font-size=\"14.00\"> &#160;&#160;&#160;n = len(nums)</text>\n", "<text text-anchor=\"middle\" x=\"1018.58\" y=\"-219.81\" font-family=\"Times,serif\" font-size=\"14.00\"> &#160;&#160;&#160;dp = [1] * n</text>\n", "<text text-anchor=\"middle\" x=\"1018.58\" y=\"-188.81\" font-family=\"Times,serif\" font-size=\"14.00\"> &#160;&#160;&#160;for i in range(1,...</text>\n", "</g>\n", "<!-- str0&#45;&gt;format0 -->\n", "<g id=\"edge3\" class=\"edge\">\n", "<title>str0&#45;&gt;format0</title>\n", "<path fill=\"none\" stroke=\"black\" d=\"M867.29,-180.96C830.04,-165.19 790.24,-148.34 753.27,-132.69\"/>\n", "<polygon fill=\"black\" stroke=\"black\" points=\"754.51,-129.41 743.94,-128.74 751.78,-135.86 754.51,-129.41\"/>\n", "</g>\n", "</g>\n", "</svg>\n"], "text/plain": ["<graphviz.graphs.Digraph at 0x7c4662f2afe0>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "code = trace.node(initial_solution, trainable=True)\n", "opt = OptoPrimeMulti([code])\n", "\n", "feedback = \"Think about the problem and the code snippet. Does the code solve the problem? What is the runtime complexity? Improve the runtime complexity of the code.\"\n", "format_string = \"Problem: {problem_text}\\nCurrent Code: {solution}\"\n", "\n", "from opto.trace import operators as ops\n", "problem = ops.format(format_string, problem_text=problem_text, solution=code)\n", "opt.zero_feedback()\n", "\n", "# Let's visualize our computation graph.\n", "problem.backward(feedback, visualize=True)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "BFgB5Ngfb0rJ", "outputId": "6f5c2f0f-693a-4954-9898-acd59addaf08"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Temperatures for responses: [1.3, 0.9750000000000001, 0.65, 0.32499999999999996, 0.0]\n", "LLM responses:\n", " ['{\\n\"reasoning\": \"The #Instruction asks for changes in #Variables based on #Feedback about improving the output. The #Feedback indicates that while the current code solves the problem of finding the length of the longest increasing subsequence, the runtime complexity of the algorithm is O(n^2), which can be optimized to O(n log n) using binary search. Therefore, the suggested improvement involves changing `str0` to implement a more efficient version of the longest increasing subsequence algorithm. A common approach is to utilize a list to track the smallest tail for all subsequences of a given length.\",\\n\"answer\": \"\",\\n\"suggestion\": {\\n    \"str0\": \"def longest_increasing_subsequence(nums):\\\\n    from bisect import bisect_left\\\\n    if not nums:\\\\n        return 0\\\\n    lis = []\\\\n    for x in nums:\\\\n        i = bisect_left(lis, x)\\\\n        if i == len(lis):\\\\n            lis.append(x)\\\\n        else:\\\\n            lis[i] = x\\\\n    return len(lis)\"\\n}\\n}']\n", "LLM responses:\n", " ['{\\n\"reasoning\": \"The instruction asks to improve the output based on the feedback. The feedback indicates that while the provided code for finding the longest increasing subsequence (LIS) works, its runtime complexity can be enhanced. The current solution has a time complexity of O(n^2), which can be improved to O(n log n) using a more efficient algorithm such as binary search with a dynamic array. This involves using a list to keep track of the smallest tail of all increasing subsequences of different lengths and applying binary search to maintain this list when inserting new elements. Therefore, I suggest changing the implementation of the function `str0` to include this optimized approach. The expected result is a new version of the LIS function that operates with better efficiency.\",\\n\"answer\": \"The updated code for the longest increasing subsequence implementation should look like this:\\\\ndef longest_increasing_subsequence(nums):\\\\n    from bisect import bisect_left\\\\n    lis = []\\\\n    for num in nums:\\\\n        pos = bisect_left(lis, num)\\\\n        if pos == len(lis):\\\\n            lis.append(num)\\\\n        else:\\\\n            lis[pos] = num\\\\n    return len(lis)\",\\n\"suggestion\": {\\n    \"str0\": \"def longest_increasing_subsequence(nums):\\\\n    from bisect import bisect_left\\\\n    lis = []\\\\n    for num in nums:\\\\n        pos = bisect_left(lis, num)\\\\n        if pos == len(lis):\\\\n            lis.append(num)\\\\n        else:\\\\n            lis[pos] = num\\\\n    return len(lis)\"\\n}}\\n']\n", "LLM responses:\n", " ['{\\n\"reasoning\": \"The instruction asks to improve the output based on the feedback provided. The feedback suggests that while the current code does solve the problem of finding the length of the longest increasing subsequence, its runtime complexity can be improved. The current code has a time complexity of O(n^2) due to the nested loops used for comparing elements. A more efficient approach would be to use a binary search algorithm that can reduce the complexity to O(n log n). Therefore, I need to suggest a change in the implementation of the function \\'longest_increasing_subsequence\\' to improve its runtime complexity.\",\\n\"answer\": \"The improved solution should use a dynamic programming approach with binary search.\",\\n\"suggestion\": {\\n    \"str0\": \"def longest_increasing_subsequence(nums):\\\\n    from bisect import bisect_left\\\\n    subsequence = []\\\\n    for num in nums:\\\\n        pos = bisect_left(subsequence, num)\\\\n        if pos == len(subsequence):\\\\n            subsequence.append(num)\\\\n        else:\\\\n            subsequence[pos] = num\\\\n    return len(subsequence)\"\\n}\\n}']\n", "LLM responses:\n", " ['{\\n\"reasoning\": \"The instruction asks to improve the output based on the feedback, which suggests that while the current code solves the problem of finding the length of the longest increasing subsequence (LIS), it could be optimized for better runtime complexity. The current implementation has a time complexity of O(n^2) due to the nested loops. To improve this, we can implement a more efficient algorithm that uses binary search, reducing the time complexity to O(n log n). The suggested changes involve modifying the `str0` variable to include a new implementation of the LIS algorithm that utilizes this optimized approach. This change is expected to enhance the performance of the code without altering its functionality.\", \\n\"answer\": \"The current implementation of the longest increasing subsequence function can be improved for better runtime complexity.\", \\n\"suggestion\": {\\n    \"str0\": \"def longest_increasing_subsequence(nums):\\\\n    from bisect import bisect_left\\\\n    lis = []\\\\n    for num in nums:\\\\n        pos = bisect_left(lis, num)\\\\n        if pos == len(lis):\\\\n            lis.append(num)\\\\n        else:\\\\n            lis[pos] = num\\\\n    return len(lis)\"\\n}}\\n']\n", "LLM responses:\n", " ['{\\n\"reasoning\": \"The instruction asks to improve the output based on the feedback provided. The feedback suggests that while the code solves the problem of finding the longest increasing subsequence (LIS), it has a runtime complexity of O(n^2) due to the nested loops. To improve the runtime complexity, we can implement a more efficient algorithm that uses binary search, which can reduce the complexity to O(n log n). Therefore, I will suggest a new implementation for the function \\'longest_increasing_subsequence\\' that utilizes binary search to achieve this improved performance.\",\\n\"answer\": \"The current code does solve the problem of finding the longest increasing subsequence, but it can be optimized for better performance.\",\\n\"suggestion\": {\\n    \"str0\": \"def longest_increasing_subsequence(nums):\\\\n    from bisect import bisect_left\\\\n    lis = []\\\\n    for num in nums:\\\\n        pos = bisect_left(lis, num)\\\\n        if pos == len(lis):\\\\n            lis.append(num)\\\\n        else:\\\\n            lis[pos] = num\\\\n    return len(lis)\"\\n}\\n}']\n"]}], "source": ["# Let's update the code\n", "opt.step(verbose='output')\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "h9Peebbkc5Mv", "outputId": "31a2bc3c-6835-444b-b42e-aadfcb61e6be"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{<opto.trace.nodes.ParameterNode object at 0x7c4662f75ff0>: 'def longest_increasing_subsequence(nums):\\n    from bisect import bisect_left\\n    if not nums:\\n        return 0\\n    lis = []\\n    for x in nums:\\n        i = bisect_left(lis, x)\\n        if i == len(lis):\\n            lis.append(x)\\n        else:\\n            lis[i] = x\\n    return len(lis)'}\n", "\n", "{<opto.trace.nodes.ParameterNode object at 0x7c4662f75ff0>: 'def longest_increasing_subsequence(nums):\\n    from bisect import bisect_left\\n    lis = []\\n    for num in nums:\\n        pos = bisect_left(lis, num)\\n        if pos == len(lis):\\n            lis.append(num)\\n        else:\\n            lis[pos] = num\\n    return len(lis)'}\n", "\n", "{<opto.trace.nodes.ParameterNode object at 0x7c4662f75ff0>: 'def longest_increasing_subsequence(nums):\\n    from bisect import bisect_left\\n    subsequence = []\\n    for num in nums:\\n        pos = bisect_left(subsequence, num)\\n        if pos == len(subsequence):\\n            subsequence.append(num)\\n        else:\\n            subsequence[pos] = num\\n    return len(subsequence)'}\n", "\n", "{<opto.trace.nodes.ParameterNode object at 0x7c4662f75ff0>: 'def longest_increasing_subsequence(nums):\\n    from bisect import bisect_left\\n    lis = []\\n    for num in nums:\\n        pos = bisect_left(lis, num)\\n        if pos == len(lis):\\n            lis.append(num)\\n        else:\\n            lis[pos] = num\\n    return len(lis)'}\n", "\n", "{<opto.trace.nodes.ParameterNode object at 0x7c4662f75ff0>: 'def longest_increasing_subsequence(nums):\\n    from bisect import bisect_left\\n    lis = []\\n    for num in nums:\\n        pos = bisect_left(lis, num)\\n        if pos == len(lis):\\n            lis.append(num)\\n        else:\\n            lis[pos] = num\\n    return len(lis)'}\n", "\n"]}], "source": ["for c in opt.candidates:\n", "  print(f\"{c}\\n\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "RC6AQahriWFc", "outputId": "282e7f14-cf29-4f73-93a1-49de05ae837d"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Candidate 1:\n", "  Longest Increasing Subsequence Length: 176\n", "  Runtime: 0.00793 seconds\n", "  Code: <<<def longest_increasing_subsequence(nums):     from bisect import bisect_left     if not nums:         return 0     lis = []     for x in nums:         i = bisect_left(lis, x)         if i == len(lis):             lis.append(x)         else:             lis[i] = x     return len(lis)>>>\n", "\n", "Candidate 2:\n", "  Longest Increasing Subsequence Length: 176\n", "  Runtime: 0.01296 seconds\n", "  Code: <<<def longest_increasing_subsequence(nums):     from bisect import bisect_left     lis = []     for num in nums:         pos = bisect_left(lis, num)         if pos == len(lis):             lis.append(num)         else:             lis[pos] = num     return len(lis)>>>\n", "\n", "Candidate 3:\n", "  Longest Increasing Subsequence Length: 176\n", "  Runtime: 0.01116 seconds\n", "  Code: <<<def longest_increasing_subsequence(nums):     from bisect import bisect_left     subsequence = []     for num in nums:         pos = bisect_left(subsequence, num)         if pos == len(subsequence):             subsequence.append(num)         else:             subsequence[pos] = num     return len(subsequence)>>>\n", "\n", "Candidate 4:\n", "  Longest Increasing Subsequence Length: 176\n", "  Runtime: 0.01040 seconds\n", "  Code: <<<def longest_increasing_subsequence(nums):     from bisect import bisect_left     lis = []     for num in nums:         pos = bisect_left(lis, num)         if pos == len(lis):             lis.append(num)         else:             lis[pos] = num     return len(lis)>>>\n", "\n", "Candidate 5:\n", "  Longest Increasing Subsequence Length: 176\n", "  Runtime: 0.01898 seconds\n", "  Code: <<<def longest_increasing_subsequence(nums):     from bisect import bisect_left     lis = []     for num in nums:         pos = bisect_left(lis, num)         if pos == len(lis):             lis.append(num)         else:             lis[pos] = num     return len(lis)>>>\n", "\n", "Execution Summary:\n", "Candidate 1: Result = 176, Runtime = 0.00793 seconds\n", "Candidate 2: Result = 176, Runtime = 0.01296 seconds\n", "Candidate 3: Result = 176, Runtime = 0.01116 seconds\n", "Candidate 4: Result = 176, Runtime = 0.01040 seconds\n", "Candidate 5: Result = 176, Runtime = 0.01898 seconds\n"]}], "source": ["# Test all candidates and log execution times\n", "execution_results = []\n", "\n", "for i, candidate in enumerate(opt.candidates):\n", "    if not candidate:  # <PERSON><PERSON> invalid candidates\n", "        print(f\"Candidate {i+1}: Skipped (Invalid)\")\n", "        continue\n", "\n", "    # Extract the function code from the dictionary\n", "    func_code = list(candidate.values())[0]  # Assumes there's only one key-value pair in the dictionary\n", "    if not func_code:\n", "        print(f\"Candidate {i+1}: No code found\")\n", "        continue\n", "\n", "    # Compile and run the function\n", "    func = run_function_in_interpreter(func_code)  # Extract and run candidate function\n", "    try:\n", "        start_time = time.time()\n", "        result = func(nums)  # Test the function\n", "        end_time = time.time()\n", "\n", "        runtime = end_time - start_time\n", "        execution_results.append({\n", "            \"candidate\": i + 1,\n", "            \"result\": result,\n", "            \"runtime\": runtime\n", "        })\n", "\n", "        func_code_nonl = func_code.replace('\\n',' ')\n", "        print(f\"Candidate {i+1}:\")\n", "        print(f\"  Longest Increasing Subsequence Length: {result}\")\n", "        print(f\"  Runtime: {runtime:.5f} seconds\")\n", "        print(f\"  Code: <<<{func_code_nonl}>>>\\n\")\n", "\n", "    except Exception as e:\n", "        print(f\"Candidate {i+1}: Failed with error: {e}\\n\")\n", "\n", "# Display a summary of all candidate results\n", "print(\"Execution Summary:\")\n", "for res in execution_results:\n", "    print(f\"Candidate {res['candidate']}: Result = {res['result']}, Runtime = {res['runtime']:.5f} seconds\")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J73bk2Ieb0rJ", "outputId": "6b7d059c-03bc-4bae-f764-aaad8513693e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Longest Increasing Subsequence Length: 176\n", "Runtime: 0.00555 seconds\n", "All test cases passed!\n"]}], "source": ["# Hopefully, we should get much better runtime!\n", "longest_increasing_subsequence = run_function_in_interpreter(code.data)\n", "\n", "start_time = time.time()\n", "lis = longest_increasing_subsequence(nums)\n", "end_time = time.time()\n", "\n", "print(f\"Longest Increasing Subsequence Length: {lis}\")\n", "print(f\"Runtime: {end_time - start_time:.5f} seconds\")\n", "\n", "test_longest_increasing_subsequence(longest_increasing_subsequence)"]}, {"cell_type": "markdown", "metadata": {"id": "oKL459B3b0rJ"}, "source": ["At this point, OptoPrime in Trace solves the problem. There's no need to further iterate."]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "6wgWIOZ9b0rJ", "outputId": "e8c91b2e-7dee-4254-911b-ad91b035a3d7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["def longest_increasing_subsequence(nums):\n", "    from bisect import bisect_left\n", "    lis = []\n", "    for num in nums:\n", "        pos = bisect_left(lis, num)\n", "        if pos == len(lis):\n", "            lis.append(num)\n", "        else:\n", "            lis[pos] = num\n", "    return len(lis)\n"]}], "source": ["print(code.data)"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "trace-3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 0}