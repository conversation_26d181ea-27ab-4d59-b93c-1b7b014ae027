aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiosignal==1.4.0
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
black==25.1.0
blinker==1.9.0
certifi==2025.7.14
charset-normalizer==3.4.2
click==8.2.1
contourpy==1.3.3
cycler==0.12.1
distro==1.9.0
filelock==3.18.0
flask==3.1.1
fonttools==4.59.0
frozenlist==1.7.0
fsspec==2025.7.0
graphviz==0.21
h11==0.16.0
hf-xet==1.1.5
httpcore==1.0.9
httpx==0.28.1
huggingface-hub==0.34.1
idna==3.10
importlib-metadata==8.7.0
itsdangerous==2.2.0
jinja2==3.1.6
jiter==0.10.0
joblib==1.5.1
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
litellm==1.74.8
markupsafe==3.0.2
matplotlib==3.8.0
multidict==6.6.3
mypy-extensions==1.1.0
numpy==1.26.1
nvidia-nccl-cu12==2.27.6
openai==1.97.1
-e file:///data/workspace/AlphaEvolve-for-Anomaly-Detector-Synthesis/openevolve-main
packaging==25.0
pandas==2.1.2
pathspec==0.12.1
pillow==11.3.0
platformdirs==4.3.8
propcache==0.3.2
pydantic==2.11.7
pydantic-core==2.33.2
pyparsing==3.2.3
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
pytz==2025.2
pyyaml==6.0.2
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rpds-py==0.26.0
scikit-learn==1.3.2
scipy==1.16.1
six==1.17.0
sniffio==1.3.1
threadpoolctl==3.6.0
tiktoken==0.9.0
tokenizers==0.21.2
toml==0.10.2
tqdm==4.66.1
trace-opt==*******
typing-extensions==4.14.1
typing-inspection==0.4.1
tzdata==2025.2
urllib3==2.5.0
werkzeug==3.1.3
xgboost==3.0.2
yarl==1.20.1
zipp==3.23.0
