import pandas as pd
import json
import os
from tqdm import tqdm

path_csv = "平均延时-详细数据.csv"
path_txt = "1.txt"

path_in = "/Users/<USER>/Documents/GitHub/cos_ai_service/dataprod/avg_delay/csv"
path_out = "/Users/<USER>/Documents/GitHub/cos_ai_service/dataprod/avg_delay/case1"


def transform(data):
    timestamps = data["时间:当前周期"].tolist()
    current_values = data["详细数据:当前周期"].tolist()
    yoy_1_day_values = data["详细数据:同比(一天前)"].tolist()
    yoy_7_days_values = data["详细数据:同比(一周前)"].tolist()

    val_status_cur = [0] * len(current_values)
    val_status_yoy_1_day = [0] * len(yoy_1_day_values)
    val_status_yoy_7_days = [0] * len(yoy_7_days_values)

    for i in range(len(current_values)):
        if current_values[i] == '-':
            current_values[i] = 1
            val_status_cur[i] = 1
        if yoy_1_day_values[i] == '-':
            yoy_1_day_values[i] = 1
            val_status_yoy_1_day[i] = 1
        if yoy_7_days_values[i] == '-':
            yoy_7_days_values[i] = 1
            val_status_yoy_7_days[i] = 1

    res = {
        "timestamps": timestamps,
        "metric_data": {
            "series": [
                {
                    "label": "实时",
                    "metric_val": [float(i) if i != '-' else 0 for i in current_values],
                    "val_status": val_status_cur
                },
                {
                    "label": "环比1天",
                    "metric_val": [float(i) if i != '-' else 0 for i in yoy_1_day_values],
                    "val_status": val_status_yoy_1_day
                },
                {
                    "label": "环比7天",
                    "metric_val": [float(i) if i != '-' else 0 for i in yoy_7_days_values],
                    "val_status": val_status_yoy_7_days
                }
            ],
            "is_change_err": None,
            "change_period": True
        },
        "metric_info":{
            "zhiyan_cal_method" : 1,
            "project_id" : 1,
            "app_mark" :  1,
            "metric_name" :  "test",
            "metric_ch_name" :  "测试",
            "sec_lvl_name" :  1,
            "env" :  "prod"
        },
        "param": {
            "param_sensitive": 5
        },
        "trace_id": 665  # 生成唯一的 trace_id
    }

    return res

    # # 单次
    # data = transform(data)
    # path_txt = "1.txt"
    # with open(path_txt, "w", encoding="utf-8") as f:
    #     f.write(json.dumps(data, ensure_ascii=False))
    #
    # with open(path_txt, "r", encoding="utf-8") as f:
    #     data = json.loads(f.read())
    # print()


# 批处理
for filename in tqdm(os.listdir(path_in)):
    if filename.endswith('.csv'):
        csv_path = os.path.join(path_in, filename)
        txt_filename = os.path.splitext(filename)[0] + '.txt'
        txt_path = os.path.join(path_out, txt_filename)

        data = pd.read_csv(csv_path)

        transformed_data = transform(data)

        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write(json.dumps(transformed_data, ensure_ascii=False))