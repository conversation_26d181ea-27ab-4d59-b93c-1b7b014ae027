{"timestamps": ["2025-07-21 14:10", "2025-07-21 14:11", "2025-07-21 14:12", "2025-07-21 14:13", "2025-07-21 14:14", "2025-07-21 14:15", "2025-07-21 14:16", "2025-07-21 14:17", "2025-07-21 14:18", "2025-07-21 14:19", "2025-07-21 14:20", "2025-07-21 14:21", "2025-07-21 14:22", "2025-07-21 14:23", "2025-07-21 14:24", "2025-07-21 14:25", "2025-07-21 14:26", "2025-07-21 14:27", "2025-07-21 14:28", "2025-07-21 14:29", "2025-07-21 14:30", "2025-07-21 14:31", "2025-07-21 14:32", "2025-07-21 14:33", "2025-07-21 14:34", "2025-07-21 14:35", "2025-07-21 14:36", "2025-07-21 14:37", "2025-07-21 14:38", "2025-07-21 14:39", "2025-07-21 14:40", "2025-07-21 14:41", "2025-07-21 14:42", "2025-07-21 14:43", "2025-07-21 14:44", "2025-07-21 14:45", "2025-07-21 14:46", "2025-07-21 14:47", "2025-07-21 14:48", "2025-07-21 14:49", "2025-07-21 14:50", "2025-07-21 14:51", "2025-07-21 14:52", "2025-07-21 14:53", "2025-07-21 14:54", "2025-07-21 14:55", "2025-07-21 14:56", "2025-07-21 14:57", "2025-07-21 14:58", "2025-07-21 14:59", "2025-07-21 15:00", "2025-07-21 15:01", "2025-07-21 15:02", "2025-07-21 15:03", "2025-07-21 15:04", "2025-07-21 15:05", "2025-07-21 15:06", "2025-07-21 15:07", "2025-07-21 15:08", "2025-07-21 15:09", "2025-07-21 15:10", "2025-07-21 15:11", "2025-07-21 15:12", "2025-07-21 15:13", "2025-07-21 15:14", "2025-07-21 15:15", "2025-07-21 15:16", "2025-07-21 15:17", "2025-07-21 15:18", "2025-07-21 15:19", "2025-07-21 15:20", "2025-07-21 15:21", "2025-07-21 15:22", "2025-07-21 15:23", "2025-07-21 15:24", "2025-07-21 15:25", "2025-07-21 15:26", "2025-07-21 15:27", "2025-07-21 15:28", "2025-07-21 15:29", "2025-07-21 15:30", "2025-07-21 15:31", "2025-07-21 15:32", "2025-07-21 15:33", "2025-07-21 15:34", "2025-07-21 15:35", "2025-07-21 15:36", "2025-07-21 15:37", "2025-07-21 15:38", "2025-07-21 15:39", "2025-07-21 15:40", "2025-07-21 15:41", "2025-07-21 15:42", "2025-07-21 15:43", "2025-07-21 15:44", "2025-07-21 15:45", "2025-07-21 15:46", "2025-07-21 15:47", "2025-07-21 15:48", "2025-07-21 15:49", "2025-07-21 15:50", "2025-07-21 15:51", "2025-07-21 15:52", "2025-07-21 15:53", "2025-07-21 15:54", "2025-07-21 15:55", "2025-07-21 15:56", "2025-07-21 15:57", "2025-07-21 15:58", "2025-07-21 15:59", "2025-07-21 16:00", "2025-07-21 16:01", "2025-07-21 16:02", "2025-07-21 16:03", "2025-07-21 16:04", "2025-07-21 16:05", "2025-07-21 16:06", "2025-07-21 16:07", "2025-07-21 16:08", "2025-07-21 16:09", "2025-07-21 16:10", "2025-07-21 16:11", "2025-07-21 16:12", "2025-07-21 16:13", "2025-07-21 16:14", "2025-07-21 16:15", "2025-07-21 16:16", "2025-07-21 16:17", "2025-07-21 16:18", "2025-07-21 16:19", "2025-07-21 16:20", "2025-07-21 16:21", "2025-07-21 16:22", "2025-07-21 16:23", "2025-07-21 16:24", "2025-07-21 16:25", "2025-07-21 16:26", "2025-07-21 16:27", "2025-07-21 16:28", "2025-07-21 16:29", "2025-07-21 16:30", "2025-07-21 16:31", "2025-07-21 16:32", "2025-07-21 16:33", "2025-07-21 16:34", "2025-07-21 16:35", "2025-07-21 16:36", "2025-07-21 16:37", "2025-07-21 16:38", "2025-07-21 16:39", "2025-07-21 16:40", "2025-07-21 16:41", "2025-07-21 16:42", "2025-07-21 16:43", "2025-07-21 16:44", "2025-07-21 16:45", "2025-07-21 16:46", "2025-07-21 16:47", "2025-07-21 16:48", "2025-07-21 16:49", "2025-07-21 16:50", "2025-07-21 16:51", "2025-07-21 16:52", "2025-07-21 16:53", "2025-07-21 16:54", "2025-07-21 16:55", "2025-07-21 16:56", "2025-07-21 16:57", "2025-07-21 16:58", "2025-07-21 16:59", "2025-07-21 17:00", "2025-07-21 17:01", "2025-07-21 17:02", "2025-07-21 17:03", "2025-07-21 17:04", "2025-07-21 17:05", "2025-07-21 17:06", "2025-07-21 17:07", "2025-07-21 17:08", "2025-07-21 17:09", "2025-07-21 17:10", "2025-07-21 17:11", "2025-07-21 17:12", "2025-07-21 17:13", "2025-07-21 17:14", "2025-07-21 17:15", "2025-07-21 17:16", "2025-07-21 17:17", "2025-07-21 17:18", "2025-07-21 17:19", "2025-07-21 17:20", "2025-07-21 17:21", "2025-07-21 17:22", "2025-07-21 17:23", "2025-07-21 17:24", "2025-07-21 17:25", "2025-07-21 17:26", "2025-07-21 17:27", "2025-07-21 17:28", "2025-07-21 17:29", "2025-07-21 17:30", "2025-07-21 17:31", "2025-07-21 17:32", "2025-07-21 17:33", "2025-07-21 17:34", "2025-07-21 17:35", "2025-07-21 17:36", "2025-07-21 17:37", "2025-07-21 17:38", "2025-07-21 17:39", "2025-07-21 17:40", "2025-07-21 17:41", "2025-07-21 17:42", "2025-07-21 17:43", "2025-07-21 17:44", "2025-07-21 17:45", "2025-07-21 17:46", "2025-07-21 17:47", "2025-07-21 17:48", "2025-07-21 17:49", "2025-07-21 17:50", "2025-07-21 17:51", "2025-07-21 17:52", "2025-07-21 17:53", "2025-07-21 17:54", "2025-07-21 17:55", "2025-07-21 17:56", "2025-07-21 17:57", "2025-07-21 17:58", "2025-07-21 17:59", "2025-07-21 18:00", "2025-07-21 18:01", "2025-07-21 18:02", "2025-07-21 18:03", "2025-07-21 18:04", "2025-07-21 18:05", "2025-07-21 18:06", "2025-07-21 18:07", "2025-07-21 18:08", "2025-07-21 18:09", "2025-07-21 18:10", "2025-07-21 18:11", "2025-07-21 18:12", "2025-07-21 18:13", "2025-07-21 18:14", "2025-07-21 18:15", "2025-07-21 18:16", "2025-07-21 18:17", "2025-07-21 18:18", "2025-07-21 18:19", "2025-07-21 18:20", "2025-07-21 18:21", "2025-07-21 18:22", "2025-07-21 18:23", "2025-07-21 18:24", "2025-07-21 18:25", "2025-07-21 18:26", "2025-07-21 18:27", "2025-07-21 18:28", "2025-07-21 18:29", "2025-07-21 18:30", "2025-07-21 18:31", "2025-07-21 18:32", "2025-07-21 18:33", "2025-07-21 18:34", "2025-07-21 18:35", "2025-07-21 18:36", "2025-07-21 18:37", "2025-07-21 18:38", "2025-07-21 18:39", "2025-07-21 18:40", "2025-07-21 18:41", "2025-07-21 18:42", "2025-07-21 18:43", "2025-07-21 18:44", "2025-07-21 18:45", "2025-07-21 18:46", "2025-07-21 18:47", "2025-07-21 18:48", "2025-07-21 18:49", "2025-07-21 18:50", "2025-07-21 18:51", "2025-07-21 18:52", "2025-07-21 18:53", "2025-07-21 18:54", "2025-07-21 18:55", "2025-07-21 18:56", "2025-07-21 18:57", "2025-07-21 18:58", "2025-07-21 18:59", "2025-07-21 19:00", "2025-07-21 19:01", "2025-07-21 19:02", "2025-07-21 19:03", "2025-07-21 19:04", "2025-07-21 19:05", "2025-07-21 19:06", "2025-07-21 19:07", "2025-07-21 19:08", "2025-07-21 19:09", "2025-07-21 19:10", "2025-07-21 19:11", "2025-07-21 19:12", "2025-07-21 19:13", "2025-07-21 19:14", "2025-07-21 19:15", "2025-07-21 19:16", "2025-07-21 19:17", "2025-07-21 19:18", "2025-07-21 19:19", "2025-07-21 19:20", "2025-07-21 19:21", "2025-07-21 19:22", "2025-07-21 19:23", "2025-07-21 19:24", "2025-07-21 19:25", "2025-07-21 19:26", "2025-07-21 19:27", "2025-07-21 19:28", "2025-07-21 19:29", "2025-07-21 19:30", "2025-07-21 19:31", "2025-07-21 19:32", "2025-07-21 19:33", "2025-07-21 19:34", "2025-07-21 19:35", "2025-07-21 19:36", "2025-07-21 19:37", "2025-07-21 19:38", "2025-07-21 19:39", "2025-07-21 19:40", "2025-07-21 19:41", "2025-07-21 19:42", "2025-07-21 19:43", "2025-07-21 19:44", "2025-07-21 19:45", "2025-07-21 19:46", "2025-07-21 19:47", "2025-07-21 19:48", "2025-07-21 19:49", "2025-07-21 19:50", "2025-07-21 19:51", "2025-07-21 19:52", "2025-07-21 19:53", "2025-07-21 19:54", "2025-07-21 19:55", "2025-07-21 19:56", "2025-07-21 19:57", "2025-07-21 19:58", "2025-07-21 19:59", "2025-07-21 20:00", "2025-07-21 20:01", "2025-07-21 20:02", "2025-07-21 20:03", "2025-07-21 20:04", "2025-07-21 20:05", "2025-07-21 20:06", "2025-07-21 20:07", "2025-07-21 20:08", "2025-07-21 20:09", "2025-07-21 20:10", "2025-07-21 20:11", "2025-07-21 20:12", "2025-07-21 20:13", "2025-07-21 20:14", "2025-07-21 20:15", "2025-07-21 20:16", "2025-07-21 20:17", "2025-07-21 20:18", "2025-07-21 20:19", "2025-07-21 20:20", "2025-07-21 20:21", "2025-07-21 20:22", "2025-07-21 20:23", "2025-07-21 20:24", "2025-07-21 20:25", "2025-07-21 20:26", "2025-07-21 20:27", "2025-07-21 20:28", "2025-07-21 20:29", "2025-07-21 20:30", "2025-07-21 20:31", "2025-07-21 20:32", "2025-07-21 20:33", "2025-07-21 20:34", "2025-07-21 20:35", "2025-07-21 20:36", "2025-07-21 20:37", "2025-07-21 20:38", "2025-07-21 20:39", "2025-07-21 20:40", "2025-07-21 20:41", "2025-07-21 20:42", "2025-07-21 20:43", "2025-07-21 20:44", "2025-07-21 20:45", "2025-07-21 20:46", "2025-07-21 20:47", "2025-07-21 20:48", "2025-07-21 20:49", "2025-07-21 20:50", "2025-07-21 20:51", "2025-07-21 20:52", "2025-07-21 20:53", "2025-07-21 20:54", "2025-07-21 20:55", "2025-07-21 20:56", "2025-07-21 20:57", "2025-07-21 20:58", "2025-07-21 20:59", "2025-07-21 21:00", "2025-07-21 21:01", "2025-07-21 21:02", "2025-07-21 21:03", "2025-07-21 21:04", "2025-07-21 21:05", "2025-07-21 21:06", "2025-07-21 21:07", "2025-07-21 21:08", "2025-07-21 21:09", "2025-07-21 21:10", "2025-07-21 21:11", "2025-07-21 21:12", "2025-07-21 21:13", "2025-07-21 21:14", "2025-07-21 21:15", "2025-07-21 21:16", "2025-07-21 21:17", "2025-07-21 21:18", "2025-07-21 21:19", "2025-07-21 21:20", "2025-07-21 21:21", "2025-07-21 21:22", "2025-07-21 21:23", "2025-07-21 21:24", "2025-07-21 21:25", "2025-07-21 21:26", "2025-07-21 21:27", "2025-07-21 21:28", "2025-07-21 21:29", "2025-07-21 21:30", "2025-07-21 21:31", "2025-07-21 21:32", "2025-07-21 21:33", "2025-07-21 21:34", "2025-07-21 21:35", "2025-07-21 21:36", "2025-07-21 21:37", "2025-07-21 21:38", "2025-07-21 21:39", "2025-07-21 21:40", "2025-07-21 21:41", "2025-07-21 21:42", "2025-07-21 21:43", "2025-07-21 21:44", "2025-07-21 21:45", "2025-07-21 21:46", "2025-07-21 21:47", "2025-07-21 21:48", "2025-07-21 21:49", "2025-07-21 21:50", "2025-07-21 21:51", "2025-07-21 21:52", "2025-07-21 21:53", "2025-07-21 21:54", "2025-07-21 21:55", "2025-07-21 21:56", "2025-07-21 21:57", "2025-07-21 21:58", "2025-07-21 21:59", "2025-07-21 22:00", "2025-07-21 22:01", "2025-07-21 22:02", "2025-07-21 22:03", "2025-07-21 22:04", "2025-07-21 22:05", "2025-07-21 22:06", "2025-07-21 22:07", "2025-07-21 22:08", "2025-07-21 22:09", "2025-07-21 22:10", "2025-07-21 22:11", "2025-07-21 22:12", "2025-07-21 22:13", "2025-07-21 22:14", "2025-07-21 22:15", "2025-07-21 22:16", "2025-07-21 22:17", "2025-07-21 22:18", "2025-07-21 22:19", "2025-07-21 22:20", "2025-07-21 22:21", "2025-07-21 22:22", "2025-07-21 22:23", "2025-07-21 22:24", "2025-07-21 22:25", "2025-07-21 22:26", "2025-07-21 22:27", "2025-07-21 22:28", "2025-07-21 22:29", "2025-07-21 22:30", "2025-07-21 22:31", "2025-07-21 22:32", "2025-07-21 22:33", "2025-07-21 22:34", "2025-07-21 22:35", "2025-07-21 22:36", "2025-07-21 22:37", "2025-07-21 22:38", "2025-07-21 22:39", "2025-07-21 22:40", "2025-07-21 22:41", "2025-07-21 22:42", "2025-07-21 22:43", "2025-07-21 22:44", "2025-07-21 22:45", "2025-07-21 22:46", "2025-07-21 22:47", "2025-07-21 22:48", "2025-07-21 22:49", "2025-07-21 22:50", "2025-07-21 22:51", "2025-07-21 22:52", "2025-07-21 22:53", "2025-07-21 22:54", "2025-07-21 22:55", "2025-07-21 22:56", "2025-07-21 22:57", "2025-07-21 22:58", "2025-07-21 22:59", "2025-07-21 23:00", "2025-07-21 23:01", "2025-07-21 23:02", "2025-07-21 23:03", "2025-07-21 23:04", "2025-07-21 23:05", "2025-07-21 23:06", "2025-07-21 23:07", "2025-07-21 23:08", "2025-07-21 23:09", "2025-07-21 23:10", "2025-07-21 23:11", "2025-07-21 23:12", "2025-07-21 23:13", "2025-07-21 23:14", "2025-07-21 23:15", "2025-07-21 23:16", "2025-07-21 23:17", "2025-07-21 23:18", "2025-07-21 23:19", "2025-07-21 23:20", "2025-07-21 23:21", "2025-07-21 23:22", "2025-07-21 23:23", "2025-07-21 23:24", "2025-07-21 23:25", "2025-07-21 23:26", "2025-07-21 23:27", "2025-07-21 23:28", "2025-07-21 23:29", "2025-07-21 23:30", "2025-07-21 23:31", "2025-07-21 23:32", "2025-07-21 23:33", "2025-07-21 23:34", "2025-07-21 23:35", "2025-07-21 23:36", "2025-07-21 23:37", "2025-07-21 23:38", "2025-07-21 23:39", "2025-07-21 23:40", "2025-07-21 23:41", "2025-07-21 23:42", "2025-07-21 23:43", "2025-07-21 23:44", "2025-07-21 23:45", "2025-07-21 23:46", "2025-07-21 23:47", "2025-07-21 23:48", "2025-07-21 23:49", "2025-07-21 23:50", "2025-07-21 23:51", "2025-07-21 23:52", "2025-07-21 23:53", "2025-07-21 23:54", "2025-07-21 23:55", "2025-07-21 23:56", "2025-07-21 23:57", "2025-07-21 23:58", "2025-07-21 23:59", "2025-07-22 00:00", "2025-07-22 00:01", "2025-07-22 00:02", "2025-07-22 00:03", "2025-07-22 00:04", "2025-07-22 00:05", "2025-07-22 00:06", "2025-07-22 00:07", "2025-07-22 00:08", "2025-07-22 00:09", "2025-07-22 00:10", "2025-07-22 00:11", "2025-07-22 00:12", "2025-07-22 00:13", "2025-07-22 00:14", "2025-07-22 00:15", "2025-07-22 00:16", "2025-07-22 00:17", "2025-07-22 00:18", "2025-07-22 00:19", "2025-07-22 00:20", "2025-07-22 00:21", "2025-07-22 00:22", "2025-07-22 00:23", "2025-07-22 00:24", "2025-07-22 00:25", "2025-07-22 00:26", "2025-07-22 00:27", "2025-07-22 00:28", "2025-07-22 00:29", "2025-07-22 00:30", "2025-07-22 00:31", "2025-07-22 00:32", "2025-07-22 00:33", "2025-07-22 00:34", "2025-07-22 00:35", "2025-07-22 00:36", "2025-07-22 00:37", "2025-07-22 00:38", "2025-07-22 00:39", "2025-07-22 00:40", "2025-07-22 00:41", "2025-07-22 00:42", "2025-07-22 00:43", "2025-07-22 00:44", "2025-07-22 00:45", "2025-07-22 00:46", "2025-07-22 00:47", "2025-07-22 00:48", "2025-07-22 00:49", "2025-07-22 00:50", "2025-07-22 00:51", "2025-07-22 00:52", "2025-07-22 00:53", "2025-07-22 00:54", "2025-07-22 00:55", "2025-07-22 00:56", "2025-07-22 00:57", "2025-07-22 00:58", "2025-07-22 00:59", "2025-07-22 01:00", "2025-07-22 01:01", "2025-07-22 01:02", "2025-07-22 01:03", "2025-07-22 01:04", "2025-07-22 01:05", "2025-07-22 01:06", "2025-07-22 01:07", "2025-07-22 01:08", "2025-07-22 01:09", "2025-07-22 01:10", "2025-07-22 01:11", "2025-07-22 01:12", "2025-07-22 01:13", "2025-07-22 01:14", "2025-07-22 01:15", "2025-07-22 01:16", "2025-07-22 01:17", "2025-07-22 01:18", "2025-07-22 01:19", "2025-07-22 01:20", "2025-07-22 01:21", "2025-07-22 01:22", "2025-07-22 01:23", "2025-07-22 01:24", "2025-07-22 01:25", "2025-07-22 01:26", "2025-07-22 01:27", "2025-07-22 01:28", "2025-07-22 01:29", "2025-07-22 01:30", "2025-07-22 01:31", "2025-07-22 01:32", "2025-07-22 01:33", "2025-07-22 01:34", "2025-07-22 01:35", "2025-07-22 01:36", "2025-07-22 01:37", "2025-07-22 01:38", "2025-07-22 01:39", "2025-07-22 01:40", "2025-07-22 01:41", "2025-07-22 01:42", "2025-07-22 01:43", "2025-07-22 01:44", "2025-07-22 01:45", "2025-07-22 01:46", "2025-07-22 01:47", "2025-07-22 01:48", "2025-07-22 01:49", "2025-07-22 01:50", "2025-07-22 01:51", "2025-07-22 01:52", "2025-07-22 01:53", "2025-07-22 01:54", "2025-07-22 01:55", "2025-07-22 01:56", "2025-07-22 01:57", "2025-07-22 01:58", "2025-07-22 01:59", "2025-07-22 02:00", "2025-07-22 02:01", "2025-07-22 02:02", "2025-07-22 02:03", "2025-07-22 02:04", "2025-07-22 02:05", "2025-07-22 02:06", "2025-07-22 02:07", "2025-07-22 02:08", "2025-07-22 02:09", "2025-07-22 02:10", "2025-07-22 02:11", "2025-07-22 02:12", "2025-07-22 02:13", "2025-07-22 02:14", "2025-07-22 02:15", "2025-07-22 02:16", "2025-07-22 02:17", "2025-07-22 02:18", "2025-07-22 02:19", "2025-07-22 02:20", "2025-07-22 02:21", "2025-07-22 02:22", "2025-07-22 02:23", "2025-07-22 02:24", "2025-07-22 02:25", "2025-07-22 02:26", "2025-07-22 02:27", "2025-07-22 02:28", "2025-07-22 02:29", "2025-07-22 02:30", "2025-07-22 02:31", "2025-07-22 02:32", "2025-07-22 02:33", "2025-07-22 02:34", "2025-07-22 02:35", "2025-07-22 02:36", "2025-07-22 02:37", "2025-07-22 02:38", "2025-07-22 02:39", "2025-07-22 02:40", "2025-07-22 02:41", "2025-07-22 02:42", "2025-07-22 02:43", "2025-07-22 02:44", "2025-07-22 02:45", "2025-07-22 02:46", "2025-07-22 02:47", "2025-07-22 02:48", "2025-07-22 02:49", "2025-07-22 02:50", "2025-07-22 02:51", "2025-07-22 02:52", "2025-07-22 02:53", "2025-07-22 02:54", "2025-07-22 02:55", "2025-07-22 02:56", "2025-07-22 02:57", "2025-07-22 02:58", "2025-07-22 02:59", "2025-07-22 03:00", "2025-07-22 03:01", "2025-07-22 03:02", "2025-07-22 03:03", "2025-07-22 03:04", "2025-07-22 03:05", "2025-07-22 03:06", "2025-07-22 03:07", "2025-07-22 03:08", "2025-07-22 03:09", "2025-07-22 03:10", "2025-07-22 03:11", "2025-07-22 03:12", "2025-07-22 03:13", "2025-07-22 03:14", "2025-07-22 03:15", "2025-07-22 03:16", "2025-07-22 03:17", "2025-07-22 03:18", "2025-07-22 03:19", "2025-07-22 03:20", "2025-07-22 03:21", "2025-07-22 03:22", "2025-07-22 03:23", "2025-07-22 03:24", "2025-07-22 03:25", "2025-07-22 03:26", "2025-07-22 03:27", "2025-07-22 03:28", "2025-07-22 03:29", "2025-07-22 03:30", "2025-07-22 03:31", "2025-07-22 03:32", "2025-07-22 03:33", "2025-07-22 03:34", "2025-07-22 03:35", "2025-07-22 03:36", "2025-07-22 03:37", "2025-07-22 03:38", "2025-07-22 03:39", "2025-07-22 03:40", "2025-07-22 03:41", "2025-07-22 03:42", "2025-07-22 03:43", "2025-07-22 03:44", "2025-07-22 03:45", "2025-07-22 03:46", "2025-07-22 03:47", "2025-07-22 03:48", "2025-07-22 03:49", "2025-07-22 03:50", "2025-07-22 03:51", "2025-07-22 03:52", "2025-07-22 03:53", "2025-07-22 03:54", "2025-07-22 03:55", "2025-07-22 03:56", "2025-07-22 03:57", "2025-07-22 03:58", "2025-07-22 03:59", "2025-07-22 04:00", "2025-07-22 04:01", "2025-07-22 04:02", "2025-07-22 04:03", "2025-07-22 04:04", "2025-07-22 04:05", "2025-07-22 04:06", "2025-07-22 04:07", "2025-07-22 04:08", "2025-07-22 04:09", "2025-07-22 04:10", "2025-07-22 04:11", "2025-07-22 04:12", "2025-07-22 04:13", "2025-07-22 04:14", "2025-07-22 04:15", "2025-07-22 04:16", "2025-07-22 04:17", "2025-07-22 04:18", "2025-07-22 04:19", "2025-07-22 04:20", "2025-07-22 04:21", "2025-07-22 04:22", "2025-07-22 04:23", "2025-07-22 04:24", "2025-07-22 04:25", "2025-07-22 04:26", "2025-07-22 04:27", "2025-07-22 04:28", "2025-07-22 04:29", "2025-07-22 04:30", "2025-07-22 04:31", "2025-07-22 04:32", "2025-07-22 04:33", "2025-07-22 04:34", "2025-07-22 04:35", "2025-07-22 04:36", "2025-07-22 04:37", "2025-07-22 04:38", "2025-07-22 04:39", "2025-07-22 04:40", "2025-07-22 04:41", "2025-07-22 04:42", "2025-07-22 04:43", "2025-07-22 04:44", "2025-07-22 04:45", "2025-07-22 04:46", "2025-07-22 04:47", "2025-07-22 04:48", "2025-07-22 04:49", "2025-07-22 04:50", "2025-07-22 04:51", "2025-07-22 04:52", "2025-07-22 04:53", "2025-07-22 04:54", "2025-07-22 04:55", "2025-07-22 04:56", "2025-07-22 04:57", "2025-07-22 04:58", "2025-07-22 04:59", "2025-07-22 05:00", "2025-07-22 05:01", "2025-07-22 05:02", "2025-07-22 05:03", "2025-07-22 05:04", "2025-07-22 05:05", "2025-07-22 05:06", "2025-07-22 05:07", "2025-07-22 05:08", "2025-07-22 05:09", "2025-07-22 05:10", "2025-07-22 05:11", "2025-07-22 05:12", "2025-07-22 05:13", "2025-07-22 05:14", "2025-07-22 05:15", "2025-07-22 05:16", "2025-07-22 05:17", "2025-07-22 05:18", "2025-07-22 05:19", "2025-07-22 05:20", "2025-07-22 05:21", "2025-07-22 05:22", "2025-07-22 05:23", "2025-07-22 05:24", "2025-07-22 05:25", "2025-07-22 05:26", "2025-07-22 05:27", "2025-07-22 05:28", "2025-07-22 05:29", "2025-07-22 05:30", "2025-07-22 05:31", "2025-07-22 05:32", "2025-07-22 05:33", "2025-07-22 05:34", "2025-07-22 05:35", "2025-07-22 05:36", "2025-07-22 05:37", "2025-07-22 05:38", "2025-07-22 05:39", "2025-07-22 05:40", "2025-07-22 05:41", "2025-07-22 05:42", "2025-07-22 05:43", "2025-07-22 05:44", "2025-07-22 05:45", "2025-07-22 05:46", "2025-07-22 05:47", "2025-07-22 05:48", "2025-07-22 05:49", "2025-07-22 05:50", "2025-07-22 05:51", "2025-07-22 05:52", "2025-07-22 05:53", "2025-07-22 05:54", "2025-07-22 05:55", "2025-07-22 05:56", "2025-07-22 05:57", "2025-07-22 05:58", "2025-07-22 05:59", "2025-07-22 06:00", "2025-07-22 06:01", "2025-07-22 06:02", "2025-07-22 06:03", "2025-07-22 06:04", "2025-07-22 06:05", "2025-07-22 06:06", "2025-07-22 06:07", "2025-07-22 06:08", "2025-07-22 06:09", "2025-07-22 06:10", "2025-07-22 06:11", "2025-07-22 06:12", "2025-07-22 06:13", "2025-07-22 06:14", "2025-07-22 06:15", "2025-07-22 06:16", "2025-07-22 06:17", "2025-07-22 06:18", "2025-07-22 06:19", "2025-07-22 06:20", "2025-07-22 06:21", "2025-07-22 06:22", "2025-07-22 06:23", "2025-07-22 06:24", "2025-07-22 06:25", "2025-07-22 06:26", "2025-07-22 06:27", "2025-07-22 06:28", "2025-07-22 06:29", "2025-07-22 06:30", "2025-07-22 06:31", "2025-07-22 06:32", "2025-07-22 06:33", "2025-07-22 06:34", "2025-07-22 06:35", "2025-07-22 06:36", "2025-07-22 06:37", "2025-07-22 06:38", "2025-07-22 06:39", "2025-07-22 06:40", "2025-07-22 06:41", "2025-07-22 06:42", "2025-07-22 06:43", "2025-07-22 06:44", "2025-07-22 06:45", "2025-07-22 06:46", "2025-07-22 06:47", "2025-07-22 06:48", "2025-07-22 06:49", "2025-07-22 06:50", "2025-07-22 06:51", "2025-07-22 06:52", "2025-07-22 06:53", "2025-07-22 06:54", "2025-07-22 06:55", "2025-07-22 06:56", "2025-07-22 06:57", "2025-07-22 06:58", "2025-07-22 06:59", "2025-07-22 07:00", "2025-07-22 07:01", "2025-07-22 07:02", "2025-07-22 07:03", "2025-07-22 07:04", "2025-07-22 07:05", "2025-07-22 07:06", "2025-07-22 07:07", "2025-07-22 07:08", "2025-07-22 07:09", "2025-07-22 07:10", "2025-07-22 07:11", "2025-07-22 07:12", "2025-07-22 07:13", "2025-07-22 07:14", "2025-07-22 07:15", "2025-07-22 07:16", "2025-07-22 07:17", "2025-07-22 07:18", "2025-07-22 07:19", "2025-07-22 07:20", "2025-07-22 07:21", "2025-07-22 07:22", "2025-07-22 07:23", "2025-07-22 07:24", "2025-07-22 07:25", "2025-07-22 07:26", "2025-07-22 07:27", "2025-07-22 07:28", "2025-07-22 07:29", "2025-07-22 07:30", "2025-07-22 07:31", "2025-07-22 07:32", "2025-07-22 07:33", "2025-07-22 07:34", "2025-07-22 07:35", "2025-07-22 07:36", "2025-07-22 07:37", "2025-07-22 07:38", "2025-07-22 07:39", "2025-07-22 07:40", "2025-07-22 07:41", "2025-07-22 07:42", "2025-07-22 07:43", "2025-07-22 07:44", "2025-07-22 07:45", "2025-07-22 07:46", "2025-07-22 07:47", "2025-07-22 07:48", "2025-07-22 07:49", "2025-07-22 07:50", "2025-07-22 07:51", "2025-07-22 07:52", "2025-07-22 07:53", "2025-07-22 07:54", "2025-07-22 07:55", "2025-07-22 07:56", "2025-07-22 07:57", "2025-07-22 07:58", "2025-07-22 07:59", "2025-07-22 08:00", "2025-07-22 08:01", "2025-07-22 08:02", "2025-07-22 08:03", "2025-07-22 08:04", "2025-07-22 08:05", "2025-07-22 08:06", "2025-07-22 08:07", "2025-07-22 08:08", "2025-07-22 08:09", "2025-07-22 08:10", "2025-07-22 08:11", "2025-07-22 08:12", "2025-07-22 08:13", "2025-07-22 08:14", "2025-07-22 08:15", "2025-07-22 08:16", "2025-07-22 08:17", "2025-07-22 08:18", "2025-07-22 08:19", "2025-07-22 08:20", "2025-07-22 08:21", "2025-07-22 08:22", "2025-07-22 08:23", "2025-07-22 08:24", "2025-07-22 08:25", "2025-07-22 08:26", "2025-07-22 08:27", "2025-07-22 08:28", "2025-07-22 08:29", "2025-07-22 08:30", "2025-07-22 08:31", "2025-07-22 08:32", "2025-07-22 08:33", "2025-07-22 08:34", "2025-07-22 08:35", "2025-07-22 08:36", "2025-07-22 08:37", "2025-07-22 08:38", "2025-07-22 08:39", "2025-07-22 08:40", "2025-07-22 08:41", "2025-07-22 08:42", "2025-07-22 08:43", "2025-07-22 08:44", "2025-07-22 08:45", "2025-07-22 08:46", "2025-07-22 08:47", "2025-07-22 08:48", "2025-07-22 08:49", "2025-07-22 08:50", "2025-07-22 08:51", "2025-07-22 08:52", "2025-07-22 08:53", "2025-07-22 08:54", "2025-07-22 08:55", "2025-07-22 08:56", "2025-07-22 08:57", "2025-07-22 08:58", "2025-07-22 08:59", "2025-07-22 09:00", "2025-07-22 09:01", "2025-07-22 09:02", "2025-07-22 09:03", "2025-07-22 09:04", "2025-07-22 09:05", "2025-07-22 09:06", "2025-07-22 09:07", "2025-07-22 09:08", "2025-07-22 09:09", "2025-07-22 09:10", "2025-07-22 09:11", "2025-07-22 09:12", "2025-07-22 09:13", "2025-07-22 09:14", "2025-07-22 09:15", "2025-07-22 09:16", "2025-07-22 09:17", "2025-07-22 09:18", "2025-07-22 09:19", "2025-07-22 09:20", "2025-07-22 09:21", "2025-07-22 09:22", "2025-07-22 09:23", "2025-07-22 09:24", "2025-07-22 09:25", "2025-07-22 09:26", "2025-07-22 09:27", "2025-07-22 09:28", "2025-07-22 09:29", "2025-07-22 09:30", "2025-07-22 09:31", "2025-07-22 09:32", "2025-07-22 09:33", "2025-07-22 09:34", "2025-07-22 09:35", "2025-07-22 09:36", "2025-07-22 09:37", "2025-07-22 09:38", "2025-07-22 09:39", "2025-07-22 09:40", "2025-07-22 09:41", "2025-07-22 09:42", "2025-07-22 09:43", "2025-07-22 09:44", "2025-07-22 09:45", "2025-07-22 09:46", "2025-07-22 09:47", "2025-07-22 09:48", "2025-07-22 09:49", "2025-07-22 09:50", "2025-07-22 09:51", "2025-07-22 09:52", "2025-07-22 09:53", "2025-07-22 09:54", "2025-07-22 09:55", "2025-07-22 09:56", "2025-07-22 09:57", "2025-07-22 09:58", "2025-07-22 09:59", "2025-07-22 10:00", "2025-07-22 10:01", "2025-07-22 10:02", "2025-07-22 10:03", "2025-07-22 10:04", "2025-07-22 10:05", "2025-07-22 10:06", "2025-07-22 10:07", "2025-07-22 10:08", "2025-07-22 10:09", "2025-07-22 10:10", "2025-07-22 10:11", "2025-07-22 10:12", "2025-07-22 10:13", "2025-07-22 10:14", "2025-07-22 10:15", "2025-07-22 10:16", "2025-07-22 10:17", "2025-07-22 10:18", "2025-07-22 10:19", "2025-07-22 10:20", "2025-07-22 10:21", "2025-07-22 10:22", "2025-07-22 10:23", "2025-07-22 10:24", "2025-07-22 10:25", "2025-07-22 10:26", "2025-07-22 10:27", "2025-07-22 10:28", "2025-07-22 10:29", "2025-07-22 10:30", "2025-07-22 10:31", "2025-07-22 10:32", "2025-07-22 10:33", "2025-07-22 10:34", "2025-07-22 10:35", "2025-07-22 10:36", "2025-07-22 10:37", "2025-07-22 10:38", "2025-07-22 10:39", "2025-07-22 10:40", "2025-07-22 10:41", "2025-07-22 10:42", "2025-07-22 10:43", "2025-07-22 10:44", "2025-07-22 10:45", "2025-07-22 10:46", "2025-07-22 10:47", "2025-07-22 10:48", "2025-07-22 10:49", "2025-07-22 10:50", "2025-07-22 10:51", "2025-07-22 10:52", "2025-07-22 10:53", "2025-07-22 10:54", "2025-07-22 10:55", "2025-07-22 10:56", "2025-07-22 10:57", "2025-07-22 10:58", "2025-07-22 10:59", "2025-07-22 11:00", "2025-07-22 11:01", "2025-07-22 11:02", "2025-07-22 11:03", "2025-07-22 11:04", "2025-07-22 11:05", "2025-07-22 11:06", "2025-07-22 11:07", "2025-07-22 11:08", "2025-07-22 11:09", "2025-07-22 11:10", "2025-07-22 11:11", "2025-07-22 11:12", "2025-07-22 11:13", "2025-07-22 11:14", "2025-07-22 11:15", "2025-07-22 11:16", "2025-07-22 11:17", "2025-07-22 11:18", "2025-07-22 11:19", "2025-07-22 11:20", "2025-07-22 11:21", "2025-07-22 11:22", "2025-07-22 11:23", "2025-07-22 11:24", "2025-07-22 11:25", "2025-07-22 11:26", "2025-07-22 11:27", "2025-07-22 11:28", "2025-07-22 11:29", "2025-07-22 11:30", "2025-07-22 11:31", "2025-07-22 11:32", "2025-07-22 11:33", "2025-07-22 11:34", "2025-07-22 11:35", "2025-07-22 11:36", "2025-07-22 11:37", "2025-07-22 11:38", "2025-07-22 11:39", "2025-07-22 11:40", "2025-07-22 11:41", "2025-07-22 11:42", "2025-07-22 11:43", "2025-07-22 11:44", "2025-07-22 11:45", "2025-07-22 11:46", "2025-07-22 11:47", "2025-07-22 11:48", "2025-07-22 11:49", "2025-07-22 11:50", "2025-07-22 11:51", "2025-07-22 11:52", "2025-07-22 11:53", "2025-07-22 11:54", "2025-07-22 11:55", "2025-07-22 11:56", "2025-07-22 11:57", "2025-07-22 11:58", "2025-07-22 11:59", "2025-07-22 12:00", "2025-07-22 12:01", "2025-07-22 12:02", "2025-07-22 12:03", "2025-07-22 12:04", "2025-07-22 12:05", "2025-07-22 12:06", "2025-07-22 12:07", "2025-07-22 12:08", "2025-07-22 12:09", "2025-07-22 12:10", "2025-07-22 12:11", "2025-07-22 12:12", "2025-07-22 12:13", "2025-07-22 12:14", "2025-07-22 12:15", "2025-07-22 12:16", "2025-07-22 12:17", "2025-07-22 12:18", "2025-07-22 12:19", "2025-07-22 12:20", "2025-07-22 12:21", "2025-07-22 12:22", "2025-07-22 12:23", "2025-07-22 12:24", "2025-07-22 12:25", "2025-07-22 12:26", "2025-07-22 12:27", "2025-07-22 12:28", "2025-07-22 12:29", "2025-07-22 12:30", "2025-07-22 12:31", "2025-07-22 12:32", "2025-07-22 12:33", "2025-07-22 12:34", "2025-07-22 12:35", "2025-07-22 12:36", "2025-07-22 12:37", "2025-07-22 12:38", "2025-07-22 12:39", "2025-07-22 12:40", "2025-07-22 12:41", "2025-07-22 12:42", "2025-07-22 12:43", "2025-07-22 12:44", "2025-07-22 12:45", "2025-07-22 12:46", "2025-07-22 12:47", "2025-07-22 12:48", "2025-07-22 12:49", "2025-07-22 12:50", "2025-07-22 12:51", "2025-07-22 12:52", "2025-07-22 12:53", "2025-07-22 12:54", "2025-07-22 12:55", "2025-07-22 12:56", "2025-07-22 12:57", "2025-07-22 12:58", "2025-07-22 12:59", "2025-07-22 13:00", "2025-07-22 13:01", "2025-07-22 13:02", "2025-07-22 13:03", "2025-07-22 13:04", "2025-07-22 13:05", "2025-07-22 13:06", "2025-07-22 13:07", "2025-07-22 13:08", "2025-07-22 13:09", "2025-07-22 13:10", "2025-07-22 13:11", "2025-07-22 13:12", "2025-07-22 13:13", "2025-07-22 13:14", "2025-07-22 13:15", "2025-07-22 13:16", "2025-07-22 13:17", "2025-07-22 13:18", "2025-07-22 13:19", "2025-07-22 13:20", "2025-07-22 13:21", "2025-07-22 13:22", "2025-07-22 13:23", "2025-07-22 13:24", "2025-07-22 13:25", "2025-07-22 13:26", "2025-07-22 13:27", "2025-07-22 13:28", "2025-07-22 13:29", "2025-07-22 13:30", "2025-07-22 13:31", "2025-07-22 13:32", "2025-07-22 13:33", "2025-07-22 13:34", "2025-07-22 13:35", "2025-07-22 13:36", "2025-07-22 13:37", "2025-07-22 13:38", "2025-07-22 13:39", "2025-07-22 13:40", "2025-07-22 13:41", "2025-07-22 13:42", "2025-07-22 13:43", "2025-07-22 13:44", "2025-07-22 13:45", "2025-07-22 13:46", "2025-07-22 13:47", "2025-07-22 13:48", "2025-07-22 13:49", "2025-07-22 13:50", "2025-07-22 13:51", "2025-07-22 13:52", "2025-07-22 13:53", "2025-07-22 13:54", "2025-07-22 13:55", "2025-07-22 13:56", "2025-07-22 13:57", "2025-07-22 13:58", "2025-07-22 13:59", "2025-07-22 14:00", "2025-07-22 14:01", "2025-07-22 14:02", "2025-07-22 14:03", "2025-07-22 14:04", "2025-07-22 14:05", "2025-07-22 14:06", "2025-07-22 14:07", "2025-07-22 14:08", "2025-07-22 14:09", "2025-07-22 14:10", "2025-07-22 14:11", "2025-07-22 14:12", "2025-07-22 14:13", "2025-07-22 14:14", "2025-07-22 14:15", "2025-07-22 14:16", "2025-07-22 14:17", "2025-07-22 14:18", "2025-07-22 14:19", "2025-07-22 14:20", "2025-07-22 14:21", "2025-07-22 14:22", "2025-07-22 14:23", "2025-07-22 14:24", "2025-07-22 14:25", "2025-07-22 14:26", "2025-07-22 14:27", "2025-07-22 14:28", "2025-07-22 14:29", "2025-07-22 14:30", "2025-07-22 14:31", "2025-07-22 14:32", "2025-07-22 14:33", "2025-07-22 14:34", "2025-07-22 14:35", "2025-07-22 14:36", "2025-07-22 14:37", "2025-07-22 14:38", "2025-07-22 14:39", "2025-07-22 14:40", "2025-07-22 14:41", "2025-07-22 14:42", "2025-07-22 14:43", "2025-07-22 14:44", "2025-07-22 14:45", "2025-07-22 14:46", "2025-07-22 14:47", "2025-07-22 14:48", "2025-07-22 14:49", "2025-07-22 14:50", "2025-07-22 14:51", "2025-07-22 14:52", "2025-07-22 14:53", "2025-07-22 14:54", "2025-07-22 14:55", "2025-07-22 14:56", "2025-07-22 14:57", "2025-07-22 14:58", "2025-07-22 14:59", "2025-07-22 15:00", "2025-07-22 15:01", "2025-07-22 15:02", "2025-07-22 15:03", "2025-07-22 15:04", "2025-07-22 15:05", "2025-07-22 15:06", "2025-07-22 15:07", "2025-07-22 15:08", "2025-07-22 15:09", "2025-07-22 15:10", "2025-07-22 15:11", "2025-07-22 15:12", "2025-07-22 15:13", "2025-07-22 15:14", "2025-07-22 15:15", "2025-07-22 15:16", "2025-07-22 15:17", "2025-07-22 15:18", "2025-07-22 15:19", "2025-07-22 15:20", "2025-07-22 15:21", "2025-07-22 15:22", "2025-07-22 15:23", "2025-07-22 15:24", "2025-07-22 15:25", "2025-07-22 15:26", "2025-07-22 15:27", "2025-07-22 15:28", "2025-07-22 15:29", "2025-07-22 15:30", "2025-07-22 15:31", "2025-07-22 15:32", "2025-07-22 15:33", "2025-07-22 15:34", "2025-07-22 15:35", "2025-07-22 15:36", "2025-07-22 15:37", "2025-07-22 15:38", "2025-07-22 15:39", "2025-07-22 15:40", "2025-07-22 15:41", "2025-07-22 15:42", "2025-07-22 15:43", "2025-07-22 15:44", "2025-07-22 15:45", "2025-07-22 15:46", "2025-07-22 15:47", "2025-07-22 15:48", "2025-07-22 15:49", "2025-07-22 15:50", "2025-07-22 15:51", "2025-07-22 15:52", "2025-07-22 15:53", "2025-07-22 15:54", "2025-07-22 15:55", "2025-07-22 15:56", "2025-07-22 15:57", "2025-07-22 15:58", "2025-07-22 15:59", "2025-07-22 16:00", "2025-07-22 16:01", "2025-07-22 16:02", "2025-07-22 16:03", "2025-07-22 16:04", "2025-07-22 16:05", "2025-07-22 16:06", "2025-07-22 16:07", "2025-07-22 16:08", "2025-07-22 16:09", "2025-07-22 16:10", "2025-07-22 16:11", "2025-07-22 16:12", "2025-07-22 16:13", "2025-07-22 16:14", "2025-07-22 16:15", "2025-07-22 16:16", "2025-07-22 16:17", "2025-07-22 16:18", "2025-07-22 16:19", "2025-07-22 16:20", "2025-07-22 16:21", "2025-07-22 16:22", "2025-07-22 16:23", "2025-07-22 16:24", "2025-07-22 16:25", "2025-07-22 16:26", "2025-07-22 16:27", "2025-07-22 16:28", "2025-07-22 16:29", "2025-07-22 16:30", "2025-07-22 16:31", "2025-07-22 16:32", "2025-07-22 16:33", "2025-07-22 16:34", "2025-07-22 16:35", "2025-07-22 16:36", "2025-07-22 16:37", "2025-07-22 16:38", "2025-07-22 16:39", "2025-07-22 16:40", "2025-07-22 16:41", "2025-07-22 16:42", "2025-07-22 16:43", "2025-07-22 16:44", "2025-07-22 16:45", "2025-07-22 16:46", "2025-07-22 16:47", "2025-07-22 16:48", "2025-07-22 16:49", "2025-07-22 16:50", "2025-07-22 16:51", "2025-07-22 16:52", "2025-07-22 16:53", "2025-07-22 16:54", "2025-07-22 16:55", "2025-07-22 16:56", "2025-07-22 16:57", "2025-07-22 16:58", "2025-07-22 16:59", "2025-07-22 17:00", "2025-07-22 17:01", "2025-07-22 17:02", "2025-07-22 17:03", "2025-07-22 17:04", "2025-07-22 17:05", "2025-07-22 17:06", "2025-07-22 17:07", "2025-07-22 17:08", "2025-07-22 17:09", "2025-07-22 17:10", "2025-07-22 17:11", "2025-07-22 17:12", "2025-07-22 17:13", "2025-07-22 17:14", "2025-07-22 17:15", "2025-07-22 17:16", "2025-07-22 17:17", "2025-07-22 17:18", "2025-07-22 17:19", "2025-07-22 17:20", "2025-07-22 17:21", "2025-07-22 17:22", "2025-07-22 17:23", "2025-07-22 17:24", "2025-07-22 17:25", "2025-07-22 17:26", "2025-07-22 17:27", "2025-07-22 17:28", "2025-07-22 17:29", "2025-07-22 17:30", "2025-07-22 17:31", "2025-07-22 17:32", "2025-07-22 17:33", "2025-07-22 17:34", "2025-07-22 17:35", "2025-07-22 17:36", "2025-07-22 17:37", "2025-07-22 17:38", "2025-07-22 17:39", "2025-07-22 17:40", "2025-07-22 17:41", "2025-07-22 17:42", "2025-07-22 17:43", "2025-07-22 17:44", "2025-07-22 17:45", "2025-07-22 17:46", "2025-07-22 17:47", "2025-07-22 17:48", "2025-07-22 17:49", "2025-07-22 17:50", "2025-07-22 17:51", "2025-07-22 17:52", "2025-07-22 17:53", "2025-07-22 17:54", "2025-07-22 17:55", "2025-07-22 17:56", "2025-07-22 17:57", "2025-07-22 17:58", "2025-07-22 17:59", "2025-07-22 18:00", "2025-07-22 18:01", "2025-07-22 18:02", "2025-07-22 18:03", "2025-07-22 18:04", "2025-07-22 18:05", "2025-07-22 18:06", "2025-07-22 18:07", "2025-07-22 18:08", "2025-07-22 18:09", "2025-07-22 18:10", "2025-07-22 18:11", "2025-07-22 18:12", "2025-07-22 18:13", "2025-07-22 18:14", "2025-07-22 18:15", "2025-07-22 18:16", "2025-07-22 18:17", "2025-07-22 18:18", "2025-07-22 18:19", "2025-07-22 18:20", "2025-07-22 18:21", "2025-07-22 18:22", "2025-07-22 18:23", "2025-07-22 18:24", "2025-07-22 18:25", "2025-07-22 18:26", "2025-07-22 18:27", "2025-07-22 18:28", "2025-07-22 18:29", "2025-07-22 18:30", "2025-07-22 18:31", "2025-07-22 18:32", "2025-07-22 18:33", "2025-07-22 18:34", "2025-07-22 18:35", "2025-07-22 18:36", "2025-07-22 18:37", "2025-07-22 18:38", "2025-07-22 18:39", "2025-07-22 18:40", "2025-07-22 18:41", "2025-07-22 18:42", "2025-07-22 18:43", "2025-07-22 18:44", "2025-07-22 18:45", "2025-07-22 18:46", "2025-07-22 18:47", "2025-07-22 18:48", "2025-07-22 18:49", "2025-07-22 18:50", "2025-07-22 18:51", "2025-07-22 18:52", "2025-07-22 18:53", "2025-07-22 18:54", "2025-07-22 18:55", "2025-07-22 18:56", "2025-07-22 18:57", "2025-07-22 18:58", "2025-07-22 18:59", "2025-07-22 19:00", "2025-07-22 19:01", "2025-07-22 19:02", "2025-07-22 19:03", "2025-07-22 19:04", "2025-07-22 19:05", "2025-07-22 19:06", "2025-07-22 19:07", "2025-07-22 19:08", "2025-07-22 19:09", "2025-07-22 19:10", "2025-07-22 19:11", "2025-07-22 19:12", "2025-07-22 19:13", "2025-07-22 19:14", "2025-07-22 19:15", "2025-07-22 19:16", "2025-07-22 19:17", "2025-07-22 19:18", "2025-07-22 19:19", "2025-07-22 19:20", "2025-07-22 19:21", "2025-07-22 19:22", "2025-07-22 19:23", "2025-07-22 19:24", "2025-07-22 19:25", "2025-07-22 19:26", "2025-07-22 19:27", "2025-07-22 19:28", "2025-07-22 19:29", "2025-07-22 19:30", "2025-07-22 19:31", "2025-07-22 19:32", "2025-07-22 19:33", "2025-07-22 19:34", "2025-07-22 19:35", "2025-07-22 19:36", "2025-07-22 19:37", "2025-07-22 19:38", "2025-07-22 19:39", "2025-07-22 19:40", "2025-07-22 19:41", "2025-07-22 19:42", "2025-07-22 19:43", "2025-07-22 19:44", "2025-07-22 19:45", "2025-07-22 19:46", "2025-07-22 19:47", "2025-07-22 19:48", "2025-07-22 19:49", "2025-07-22 19:50", "2025-07-22 19:51", "2025-07-22 19:52", "2025-07-22 19:53", "2025-07-22 19:54", "2025-07-22 19:55", "2025-07-22 19:56", "2025-07-22 19:57", "2025-07-22 19:58", "2025-07-22 19:59", "2025-07-22 20:00", "2025-07-22 20:01", "2025-07-22 20:02", "2025-07-22 20:03", "2025-07-22 20:04", "2025-07-22 20:05", "2025-07-22 20:06", "2025-07-22 20:07", "2025-07-22 20:08", "2025-07-22 20:09", "2025-07-22 20:10", "2025-07-22 20:11", "2025-07-22 20:12", "2025-07-22 20:13", "2025-07-22 20:14", "2025-07-22 20:15", "2025-07-22 20:16", "2025-07-22 20:17", "2025-07-22 20:18", "2025-07-22 20:19", "2025-07-22 20:20", "2025-07-22 20:21", "2025-07-22 20:22", "2025-07-22 20:23", "2025-07-22 20:24", "2025-07-22 20:25", "2025-07-22 20:26", "2025-07-22 20:27", "2025-07-22 20:28", "2025-07-22 20:29", "2025-07-22 20:30", "2025-07-22 20:31", "2025-07-22 20:32", "2025-07-22 20:33", "2025-07-22 20:34", "2025-07-22 20:35", "2025-07-22 20:36", "2025-07-22 20:37", "2025-07-22 20:38", "2025-07-22 20:39", "2025-07-22 20:40", "2025-07-22 20:41", "2025-07-22 20:42", "2025-07-22 20:43", "2025-07-22 20:44", "2025-07-22 20:45", "2025-07-22 20:46", "2025-07-22 20:47", "2025-07-22 20:48", "2025-07-22 20:49", "2025-07-22 20:50", "2025-07-22 20:51", "2025-07-22 20:52", "2025-07-22 20:53", "2025-07-22 20:54", "2025-07-22 20:55", "2025-07-22 20:56", "2025-07-22 20:57", "2025-07-22 20:58", "2025-07-22 20:59", "2025-07-22 21:00", "2025-07-22 21:01", "2025-07-22 21:02", "2025-07-22 21:03", "2025-07-22 21:04", "2025-07-22 21:05", "2025-07-22 21:06", "2025-07-22 21:07", "2025-07-22 21:08", "2025-07-22 21:09", "2025-07-22 21:10", "2025-07-22 21:11", "2025-07-22 21:12", "2025-07-22 21:13", "2025-07-22 21:14", "2025-07-22 21:15", "2025-07-22 21:16", "2025-07-22 21:17", "2025-07-22 21:18", "2025-07-22 21:19", "2025-07-22 21:20", "2025-07-22 21:21", "2025-07-22 21:22", "2025-07-22 21:23", "2025-07-22 21:24", "2025-07-22 21:25", "2025-07-22 21:26", "2025-07-22 21:27", "2025-07-22 21:28", "2025-07-22 21:29", "2025-07-22 21:30", "2025-07-22 21:31", "2025-07-22 21:32", "2025-07-22 21:33", "2025-07-22 21:34", "2025-07-22 21:35", "2025-07-22 21:36", "2025-07-22 21:37", "2025-07-22 21:38", "2025-07-22 21:39", "2025-07-22 21:40", "2025-07-22 21:41", "2025-07-22 21:42", "2025-07-22 21:43", "2025-07-22 21:44", "2025-07-22 21:45", "2025-07-22 21:46", "2025-07-22 21:47", "2025-07-22 21:48", "2025-07-22 21:49", "2025-07-22 21:50", "2025-07-22 21:51", "2025-07-22 21:52", "2025-07-22 21:53", "2025-07-22 21:54", "2025-07-22 21:55", "2025-07-22 21:56", "2025-07-22 21:57", "2025-07-22 21:58", "2025-07-22 21:59", "2025-07-22 22:00", "2025-07-22 22:01", "2025-07-22 22:02", "2025-07-22 22:03", "2025-07-22 22:04", "2025-07-22 22:05", "2025-07-22 22:06", "2025-07-22 22:07", "2025-07-22 22:08", "2025-07-22 22:09", "2025-07-22 22:10", "2025-07-22 22:11", "2025-07-22 22:12", "2025-07-22 22:13", "2025-07-22 22:14", "2025-07-22 22:15", "2025-07-22 22:16", "2025-07-22 22:17", "2025-07-22 22:18", "2025-07-22 22:19", "2025-07-22 22:20", "2025-07-22 22:21", "2025-07-22 22:22", "2025-07-22 22:23", "2025-07-22 22:24", "2025-07-22 22:25", "2025-07-22 22:26", "2025-07-22 22:27", "2025-07-22 22:28", "2025-07-22 22:29", "2025-07-22 22:30", "2025-07-22 22:31", "2025-07-22 22:32", "2025-07-22 22:33", "2025-07-22 22:34", "2025-07-22 22:35", "2025-07-22 22:36", "2025-07-22 22:37", "2025-07-22 22:38", "2025-07-22 22:39", "2025-07-22 22:40", "2025-07-22 22:41", "2025-07-22 22:42", "2025-07-22 22:43", "2025-07-22 22:44", "2025-07-22 22:45", "2025-07-22 22:46", "2025-07-22 22:47", "2025-07-22 22:48", "2025-07-22 22:49", "2025-07-22 22:50", "2025-07-22 22:51", "2025-07-22 22:52", "2025-07-22 22:53", "2025-07-22 22:54", "2025-07-22 22:55", "2025-07-22 22:56", "2025-07-22 22:57", "2025-07-22 22:58", "2025-07-22 22:59", "2025-07-22 23:00", "2025-07-22 23:01", "2025-07-22 23:02", "2025-07-22 23:03", "2025-07-22 23:04", "2025-07-22 23:05", "2025-07-22 23:06", "2025-07-22 23:07", "2025-07-22 23:08", "2025-07-22 23:09", "2025-07-22 23:10", "2025-07-22 23:11", "2025-07-22 23:12", "2025-07-22 23:13", "2025-07-22 23:14", "2025-07-22 23:15", "2025-07-22 23:16", "2025-07-22 23:17", "2025-07-22 23:18", "2025-07-22 23:19", "2025-07-22 23:20", "2025-07-22 23:21", "2025-07-22 23:22", "2025-07-22 23:23", "2025-07-22 23:24", "2025-07-22 23:25", "2025-07-22 23:26", "2025-07-22 23:27", "2025-07-22 23:28", "2025-07-22 23:29", "2025-07-22 23:30", "2025-07-22 23:31", "2025-07-22 23:32", "2025-07-22 23:33", "2025-07-22 23:34", "2025-07-22 23:35", "2025-07-22 23:36", "2025-07-22 23:37", "2025-07-22 23:38", "2025-07-22 23:39", "2025-07-22 23:40", "2025-07-22 23:41", "2025-07-22 23:42", "2025-07-22 23:43", "2025-07-22 23:44", "2025-07-22 23:45", "2025-07-22 23:46", "2025-07-22 23:47", "2025-07-22 23:48", "2025-07-22 23:49", "2025-07-22 23:50", "2025-07-22 23:51", "2025-07-22 23:52", "2025-07-22 23:53", "2025-07-22 23:54", "2025-07-22 23:55", "2025-07-22 23:56", "2025-07-22 23:57", "2025-07-22 23:58", "2025-07-22 23:59", "2025-07-23 00:00", "2025-07-23 00:01", "2025-07-23 00:02", "2025-07-23 00:03", "2025-07-23 00:04", "2025-07-23 00:05", "2025-07-23 00:06", "2025-07-23 00:07", "2025-07-23 00:08", "2025-07-23 00:09", "2025-07-23 00:10", "2025-07-23 00:11", "2025-07-23 00:12", "2025-07-23 00:13", "2025-07-23 00:14", "2025-07-23 00:15", "2025-07-23 00:16", "2025-07-23 00:17", "2025-07-23 00:18", "2025-07-23 00:19", "2025-07-23 00:20", "2025-07-23 00:21", "2025-07-23 00:22", "2025-07-23 00:23", "2025-07-23 00:24", "2025-07-23 00:25", "2025-07-23 00:26", "2025-07-23 00:27", "2025-07-23 00:28", "2025-07-23 00:29", "2025-07-23 00:30", "2025-07-23 00:31", "2025-07-23 00:32", "2025-07-23 00:33", "2025-07-23 00:34", "2025-07-23 00:35", "2025-07-23 00:36", "2025-07-23 00:37", "2025-07-23 00:38", "2025-07-23 00:39", "2025-07-23 00:40", "2025-07-23 00:41", "2025-07-23 00:42", "2025-07-23 00:43", "2025-07-23 00:44", "2025-07-23 00:45", "2025-07-23 00:46", "2025-07-23 00:47", "2025-07-23 00:48", "2025-07-23 00:49", "2025-07-23 00:50", "2025-07-23 00:51", "2025-07-23 00:52", "2025-07-23 00:53", "2025-07-23 00:54", "2025-07-23 00:55", "2025-07-23 00:56", "2025-07-23 00:57", "2025-07-23 00:58", "2025-07-23 00:59", "2025-07-23 01:00", "2025-07-23 01:01", "2025-07-23 01:02", "2025-07-23 01:03", "2025-07-23 01:04", "2025-07-23 01:05", "2025-07-23 01:06", "2025-07-23 01:07", "2025-07-23 01:08", "2025-07-23 01:09", "2025-07-23 01:10", "2025-07-23 01:11", "2025-07-23 01:12", "2025-07-23 01:13", "2025-07-23 01:14", "2025-07-23 01:15", "2025-07-23 01:16", "2025-07-23 01:17", "2025-07-23 01:18", "2025-07-23 01:19", "2025-07-23 01:20", "2025-07-23 01:21", "2025-07-23 01:22", "2025-07-23 01:23", "2025-07-23 01:24", "2025-07-23 01:25", "2025-07-23 01:26", "2025-07-23 01:27", "2025-07-23 01:28", "2025-07-23 01:29", "2025-07-23 01:30", "2025-07-23 01:31", "2025-07-23 01:32", "2025-07-23 01:33", "2025-07-23 01:34", "2025-07-23 01:35", "2025-07-23 01:36", "2025-07-23 01:37", "2025-07-23 01:38", "2025-07-23 01:39", "2025-07-23 01:40", "2025-07-23 01:41", "2025-07-23 01:42", "2025-07-23 01:43", "2025-07-23 01:44", "2025-07-23 01:45", "2025-07-23 01:46", "2025-07-23 01:47", "2025-07-23 01:48", "2025-07-23 01:49", "2025-07-23 01:50", "2025-07-23 01:51", "2025-07-23 01:52", "2025-07-23 01:53", "2025-07-23 01:54", "2025-07-23 01:55", "2025-07-23 01:56", "2025-07-23 01:57", "2025-07-23 01:58", "2025-07-23 01:59", "2025-07-23 02:00", "2025-07-23 02:01", "2025-07-23 02:02", "2025-07-23 02:03", "2025-07-23 02:04", "2025-07-23 02:05", "2025-07-23 02:06", "2025-07-23 02:07", "2025-07-23 02:08", "2025-07-23 02:09", "2025-07-23 02:10", "2025-07-23 02:11", "2025-07-23 02:12", "2025-07-23 02:13", "2025-07-23 02:14", "2025-07-23 02:15", "2025-07-23 02:16", "2025-07-23 02:17", "2025-07-23 02:18", "2025-07-23 02:19", "2025-07-23 02:20", "2025-07-23 02:21", "2025-07-23 02:22", "2025-07-23 02:23", "2025-07-23 02:24", "2025-07-23 02:25", "2025-07-23 02:26", "2025-07-23 02:27", "2025-07-23 02:28", "2025-07-23 02:29", "2025-07-23 02:30", "2025-07-23 02:31", "2025-07-23 02:32", "2025-07-23 02:33", "2025-07-23 02:34", "2025-07-23 02:35", "2025-07-23 02:36", "2025-07-23 02:37", "2025-07-23 02:38", "2025-07-23 02:39", "2025-07-23 02:40", "2025-07-23 02:41", "2025-07-23 02:42", "2025-07-23 02:43", "2025-07-23 02:44", "2025-07-23 02:45", "2025-07-23 02:46", "2025-07-23 02:47", "2025-07-23 02:48", "2025-07-23 02:49", "2025-07-23 02:50", "2025-07-23 02:51", "2025-07-23 02:52", "2025-07-23 02:53", "2025-07-23 02:54", "2025-07-23 02:55", "2025-07-23 02:56", "2025-07-23 02:57", "2025-07-23 02:58", "2025-07-23 02:59", "2025-07-23 03:00", "2025-07-23 03:01", "2025-07-23 03:02", "2025-07-23 03:03", "2025-07-23 03:04", "2025-07-23 03:05", "2025-07-23 03:06", "2025-07-23 03:07", "2025-07-23 03:08", "2025-07-23 03:09", "2025-07-23 03:10", "2025-07-23 03:11", "2025-07-23 03:12", "2025-07-23 03:13", "2025-07-23 03:14", "2025-07-23 03:15", "2025-07-23 03:16", "2025-07-23 03:17", "2025-07-23 03:18", "2025-07-23 03:19", "2025-07-23 03:20", "2025-07-23 03:21", "2025-07-23 03:22", "2025-07-23 03:23", "2025-07-23 03:24", "2025-07-23 03:25", "2025-07-23 03:26", "2025-07-23 03:27", "2025-07-23 03:28", "2025-07-23 03:29", "2025-07-23 03:30", "2025-07-23 03:31", "2025-07-23 03:32", "2025-07-23 03:33", "2025-07-23 03:34", "2025-07-23 03:35", "2025-07-23 03:36", "2025-07-23 03:37", "2025-07-23 03:38", "2025-07-23 03:39", "2025-07-23 03:40", "2025-07-23 03:41", "2025-07-23 03:42", "2025-07-23 03:43", "2025-07-23 03:44", "2025-07-23 03:45", "2025-07-23 03:46", "2025-07-23 03:47", "2025-07-23 03:48", "2025-07-23 03:49", "2025-07-23 03:50", "2025-07-23 03:51", "2025-07-23 03:52", "2025-07-23 03:53", "2025-07-23 03:54", "2025-07-23 03:55", "2025-07-23 03:56", "2025-07-23 03:57", "2025-07-23 03:58", "2025-07-23 03:59", "2025-07-23 04:00", "2025-07-23 04:01", "2025-07-23 04:02", "2025-07-23 04:03", "2025-07-23 04:04", "2025-07-23 04:05", "2025-07-23 04:06", "2025-07-23 04:07", "2025-07-23 04:08", "2025-07-23 04:09", "2025-07-23 04:10", "2025-07-23 04:11", "2025-07-23 04:12", "2025-07-23 04:13", "2025-07-23 04:14", "2025-07-23 04:15", "2025-07-23 04:16", "2025-07-23 04:17", "2025-07-23 04:18", "2025-07-23 04:19", "2025-07-23 04:20", "2025-07-23 04:21", "2025-07-23 04:22", "2025-07-23 04:23", "2025-07-23 04:24", "2025-07-23 04:25", "2025-07-23 04:26", "2025-07-23 04:27", "2025-07-23 04:28", "2025-07-23 04:29", "2025-07-23 04:30", "2025-07-23 04:31", "2025-07-23 04:32", "2025-07-23 04:33", "2025-07-23 04:34", "2025-07-23 04:35", "2025-07-23 04:36", "2025-07-23 04:37", "2025-07-23 04:38", "2025-07-23 04:39", "2025-07-23 04:40", "2025-07-23 04:41", "2025-07-23 04:42", "2025-07-23 04:43", "2025-07-23 04:44", "2025-07-23 04:45", "2025-07-23 04:46", "2025-07-23 04:47", "2025-07-23 04:48", "2025-07-23 04:49", "2025-07-23 04:50", "2025-07-23 04:51", "2025-07-23 04:52", "2025-07-23 04:53", "2025-07-23 04:54", "2025-07-23 04:55", "2025-07-23 04:56", "2025-07-23 04:57", "2025-07-23 04:58", "2025-07-23 04:59", "2025-07-23 05:00", "2025-07-23 05:01", "2025-07-23 05:02", "2025-07-23 05:03", "2025-07-23 05:04", "2025-07-23 05:05", "2025-07-23 05:06", "2025-07-23 05:07", "2025-07-23 05:08", "2025-07-23 05:09", "2025-07-23 05:10", "2025-07-23 05:11", "2025-07-23 05:12", "2025-07-23 05:13", "2025-07-23 05:14", "2025-07-23 05:15", "2025-07-23 05:16", "2025-07-23 05:17", "2025-07-23 05:18", "2025-07-23 05:19", "2025-07-23 05:20", "2025-07-23 05:21", "2025-07-23 05:22", "2025-07-23 05:23", "2025-07-23 05:24", "2025-07-23 05:25", "2025-07-23 05:26", "2025-07-23 05:27", "2025-07-23 05:28", "2025-07-23 05:29", "2025-07-23 05:30", "2025-07-23 05:31", "2025-07-23 05:32", "2025-07-23 05:33", "2025-07-23 05:34", "2025-07-23 05:35", "2025-07-23 05:36", "2025-07-23 05:37", "2025-07-23 05:38", "2025-07-23 05:39", "2025-07-23 05:40", "2025-07-23 05:41", "2025-07-23 05:42", "2025-07-23 05:43", "2025-07-23 05:44", "2025-07-23 05:45", "2025-07-23 05:46", "2025-07-23 05:47", "2025-07-23 05:48", "2025-07-23 05:49", "2025-07-23 05:50", "2025-07-23 05:51", "2025-07-23 05:52", "2025-07-23 05:53", "2025-07-23 05:54", "2025-07-23 05:55", "2025-07-23 05:56", "2025-07-23 05:57", "2025-07-23 05:58", "2025-07-23 05:59", "2025-07-23 06:00", "2025-07-23 06:01", "2025-07-23 06:02", "2025-07-23 06:03", "2025-07-23 06:04", "2025-07-23 06:05", "2025-07-23 06:06", "2025-07-23 06:07", "2025-07-23 06:08", "2025-07-23 06:09", "2025-07-23 06:10", "2025-07-23 06:11", "2025-07-23 06:12", "2025-07-23 06:13", "2025-07-23 06:14", "2025-07-23 06:15", "2025-07-23 06:16", "2025-07-23 06:17", "2025-07-23 06:18", "2025-07-23 06:19", "2025-07-23 06:20", "2025-07-23 06:21", "2025-07-23 06:22", "2025-07-23 06:23", "2025-07-23 06:24", "2025-07-23 06:25", "2025-07-23 06:26", "2025-07-23 06:27", "2025-07-23 06:28", "2025-07-23 06:29", "2025-07-23 06:30", "2025-07-23 06:31", "2025-07-23 06:32", "2025-07-23 06:33", "2025-07-23 06:34", "2025-07-23 06:35", "2025-07-23 06:36", "2025-07-23 06:37", "2025-07-23 06:38", "2025-07-23 06:39", "2025-07-23 06:40", "2025-07-23 06:41", "2025-07-23 06:42", "2025-07-23 06:43", "2025-07-23 06:44", "2025-07-23 06:45", "2025-07-23 06:46", "2025-07-23 06:47", "2025-07-23 06:48", "2025-07-23 06:49", "2025-07-23 06:50", "2025-07-23 06:51", "2025-07-23 06:52", "2025-07-23 06:53", "2025-07-23 06:54", "2025-07-23 06:55", "2025-07-23 06:56", "2025-07-23 06:57", "2025-07-23 06:58", "2025-07-23 06:59", "2025-07-23 07:00", "2025-07-23 07:01", "2025-07-23 07:02", "2025-07-23 07:03", "2025-07-23 07:04", "2025-07-23 07:05", "2025-07-23 07:06", "2025-07-23 07:07", "2025-07-23 07:08", "2025-07-23 07:09", "2025-07-23 07:10", "2025-07-23 07:11", "2025-07-23 07:12", "2025-07-23 07:13", "2025-07-23 07:14", "2025-07-23 07:15", "2025-07-23 07:16", "2025-07-23 07:17", "2025-07-23 07:18", "2025-07-23 07:19", "2025-07-23 07:20", "2025-07-23 07:21", "2025-07-23 07:22", "2025-07-23 07:23", "2025-07-23 07:24", "2025-07-23 07:25", "2025-07-23 07:26", "2025-07-23 07:27", "2025-07-23 07:28", "2025-07-23 07:29", "2025-07-23 07:30", "2025-07-23 07:31", "2025-07-23 07:32", "2025-07-23 07:33", "2025-07-23 07:34", "2025-07-23 07:35", "2025-07-23 07:36", "2025-07-23 07:37", "2025-07-23 07:38", "2025-07-23 07:39", "2025-07-23 07:40", "2025-07-23 07:41", "2025-07-23 07:42", "2025-07-23 07:43", "2025-07-23 07:44", "2025-07-23 07:45", "2025-07-23 07:46", "2025-07-23 07:47", "2025-07-23 07:48", "2025-07-23 07:49", "2025-07-23 07:50", "2025-07-23 07:51", "2025-07-23 07:52", "2025-07-23 07:53", "2025-07-23 07:54", "2025-07-23 07:55", "2025-07-23 07:56", "2025-07-23 07:57", "2025-07-23 07:58", "2025-07-23 07:59", "2025-07-23 08:00", "2025-07-23 08:01", "2025-07-23 08:02", "2025-07-23 08:03", "2025-07-23 08:04", "2025-07-23 08:05", "2025-07-23 08:06", "2025-07-23 08:07", "2025-07-23 08:08", "2025-07-23 08:09", "2025-07-23 08:10", "2025-07-23 08:11", "2025-07-23 08:12", "2025-07-23 08:13", "2025-07-23 08:14", "2025-07-23 08:15", "2025-07-23 08:16", "2025-07-23 08:17", "2025-07-23 08:18", "2025-07-23 08:19", "2025-07-23 08:20", "2025-07-23 08:21", "2025-07-23 08:22", "2025-07-23 08:23", "2025-07-23 08:24", "2025-07-23 08:25", "2025-07-23 08:26", "2025-07-23 08:27", "2025-07-23 08:28", "2025-07-23 08:29", "2025-07-23 08:30", "2025-07-23 08:31", "2025-07-23 08:32", "2025-07-23 08:33", "2025-07-23 08:34", "2025-07-23 08:35", "2025-07-23 08:36", "2025-07-23 08:37", "2025-07-23 08:38", "2025-07-23 08:39", "2025-07-23 08:40", "2025-07-23 08:41", "2025-07-23 08:42", "2025-07-23 08:43", "2025-07-23 08:44", "2025-07-23 08:45", "2025-07-23 08:46", "2025-07-23 08:47", "2025-07-23 08:48", "2025-07-23 08:49", "2025-07-23 08:50", "2025-07-23 08:51", "2025-07-23 08:52", "2025-07-23 08:53", "2025-07-23 08:54", "2025-07-23 08:55", "2025-07-23 08:56", "2025-07-23 08:57", "2025-07-23 08:58", "2025-07-23 08:59", "2025-07-23 09:00", "2025-07-23 09:01", "2025-07-23 09:02", "2025-07-23 09:03", "2025-07-23 09:04", "2025-07-23 09:05", "2025-07-23 09:06", "2025-07-23 09:07", "2025-07-23 09:08", "2025-07-23 09:09", "2025-07-23 09:10", "2025-07-23 09:11", "2025-07-23 09:12", "2025-07-23 09:13", "2025-07-23 09:14", "2025-07-23 09:15", "2025-07-23 09:16", "2025-07-23 09:17", "2025-07-23 09:18", "2025-07-23 09:19", "2025-07-23 09:20", "2025-07-23 09:21", "2025-07-23 09:22", "2025-07-23 09:23", "2025-07-23 09:24", "2025-07-23 09:25", "2025-07-23 09:26", "2025-07-23 09:27", "2025-07-23 09:28", "2025-07-23 09:29", "2025-07-23 09:30", "2025-07-23 09:31", "2025-07-23 09:32", "2025-07-23 09:33", "2025-07-23 09:34", "2025-07-23 09:35", "2025-07-23 09:36", "2025-07-23 09:37", "2025-07-23 09:38", "2025-07-23 09:39", "2025-07-23 09:40", "2025-07-23 09:41", "2025-07-23 09:42", "2025-07-23 09:43", "2025-07-23 09:44", "2025-07-23 09:45", "2025-07-23 09:46", "2025-07-23 09:47", "2025-07-23 09:48", "2025-07-23 09:49", "2025-07-23 09:50", "2025-07-23 09:51", "2025-07-23 09:52", "2025-07-23 09:53", "2025-07-23 09:54", "2025-07-23 09:55", "2025-07-23 09:56", "2025-07-23 09:57", "2025-07-23 09:58", "2025-07-23 09:59", "2025-07-23 10:00", "2025-07-23 10:01", "2025-07-23 10:02", "2025-07-23 10:03", "2025-07-23 10:04", "2025-07-23 10:05", "2025-07-23 10:06", "2025-07-23 10:07", "2025-07-23 10:08", "2025-07-23 10:09", "2025-07-23 10:10", "2025-07-23 10:11", "2025-07-23 10:12", "2025-07-23 10:13", "2025-07-23 10:14", "2025-07-23 10:15", "2025-07-23 10:16", "2025-07-23 10:17", "2025-07-23 10:18", "2025-07-23 10:19", "2025-07-23 10:20", "2025-07-23 10:21", "2025-07-23 10:22", "2025-07-23 10:23", "2025-07-23 10:24", "2025-07-23 10:25", "2025-07-23 10:26", "2025-07-23 10:27", "2025-07-23 10:28", "2025-07-23 10:29", "2025-07-23 10:30", "2025-07-23 10:31", "2025-07-23 10:32", "2025-07-23 10:33", "2025-07-23 10:34", "2025-07-23 10:35", "2025-07-23 10:36", "2025-07-23 10:37", "2025-07-23 10:38", "2025-07-23 10:39", "2025-07-23 10:40", "2025-07-23 10:41", "2025-07-23 10:42", "2025-07-23 10:43", "2025-07-23 10:44", "2025-07-23 10:45", "2025-07-23 10:46", "2025-07-23 10:47", "2025-07-23 10:48", "2025-07-23 10:49", "2025-07-23 10:50", "2025-07-23 10:51", "2025-07-23 10:52", "2025-07-23 10:53", "2025-07-23 10:54", "2025-07-23 10:55", "2025-07-23 10:56", "2025-07-23 10:57", "2025-07-23 10:58", "2025-07-23 10:59", "2025-07-23 11:00", "2025-07-23 11:01", "2025-07-23 11:02", "2025-07-23 11:03", "2025-07-23 11:04", "2025-07-23 11:05", "2025-07-23 11:06", "2025-07-23 11:07", "2025-07-23 11:08", "2025-07-23 11:09", "2025-07-23 11:10", "2025-07-23 11:11", "2025-07-23 11:12", "2025-07-23 11:13", "2025-07-23 11:14", "2025-07-23 11:15", "2025-07-23 11:16", "2025-07-23 11:17", "2025-07-23 11:18", "2025-07-23 11:19", "2025-07-23 11:20", "2025-07-23 11:21", "2025-07-23 11:22", "2025-07-23 11:23", "2025-07-23 11:24", "2025-07-23 11:25", "2025-07-23 11:26", "2025-07-23 11:27", "2025-07-23 11:28", "2025-07-23 11:29", "2025-07-23 11:30", "2025-07-23 11:31", "2025-07-23 11:32", "2025-07-23 11:33", "2025-07-23 11:34", "2025-07-23 11:35", "2025-07-23 11:36", "2025-07-23 11:37", "2025-07-23 11:38", "2025-07-23 11:39", "2025-07-23 11:40", "2025-07-23 11:41", "2025-07-23 11:42", "2025-07-23 11:43", "2025-07-23 11:44", "2025-07-23 11:45", "2025-07-23 11:46", "2025-07-23 11:47", "2025-07-23 11:48", "2025-07-23 11:49", "2025-07-23 11:50", "2025-07-23 11:51", "2025-07-23 11:52", "2025-07-23 11:53", "2025-07-23 11:54", "2025-07-23 11:55", "2025-07-23 11:56", "2025-07-23 11:57", "2025-07-23 11:58", "2025-07-23 11:59", "2025-07-23 12:00", "2025-07-23 12:01", "2025-07-23 12:02", "2025-07-23 12:03", "2025-07-23 12:04", "2025-07-23 12:05", "2025-07-23 12:06", "2025-07-23 12:07", "2025-07-23 12:08", "2025-07-23 12:09", "2025-07-23 12:10", "2025-07-23 12:11", "2025-07-23 12:12", "2025-07-23 12:13", "2025-07-23 12:14", "2025-07-23 12:15", "2025-07-23 12:16", "2025-07-23 12:17", "2025-07-23 12:18", "2025-07-23 12:19", "2025-07-23 12:20", "2025-07-23 12:21", "2025-07-23 12:22", "2025-07-23 12:23", "2025-07-23 12:24", "2025-07-23 12:25", "2025-07-23 12:26", "2025-07-23 12:27", "2025-07-23 12:28", "2025-07-23 12:29", "2025-07-23 12:30", "2025-07-23 12:31", "2025-07-23 12:32", "2025-07-23 12:33", "2025-07-23 12:34", "2025-07-23 12:35", "2025-07-23 12:36", "2025-07-23 12:37", "2025-07-23 12:38", "2025-07-23 12:39", "2025-07-23 12:40", "2025-07-23 12:41", "2025-07-23 12:42", "2025-07-23 12:43", "2025-07-23 12:44", "2025-07-23 12:45", "2025-07-23 12:46", "2025-07-23 12:47", "2025-07-23 12:48", "2025-07-23 12:49", "2025-07-23 12:50", "2025-07-23 12:51", "2025-07-23 12:52", "2025-07-23 12:53", "2025-07-23 12:54", "2025-07-23 12:55", "2025-07-23 12:56", "2025-07-23 12:57", "2025-07-23 12:58", "2025-07-23 12:59", "2025-07-23 13:00", "2025-07-23 13:01", "2025-07-23 13:02", "2025-07-23 13:03", "2025-07-23 13:04", "2025-07-23 13:05", "2025-07-23 13:06", "2025-07-23 13:07", "2025-07-23 13:08", "2025-07-23 13:09", "2025-07-23 13:10", "2025-07-23 13:11", "2025-07-23 13:12", "2025-07-23 13:13", "2025-07-23 13:14", "2025-07-23 13:15", "2025-07-23 13:16", "2025-07-23 13:17", "2025-07-23 13:18", "2025-07-23 13:19", "2025-07-23 13:20", "2025-07-23 13:21", "2025-07-23 13:22", "2025-07-23 13:23", "2025-07-23 13:24", "2025-07-23 13:25", "2025-07-23 13:26", "2025-07-23 13:27", "2025-07-23 13:28", "2025-07-23 13:29", "2025-07-23 13:30", "2025-07-23 13:31", "2025-07-23 13:32", "2025-07-23 13:33", "2025-07-23 13:34", "2025-07-23 13:35", "2025-07-23 13:36", "2025-07-23 13:37", "2025-07-23 13:38", "2025-07-23 13:39", "2025-07-23 13:40", "2025-07-23 13:41", "2025-07-23 13:42", "2025-07-23 13:43", "2025-07-23 13:44", "2025-07-23 13:45", "2025-07-23 13:46", "2025-07-23 13:47", "2025-07-23 13:48", "2025-07-23 13:49", "2025-07-23 13:50", "2025-07-23 13:51", "2025-07-23 13:52", "2025-07-23 13:53", "2025-07-23 13:54", "2025-07-23 13:55", "2025-07-23 13:56", "2025-07-23 13:57", "2025-07-23 13:58", "2025-07-23 13:59", "2025-07-23 14:00", "2025-07-23 14:01", "2025-07-23 14:02", "2025-07-23 14:03", "2025-07-23 14:04", "2025-07-23 14:05", "2025-07-23 14:06", "2025-07-23 14:07", "2025-07-23 14:08", "2025-07-23 14:09", "2025-07-23 14:10", "2025-07-23 14:11", "2025-07-23 14:12", "2025-07-23 14:13", "2025-07-23 14:14", "2025-07-23 14:15", "2025-07-23 14:16", "2025-07-23 14:17", "2025-07-23 14:18", "2025-07-23 14:19", "2025-07-23 14:20", "2025-07-23 14:21", "2025-07-23 14:22", "2025-07-23 14:23", "2025-07-23 14:24", "2025-07-23 14:25", "2025-07-23 14:26", "2025-07-23 14:27", "2025-07-23 14:28", "2025-07-23 14:29", "2025-07-23 14:30", "2025-07-23 14:31", "2025-07-23 14:32", "2025-07-23 14:33", "2025-07-23 14:34", "2025-07-23 14:35", "2025-07-23 14:36", "2025-07-23 14:37", "2025-07-23 14:38", "2025-07-23 14:39", "2025-07-23 14:40", "2025-07-23 14:41", "2025-07-23 14:42", "2025-07-23 14:43", "2025-07-23 14:44", "2025-07-23 14:45", "2025-07-23 14:46", "2025-07-23 14:47", "2025-07-23 14:48", "2025-07-23 14:49", "2025-07-23 14:50", "2025-07-23 14:51", "2025-07-23 14:52", "2025-07-23 14:53", "2025-07-23 14:54", "2025-07-23 14:55", "2025-07-23 14:56", "2025-07-23 14:57", "2025-07-23 14:58", "2025-07-23 14:59", "2025-07-23 15:00", "2025-07-23 15:01", "2025-07-23 15:02", "2025-07-23 15:03", "2025-07-23 15:04", "2025-07-23 15:05", "2025-07-23 15:06", "2025-07-23 15:07", "2025-07-23 15:08", "2025-07-23 15:09", "2025-07-23 15:10", "2025-07-23 15:11", "2025-07-23 15:12", "2025-07-23 15:13", "2025-07-23 15:14", "2025-07-23 15:15", "2025-07-23 15:16", "2025-07-23 15:17", "2025-07-23 15:18", "2025-07-23 15:19", "2025-07-23 15:20", "2025-07-23 15:21", "2025-07-23 15:22", "2025-07-23 15:23", "2025-07-23 15:24", "2025-07-23 15:25", "2025-07-23 15:26", "2025-07-23 15:27", "2025-07-23 15:28", "2025-07-23 15:29", "2025-07-23 15:30", "2025-07-23 15:31", "2025-07-23 15:32", "2025-07-23 15:33", "2025-07-23 15:34", "2025-07-23 15:35", "2025-07-23 15:36", "2025-07-23 15:37", "2025-07-23 15:38", "2025-07-23 15:39", "2025-07-23 15:40", "2025-07-23 15:41", "2025-07-23 15:42", "2025-07-23 15:43", "2025-07-23 15:44", "2025-07-23 15:45", "2025-07-23 15:46", "2025-07-23 15:47", "2025-07-23 15:48", "2025-07-23 15:49", "2025-07-23 15:50", "2025-07-23 15:51", "2025-07-23 15:52", "2025-07-23 15:53", "2025-07-23 15:54", "2025-07-23 15:55", "2025-07-23 15:56", "2025-07-23 15:57", "2025-07-23 15:58", "2025-07-23 15:59", "2025-07-23 16:00", "2025-07-23 16:01", "2025-07-23 16:02", "2025-07-23 16:03", "2025-07-23 16:04", "2025-07-23 16:05", "2025-07-23 16:06", "2025-07-23 16:07", "2025-07-23 16:08", "2025-07-23 16:09", "2025-07-23 16:10", "2025-07-23 16:11", "2025-07-23 16:12", "2025-07-23 16:13", "2025-07-23 16:14", "2025-07-23 16:15", "2025-07-23 16:16", "2025-07-23 16:17", "2025-07-23 16:18", "2025-07-23 16:19", "2025-07-23 16:20", "2025-07-23 16:21", "2025-07-23 16:22", "2025-07-23 16:23", "2025-07-23 16:24", "2025-07-23 16:25", "2025-07-23 16:26", "2025-07-23 16:27", "2025-07-23 16:28", "2025-07-23 16:29", "2025-07-23 16:30", "2025-07-23 16:31", "2025-07-23 16:32", "2025-07-23 16:33", "2025-07-23 16:34", "2025-07-23 16:35", "2025-07-23 16:36", "2025-07-23 16:37", "2025-07-23 16:38", "2025-07-23 16:39", "2025-07-23 16:40", "2025-07-23 16:41", "2025-07-23 16:42", "2025-07-23 16:43", "2025-07-23 16:44", "2025-07-23 16:45", "2025-07-23 16:46", "2025-07-23 16:47", "2025-07-23 16:48", "2025-07-23 16:49", "2025-07-23 16:50", "2025-07-23 16:51", "2025-07-23 16:52", "2025-07-23 16:53", "2025-07-23 16:54", "2025-07-23 16:55", "2025-07-23 16:56", "2025-07-23 16:57", "2025-07-23 16:58", "2025-07-23 16:59", "2025-07-23 17:00", "2025-07-23 17:01", "2025-07-23 17:02", "2025-07-23 17:03", "2025-07-23 17:04", "2025-07-23 17:05", "2025-07-23 17:06", "2025-07-23 17:07", "2025-07-23 17:08", "2025-07-23 17:09", "2025-07-23 17:10", "2025-07-23 17:11", "2025-07-23 17:12", "2025-07-23 17:13", "2025-07-23 17:14", "2025-07-23 17:15", "2025-07-23 17:16", "2025-07-23 17:17", "2025-07-23 17:18", "2025-07-23 17:19", "2025-07-23 17:20", "2025-07-23 17:21", "2025-07-23 17:22", "2025-07-23 17:23", "2025-07-23 17:24", "2025-07-23 17:25", "2025-07-23 17:26", "2025-07-23 17:27", "2025-07-23 17:28", "2025-07-23 17:29", "2025-07-23 17:30", "2025-07-23 17:31", "2025-07-23 17:32", "2025-07-23 17:33", "2025-07-23 17:34", "2025-07-23 17:35", "2025-07-23 17:36", "2025-07-23 17:37", "2025-07-23 17:38", "2025-07-23 17:39", "2025-07-23 17:40", "2025-07-23 17:41", "2025-07-23 17:42", "2025-07-23 17:43", "2025-07-23 17:44", "2025-07-23 17:45", "2025-07-23 17:46", "2025-07-23 17:47", "2025-07-23 17:48", "2025-07-23 17:49", "2025-07-23 17:50", "2025-07-23 17:51", "2025-07-23 17:52", "2025-07-23 17:53", "2025-07-23 17:54", "2025-07-23 17:55", "2025-07-23 17:56", "2025-07-23 17:57", "2025-07-23 17:58", "2025-07-23 17:59", "2025-07-23 18:00", "2025-07-23 18:01", "2025-07-23 18:02", "2025-07-23 18:03", "2025-07-23 18:04", "2025-07-23 18:05", "2025-07-23 18:06", "2025-07-23 18:07", "2025-07-23 18:08", "2025-07-23 18:09", "2025-07-23 18:10", "2025-07-23 18:11", "2025-07-23 18:12", "2025-07-23 18:13", "2025-07-23 18:14", "2025-07-23 18:15", "2025-07-23 18:16", "2025-07-23 18:17", "2025-07-23 18:18", "2025-07-23 18:19", "2025-07-23 18:20", "2025-07-23 18:21", "2025-07-23 18:22", "2025-07-23 18:23", "2025-07-23 18:24", "2025-07-23 18:25", "2025-07-23 18:26", "2025-07-23 18:27", "2025-07-23 18:28", "2025-07-23 18:29", "2025-07-23 18:30", "2025-07-23 18:31", "2025-07-23 18:32", "2025-07-23 18:33", "2025-07-23 18:34", "2025-07-23 18:35", "2025-07-23 18:36", "2025-07-23 18:37", "2025-07-23 18:38", "2025-07-23 18:39", "2025-07-23 18:40", "2025-07-23 18:41", "2025-07-23 18:42", "2025-07-23 18:43", "2025-07-23 18:44", "2025-07-23 18:45", "2025-07-23 18:46", "2025-07-23 18:47", "2025-07-23 18:48", "2025-07-23 18:49", "2025-07-23 18:50", "2025-07-23 18:51", "2025-07-23 18:52", "2025-07-23 18:53", "2025-07-23 18:54", "2025-07-23 18:55", "2025-07-23 18:56", "2025-07-23 18:57", "2025-07-23 18:58", "2025-07-23 18:59", "2025-07-23 19:00", "2025-07-23 19:01", "2025-07-23 19:02", "2025-07-23 19:03", "2025-07-23 19:04", "2025-07-23 19:05", "2025-07-23 19:06", "2025-07-23 19:07", "2025-07-23 19:08", "2025-07-23 19:09", "2025-07-23 19:10", "2025-07-23 19:11", "2025-07-23 19:12", "2025-07-23 19:13", "2025-07-23 19:14", "2025-07-23 19:15", "2025-07-23 19:16", "2025-07-23 19:17", "2025-07-23 19:18", "2025-07-23 19:19", "2025-07-23 19:20", "2025-07-23 19:21", "2025-07-23 19:22", "2025-07-23 19:23", "2025-07-23 19:24", "2025-07-23 19:25", "2025-07-23 19:26", "2025-07-23 19:27", "2025-07-23 19:28", "2025-07-23 19:29", "2025-07-23 19:30", "2025-07-23 19:31", "2025-07-23 19:32", "2025-07-23 19:33", "2025-07-23 19:34", "2025-07-23 19:35", "2025-07-23 19:36", "2025-07-23 19:37", "2025-07-23 19:38", "2025-07-23 19:39", "2025-07-23 19:40", "2025-07-23 19:41", "2025-07-23 19:42", "2025-07-23 19:43", "2025-07-23 19:44", "2025-07-23 19:45", "2025-07-23 19:46", "2025-07-23 19:47", "2025-07-23 19:48", "2025-07-23 19:49", "2025-07-23 19:50", "2025-07-23 19:51", "2025-07-23 19:52", "2025-07-23 19:53", "2025-07-23 19:54", "2025-07-23 19:55", "2025-07-23 19:56", "2025-07-23 19:57", "2025-07-23 19:58", "2025-07-23 19:59", "2025-07-23 20:00", "2025-07-23 20:01", "2025-07-23 20:02", "2025-07-23 20:03", "2025-07-23 20:04", "2025-07-23 20:05", "2025-07-23 20:06", "2025-07-23 20:07", "2025-07-23 20:08", "2025-07-23 20:09", "2025-07-23 20:10", "2025-07-23 20:11", "2025-07-23 20:12", "2025-07-23 20:13", "2025-07-23 20:14", "2025-07-23 20:15", "2025-07-23 20:16", "2025-07-23 20:17", "2025-07-23 20:18", "2025-07-23 20:19", "2025-07-23 20:20", "2025-07-23 20:21", "2025-07-23 20:22", "2025-07-23 20:23", "2025-07-23 20:24", "2025-07-23 20:25", "2025-07-23 20:26", "2025-07-23 20:27", "2025-07-23 20:28", "2025-07-23 20:29", "2025-07-23 20:30", "2025-07-23 20:31", "2025-07-23 20:32", "2025-07-23 20:33", "2025-07-23 20:34", "2025-07-23 20:35", "2025-07-23 20:36", "2025-07-23 20:37", "2025-07-23 20:38", "2025-07-23 20:39", "2025-07-23 20:40", "2025-07-23 20:41", "2025-07-23 20:42", "2025-07-23 20:43", "2025-07-23 20:44", "2025-07-23 20:45", "2025-07-23 20:46", "2025-07-23 20:47", "2025-07-23 20:48", "2025-07-23 20:49", "2025-07-23 20:50", "2025-07-23 20:51", "2025-07-23 20:52", "2025-07-23 20:53", "2025-07-23 20:54", "2025-07-23 20:55", "2025-07-23 20:56", "2025-07-23 20:57", "2025-07-23 20:58", "2025-07-23 20:59", "2025-07-23 21:00", "2025-07-23 21:01", "2025-07-23 21:02", "2025-07-23 21:03", "2025-07-23 21:04", "2025-07-23 21:05", "2025-07-23 21:06", "2025-07-23 21:07", "2025-07-23 21:08", "2025-07-23 21:09", "2025-07-23 21:10", "2025-07-23 21:11", "2025-07-23 21:12", "2025-07-23 21:13", "2025-07-23 21:14", "2025-07-23 21:15", "2025-07-23 21:16", "2025-07-23 21:17", "2025-07-23 21:18", "2025-07-23 21:19", "2025-07-23 21:20", "2025-07-23 21:21", "2025-07-23 21:22", "2025-07-23 21:23", "2025-07-23 21:24", "2025-07-23 21:25", "2025-07-23 21:26", "2025-07-23 21:27", "2025-07-23 21:28", "2025-07-23 21:29", "2025-07-23 21:30", "2025-07-23 21:31", "2025-07-23 21:32", "2025-07-23 21:33", "2025-07-23 21:34", "2025-07-23 21:35", "2025-07-23 21:36", "2025-07-23 21:37", "2025-07-23 21:38", "2025-07-23 21:39", "2025-07-23 21:40", "2025-07-23 21:41", "2025-07-23 21:42", "2025-07-23 21:43", "2025-07-23 21:44", "2025-07-23 21:45", "2025-07-23 21:46", "2025-07-23 21:47", "2025-07-23 21:48", "2025-07-23 21:49", "2025-07-23 21:50", "2025-07-23 21:51", "2025-07-23 21:52", "2025-07-23 21:53", "2025-07-23 21:54", "2025-07-23 21:55", "2025-07-23 21:56", "2025-07-23 21:57", "2025-07-23 21:58", "2025-07-23 21:59", "2025-07-23 22:00", "2025-07-23 22:01", "2025-07-23 22:02", "2025-07-23 22:03", "2025-07-23 22:04", "2025-07-23 22:05", "2025-07-23 22:06", "2025-07-23 22:07", "2025-07-23 22:08", "2025-07-23 22:09", "2025-07-23 22:10", "2025-07-23 22:11", "2025-07-23 22:12", "2025-07-23 22:13", "2025-07-23 22:14", "2025-07-23 22:15", "2025-07-23 22:16", "2025-07-23 22:17", "2025-07-23 22:18", "2025-07-23 22:19", "2025-07-23 22:20", "2025-07-23 22:21", "2025-07-23 22:22", "2025-07-23 22:23", "2025-07-23 22:24", "2025-07-23 22:25", "2025-07-23 22:26", "2025-07-23 22:27", "2025-07-23 22:28", "2025-07-23 22:29", "2025-07-23 22:30", "2025-07-23 22:31", "2025-07-23 22:32", "2025-07-23 22:33", "2025-07-23 22:34", "2025-07-23 22:35", "2025-07-23 22:36", "2025-07-23 22:37", "2025-07-23 22:38", "2025-07-23 22:39", "2025-07-23 22:40", "2025-07-23 22:41", "2025-07-23 22:42", "2025-07-23 22:43", "2025-07-23 22:44", "2025-07-23 22:45", "2025-07-23 22:46", "2025-07-23 22:47", "2025-07-23 22:48", "2025-07-23 22:49", "2025-07-23 22:50", "2025-07-23 22:51", "2025-07-23 22:52", "2025-07-23 22:53", "2025-07-23 22:54", "2025-07-23 22:55", "2025-07-23 22:56", "2025-07-23 22:57", "2025-07-23 22:58", "2025-07-23 22:59", "2025-07-23 23:00", "2025-07-23 23:01", "2025-07-23 23:02", "2025-07-23 23:03", "2025-07-23 23:04", "2025-07-23 23:05", "2025-07-23 23:06", "2025-07-23 23:07", "2025-07-23 23:08", "2025-07-23 23:09", "2025-07-23 23:10", "2025-07-23 23:11", "2025-07-23 23:12", "2025-07-23 23:13", "2025-07-23 23:14", "2025-07-23 23:15", "2025-07-23 23:16", "2025-07-23 23:17", "2025-07-23 23:18", "2025-07-23 23:19", "2025-07-23 23:20", "2025-07-23 23:21", "2025-07-23 23:22", "2025-07-23 23:23", "2025-07-23 23:24", "2025-07-23 23:25", "2025-07-23 23:26", "2025-07-23 23:27", "2025-07-23 23:28", "2025-07-23 23:29", "2025-07-23 23:30", "2025-07-23 23:31", "2025-07-23 23:32", "2025-07-23 23:33", "2025-07-23 23:34", "2025-07-23 23:35", "2025-07-23 23:36", "2025-07-23 23:37", "2025-07-23 23:38", "2025-07-23 23:39", "2025-07-23 23:40", "2025-07-23 23:41", "2025-07-23 23:42", "2025-07-23 23:43", "2025-07-23 23:44", "2025-07-23 23:45", "2025-07-23 23:46", "2025-07-23 23:47", "2025-07-23 23:48", "2025-07-23 23:49", "2025-07-23 23:50", "2025-07-23 23:51", "2025-07-23 23:52", "2025-07-23 23:53", "2025-07-23 23:54", "2025-07-23 23:55", "2025-07-23 23:56", "2025-07-23 23:57", "2025-07-23 23:58", "2025-07-23 23:59", "2025-07-24 00:00", "2025-07-24 00:01", "2025-07-24 00:02", "2025-07-24 00:03", "2025-07-24 00:04", "2025-07-24 00:05", "2025-07-24 00:06", "2025-07-24 00:07", "2025-07-24 00:08", "2025-07-24 00:09", "2025-07-24 00:10", "2025-07-24 00:11", "2025-07-24 00:12", "2025-07-24 00:13", "2025-07-24 00:14", "2025-07-24 00:15", "2025-07-24 00:16", "2025-07-24 00:17", "2025-07-24 00:18", "2025-07-24 00:19", "2025-07-24 00:20", "2025-07-24 00:21", "2025-07-24 00:22", "2025-07-24 00:23", "2025-07-24 00:24", "2025-07-24 00:25", "2025-07-24 00:26", "2025-07-24 00:27", "2025-07-24 00:28", "2025-07-24 00:29", "2025-07-24 00:30", "2025-07-24 00:31", "2025-07-24 00:32", "2025-07-24 00:33", "2025-07-24 00:34", "2025-07-24 00:35", "2025-07-24 00:36", "2025-07-24 00:37", "2025-07-24 00:38", "2025-07-24 00:39", "2025-07-24 00:40", "2025-07-24 00:41", "2025-07-24 00:42", "2025-07-24 00:43", "2025-07-24 00:44", "2025-07-24 00:45", "2025-07-24 00:46", "2025-07-24 00:47", "2025-07-24 00:48", "2025-07-24 00:49", "2025-07-24 00:50", "2025-07-24 00:51", "2025-07-24 00:52", "2025-07-24 00:53", "2025-07-24 00:54", "2025-07-24 00:55", "2025-07-24 00:56", "2025-07-24 00:57", "2025-07-24 00:58", "2025-07-24 00:59", "2025-07-24 01:00", "2025-07-24 01:01", "2025-07-24 01:02", "2025-07-24 01:03", "2025-07-24 01:04", "2025-07-24 01:05", "2025-07-24 01:06", "2025-07-24 01:07", "2025-07-24 01:08", "2025-07-24 01:09", "2025-07-24 01:10", "2025-07-24 01:11", "2025-07-24 01:12", "2025-07-24 01:13", "2025-07-24 01:14", "2025-07-24 01:15", "2025-07-24 01:16", "2025-07-24 01:17", "2025-07-24 01:18", "2025-07-24 01:19", "2025-07-24 01:20", "2025-07-24 01:21", "2025-07-24 01:22", "2025-07-24 01:23", "2025-07-24 01:24", "2025-07-24 01:25", "2025-07-24 01:26", "2025-07-24 01:27", "2025-07-24 01:28", "2025-07-24 01:29", "2025-07-24 01:30", "2025-07-24 01:31", "2025-07-24 01:32", "2025-07-24 01:33", "2025-07-24 01:34", "2025-07-24 01:35", "2025-07-24 01:36", "2025-07-24 01:37", "2025-07-24 01:38", "2025-07-24 01:39", "2025-07-24 01:40", "2025-07-24 01:41", "2025-07-24 01:42", "2025-07-24 01:43", "2025-07-24 01:44", "2025-07-24 01:45", "2025-07-24 01:46", "2025-07-24 01:47", "2025-07-24 01:48", "2025-07-24 01:49", "2025-07-24 01:50", "2025-07-24 01:51", "2025-07-24 01:52", "2025-07-24 01:53", "2025-07-24 01:54", "2025-07-24 01:55", "2025-07-24 01:56", "2025-07-24 01:57", "2025-07-24 01:58", "2025-07-24 01:59", "2025-07-24 02:00", "2025-07-24 02:01", "2025-07-24 02:02", "2025-07-24 02:03", "2025-07-24 02:04", "2025-07-24 02:05", "2025-07-24 02:06", "2025-07-24 02:07", "2025-07-24 02:08", "2025-07-24 02:09", "2025-07-24 02:10", "2025-07-24 02:11", "2025-07-24 02:12", "2025-07-24 02:13", "2025-07-24 02:14", "2025-07-24 02:15", "2025-07-24 02:16", "2025-07-24 02:17", "2025-07-24 02:18", "2025-07-24 02:19", "2025-07-24 02:20", "2025-07-24 02:21", "2025-07-24 02:22", "2025-07-24 02:23", "2025-07-24 02:24", "2025-07-24 02:25", "2025-07-24 02:26", "2025-07-24 02:27", "2025-07-24 02:28", "2025-07-24 02:29", "2025-07-24 02:30", "2025-07-24 02:31", "2025-07-24 02:32", "2025-07-24 02:33", "2025-07-24 02:34", "2025-07-24 02:35", "2025-07-24 02:36", "2025-07-24 02:37", "2025-07-24 02:38", "2025-07-24 02:39", "2025-07-24 02:40", "2025-07-24 02:41", "2025-07-24 02:42", "2025-07-24 02:43", "2025-07-24 02:44", "2025-07-24 02:45", "2025-07-24 02:46", "2025-07-24 02:47", "2025-07-24 02:48", "2025-07-24 02:49", "2025-07-24 02:50", "2025-07-24 02:51", "2025-07-24 02:52", "2025-07-24 02:53", "2025-07-24 02:54", "2025-07-24 02:55", "2025-07-24 02:56", "2025-07-24 02:57", "2025-07-24 02:58", "2025-07-24 02:59", "2025-07-24 03:00", "2025-07-24 03:01", "2025-07-24 03:02", "2025-07-24 03:03", "2025-07-24 03:04", "2025-07-24 03:05", "2025-07-24 03:06", "2025-07-24 03:07", "2025-07-24 03:08", "2025-07-24 03:09", "2025-07-24 03:10", "2025-07-24 03:11", "2025-07-24 03:12", "2025-07-24 03:13", "2025-07-24 03:14", "2025-07-24 03:15", "2025-07-24 03:16", "2025-07-24 03:17", "2025-07-24 03:18", "2025-07-24 03:19", "2025-07-24 03:20", "2025-07-24 03:21", "2025-07-24 03:22", "2025-07-24 03:23", "2025-07-24 03:24", "2025-07-24 03:25", "2025-07-24 03:26", "2025-07-24 03:27", "2025-07-24 03:28", "2025-07-24 03:29", "2025-07-24 03:30", "2025-07-24 03:31", "2025-07-24 03:32", "2025-07-24 03:33", "2025-07-24 03:34", "2025-07-24 03:35", "2025-07-24 03:36", "2025-07-24 03:37", "2025-07-24 03:38", "2025-07-24 03:39", "2025-07-24 03:40", "2025-07-24 03:41", "2025-07-24 03:42", "2025-07-24 03:43", "2025-07-24 03:44", "2025-07-24 03:45", "2025-07-24 03:46", "2025-07-24 03:47", "2025-07-24 03:48", "2025-07-24 03:49", "2025-07-24 03:50", "2025-07-24 03:51", "2025-07-24 03:52", "2025-07-24 03:53", "2025-07-24 03:54", "2025-07-24 03:55", "2025-07-24 03:56", "2025-07-24 03:57", "2025-07-24 03:58", "2025-07-24 03:59", "2025-07-24 04:00", "2025-07-24 04:01", "2025-07-24 04:02", "2025-07-24 04:03", "2025-07-24 04:04", "2025-07-24 04:05", "2025-07-24 04:06", "2025-07-24 04:07", "2025-07-24 04:08", "2025-07-24 04:09", "2025-07-24 04:10", "2025-07-24 04:11", "2025-07-24 04:12", "2025-07-24 04:13", "2025-07-24 04:14", "2025-07-24 04:15", "2025-07-24 04:16", "2025-07-24 04:17", "2025-07-24 04:18", "2025-07-24 04:19", "2025-07-24 04:20", "2025-07-24 04:21", "2025-07-24 04:22", "2025-07-24 04:23", "2025-07-24 04:24", "2025-07-24 04:25", "2025-07-24 04:26", "2025-07-24 04:27", "2025-07-24 04:28", "2025-07-24 04:29", "2025-07-24 04:30", "2025-07-24 04:31", "2025-07-24 04:32", "2025-07-24 04:33", "2025-07-24 04:34", "2025-07-24 04:35", "2025-07-24 04:36", "2025-07-24 04:37", "2025-07-24 04:38", "2025-07-24 04:39", "2025-07-24 04:40", "2025-07-24 04:41", "2025-07-24 04:42", "2025-07-24 04:43", "2025-07-24 04:44", "2025-07-24 04:45", "2025-07-24 04:46", "2025-07-24 04:47", "2025-07-24 04:48", "2025-07-24 04:49", "2025-07-24 04:50", "2025-07-24 04:51", "2025-07-24 04:52", "2025-07-24 04:53", "2025-07-24 04:54", "2025-07-24 04:55", "2025-07-24 04:56", "2025-07-24 04:57", "2025-07-24 04:58", "2025-07-24 04:59", "2025-07-24 05:00", "2025-07-24 05:01", "2025-07-24 05:02", "2025-07-24 05:03", "2025-07-24 05:04", "2025-07-24 05:05", "2025-07-24 05:06", "2025-07-24 05:07", "2025-07-24 05:08", "2025-07-24 05:09", "2025-07-24 05:10", "2025-07-24 05:11", "2025-07-24 05:12", "2025-07-24 05:13", "2025-07-24 05:14", "2025-07-24 05:15", "2025-07-24 05:16", "2025-07-24 05:17", "2025-07-24 05:18", "2025-07-24 05:19", "2025-07-24 05:20", "2025-07-24 05:21", "2025-07-24 05:22", "2025-07-24 05:23", "2025-07-24 05:24", "2025-07-24 05:25", "2025-07-24 05:26", "2025-07-24 05:27", "2025-07-24 05:28", "2025-07-24 05:29", "2025-07-24 05:30", "2025-07-24 05:31", "2025-07-24 05:32", "2025-07-24 05:33", "2025-07-24 05:34", "2025-07-24 05:35", "2025-07-24 05:36", "2025-07-24 05:37", "2025-07-24 05:38", "2025-07-24 05:39", "2025-07-24 05:40", "2025-07-24 05:41", "2025-07-24 05:42", "2025-07-24 05:43", "2025-07-24 05:44", "2025-07-24 05:45", "2025-07-24 05:46", "2025-07-24 05:47", "2025-07-24 05:48", "2025-07-24 05:49", "2025-07-24 05:50", "2025-07-24 05:51", "2025-07-24 05:52", "2025-07-24 05:53", "2025-07-24 05:54", "2025-07-24 05:55", "2025-07-24 05:56", "2025-07-24 05:57", "2025-07-24 05:58", "2025-07-24 05:59", "2025-07-24 06:00", "2025-07-24 06:01", "2025-07-24 06:02", "2025-07-24 06:03", "2025-07-24 06:04", "2025-07-24 06:05", "2025-07-24 06:06", "2025-07-24 06:07", "2025-07-24 06:08", "2025-07-24 06:09", "2025-07-24 06:10", "2025-07-24 06:11", "2025-07-24 06:12", "2025-07-24 06:13", "2025-07-24 06:14", "2025-07-24 06:15", "2025-07-24 06:16", "2025-07-24 06:17", "2025-07-24 06:18", "2025-07-24 06:19", "2025-07-24 06:20", "2025-07-24 06:21", "2025-07-24 06:22", "2025-07-24 06:23", "2025-07-24 06:24", "2025-07-24 06:25", "2025-07-24 06:26", "2025-07-24 06:27", "2025-07-24 06:28", "2025-07-24 06:29", "2025-07-24 06:30", "2025-07-24 06:31", "2025-07-24 06:32", "2025-07-24 06:33", "2025-07-24 06:34", "2025-07-24 06:35", "2025-07-24 06:36", "2025-07-24 06:37", "2025-07-24 06:38", "2025-07-24 06:39", "2025-07-24 06:40", "2025-07-24 06:41", "2025-07-24 06:42", "2025-07-24 06:43", "2025-07-24 06:44", "2025-07-24 06:45", "2025-07-24 06:46", "2025-07-24 06:47", "2025-07-24 06:48", "2025-07-24 06:49", "2025-07-24 06:50", "2025-07-24 06:51", "2025-07-24 06:52", "2025-07-24 06:53", "2025-07-24 06:54", "2025-07-24 06:55", "2025-07-24 06:56", "2025-07-24 06:57", "2025-07-24 06:58", "2025-07-24 06:59", "2025-07-24 07:00", "2025-07-24 07:01", "2025-07-24 07:02", "2025-07-24 07:03", "2025-07-24 07:04", "2025-07-24 07:05", "2025-07-24 07:06", "2025-07-24 07:07", "2025-07-24 07:08", "2025-07-24 07:09", "2025-07-24 07:10", "2025-07-24 07:11", "2025-07-24 07:12", "2025-07-24 07:13", "2025-07-24 07:14", "2025-07-24 07:15", "2025-07-24 07:16", "2025-07-24 07:17", "2025-07-24 07:18", "2025-07-24 07:19", "2025-07-24 07:20", "2025-07-24 07:21", "2025-07-24 07:22", "2025-07-24 07:23", "2025-07-24 07:24", "2025-07-24 07:25", "2025-07-24 07:26", "2025-07-24 07:27", "2025-07-24 07:28", "2025-07-24 07:29", "2025-07-24 07:30", "2025-07-24 07:31", "2025-07-24 07:32", "2025-07-24 07:33", "2025-07-24 07:34", "2025-07-24 07:35", "2025-07-24 07:36", "2025-07-24 07:37", "2025-07-24 07:38", "2025-07-24 07:39", "2025-07-24 07:40", "2025-07-24 07:41", "2025-07-24 07:42", "2025-07-24 07:43", "2025-07-24 07:44", "2025-07-24 07:45", "2025-07-24 07:46", "2025-07-24 07:47", "2025-07-24 07:48", "2025-07-24 07:49", "2025-07-24 07:50", "2025-07-24 07:51", "2025-07-24 07:52", "2025-07-24 07:53", "2025-07-24 07:54", "2025-07-24 07:55", "2025-07-24 07:56", "2025-07-24 07:57", "2025-07-24 07:58", "2025-07-24 07:59", "2025-07-24 08:00", "2025-07-24 08:01", "2025-07-24 08:02", "2025-07-24 08:03", "2025-07-24 08:04", "2025-07-24 08:05", "2025-07-24 08:06", "2025-07-24 08:07", "2025-07-24 08:08", "2025-07-24 08:09", "2025-07-24 08:10", "2025-07-24 08:11", "2025-07-24 08:12", "2025-07-24 08:13", "2025-07-24 08:14", "2025-07-24 08:15", "2025-07-24 08:16", "2025-07-24 08:17", "2025-07-24 08:18", "2025-07-24 08:19", "2025-07-24 08:20", "2025-07-24 08:21", "2025-07-24 08:22", "2025-07-24 08:23", "2025-07-24 08:24", "2025-07-24 08:25", "2025-07-24 08:26", "2025-07-24 08:27", "2025-07-24 08:28", "2025-07-24 08:29", "2025-07-24 08:30", "2025-07-24 08:31", "2025-07-24 08:32", "2025-07-24 08:33", "2025-07-24 08:34", "2025-07-24 08:35", "2025-07-24 08:36", "2025-07-24 08:37", "2025-07-24 08:38", "2025-07-24 08:39", "2025-07-24 08:40", "2025-07-24 08:41", "2025-07-24 08:42", "2025-07-24 08:43", "2025-07-24 08:44", "2025-07-24 08:45", "2025-07-24 08:46", "2025-07-24 08:47", "2025-07-24 08:48", "2025-07-24 08:49", "2025-07-24 08:50", "2025-07-24 08:51", "2025-07-24 08:52", "2025-07-24 08:53", "2025-07-24 08:54", "2025-07-24 08:55", "2025-07-24 08:56", "2025-07-24 08:57", "2025-07-24 08:58", "2025-07-24 08:59", "2025-07-24 09:00", "2025-07-24 09:01", "2025-07-24 09:02", "2025-07-24 09:03", "2025-07-24 09:04", "2025-07-24 09:05", "2025-07-24 09:06", "2025-07-24 09:07", "2025-07-24 09:08", "2025-07-24 09:09", "2025-07-24 09:10", "2025-07-24 09:11", "2025-07-24 09:12", "2025-07-24 09:13", "2025-07-24 09:14", "2025-07-24 09:15", "2025-07-24 09:16", "2025-07-24 09:17", "2025-07-24 09:18", "2025-07-24 09:19", "2025-07-24 09:20", "2025-07-24 09:21", "2025-07-24 09:22", "2025-07-24 09:23", "2025-07-24 09:24", "2025-07-24 09:25", "2025-07-24 09:26", "2025-07-24 09:27", "2025-07-24 09:28", "2025-07-24 09:29", "2025-07-24 09:30", "2025-07-24 09:31", "2025-07-24 09:32", "2025-07-24 09:33", "2025-07-24 09:34", "2025-07-24 09:35", "2025-07-24 09:36", "2025-07-24 09:37", "2025-07-24 09:38", "2025-07-24 09:39", "2025-07-24 09:40", "2025-07-24 09:41", "2025-07-24 09:42", "2025-07-24 09:43", "2025-07-24 09:44", "2025-07-24 09:45", "2025-07-24 09:46", "2025-07-24 09:47", "2025-07-24 09:48", "2025-07-24 09:49", "2025-07-24 09:50", "2025-07-24 09:51", "2025-07-24 09:52", "2025-07-24 09:53", "2025-07-24 09:54", "2025-07-24 09:55", "2025-07-24 09:56", "2025-07-24 09:57", "2025-07-24 09:58", "2025-07-24 09:59", "2025-07-24 10:00", "2025-07-24 10:01", "2025-07-24 10:02", "2025-07-24 10:03", "2025-07-24 10:04", "2025-07-24 10:05", "2025-07-24 10:06", "2025-07-24 10:07", "2025-07-24 10:08", "2025-07-24 10:09", "2025-07-24 10:10", "2025-07-24 10:11", "2025-07-24 10:12", "2025-07-24 10:13", "2025-07-24 10:14", "2025-07-24 10:15", "2025-07-24 10:16", "2025-07-24 10:17", "2025-07-24 10:18", "2025-07-24 10:19", "2025-07-24 10:20", "2025-07-24 10:21", "2025-07-24 10:22", "2025-07-24 10:23", "2025-07-24 10:24", "2025-07-24 10:25", "2025-07-24 10:26", "2025-07-24 10:27", "2025-07-24 10:28", "2025-07-24 10:29", "2025-07-24 10:30", "2025-07-24 10:31", "2025-07-24 10:32", "2025-07-24 10:33", "2025-07-24 10:34", "2025-07-24 10:35", "2025-07-24 10:36", "2025-07-24 10:37", "2025-07-24 10:38", "2025-07-24 10:39", "2025-07-24 10:40", "2025-07-24 10:41", "2025-07-24 10:42", "2025-07-24 10:43", "2025-07-24 10:44", "2025-07-24 10:45", "2025-07-24 10:46", "2025-07-24 10:47", "2025-07-24 10:48", "2025-07-24 10:49", "2025-07-24 10:50", "2025-07-24 10:51", "2025-07-24 10:52", "2025-07-24 10:53", "2025-07-24 10:54", "2025-07-24 10:55", "2025-07-24 10:56", "2025-07-24 10:57", "2025-07-24 10:58", "2025-07-24 10:59", "2025-07-24 11:00", "2025-07-24 11:01", "2025-07-24 11:02", "2025-07-24 11:03", "2025-07-24 11:04", "2025-07-24 11:05", "2025-07-24 11:06", "2025-07-24 11:07", "2025-07-24 11:08", "2025-07-24 11:09", "2025-07-24 11:10", "2025-07-24 11:11", "2025-07-24 11:12", "2025-07-24 11:13", "2025-07-24 11:14", "2025-07-24 11:15", "2025-07-24 11:16", "2025-07-24 11:17", "2025-07-24 11:18", "2025-07-24 11:19", "2025-07-24 11:20", "2025-07-24 11:21", "2025-07-24 11:22", "2025-07-24 11:23", "2025-07-24 11:24", "2025-07-24 11:25", "2025-07-24 11:26", "2025-07-24 11:27", "2025-07-24 11:28", "2025-07-24 11:29", "2025-07-24 11:30", "2025-07-24 11:31", "2025-07-24 11:32", "2025-07-24 11:33", "2025-07-24 11:34", "2025-07-24 11:35", "2025-07-24 11:36", "2025-07-24 11:37", "2025-07-24 11:38", "2025-07-24 11:39", "2025-07-24 11:40", "2025-07-24 11:41", "2025-07-24 11:42", "2025-07-24 11:43", "2025-07-24 11:44", "2025-07-24 11:45", "2025-07-24 11:46", "2025-07-24 11:47", "2025-07-24 11:48", "2025-07-24 11:49", "2025-07-24 11:50", "2025-07-24 11:51", "2025-07-24 11:52", "2025-07-24 11:53", "2025-07-24 11:54", "2025-07-24 11:55", "2025-07-24 11:56", "2025-07-24 11:57", "2025-07-24 11:58", "2025-07-24 11:59", "2025-07-24 12:00", "2025-07-24 12:01", "2025-07-24 12:02", "2025-07-24 12:03", "2025-07-24 12:04", "2025-07-24 12:05", "2025-07-24 12:06", "2025-07-24 12:07", "2025-07-24 12:08", "2025-07-24 12:09", "2025-07-24 12:10", "2025-07-24 12:11", "2025-07-24 12:12", "2025-07-24 12:13", "2025-07-24 12:14", "2025-07-24 12:15", "2025-07-24 12:16", "2025-07-24 12:17", "2025-07-24 12:18", "2025-07-24 12:19", "2025-07-24 12:20", "2025-07-24 12:21", "2025-07-24 12:22", "2025-07-24 12:23", "2025-07-24 12:24", "2025-07-24 12:25", "2025-07-24 12:26", "2025-07-24 12:27", "2025-07-24 12:28", "2025-07-24 12:29", "2025-07-24 12:30", "2025-07-24 12:31", "2025-07-24 12:32", "2025-07-24 12:33", "2025-07-24 12:34", "2025-07-24 12:35", "2025-07-24 12:36", "2025-07-24 12:37", "2025-07-24 12:38", "2025-07-24 12:39", "2025-07-24 12:40", "2025-07-24 12:41", "2025-07-24 12:42", "2025-07-24 12:43", "2025-07-24 12:44", "2025-07-24 12:45", "2025-07-24 12:46", "2025-07-24 12:47", "2025-07-24 12:48", "2025-07-24 12:49", "2025-07-24 12:50", "2025-07-24 12:51", "2025-07-24 12:52", "2025-07-24 12:53", "2025-07-24 12:54", "2025-07-24 12:55", "2025-07-24 12:56", "2025-07-24 12:57", "2025-07-24 12:58", "2025-07-24 12:59", "2025-07-24 13:00", "2025-07-24 13:01", "2025-07-24 13:02", "2025-07-24 13:03", "2025-07-24 13:04", "2025-07-24 13:05", "2025-07-24 13:06", "2025-07-24 13:07", "2025-07-24 13:08", "2025-07-24 13:09", "2025-07-24 13:10", "2025-07-24 13:11", "2025-07-24 13:12", "2025-07-24 13:13", "2025-07-24 13:14", "2025-07-24 13:15", "2025-07-24 13:16", "2025-07-24 13:17", "2025-07-24 13:18", "2025-07-24 13:19", "2025-07-24 13:20", "2025-07-24 13:21", "2025-07-24 13:22", "2025-07-24 13:23", "2025-07-24 13:24", "2025-07-24 13:25", "2025-07-24 13:26", "2025-07-24 13:27", "2025-07-24 13:28", "2025-07-24 13:29", "2025-07-24 13:30", "2025-07-24 13:31", "2025-07-24 13:32", "2025-07-24 13:33", "2025-07-24 13:34", "2025-07-24 13:35", "2025-07-24 13:36", "2025-07-24 13:37", "2025-07-24 13:38", "2025-07-24 13:39", "2025-07-24 13:40", "2025-07-24 13:41", "2025-07-24 13:42", "2025-07-24 13:43", "2025-07-24 13:44", "2025-07-24 13:45", "2025-07-24 13:46", "2025-07-24 13:47", "2025-07-24 13:48", "2025-07-24 13:49", "2025-07-24 13:50", "2025-07-24 13:51", "2025-07-24 13:52", "2025-07-24 13:53", "2025-07-24 13:54", "2025-07-24 13:55", "2025-07-24 13:56", "2025-07-24 13:57", "2025-07-24 13:58", "2025-07-24 13:59", "2025-07-24 14:00", "2025-07-24 14:01", "2025-07-24 14:02", "2025-07-24 14:03", "2025-07-24 14:04", "2025-07-24 14:05", "2025-07-24 14:06", "2025-07-24 14:07", "2025-07-24 14:08", "2025-07-24 14:09", "2025-07-24 14:10", "2025-07-24 14:11", "2025-07-24 14:12", "2025-07-24 14:13", "2025-07-24 14:14", "2025-07-24 14:15", "2025-07-24 14:16", "2025-07-24 14:17", "2025-07-24 14:18", "2025-07-24 14:19", "2025-07-24 14:20", "2025-07-24 14:21", "2025-07-24 14:22", "2025-07-24 14:23", "2025-07-24 14:24", "2025-07-24 14:25", "2025-07-24 14:26", "2025-07-24 14:27", "2025-07-24 14:28", "2025-07-24 14:29", "2025-07-24 14:30", "2025-07-24 14:31", "2025-07-24 14:32", "2025-07-24 14:33", "2025-07-24 14:34", "2025-07-24 14:35", "2025-07-24 14:36", "2025-07-24 14:37", "2025-07-24 14:38", "2025-07-24 14:39", "2025-07-24 14:40", "2025-07-24 14:41", "2025-07-24 14:42", "2025-07-24 14:43", "2025-07-24 14:44", "2025-07-24 14:45", "2025-07-24 14:46", "2025-07-24 14:47", "2025-07-24 14:48", "2025-07-24 14:49", "2025-07-24 14:50", "2025-07-24 14:51", "2025-07-24 14:52", "2025-07-24 14:53", "2025-07-24 14:54", "2025-07-24 14:55", "2025-07-24 14:56", "2025-07-24 14:57", "2025-07-24 14:58", "2025-07-24 14:59", "2025-07-24 15:00", "2025-07-24 15:01", "2025-07-24 15:02", "2025-07-24 15:03", "2025-07-24 15:04", "2025-07-24 15:05", "2025-07-24 15:06", "2025-07-24 15:07", "2025-07-24 15:08", "2025-07-24 15:09", "2025-07-24 15:10", "2025-07-24 15:11", "2025-07-24 15:12", "2025-07-24 15:13", "2025-07-24 15:14", "2025-07-24 15:15", "2025-07-24 15:16", "2025-07-24 15:17", "2025-07-24 15:18", "2025-07-24 15:19", "2025-07-24 15:20", "2025-07-24 15:21", "2025-07-24 15:22", "2025-07-24 15:23", "2025-07-24 15:24", "2025-07-24 15:25", "2025-07-24 15:26", "2025-07-24 15:27", "2025-07-24 15:28", "2025-07-24 15:29", "2025-07-24 15:30", "2025-07-24 15:31", "2025-07-24 15:32", "2025-07-24 15:33", "2025-07-24 15:34", "2025-07-24 15:35", "2025-07-24 15:36", "2025-07-24 15:37", "2025-07-24 15:38", "2025-07-24 15:39", "2025-07-24 15:40", "2025-07-24 15:41", "2025-07-24 15:42", "2025-07-24 15:43", "2025-07-24 15:44", "2025-07-24 15:45", "2025-07-24 15:46", "2025-07-24 15:47", "2025-07-24 15:48", "2025-07-24 15:49", "2025-07-24 15:50", "2025-07-24 15:51", "2025-07-24 15:52", "2025-07-24 15:53", "2025-07-24 15:54", "2025-07-24 15:55", "2025-07-24 15:56", "2025-07-24 15:57", "2025-07-24 15:58", "2025-07-24 15:59", "2025-07-24 16:00", "2025-07-24 16:01", "2025-07-24 16:02", "2025-07-24 16:03", "2025-07-24 16:04", "2025-07-24 16:05", "2025-07-24 16:06", "2025-07-24 16:07", "2025-07-24 16:08", "2025-07-24 16:09", "2025-07-24 16:10", "2025-07-24 16:11", "2025-07-24 16:12", "2025-07-24 16:13", "2025-07-24 16:14", "2025-07-24 16:15", "2025-07-24 16:16", "2025-07-24 16:17", "2025-07-24 16:18", "2025-07-24 16:19", "2025-07-24 16:20", "2025-07-24 16:21", "2025-07-24 16:22", "2025-07-24 16:23", "2025-07-24 16:24", "2025-07-24 16:25", "2025-07-24 16:26", "2025-07-24 16:27", "2025-07-24 16:28", "2025-07-24 16:29", "2025-07-24 16:30", "2025-07-24 16:31", "2025-07-24 16:32", "2025-07-24 16:33", "2025-07-24 16:34", "2025-07-24 16:35", "2025-07-24 16:36", "2025-07-24 16:37", "2025-07-24 16:38", "2025-07-24 16:39", "2025-07-24 16:40", "2025-07-24 16:41", "2025-07-24 16:42", "2025-07-24 16:43", "2025-07-24 16:44", "2025-07-24 16:45", "2025-07-24 16:46", "2025-07-24 16:47", "2025-07-24 16:48", "2025-07-24 16:49", "2025-07-24 16:50", "2025-07-24 16:51", "2025-07-24 16:52", "2025-07-24 16:53", "2025-07-24 16:54", "2025-07-24 16:55", "2025-07-24 16:56", "2025-07-24 16:57", "2025-07-24 16:58", "2025-07-24 16:59", "2025-07-24 17:00", "2025-07-24 17:01", "2025-07-24 17:02", "2025-07-24 17:03", "2025-07-24 17:04", "2025-07-24 17:05", "2025-07-24 17:06", "2025-07-24 17:07", "2025-07-24 17:08", "2025-07-24 17:09", "2025-07-24 17:10", "2025-07-24 17:11", "2025-07-24 17:12", "2025-07-24 17:13", "2025-07-24 17:14", "2025-07-24 17:15", "2025-07-24 17:16", "2025-07-24 17:17", "2025-07-24 17:18", "2025-07-24 17:19", "2025-07-24 17:20", "2025-07-24 17:21", "2025-07-24 17:22", "2025-07-24 17:23", "2025-07-24 17:24", "2025-07-24 17:25", "2025-07-24 17:26", "2025-07-24 17:27", "2025-07-24 17:28", "2025-07-24 17:29", "2025-07-24 17:30", "2025-07-24 17:31", "2025-07-24 17:32", "2025-07-24 17:33", "2025-07-24 17:34", "2025-07-24 17:35", "2025-07-24 17:36", "2025-07-24 17:37", "2025-07-24 17:38", "2025-07-24 17:39", "2025-07-24 17:40", "2025-07-24 17:41", "2025-07-24 17:42", "2025-07-24 17:43", "2025-07-24 17:44", "2025-07-24 17:45", "2025-07-24 17:46", "2025-07-24 17:47", "2025-07-24 17:48", "2025-07-24 17:49", "2025-07-24 17:50", "2025-07-24 17:51", "2025-07-24 17:52", "2025-07-24 17:53", "2025-07-24 17:54", "2025-07-24 17:55", "2025-07-24 17:56", "2025-07-24 17:57", "2025-07-24 17:58", "2025-07-24 17:59", "2025-07-24 18:00", "2025-07-24 18:01", "2025-07-24 18:02", "2025-07-24 18:03", "2025-07-24 18:04", "2025-07-24 18:05", "2025-07-24 18:06", "2025-07-24 18:07", "2025-07-24 18:08", "2025-07-24 18:09", "2025-07-24 18:10", "2025-07-24 18:11", "2025-07-24 18:12", "2025-07-24 18:13", "2025-07-24 18:14", "2025-07-24 18:15", "2025-07-24 18:16", "2025-07-24 18:17", "2025-07-24 18:18", "2025-07-24 18:19", "2025-07-24 18:20", "2025-07-24 18:21", "2025-07-24 18:22", "2025-07-24 18:23", "2025-07-24 18:24", "2025-07-24 18:25", "2025-07-24 18:26", "2025-07-24 18:27", "2025-07-24 18:28", "2025-07-24 18:29", "2025-07-24 18:30", "2025-07-24 18:31", "2025-07-24 18:32", "2025-07-24 18:33", "2025-07-24 18:34", "2025-07-24 18:35", "2025-07-24 18:36", "2025-07-24 18:37", "2025-07-24 18:38", "2025-07-24 18:39", "2025-07-24 18:40", "2025-07-24 18:41", "2025-07-24 18:42", "2025-07-24 18:43", "2025-07-24 18:44", "2025-07-24 18:45", "2025-07-24 18:46", "2025-07-24 18:47", "2025-07-24 18:48", "2025-07-24 18:49", "2025-07-24 18:50", "2025-07-24 18:51", "2025-07-24 18:52", "2025-07-24 18:53", "2025-07-24 18:54", "2025-07-24 18:55", "2025-07-24 18:56", "2025-07-24 18:57", "2025-07-24 18:58", "2025-07-24 18:59", "2025-07-24 19:00", "2025-07-24 19:01", "2025-07-24 19:02", "2025-07-24 19:03", "2025-07-24 19:04", "2025-07-24 19:05", "2025-07-24 19:06", "2025-07-24 19:07", "2025-07-24 19:08", "2025-07-24 19:09", "2025-07-24 19:10", "2025-07-24 19:11", "2025-07-24 19:12", "2025-07-24 19:13", "2025-07-24 19:14", "2025-07-24 19:15", "2025-07-24 19:16", "2025-07-24 19:17", "2025-07-24 19:18", "2025-07-24 19:19", "2025-07-24 19:20", "2025-07-24 19:21", "2025-07-24 19:22", "2025-07-24 19:23", "2025-07-24 19:24", "2025-07-24 19:25", "2025-07-24 19:26", "2025-07-24 19:27", "2025-07-24 19:28", "2025-07-24 19:29", "2025-07-24 19:30", "2025-07-24 19:31", "2025-07-24 19:32", "2025-07-24 19:33", "2025-07-24 19:34", "2025-07-24 19:35", "2025-07-24 19:36", "2025-07-24 19:37", "2025-07-24 19:38", "2025-07-24 19:39", "2025-07-24 19:40", "2025-07-24 19:41", "2025-07-24 19:42", "2025-07-24 19:43", "2025-07-24 19:44", "2025-07-24 19:45", "2025-07-24 19:46", "2025-07-24 19:47", "2025-07-24 19:48", "2025-07-24 19:49", "2025-07-24 19:50", "2025-07-24 19:51", "2025-07-24 19:52", "2025-07-24 19:53", "2025-07-24 19:54", "2025-07-24 19:55", "2025-07-24 19:56", "2025-07-24 19:57", "2025-07-24 19:58", "2025-07-24 19:59", "2025-07-24 20:00", "2025-07-24 20:01", "2025-07-24 20:02", "2025-07-24 20:03", "2025-07-24 20:04", "2025-07-24 20:05", "2025-07-24 20:06", "2025-07-24 20:07", "2025-07-24 20:08", "2025-07-24 20:09", "2025-07-24 20:10", "2025-07-24 20:11", "2025-07-24 20:12", "2025-07-24 20:13", "2025-07-24 20:14", "2025-07-24 20:15", "2025-07-24 20:16", "2025-07-24 20:17", "2025-07-24 20:18", "2025-07-24 20:19", "2025-07-24 20:20", "2025-07-24 20:21", "2025-07-24 20:22", "2025-07-24 20:23", "2025-07-24 20:24", "2025-07-24 20:25", "2025-07-24 20:26", "2025-07-24 20:27", "2025-07-24 20:28", "2025-07-24 20:29", "2025-07-24 20:30", "2025-07-24 20:31", "2025-07-24 20:32", "2025-07-24 20:33", "2025-07-24 20:34", "2025-07-24 20:35", "2025-07-24 20:36", "2025-07-24 20:37", "2025-07-24 20:38", "2025-07-24 20:39", "2025-07-24 20:40", "2025-07-24 20:41", "2025-07-24 20:42", "2025-07-24 20:43", "2025-07-24 20:44", "2025-07-24 20:45", "2025-07-24 20:46", "2025-07-24 20:47", "2025-07-24 20:48", "2025-07-24 20:49", "2025-07-24 20:50", "2025-07-24 20:51", "2025-07-24 20:52", "2025-07-24 20:53", "2025-07-24 20:54", "2025-07-24 20:55", "2025-07-24 20:56", "2025-07-24 20:57", "2025-07-24 20:58", "2025-07-24 20:59", "2025-07-24 21:00", "2025-07-24 21:01", "2025-07-24 21:02", "2025-07-24 21:03", "2025-07-24 21:04", "2025-07-24 21:05", "2025-07-24 21:06", "2025-07-24 21:07", "2025-07-24 21:08", "2025-07-24 21:09", "2025-07-24 21:10", "2025-07-24 21:11", "2025-07-24 21:12", "2025-07-24 21:13", "2025-07-24 21:14", "2025-07-24 21:15", "2025-07-24 21:16", "2025-07-24 21:17", "2025-07-24 21:18", "2025-07-24 21:19", "2025-07-24 21:20", "2025-07-24 21:21", "2025-07-24 21:22", "2025-07-24 21:23", "2025-07-24 21:24", "2025-07-24 21:25", "2025-07-24 21:26", "2025-07-24 21:27", "2025-07-24 21:28", "2025-07-24 21:29", "2025-07-24 21:30", "2025-07-24 21:31", "2025-07-24 21:32", "2025-07-24 21:33", "2025-07-24 21:34", "2025-07-24 21:35", "2025-07-24 21:36", "2025-07-24 21:37", "2025-07-24 21:38", "2025-07-24 21:39", "2025-07-24 21:40", "2025-07-24 21:41", "2025-07-24 21:42", "2025-07-24 21:43", "2025-07-24 21:44", "2025-07-24 21:45", "2025-07-24 21:46", "2025-07-24 21:47", "2025-07-24 21:48", "2025-07-24 21:49", "2025-07-24 21:50", "2025-07-24 21:51", "2025-07-24 21:52", "2025-07-24 21:53", "2025-07-24 21:54", "2025-07-24 21:55", "2025-07-24 21:56", "2025-07-24 21:57", "2025-07-24 21:58", "2025-07-24 21:59", "2025-07-24 22:00", "2025-07-24 22:01", "2025-07-24 22:02", "2025-07-24 22:03", "2025-07-24 22:04", "2025-07-24 22:05", "2025-07-24 22:06", "2025-07-24 22:07", "2025-07-24 22:08", "2025-07-24 22:09", "2025-07-24 22:10", "2025-07-24 22:11", "2025-07-24 22:12", "2025-07-24 22:13", "2025-07-24 22:14", "2025-07-24 22:15", "2025-07-24 22:16", "2025-07-24 22:17", "2025-07-24 22:18", "2025-07-24 22:19", "2025-07-24 22:20", "2025-07-24 22:21", "2025-07-24 22:22", "2025-07-24 22:23", "2025-07-24 22:24", "2025-07-24 22:25", "2025-07-24 22:26", "2025-07-24 22:27", "2025-07-24 22:28", "2025-07-24 22:29", "2025-07-24 22:30", "2025-07-24 22:31", "2025-07-24 22:32", "2025-07-24 22:33", "2025-07-24 22:34", "2025-07-24 22:35", "2025-07-24 22:36", "2025-07-24 22:37", "2025-07-24 22:38", "2025-07-24 22:39", "2025-07-24 22:40", "2025-07-24 22:41", "2025-07-24 22:42", "2025-07-24 22:43", "2025-07-24 22:44", "2025-07-24 22:45", "2025-07-24 22:46", "2025-07-24 22:47", "2025-07-24 22:48", "2025-07-24 22:49", "2025-07-24 22:50", "2025-07-24 22:51", "2025-07-24 22:52", "2025-07-24 22:53", "2025-07-24 22:54", "2025-07-24 22:55", "2025-07-24 22:56", "2025-07-24 22:57", "2025-07-24 22:58", "2025-07-24 22:59", "2025-07-24 23:00", "2025-07-24 23:01", "2025-07-24 23:02", "2025-07-24 23:03", "2025-07-24 23:04", "2025-07-24 23:05", "2025-07-24 23:06", "2025-07-24 23:07", "2025-07-24 23:08", "2025-07-24 23:09", "2025-07-24 23:10", "2025-07-24 23:11", "2025-07-24 23:12", "2025-07-24 23:13", "2025-07-24 23:14", "2025-07-24 23:15", "2025-07-24 23:16", "2025-07-24 23:17", "2025-07-24 23:18", "2025-07-24 23:19", "2025-07-24 23:20", "2025-07-24 23:21", "2025-07-24 23:22", "2025-07-24 23:23", "2025-07-24 23:24", "2025-07-24 23:25", "2025-07-24 23:26", "2025-07-24 23:27", "2025-07-24 23:28", "2025-07-24 23:29", "2025-07-24 23:30", "2025-07-24 23:31", "2025-07-24 23:32", "2025-07-24 23:33", "2025-07-24 23:34", "2025-07-24 23:35", "2025-07-24 23:36", "2025-07-24 23:37", "2025-07-24 23:38", "2025-07-24 23:39", "2025-07-24 23:40", "2025-07-24 23:41", "2025-07-24 23:42", "2025-07-24 23:43", "2025-07-24 23:44", "2025-07-24 23:45", "2025-07-24 23:46", "2025-07-24 23:47", "2025-07-24 23:48", "2025-07-24 23:49", "2025-07-24 23:50", "2025-07-24 23:51", "2025-07-24 23:52", "2025-07-24 23:53", "2025-07-24 23:54", "2025-07-24 23:55", "2025-07-24 23:56", "2025-07-24 23:57", "2025-07-24 23:58", "2025-07-24 23:59", "2025-07-25 00:00", "2025-07-25 00:01", "2025-07-25 00:02", "2025-07-25 00:03", "2025-07-25 00:04", "2025-07-25 00:05", "2025-07-25 00:06", "2025-07-25 00:07", "2025-07-25 00:08", "2025-07-25 00:09", "2025-07-25 00:10", "2025-07-25 00:11", "2025-07-25 00:12", "2025-07-25 00:13", "2025-07-25 00:14", "2025-07-25 00:15", "2025-07-25 00:16", "2025-07-25 00:17", "2025-07-25 00:18", "2025-07-25 00:19", "2025-07-25 00:20", "2025-07-25 00:21", "2025-07-25 00:22", "2025-07-25 00:23", "2025-07-25 00:24", "2025-07-25 00:25", "2025-07-25 00:26", "2025-07-25 00:27", "2025-07-25 00:28", "2025-07-25 00:29", "2025-07-25 00:30", "2025-07-25 00:31", "2025-07-25 00:32", "2025-07-25 00:33", "2025-07-25 00:34", "2025-07-25 00:35", "2025-07-25 00:36", "2025-07-25 00:37", "2025-07-25 00:38", "2025-07-25 00:39", "2025-07-25 00:40", "2025-07-25 00:41", "2025-07-25 00:42", "2025-07-25 00:43", "2025-07-25 00:44", "2025-07-25 00:45", "2025-07-25 00:46", "2025-07-25 00:47", "2025-07-25 00:48", "2025-07-25 00:49", "2025-07-25 00:50", "2025-07-25 00:51", "2025-07-25 00:52", "2025-07-25 00:53", "2025-07-25 00:54", "2025-07-25 00:55", "2025-07-25 00:56", "2025-07-25 00:57", "2025-07-25 00:58", "2025-07-25 00:59", "2025-07-25 01:00", "2025-07-25 01:01", "2025-07-25 01:02", "2025-07-25 01:03", "2025-07-25 01:04", "2025-07-25 01:05", "2025-07-25 01:06", "2025-07-25 01:07", "2025-07-25 01:08", "2025-07-25 01:09", "2025-07-25 01:10", "2025-07-25 01:11", "2025-07-25 01:12", "2025-07-25 01:13", "2025-07-25 01:14", "2025-07-25 01:15", "2025-07-25 01:16", "2025-07-25 01:17", "2025-07-25 01:18", "2025-07-25 01:19", "2025-07-25 01:20", "2025-07-25 01:21", "2025-07-25 01:22", "2025-07-25 01:23", "2025-07-25 01:24", "2025-07-25 01:25", "2025-07-25 01:26", "2025-07-25 01:27", "2025-07-25 01:28", "2025-07-25 01:29", "2025-07-25 01:30", "2025-07-25 01:31", "2025-07-25 01:32", "2025-07-25 01:33", "2025-07-25 01:34", "2025-07-25 01:35", "2025-07-25 01:36", "2025-07-25 01:37", "2025-07-25 01:38", "2025-07-25 01:39", "2025-07-25 01:40", "2025-07-25 01:41", "2025-07-25 01:42", "2025-07-25 01:43", "2025-07-25 01:44", "2025-07-25 01:45", "2025-07-25 01:46", "2025-07-25 01:47", "2025-07-25 01:48", "2025-07-25 01:49", "2025-07-25 01:50", "2025-07-25 01:51", "2025-07-25 01:52", "2025-07-25 01:53", "2025-07-25 01:54", "2025-07-25 01:55", "2025-07-25 01:56", "2025-07-25 01:57", "2025-07-25 01:58", "2025-07-25 01:59", "2025-07-25 02:00", "2025-07-25 02:01", "2025-07-25 02:02", "2025-07-25 02:03", "2025-07-25 02:04", "2025-07-25 02:05", "2025-07-25 02:06", "2025-07-25 02:07", "2025-07-25 02:08", "2025-07-25 02:09", "2025-07-25 02:10", "2025-07-25 02:11", "2025-07-25 02:12", "2025-07-25 02:13", "2025-07-25 02:14", "2025-07-25 02:15", "2025-07-25 02:16", "2025-07-25 02:17", "2025-07-25 02:18", "2025-07-25 02:19", "2025-07-25 02:20", "2025-07-25 02:21", "2025-07-25 02:22", "2025-07-25 02:23", "2025-07-25 02:24", "2025-07-25 02:25", "2025-07-25 02:26", "2025-07-25 02:27", "2025-07-25 02:28", "2025-07-25 02:29", "2025-07-25 02:30", "2025-07-25 02:31", "2025-07-25 02:32", "2025-07-25 02:33", "2025-07-25 02:34", "2025-07-25 02:35", "2025-07-25 02:36", "2025-07-25 02:37", "2025-07-25 02:38", "2025-07-25 02:39", "2025-07-25 02:40", "2025-07-25 02:41", "2025-07-25 02:42", "2025-07-25 02:43", "2025-07-25 02:44", "2025-07-25 02:45", "2025-07-25 02:46", "2025-07-25 02:47", "2025-07-25 02:48", "2025-07-25 02:49", "2025-07-25 02:50", "2025-07-25 02:51", "2025-07-25 02:52", "2025-07-25 02:53", "2025-07-25 02:54", "2025-07-25 02:55", "2025-07-25 02:56", "2025-07-25 02:57", "2025-07-25 02:58", "2025-07-25 02:59", "2025-07-25 03:00", "2025-07-25 03:01", "2025-07-25 03:02", "2025-07-25 03:03", "2025-07-25 03:04", "2025-07-25 03:05", "2025-07-25 03:06", "2025-07-25 03:07", "2025-07-25 03:08", "2025-07-25 03:09", "2025-07-25 03:10", "2025-07-25 03:11", "2025-07-25 03:12", "2025-07-25 03:13", "2025-07-25 03:14", "2025-07-25 03:15", "2025-07-25 03:16", "2025-07-25 03:17", "2025-07-25 03:18", "2025-07-25 03:19", "2025-07-25 03:20", "2025-07-25 03:21", "2025-07-25 03:22", "2025-07-25 03:23", "2025-07-25 03:24", "2025-07-25 03:25", "2025-07-25 03:26", "2025-07-25 03:27", "2025-07-25 03:28", "2025-07-25 03:29", "2025-07-25 03:30", "2025-07-25 03:31", "2025-07-25 03:32", "2025-07-25 03:33", "2025-07-25 03:34", "2025-07-25 03:35", "2025-07-25 03:36", "2025-07-25 03:37", "2025-07-25 03:38", "2025-07-25 03:39", "2025-07-25 03:40", "2025-07-25 03:41", "2025-07-25 03:42", "2025-07-25 03:43", "2025-07-25 03:44", "2025-07-25 03:45", "2025-07-25 03:46", "2025-07-25 03:47", "2025-07-25 03:48", "2025-07-25 03:49", "2025-07-25 03:50", "2025-07-25 03:51", "2025-07-25 03:52", "2025-07-25 03:53", "2025-07-25 03:54", "2025-07-25 03:55", "2025-07-25 03:56", "2025-07-25 03:57", "2025-07-25 03:58", "2025-07-25 03:59", "2025-07-25 04:00", "2025-07-25 04:01", "2025-07-25 04:02", "2025-07-25 04:03", "2025-07-25 04:04", "2025-07-25 04:05", "2025-07-25 04:06", "2025-07-25 04:07", "2025-07-25 04:08", "2025-07-25 04:09", "2025-07-25 04:10", "2025-07-25 04:11", "2025-07-25 04:12", "2025-07-25 04:13", "2025-07-25 04:14", "2025-07-25 04:15", "2025-07-25 04:16", "2025-07-25 04:17", "2025-07-25 04:18", "2025-07-25 04:19", "2025-07-25 04:20", "2025-07-25 04:21", "2025-07-25 04:22", "2025-07-25 04:23", "2025-07-25 04:24", "2025-07-25 04:25", "2025-07-25 04:26", "2025-07-25 04:27", "2025-07-25 04:28", "2025-07-25 04:29", "2025-07-25 04:30", "2025-07-25 04:31", "2025-07-25 04:32", "2025-07-25 04:33", "2025-07-25 04:34", "2025-07-25 04:35", "2025-07-25 04:36", "2025-07-25 04:37", "2025-07-25 04:38", "2025-07-25 04:39", "2025-07-25 04:40", "2025-07-25 04:41", "2025-07-25 04:42", "2025-07-25 04:43", "2025-07-25 04:44", "2025-07-25 04:45", "2025-07-25 04:46", "2025-07-25 04:47", "2025-07-25 04:48", "2025-07-25 04:49", "2025-07-25 04:50", "2025-07-25 04:51", "2025-07-25 04:52", "2025-07-25 04:53", "2025-07-25 04:54", "2025-07-25 04:55", "2025-07-25 04:56", "2025-07-25 04:57", "2025-07-25 04:58", "2025-07-25 04:59", "2025-07-25 05:00", "2025-07-25 05:01", "2025-07-25 05:02", "2025-07-25 05:03", "2025-07-25 05:04", "2025-07-25 05:05", "2025-07-25 05:06", "2025-07-25 05:07", "2025-07-25 05:08", "2025-07-25 05:09", "2025-07-25 05:10", "2025-07-25 05:11", "2025-07-25 05:12", "2025-07-25 05:13", "2025-07-25 05:14", "2025-07-25 05:15", "2025-07-25 05:16", "2025-07-25 05:17", "2025-07-25 05:18", "2025-07-25 05:19", "2025-07-25 05:20", "2025-07-25 05:21", "2025-07-25 05:22", "2025-07-25 05:23", "2025-07-25 05:24", "2025-07-25 05:25", "2025-07-25 05:26", "2025-07-25 05:27", "2025-07-25 05:28", "2025-07-25 05:29", "2025-07-25 05:30", "2025-07-25 05:31", "2025-07-25 05:32", "2025-07-25 05:33", "2025-07-25 05:34", "2025-07-25 05:35", "2025-07-25 05:36", "2025-07-25 05:37", "2025-07-25 05:38", "2025-07-25 05:39", "2025-07-25 05:40", "2025-07-25 05:41", "2025-07-25 05:42", "2025-07-25 05:43", "2025-07-25 05:44", "2025-07-25 05:45", "2025-07-25 05:46", "2025-07-25 05:47", "2025-07-25 05:48", "2025-07-25 05:49", "2025-07-25 05:50", "2025-07-25 05:51", "2025-07-25 05:52", "2025-07-25 05:53", "2025-07-25 05:54", "2025-07-25 05:55", "2025-07-25 05:56", "2025-07-25 05:57", "2025-07-25 05:58", "2025-07-25 05:59", "2025-07-25 06:00", "2025-07-25 06:01", "2025-07-25 06:02", "2025-07-25 06:03", "2025-07-25 06:04", "2025-07-25 06:05", "2025-07-25 06:06", "2025-07-25 06:07", "2025-07-25 06:08", "2025-07-25 06:09", "2025-07-25 06:10", "2025-07-25 06:11", "2025-07-25 06:12", "2025-07-25 06:13", "2025-07-25 06:14", "2025-07-25 06:15", "2025-07-25 06:16", "2025-07-25 06:17", "2025-07-25 06:18", "2025-07-25 06:19", "2025-07-25 06:20", "2025-07-25 06:21", "2025-07-25 06:22", "2025-07-25 06:23", "2025-07-25 06:24", "2025-07-25 06:25", "2025-07-25 06:26", "2025-07-25 06:27", "2025-07-25 06:28", "2025-07-25 06:29", "2025-07-25 06:30", "2025-07-25 06:31", "2025-07-25 06:32", "2025-07-25 06:33", "2025-07-25 06:34", "2025-07-25 06:35", "2025-07-25 06:36", "2025-07-25 06:37", "2025-07-25 06:38", "2025-07-25 06:39", "2025-07-25 06:40", "2025-07-25 06:41", "2025-07-25 06:42", "2025-07-25 06:43", "2025-07-25 06:44", "2025-07-25 06:45", "2025-07-25 06:46", "2025-07-25 06:47", "2025-07-25 06:48", "2025-07-25 06:49", "2025-07-25 06:50", "2025-07-25 06:51", "2025-07-25 06:52", "2025-07-25 06:53", "2025-07-25 06:54", "2025-07-25 06:55", "2025-07-25 06:56", "2025-07-25 06:57", "2025-07-25 06:58", "2025-07-25 06:59", "2025-07-25 07:00", "2025-07-25 07:01", "2025-07-25 07:02", "2025-07-25 07:03", "2025-07-25 07:04", "2025-07-25 07:05", "2025-07-25 07:06", "2025-07-25 07:07", "2025-07-25 07:08", "2025-07-25 07:09", "2025-07-25 07:10", "2025-07-25 07:11", "2025-07-25 07:12", "2025-07-25 07:13", "2025-07-25 07:14", "2025-07-25 07:15", "2025-07-25 07:16", "2025-07-25 07:17", "2025-07-25 07:18", "2025-07-25 07:19", "2025-07-25 07:20", "2025-07-25 07:21", "2025-07-25 07:22", "2025-07-25 07:23", "2025-07-25 07:24", "2025-07-25 07:25", "2025-07-25 07:26", "2025-07-25 07:27", "2025-07-25 07:28", "2025-07-25 07:29", "2025-07-25 07:30", "2025-07-25 07:31", "2025-07-25 07:32", "2025-07-25 07:33", "2025-07-25 07:34", "2025-07-25 07:35", "2025-07-25 07:36", "2025-07-25 07:37", "2025-07-25 07:38", "2025-07-25 07:39", "2025-07-25 07:40", "2025-07-25 07:41", "2025-07-25 07:42", "2025-07-25 07:43", "2025-07-25 07:44", "2025-07-25 07:45", "2025-07-25 07:46", "2025-07-25 07:47", "2025-07-25 07:48", "2025-07-25 07:49", "2025-07-25 07:50", "2025-07-25 07:51", "2025-07-25 07:52", "2025-07-25 07:53", "2025-07-25 07:54", "2025-07-25 07:55", "2025-07-25 07:56", "2025-07-25 07:57", "2025-07-25 07:58", "2025-07-25 07:59", "2025-07-25 08:00", "2025-07-25 08:01", "2025-07-25 08:02", "2025-07-25 08:03", "2025-07-25 08:04", "2025-07-25 08:05", "2025-07-25 08:06", "2025-07-25 08:07", "2025-07-25 08:08", "2025-07-25 08:09", "2025-07-25 08:10", "2025-07-25 08:11", "2025-07-25 08:12", "2025-07-25 08:13", "2025-07-25 08:14", "2025-07-25 08:15", "2025-07-25 08:16", "2025-07-25 08:17", "2025-07-25 08:18", "2025-07-25 08:19", "2025-07-25 08:20", "2025-07-25 08:21", "2025-07-25 08:22", "2025-07-25 08:23", "2025-07-25 08:24", "2025-07-25 08:25", "2025-07-25 08:26", "2025-07-25 08:27", "2025-07-25 08:28", "2025-07-25 08:29", "2025-07-25 08:30", "2025-07-25 08:31", "2025-07-25 08:32", "2025-07-25 08:33", "2025-07-25 08:34", "2025-07-25 08:35", "2025-07-25 08:36", "2025-07-25 08:37", "2025-07-25 08:38", "2025-07-25 08:39", "2025-07-25 08:40", "2025-07-25 08:41", "2025-07-25 08:42", "2025-07-25 08:43", "2025-07-25 08:44", "2025-07-25 08:45", "2025-07-25 08:46", "2025-07-25 08:47", "2025-07-25 08:48", "2025-07-25 08:49", "2025-07-25 08:50", "2025-07-25 08:51", "2025-07-25 08:52", "2025-07-25 08:53", "2025-07-25 08:54", "2025-07-25 08:55", "2025-07-25 08:56", "2025-07-25 08:57", "2025-07-25 08:58", "2025-07-25 08:59", "2025-07-25 09:00", "2025-07-25 09:01", "2025-07-25 09:02", "2025-07-25 09:03", "2025-07-25 09:04", "2025-07-25 09:05", "2025-07-25 09:06", "2025-07-25 09:07", "2025-07-25 09:08", "2025-07-25 09:09", "2025-07-25 09:10", "2025-07-25 09:11", "2025-07-25 09:12", "2025-07-25 09:13", "2025-07-25 09:14", "2025-07-25 09:15", "2025-07-25 09:16", "2025-07-25 09:17", "2025-07-25 09:18", "2025-07-25 09:19", "2025-07-25 09:20", "2025-07-25 09:21", "2025-07-25 09:22", "2025-07-25 09:23", "2025-07-25 09:24", "2025-07-25 09:25", "2025-07-25 09:26", "2025-07-25 09:27", "2025-07-25 09:28", "2025-07-25 09:29", "2025-07-25 09:30", "2025-07-25 09:31", "2025-07-25 09:32", "2025-07-25 09:33", "2025-07-25 09:34", "2025-07-25 09:35", "2025-07-25 09:36", "2025-07-25 09:37", "2025-07-25 09:38", "2025-07-25 09:39", "2025-07-25 09:40", "2025-07-25 09:41", "2025-07-25 09:42", "2025-07-25 09:43", "2025-07-25 09:44", "2025-07-25 09:45", "2025-07-25 09:46", "2025-07-25 09:47", "2025-07-25 09:48", "2025-07-25 09:49", "2025-07-25 09:50", "2025-07-25 09:51", "2025-07-25 09:52", "2025-07-25 09:53", "2025-07-25 09:54", "2025-07-25 09:55", "2025-07-25 09:56", "2025-07-25 09:57", "2025-07-25 09:58", "2025-07-25 09:59", "2025-07-25 10:00", "2025-07-25 10:01", "2025-07-25 10:02", "2025-07-25 10:03", "2025-07-25 10:04", "2025-07-25 10:05", "2025-07-25 10:06", "2025-07-25 10:07", "2025-07-25 10:08", "2025-07-25 10:09", "2025-07-25 10:10", "2025-07-25 10:11", "2025-07-25 10:12", "2025-07-25 10:13", "2025-07-25 10:14", "2025-07-25 10:15", "2025-07-25 10:16", "2025-07-25 10:17", "2025-07-25 10:18", "2025-07-25 10:19", "2025-07-25 10:20", "2025-07-25 10:21", "2025-07-25 10:22", "2025-07-25 10:23", "2025-07-25 10:24", "2025-07-25 10:25", "2025-07-25 10:26", "2025-07-25 10:27", "2025-07-25 10:28", "2025-07-25 10:29", "2025-07-25 10:30", "2025-07-25 10:31", "2025-07-25 10:32", "2025-07-25 10:33", "2025-07-25 10:34", "2025-07-25 10:35", "2025-07-25 10:36", "2025-07-25 10:37", "2025-07-25 10:38", "2025-07-25 10:39", "2025-07-25 10:40", "2025-07-25 10:41", "2025-07-25 10:42", "2025-07-25 10:43", "2025-07-25 10:44", "2025-07-25 10:45", "2025-07-25 10:46", "2025-07-25 10:47", "2025-07-25 10:48", "2025-07-25 10:49", "2025-07-25 10:50", "2025-07-25 10:51", "2025-07-25 10:52", "2025-07-25 10:53", "2025-07-25 10:54", "2025-07-25 10:55", "2025-07-25 10:56", "2025-07-25 10:57", "2025-07-25 10:58", "2025-07-25 10:59", "2025-07-25 11:00", "2025-07-25 11:01", "2025-07-25 11:02", "2025-07-25 11:03", "2025-07-25 11:04", "2025-07-25 11:05", "2025-07-25 11:06", "2025-07-25 11:07", "2025-07-25 11:08", "2025-07-25 11:09", "2025-07-25 11:10", "2025-07-25 11:11", "2025-07-25 11:12", "2025-07-25 11:13", "2025-07-25 11:14", "2025-07-25 11:15", "2025-07-25 11:16", "2025-07-25 11:17", "2025-07-25 11:18", "2025-07-25 11:19", "2025-07-25 11:20", "2025-07-25 11:21", "2025-07-25 11:22", "2025-07-25 11:23", "2025-07-25 11:24", "2025-07-25 11:25", "2025-07-25 11:26", "2025-07-25 11:27", "2025-07-25 11:28", "2025-07-25 11:29", "2025-07-25 11:30", "2025-07-25 11:31", "2025-07-25 11:32", "2025-07-25 11:33", "2025-07-25 11:34", "2025-07-25 11:35", "2025-07-25 11:36", "2025-07-25 11:37", "2025-07-25 11:38", "2025-07-25 11:39", "2025-07-25 11:40", "2025-07-25 11:41", "2025-07-25 11:42", "2025-07-25 11:43", "2025-07-25 11:44", "2025-07-25 11:45", "2025-07-25 11:46", "2025-07-25 11:47", "2025-07-25 11:48", "2025-07-25 11:49", "2025-07-25 11:50", "2025-07-25 11:51", "2025-07-25 11:52", "2025-07-25 11:53", "2025-07-25 11:54", "2025-07-25 11:55", "2025-07-25 11:56", "2025-07-25 11:57", "2025-07-25 11:58", "2025-07-25 11:59", "2025-07-25 12:00", "2025-07-25 12:01", "2025-07-25 12:02", "2025-07-25 12:03", "2025-07-25 12:04", "2025-07-25 12:05", "2025-07-25 12:06", "2025-07-25 12:07", "2025-07-25 12:08", "2025-07-25 12:09", "2025-07-25 12:10", "2025-07-25 12:11", "2025-07-25 12:12", "2025-07-25 12:13", "2025-07-25 12:14", "2025-07-25 12:15", "2025-07-25 12:16", "2025-07-25 12:17", "2025-07-25 12:18", "2025-07-25 12:19", "2025-07-25 12:20", "2025-07-25 12:21", "2025-07-25 12:22", "2025-07-25 12:23", "2025-07-25 12:24", "2025-07-25 12:25", "2025-07-25 12:26", "2025-07-25 12:27", "2025-07-25 12:28", "2025-07-25 12:29", "2025-07-25 12:30", "2025-07-25 12:31", "2025-07-25 12:32", "2025-07-25 12:33", "2025-07-25 12:34", "2025-07-25 12:35", "2025-07-25 12:36", "2025-07-25 12:37", "2025-07-25 12:38", "2025-07-25 12:39", "2025-07-25 12:40", "2025-07-25 12:41", "2025-07-25 12:42", "2025-07-25 12:43", "2025-07-25 12:44", "2025-07-25 12:45", "2025-07-25 12:46", "2025-07-25 12:47", "2025-07-25 12:48", "2025-07-25 12:49", "2025-07-25 12:50", "2025-07-25 12:51", "2025-07-25 12:52", "2025-07-25 12:53", "2025-07-25 12:54", "2025-07-25 12:55", "2025-07-25 12:56", "2025-07-25 12:57", "2025-07-25 12:58", "2025-07-25 12:59", "2025-07-25 13:00", "2025-07-25 13:01", "2025-07-25 13:02", "2025-07-25 13:03", "2025-07-25 13:04", "2025-07-25 13:05", "2025-07-25 13:06", "2025-07-25 13:07", "2025-07-25 13:08", "2025-07-25 13:09", "2025-07-25 13:10", "2025-07-25 13:11", "2025-07-25 13:12", "2025-07-25 13:13", "2025-07-25 13:14", "2025-07-25 13:15", "2025-07-25 13:16", "2025-07-25 13:17", "2025-07-25 13:18", "2025-07-25 13:19", "2025-07-25 13:20", "2025-07-25 13:21", "2025-07-25 13:22", "2025-07-25 13:23", "2025-07-25 13:24", "2025-07-25 13:25", "2025-07-25 13:26", "2025-07-25 13:27", "2025-07-25 13:28", "2025-07-25 13:29", "2025-07-25 13:30", "2025-07-25 13:31", "2025-07-25 13:32", "2025-07-25 13:33", "2025-07-25 13:34", "2025-07-25 13:35", "2025-07-25 13:36", "2025-07-25 13:37", "2025-07-25 13:38", "2025-07-25 13:39", "2025-07-25 13:40", "2025-07-25 13:41", "2025-07-25 13:42", "2025-07-25 13:43", "2025-07-25 13:44", "2025-07-25 13:45", "2025-07-25 13:46", "2025-07-25 13:47", "2025-07-25 13:48", "2025-07-25 13:49", "2025-07-25 13:50", "2025-07-25 13:51", "2025-07-25 13:52", "2025-07-25 13:53", "2025-07-25 13:54", "2025-07-25 13:55", "2025-07-25 13:56", "2025-07-25 13:57", "2025-07-25 13:58", "2025-07-25 13:59", "2025-07-25 14:00", "2025-07-25 14:01", "2025-07-25 14:02", "2025-07-25 14:03", "2025-07-25 14:04", "2025-07-25 14:05", "2025-07-25 14:06", "2025-07-25 14:07", "2025-07-25 14:08", "2025-07-25 14:09", "2025-07-25 14:10", "2025-07-25 14:11", "2025-07-25 14:12", "2025-07-25 14:13", "2025-07-25 14:14", "2025-07-25 14:15", "2025-07-25 14:16", "2025-07-25 14:17", "2025-07-25 14:18", "2025-07-25 14:19", "2025-07-25 14:20", "2025-07-25 14:21", "2025-07-25 14:22", "2025-07-25 14:23", "2025-07-25 14:24", "2025-07-25 14:25", "2025-07-25 14:26", "2025-07-25 14:27", "2025-07-25 14:28", "2025-07-25 14:29", "2025-07-25 14:30", "2025-07-25 14:31", "2025-07-25 14:32", "2025-07-25 14:33", "2025-07-25 14:34", "2025-07-25 14:35", "2025-07-25 14:36", "2025-07-25 14:37", "2025-07-25 14:38", "2025-07-25 14:39", "2025-07-25 14:40", "2025-07-25 14:41", "2025-07-25 14:42", "2025-07-25 14:43", "2025-07-25 14:44", "2025-07-25 14:45", "2025-07-25 14:46", "2025-07-25 14:47", "2025-07-25 14:48", "2025-07-25 14:49", "2025-07-25 14:50", "2025-07-25 14:51", "2025-07-25 14:52", "2025-07-25 14:53", "2025-07-25 14:54", "2025-07-25 14:55", "2025-07-25 14:56", "2025-07-25 14:57", "2025-07-25 14:58", "2025-07-25 14:59", "2025-07-25 15:00", "2025-07-25 15:01", "2025-07-25 15:02", "2025-07-25 15:03", "2025-07-25 15:04", "2025-07-25 15:05", "2025-07-25 15:06", "2025-07-25 15:07", "2025-07-25 15:08", "2025-07-25 15:09", "2025-07-25 15:10", "2025-07-25 15:11", "2025-07-25 15:12", "2025-07-25 15:13", "2025-07-25 15:14", "2025-07-25 15:15", "2025-07-25 15:16", "2025-07-25 15:17", "2025-07-25 15:18", "2025-07-25 15:19", "2025-07-25 15:20", "2025-07-25 15:21", "2025-07-25 15:22", "2025-07-25 15:23", "2025-07-25 15:24", "2025-07-25 15:25", "2025-07-25 15:26", "2025-07-25 15:27", "2025-07-25 15:28", "2025-07-25 15:29", "2025-07-25 15:30", "2025-07-25 15:31", "2025-07-25 15:32", "2025-07-25 15:33", "2025-07-25 15:34", "2025-07-25 15:35", "2025-07-25 15:36", "2025-07-25 15:37", "2025-07-25 15:38", "2025-07-25 15:39", "2025-07-25 15:40", "2025-07-25 15:41", "2025-07-25 15:42", "2025-07-25 15:43", "2025-07-25 15:44", "2025-07-25 15:45", "2025-07-25 15:46", "2025-07-25 15:47", "2025-07-25 15:48", "2025-07-25 15:49", "2025-07-25 15:50", "2025-07-25 15:51", "2025-07-25 15:52", "2025-07-25 15:53", "2025-07-25 15:54", "2025-07-25 15:55", "2025-07-25 15:56", "2025-07-25 15:57", "2025-07-25 15:58", "2025-07-25 15:59", "2025-07-25 16:00", "2025-07-25 16:01", "2025-07-25 16:02", "2025-07-25 16:03", "2025-07-25 16:04", "2025-07-25 16:05", "2025-07-25 16:06", "2025-07-25 16:07", "2025-07-25 16:08", "2025-07-25 16:09", "2025-07-25 16:10", "2025-07-25 16:11", "2025-07-25 16:12", "2025-07-25 16:13", "2025-07-25 16:14", "2025-07-25 16:15", "2025-07-25 16:16", "2025-07-25 16:17", "2025-07-25 16:18", "2025-07-25 16:19", "2025-07-25 16:20", "2025-07-25 16:21", "2025-07-25 16:22", "2025-07-25 16:23", "2025-07-25 16:24", "2025-07-25 16:25", "2025-07-25 16:26", "2025-07-25 16:27", "2025-07-25 16:28", "2025-07-25 16:29", "2025-07-25 16:30", "2025-07-25 16:31", "2025-07-25 16:32", "2025-07-25 16:33", "2025-07-25 16:34", "2025-07-25 16:35", "2025-07-25 16:36", "2025-07-25 16:37", "2025-07-25 16:38", "2025-07-25 16:39", "2025-07-25 16:40", "2025-07-25 16:41", "2025-07-25 16:42", "2025-07-25 16:43", "2025-07-25 16:44", "2025-07-25 16:45", "2025-07-25 16:46", "2025-07-25 16:47", "2025-07-25 16:48", "2025-07-25 16:49", "2025-07-25 16:50", "2025-07-25 16:51", "2025-07-25 16:52", "2025-07-25 16:53", "2025-07-25 16:54", "2025-07-25 16:55", "2025-07-25 16:56", "2025-07-25 16:57", "2025-07-25 16:58", "2025-07-25 16:59", "2025-07-25 17:00", "2025-07-25 17:01", "2025-07-25 17:02", "2025-07-25 17:03", "2025-07-25 17:04", "2025-07-25 17:05", "2025-07-25 17:06", "2025-07-25 17:07", "2025-07-25 17:08", "2025-07-25 17:09", "2025-07-25 17:10", "2025-07-25 17:11", "2025-07-25 17:12", "2025-07-25 17:13", "2025-07-25 17:14", "2025-07-25 17:15", "2025-07-25 17:16", "2025-07-25 17:17", "2025-07-25 17:18", "2025-07-25 17:19", "2025-07-25 17:20", "2025-07-25 17:21", "2025-07-25 17:22", "2025-07-25 17:23", "2025-07-25 17:24", "2025-07-25 17:25", "2025-07-25 17:26", "2025-07-25 17:27", "2025-07-25 17:28", "2025-07-25 17:29", "2025-07-25 17:30", "2025-07-25 17:31", "2025-07-25 17:32", "2025-07-25 17:33", "2025-07-25 17:34", "2025-07-25 17:35", "2025-07-25 17:36", "2025-07-25 17:37", "2025-07-25 17:38", "2025-07-25 17:39", "2025-07-25 17:40", "2025-07-25 17:41", "2025-07-25 17:42", "2025-07-25 17:43", "2025-07-25 17:44", "2025-07-25 17:45", "2025-07-25 17:46", "2025-07-25 17:47", "2025-07-25 17:48", "2025-07-25 17:49", "2025-07-25 17:50", "2025-07-25 17:51", "2025-07-25 17:52", "2025-07-25 17:53", "2025-07-25 17:54", "2025-07-25 17:55", "2025-07-25 17:56", "2025-07-25 17:57", "2025-07-25 17:58", "2025-07-25 17:59", "2025-07-25 18:00", "2025-07-25 18:01", "2025-07-25 18:02", "2025-07-25 18:03", "2025-07-25 18:04", "2025-07-25 18:05", "2025-07-25 18:06", "2025-07-25 18:07", "2025-07-25 18:08", "2025-07-25 18:09", "2025-07-25 18:10", "2025-07-25 18:11", "2025-07-25 18:12", "2025-07-25 18:13", "2025-07-25 18:14", "2025-07-25 18:15", "2025-07-25 18:16", "2025-07-25 18:17", "2025-07-25 18:18", "2025-07-25 18:19", "2025-07-25 18:20", "2025-07-25 18:21", "2025-07-25 18:22", "2025-07-25 18:23", "2025-07-25 18:24", "2025-07-25 18:25", "2025-07-25 18:26", "2025-07-25 18:27", "2025-07-25 18:28", "2025-07-25 18:29", "2025-07-25 18:30", "2025-07-25 18:31", "2025-07-25 18:32", "2025-07-25 18:33", "2025-07-25 18:34", "2025-07-25 18:35", "2025-07-25 18:36", "2025-07-25 18:37", "2025-07-25 18:38", "2025-07-25 18:39", "2025-07-25 18:40", "2025-07-25 18:41", "2025-07-25 18:42", "2025-07-25 18:43", "2025-07-25 18:44", "2025-07-25 18:45", "2025-07-25 18:46", "2025-07-25 18:47", "2025-07-25 18:48", "2025-07-25 18:49", "2025-07-25 18:50", "2025-07-25 18:51", "2025-07-25 18:52", "2025-07-25 18:53", "2025-07-25 18:54", "2025-07-25 18:55", "2025-07-25 18:56", "2025-07-25 18:57", "2025-07-25 18:58", "2025-07-25 18:59", "2025-07-25 19:00", "2025-07-25 19:01", "2025-07-25 19:02", "2025-07-25 19:03", "2025-07-25 19:04", "2025-07-25 19:05", "2025-07-25 19:06", "2025-07-25 19:07", "2025-07-25 19:08", "2025-07-25 19:09", "2025-07-25 19:10", "2025-07-25 19:11", "2025-07-25 19:12", "2025-07-25 19:13", "2025-07-25 19:14", "2025-07-25 19:15", "2025-07-25 19:16", "2025-07-25 19:17", "2025-07-25 19:18", "2025-07-25 19:19", "2025-07-25 19:20", "2025-07-25 19:21", "2025-07-25 19:22", "2025-07-25 19:23", "2025-07-25 19:24", "2025-07-25 19:25", "2025-07-25 19:26", "2025-07-25 19:27", "2025-07-25 19:28", "2025-07-25 19:29", "2025-07-25 19:30", "2025-07-25 19:31", "2025-07-25 19:32", "2025-07-25 19:33", "2025-07-25 19:34", "2025-07-25 19:35", "2025-07-25 19:36", "2025-07-25 19:37", "2025-07-25 19:38", "2025-07-25 19:39", "2025-07-25 19:40", "2025-07-25 19:41", "2025-07-25 19:42", "2025-07-25 19:43", "2025-07-25 19:44", "2025-07-25 19:45", "2025-07-25 19:46", "2025-07-25 19:47", "2025-07-25 19:48", "2025-07-25 19:49", "2025-07-25 19:50", "2025-07-25 19:51", "2025-07-25 19:52", "2025-07-25 19:53", "2025-07-25 19:54", "2025-07-25 19:55", "2025-07-25 19:56", "2025-07-25 19:57", "2025-07-25 19:58", "2025-07-25 19:59", "2025-07-25 20:00", "2025-07-25 20:01", "2025-07-25 20:02", "2025-07-25 20:03", "2025-07-25 20:04", "2025-07-25 20:05", "2025-07-25 20:06", "2025-07-25 20:07", "2025-07-25 20:08", "2025-07-25 20:09", "2025-07-25 20:10", "2025-07-25 20:11", "2025-07-25 20:12", "2025-07-25 20:13", "2025-07-25 20:14", "2025-07-25 20:15", "2025-07-25 20:16", "2025-07-25 20:17", "2025-07-25 20:18", "2025-07-25 20:19", "2025-07-25 20:20", "2025-07-25 20:21", "2025-07-25 20:22", "2025-07-25 20:23", "2025-07-25 20:24", "2025-07-25 20:25", "2025-07-25 20:26", "2025-07-25 20:27", "2025-07-25 20:28", "2025-07-25 20:29", "2025-07-25 20:30", "2025-07-25 20:31", "2025-07-25 20:32", "2025-07-25 20:33", "2025-07-25 20:34", "2025-07-25 20:35", "2025-07-25 20:36", "2025-07-25 20:37", "2025-07-25 20:38", "2025-07-25 20:39", "2025-07-25 20:40", "2025-07-25 20:41", "2025-07-25 20:42", "2025-07-25 20:43", "2025-07-25 20:44", "2025-07-25 20:45", "2025-07-25 20:46", "2025-07-25 20:47", "2025-07-25 20:48", "2025-07-25 20:49", "2025-07-25 20:50", "2025-07-25 20:51", "2025-07-25 20:52", "2025-07-25 20:53", "2025-07-25 20:54", "2025-07-25 20:55", "2025-07-25 20:56", "2025-07-25 20:57", "2025-07-25 20:58", "2025-07-25 20:59", "2025-07-25 21:00", "2025-07-25 21:01", "2025-07-25 21:02", "2025-07-25 21:03", "2025-07-25 21:04", "2025-07-25 21:05", "2025-07-25 21:06", "2025-07-25 21:07", "2025-07-25 21:08", "2025-07-25 21:09", "2025-07-25 21:10", "2025-07-25 21:11", "2025-07-25 21:12", "2025-07-25 21:13", "2025-07-25 21:14", "2025-07-25 21:15", "2025-07-25 21:16", "2025-07-25 21:17", "2025-07-25 21:18", "2025-07-25 21:19", "2025-07-25 21:20", "2025-07-25 21:21", "2025-07-25 21:22", "2025-07-25 21:23", "2025-07-25 21:24", "2025-07-25 21:25", "2025-07-25 21:26", "2025-07-25 21:27", "2025-07-25 21:28", "2025-07-25 21:29", "2025-07-25 21:30", "2025-07-25 21:31", "2025-07-25 21:32", "2025-07-25 21:33", "2025-07-25 21:34", "2025-07-25 21:35", "2025-07-25 21:36", "2025-07-25 21:37", "2025-07-25 21:38", "2025-07-25 21:39", "2025-07-25 21:40", "2025-07-25 21:41", "2025-07-25 21:42", "2025-07-25 21:43", "2025-07-25 21:44", "2025-07-25 21:45", "2025-07-25 21:46", "2025-07-25 21:47", "2025-07-25 21:48", "2025-07-25 21:49", "2025-07-25 21:50", "2025-07-25 21:51", "2025-07-25 21:52", "2025-07-25 21:53", "2025-07-25 21:54", "2025-07-25 21:55", "2025-07-25 21:56", "2025-07-25 21:57", "2025-07-25 21:58", "2025-07-25 21:59", "2025-07-25 22:00", "2025-07-25 22:01", "2025-07-25 22:02", "2025-07-25 22:03", "2025-07-25 22:04", "2025-07-25 22:05", "2025-07-25 22:06", "2025-07-25 22:07", "2025-07-25 22:08", "2025-07-25 22:09", "2025-07-25 22:10", "2025-07-25 22:11", "2025-07-25 22:12", "2025-07-25 22:13", "2025-07-25 22:14", "2025-07-25 22:15", "2025-07-25 22:16", "2025-07-25 22:17", "2025-07-25 22:18", "2025-07-25 22:19", "2025-07-25 22:20", "2025-07-25 22:21", "2025-07-25 22:22", "2025-07-25 22:23", "2025-07-25 22:24", "2025-07-25 22:25", "2025-07-25 22:26", "2025-07-25 22:27", "2025-07-25 22:28", "2025-07-25 22:29", "2025-07-25 22:30", "2025-07-25 22:31", "2025-07-25 22:32", "2025-07-25 22:33", "2025-07-25 22:34", "2025-07-25 22:35", "2025-07-25 22:36", "2025-07-25 22:37", "2025-07-25 22:38", "2025-07-25 22:39", "2025-07-25 22:40", "2025-07-25 22:41", "2025-07-25 22:42", "2025-07-25 22:43", "2025-07-25 22:44", "2025-07-25 22:45", "2025-07-25 22:46", "2025-07-25 22:47", "2025-07-25 22:48", "2025-07-25 22:49", "2025-07-25 22:50", "2025-07-25 22:51", "2025-07-25 22:52", "2025-07-25 22:53", "2025-07-25 22:54", "2025-07-25 22:55", "2025-07-25 22:56", "2025-07-25 22:57", "2025-07-25 22:58", "2025-07-25 22:59", "2025-07-25 23:00", "2025-07-25 23:01", "2025-07-25 23:02", "2025-07-25 23:03", "2025-07-25 23:04", "2025-07-25 23:05", "2025-07-25 23:06", "2025-07-25 23:07", "2025-07-25 23:08", "2025-07-25 23:09", "2025-07-25 23:10", "2025-07-25 23:11", "2025-07-25 23:12", "2025-07-25 23:13", "2025-07-25 23:14", "2025-07-25 23:15", "2025-07-25 23:16", "2025-07-25 23:17", "2025-07-25 23:18", "2025-07-25 23:19", "2025-07-25 23:20", "2025-07-25 23:21", "2025-07-25 23:22", "2025-07-25 23:23", "2025-07-25 23:24", "2025-07-25 23:25", "2025-07-25 23:26", "2025-07-25 23:27", "2025-07-25 23:28", "2025-07-25 23:29", "2025-07-25 23:30", "2025-07-25 23:31", "2025-07-25 23:32", "2025-07-25 23:33", "2025-07-25 23:34", "2025-07-25 23:35", "2025-07-25 23:36", "2025-07-25 23:37", "2025-07-25 23:38", "2025-07-25 23:39", "2025-07-25 23:40", "2025-07-25 23:41", "2025-07-25 23:42", "2025-07-25 23:43", "2025-07-25 23:44", "2025-07-25 23:45", "2025-07-25 23:46", "2025-07-25 23:47", "2025-07-25 23:48", "2025-07-25 23:49", "2025-07-25 23:50", "2025-07-25 23:51", "2025-07-25 23:52", "2025-07-25 23:53", "2025-07-25 23:54", "2025-07-25 23:55", "2025-07-25 23:56", "2025-07-25 23:57", "2025-07-25 23:58", "2025-07-25 23:59", "2025-07-26 00:00", "2025-07-26 00:01", "2025-07-26 00:02", "2025-07-26 00:03", "2025-07-26 00:04", "2025-07-26 00:05", "2025-07-26 00:06", "2025-07-26 00:07", "2025-07-26 00:08", "2025-07-26 00:09", "2025-07-26 00:10", "2025-07-26 00:11", "2025-07-26 00:12", "2025-07-26 00:13", "2025-07-26 00:14", "2025-07-26 00:15", "2025-07-26 00:16", "2025-07-26 00:17", "2025-07-26 00:18", "2025-07-26 00:19", "2025-07-26 00:20", "2025-07-26 00:21", "2025-07-26 00:22", "2025-07-26 00:23", "2025-07-26 00:24", "2025-07-26 00:25", "2025-07-26 00:26", "2025-07-26 00:27", "2025-07-26 00:28", "2025-07-26 00:29", "2025-07-26 00:30", "2025-07-26 00:31", "2025-07-26 00:32", "2025-07-26 00:33", "2025-07-26 00:34", "2025-07-26 00:35", "2025-07-26 00:36", "2025-07-26 00:37", "2025-07-26 00:38", "2025-07-26 00:39", "2025-07-26 00:40", "2025-07-26 00:41", "2025-07-26 00:42", "2025-07-26 00:43", "2025-07-26 00:44", "2025-07-26 00:45", "2025-07-26 00:46", "2025-07-26 00:47", "2025-07-26 00:48", "2025-07-26 00:49", "2025-07-26 00:50", "2025-07-26 00:51", "2025-07-26 00:52", "2025-07-26 00:53", "2025-07-26 00:54", "2025-07-26 00:55", "2025-07-26 00:56", "2025-07-26 00:57", "2025-07-26 00:58", "2025-07-26 00:59", "2025-07-26 01:00", "2025-07-26 01:01", "2025-07-26 01:02", "2025-07-26 01:03", "2025-07-26 01:04", "2025-07-26 01:05", "2025-07-26 01:06", "2025-07-26 01:07", "2025-07-26 01:08", "2025-07-26 01:09", "2025-07-26 01:10", "2025-07-26 01:11", "2025-07-26 01:12", "2025-07-26 01:13", "2025-07-26 01:14", "2025-07-26 01:15", "2025-07-26 01:16", "2025-07-26 01:17", "2025-07-26 01:18", "2025-07-26 01:19", "2025-07-26 01:20", "2025-07-26 01:21", "2025-07-26 01:22", "2025-07-26 01:23", "2025-07-26 01:24", "2025-07-26 01:25", "2025-07-26 01:26", "2025-07-26 01:27", "2025-07-26 01:28", "2025-07-26 01:29", "2025-07-26 01:30", "2025-07-26 01:31", "2025-07-26 01:32", "2025-07-26 01:33", "2025-07-26 01:34", "2025-07-26 01:35", "2025-07-26 01:36", "2025-07-26 01:37", "2025-07-26 01:38", "2025-07-26 01:39", "2025-07-26 01:40", "2025-07-26 01:41", "2025-07-26 01:42", "2025-07-26 01:43", "2025-07-26 01:44", "2025-07-26 01:45", "2025-07-26 01:46", "2025-07-26 01:47", "2025-07-26 01:48", "2025-07-26 01:49", "2025-07-26 01:50", "2025-07-26 01:51", "2025-07-26 01:52", "2025-07-26 01:53", "2025-07-26 01:54", "2025-07-26 01:55", "2025-07-26 01:56", "2025-07-26 01:57", "2025-07-26 01:58", "2025-07-26 01:59", "2025-07-26 02:00", "2025-07-26 02:01", "2025-07-26 02:02", "2025-07-26 02:03", "2025-07-26 02:04", "2025-07-26 02:05", "2025-07-26 02:06", "2025-07-26 02:07", "2025-07-26 02:08", "2025-07-26 02:09", "2025-07-26 02:10", "2025-07-26 02:11", "2025-07-26 02:12", "2025-07-26 02:13", "2025-07-26 02:14", "2025-07-26 02:15", "2025-07-26 02:16", "2025-07-26 02:17", "2025-07-26 02:18", "2025-07-26 02:19", "2025-07-26 02:20", "2025-07-26 02:21", "2025-07-26 02:22", "2025-07-26 02:23", "2025-07-26 02:24", "2025-07-26 02:25", "2025-07-26 02:26", "2025-07-26 02:27", "2025-07-26 02:28", "2025-07-26 02:29", "2025-07-26 02:30", "2025-07-26 02:31", "2025-07-26 02:32", "2025-07-26 02:33", "2025-07-26 02:34", "2025-07-26 02:35", "2025-07-26 02:36", "2025-07-26 02:37", "2025-07-26 02:38", "2025-07-26 02:39", "2025-07-26 02:40", "2025-07-26 02:41", "2025-07-26 02:42", "2025-07-26 02:43", "2025-07-26 02:44", "2025-07-26 02:45", "2025-07-26 02:46", "2025-07-26 02:47", "2025-07-26 02:48", "2025-07-26 02:49", "2025-07-26 02:50", "2025-07-26 02:51", "2025-07-26 02:52", "2025-07-26 02:53", "2025-07-26 02:54", "2025-07-26 02:55", "2025-07-26 02:56", "2025-07-26 02:57", "2025-07-26 02:58", "2025-07-26 02:59", "2025-07-26 03:00", "2025-07-26 03:01", "2025-07-26 03:02", "2025-07-26 03:03", "2025-07-26 03:04", "2025-07-26 03:05", "2025-07-26 03:06", "2025-07-26 03:07", "2025-07-26 03:08", "2025-07-26 03:09", "2025-07-26 03:10", "2025-07-26 03:11", "2025-07-26 03:12", "2025-07-26 03:13", "2025-07-26 03:14", "2025-07-26 03:15", "2025-07-26 03:16", "2025-07-26 03:17", "2025-07-26 03:18", "2025-07-26 03:19", "2025-07-26 03:20", "2025-07-26 03:21", "2025-07-26 03:22", "2025-07-26 03:23", "2025-07-26 03:24", "2025-07-26 03:25", "2025-07-26 03:26", "2025-07-26 03:27", "2025-07-26 03:28", "2025-07-26 03:29", "2025-07-26 03:30", "2025-07-26 03:31", "2025-07-26 03:32", "2025-07-26 03:33", "2025-07-26 03:34", "2025-07-26 03:35", "2025-07-26 03:36", "2025-07-26 03:37", "2025-07-26 03:38", "2025-07-26 03:39", "2025-07-26 03:40", "2025-07-26 03:41", "2025-07-26 03:42", "2025-07-26 03:43", "2025-07-26 03:44", "2025-07-26 03:45", "2025-07-26 03:46", "2025-07-26 03:47", "2025-07-26 03:48", "2025-07-26 03:49", "2025-07-26 03:50", "2025-07-26 03:51", "2025-07-26 03:52", "2025-07-26 03:53", "2025-07-26 03:54", "2025-07-26 03:55", "2025-07-26 03:56", "2025-07-26 03:57", "2025-07-26 03:58", "2025-07-26 03:59", "2025-07-26 04:00", "2025-07-26 04:01", "2025-07-26 04:02", "2025-07-26 04:03", "2025-07-26 04:04", "2025-07-26 04:05", "2025-07-26 04:06", "2025-07-26 04:07", "2025-07-26 04:08", "2025-07-26 04:09", "2025-07-26 04:10", "2025-07-26 04:11", "2025-07-26 04:12", "2025-07-26 04:13", "2025-07-26 04:14", "2025-07-26 04:15", "2025-07-26 04:16", "2025-07-26 04:17", "2025-07-26 04:18", "2025-07-26 04:19", "2025-07-26 04:20", "2025-07-26 04:21", "2025-07-26 04:22", "2025-07-26 04:23", "2025-07-26 04:24", "2025-07-26 04:25", "2025-07-26 04:26", "2025-07-26 04:27", "2025-07-26 04:28", "2025-07-26 04:29", "2025-07-26 04:30", "2025-07-26 04:31", "2025-07-26 04:32", "2025-07-26 04:33", "2025-07-26 04:34", "2025-07-26 04:35", "2025-07-26 04:36", "2025-07-26 04:37", "2025-07-26 04:38", "2025-07-26 04:39", "2025-07-26 04:40", "2025-07-26 04:41", "2025-07-26 04:42", "2025-07-26 04:43", "2025-07-26 04:44", "2025-07-26 04:45", "2025-07-26 04:46", "2025-07-26 04:47", "2025-07-26 04:48", "2025-07-26 04:49", "2025-07-26 04:50", "2025-07-26 04:51", "2025-07-26 04:52", "2025-07-26 04:53", "2025-07-26 04:54", "2025-07-26 04:55", "2025-07-26 04:56", "2025-07-26 04:57", "2025-07-26 04:58", "2025-07-26 04:59", "2025-07-26 05:00", "2025-07-26 05:01", "2025-07-26 05:02", "2025-07-26 05:03", "2025-07-26 05:04", "2025-07-26 05:05", "2025-07-26 05:06", "2025-07-26 05:07", "2025-07-26 05:08", "2025-07-26 05:09", "2025-07-26 05:10", "2025-07-26 05:11", "2025-07-26 05:12", "2025-07-26 05:13", "2025-07-26 05:14", "2025-07-26 05:15", "2025-07-26 05:16", "2025-07-26 05:17", "2025-07-26 05:18", "2025-07-26 05:19", "2025-07-26 05:20", "2025-07-26 05:21", "2025-07-26 05:22", "2025-07-26 05:23", "2025-07-26 05:24", "2025-07-26 05:25", "2025-07-26 05:26", "2025-07-26 05:27", "2025-07-26 05:28", "2025-07-26 05:29", "2025-07-26 05:30", "2025-07-26 05:31", "2025-07-26 05:32", "2025-07-26 05:33", "2025-07-26 05:34", "2025-07-26 05:35", "2025-07-26 05:36", "2025-07-26 05:37", "2025-07-26 05:38", "2025-07-26 05:39", "2025-07-26 05:40", "2025-07-26 05:41", "2025-07-26 05:42", "2025-07-26 05:43", "2025-07-26 05:44", "2025-07-26 05:45", "2025-07-26 05:46", "2025-07-26 05:47", "2025-07-26 05:48", "2025-07-26 05:49", "2025-07-26 05:50", "2025-07-26 05:51", "2025-07-26 05:52", "2025-07-26 05:53", "2025-07-26 05:54", "2025-07-26 05:55", "2025-07-26 05:56", "2025-07-26 05:57", "2025-07-26 05:58", "2025-07-26 05:59", "2025-07-26 06:00", "2025-07-26 06:01", "2025-07-26 06:02", "2025-07-26 06:03", "2025-07-26 06:04", "2025-07-26 06:05", "2025-07-26 06:06", "2025-07-26 06:07", "2025-07-26 06:08", "2025-07-26 06:09", "2025-07-26 06:10", "2025-07-26 06:11", "2025-07-26 06:12", "2025-07-26 06:13", "2025-07-26 06:14", "2025-07-26 06:15", "2025-07-26 06:16", "2025-07-26 06:17", "2025-07-26 06:18", "2025-07-26 06:19", "2025-07-26 06:20", "2025-07-26 06:21", "2025-07-26 06:22", "2025-07-26 06:23", "2025-07-26 06:24", "2025-07-26 06:25", "2025-07-26 06:26", "2025-07-26 06:27", "2025-07-26 06:28", "2025-07-26 06:29", "2025-07-26 06:30", "2025-07-26 06:31", "2025-07-26 06:32", "2025-07-26 06:33", "2025-07-26 06:34", "2025-07-26 06:35", "2025-07-26 06:36", "2025-07-26 06:37", "2025-07-26 06:38", "2025-07-26 06:39", "2025-07-26 06:40", "2025-07-26 06:41", "2025-07-26 06:42", "2025-07-26 06:43", "2025-07-26 06:44", "2025-07-26 06:45", "2025-07-26 06:46", "2025-07-26 06:47", "2025-07-26 06:48", "2025-07-26 06:49", "2025-07-26 06:50", "2025-07-26 06:51", "2025-07-26 06:52", "2025-07-26 06:53", "2025-07-26 06:54", "2025-07-26 06:55", "2025-07-26 06:56", "2025-07-26 06:57", "2025-07-26 06:58", "2025-07-26 06:59", "2025-07-26 07:00", "2025-07-26 07:01", "2025-07-26 07:02", "2025-07-26 07:03", "2025-07-26 07:04", "2025-07-26 07:05", "2025-07-26 07:06", "2025-07-26 07:07", "2025-07-26 07:08", "2025-07-26 07:09", "2025-07-26 07:10", "2025-07-26 07:11", "2025-07-26 07:12", "2025-07-26 07:13", "2025-07-26 07:14", "2025-07-26 07:15", "2025-07-26 07:16", "2025-07-26 07:17", "2025-07-26 07:18", "2025-07-26 07:19", "2025-07-26 07:20", "2025-07-26 07:21", "2025-07-26 07:22", "2025-07-26 07:23", "2025-07-26 07:24", "2025-07-26 07:25", "2025-07-26 07:26", "2025-07-26 07:27", "2025-07-26 07:28", "2025-07-26 07:29", "2025-07-26 07:30", "2025-07-26 07:31", "2025-07-26 07:32", "2025-07-26 07:33", "2025-07-26 07:34", "2025-07-26 07:35", "2025-07-26 07:36", "2025-07-26 07:37", "2025-07-26 07:38", "2025-07-26 07:39", "2025-07-26 07:40", "2025-07-26 07:41", "2025-07-26 07:42", "2025-07-26 07:43", "2025-07-26 07:44", "2025-07-26 07:45", "2025-07-26 07:46", "2025-07-26 07:47", "2025-07-26 07:48", "2025-07-26 07:49", "2025-07-26 07:50", "2025-07-26 07:51", "2025-07-26 07:52", "2025-07-26 07:53", "2025-07-26 07:54", "2025-07-26 07:55", "2025-07-26 07:56", "2025-07-26 07:57", "2025-07-26 07:58", "2025-07-26 07:59", "2025-07-26 08:00", "2025-07-26 08:01", "2025-07-26 08:02", "2025-07-26 08:03", "2025-07-26 08:04", "2025-07-26 08:05", "2025-07-26 08:06", "2025-07-26 08:07", "2025-07-26 08:08", "2025-07-26 08:09", "2025-07-26 08:10", "2025-07-26 08:11", "2025-07-26 08:12", "2025-07-26 08:13", "2025-07-26 08:14", "2025-07-26 08:15", "2025-07-26 08:16", "2025-07-26 08:17", "2025-07-26 08:18", "2025-07-26 08:19", "2025-07-26 08:20", "2025-07-26 08:21", "2025-07-26 08:22", "2025-07-26 08:23", "2025-07-26 08:24", "2025-07-26 08:25", "2025-07-26 08:26", "2025-07-26 08:27", "2025-07-26 08:28", "2025-07-26 08:29", "2025-07-26 08:30", "2025-07-26 08:31", "2025-07-26 08:32", "2025-07-26 08:33", "2025-07-26 08:34", "2025-07-26 08:35", "2025-07-26 08:36", "2025-07-26 08:37", "2025-07-26 08:38", "2025-07-26 08:39", "2025-07-26 08:40", "2025-07-26 08:41", "2025-07-26 08:42", "2025-07-26 08:43", "2025-07-26 08:44", "2025-07-26 08:45", "2025-07-26 08:46", "2025-07-26 08:47", "2025-07-26 08:48", "2025-07-26 08:49", "2025-07-26 08:50", "2025-07-26 08:51", "2025-07-26 08:52", "2025-07-26 08:53", "2025-07-26 08:54", "2025-07-26 08:55", "2025-07-26 08:56", "2025-07-26 08:57", "2025-07-26 08:58", "2025-07-26 08:59", "2025-07-26 09:00", "2025-07-26 09:01", "2025-07-26 09:02", "2025-07-26 09:03", "2025-07-26 09:04", "2025-07-26 09:05", "2025-07-26 09:06", "2025-07-26 09:07", "2025-07-26 09:08", "2025-07-26 09:09", "2025-07-26 09:10", "2025-07-26 09:11", "2025-07-26 09:12", "2025-07-26 09:13", "2025-07-26 09:14", "2025-07-26 09:15", "2025-07-26 09:16", "2025-07-26 09:17", "2025-07-26 09:18", "2025-07-26 09:19", "2025-07-26 09:20", "2025-07-26 09:21", "2025-07-26 09:22", "2025-07-26 09:23", "2025-07-26 09:24", "2025-07-26 09:25", "2025-07-26 09:26", "2025-07-26 09:27", "2025-07-26 09:28", "2025-07-26 09:29", "2025-07-26 09:30", "2025-07-26 09:31", "2025-07-26 09:32", "2025-07-26 09:33", "2025-07-26 09:34", "2025-07-26 09:35", "2025-07-26 09:36", "2025-07-26 09:37", "2025-07-26 09:38", "2025-07-26 09:39", "2025-07-26 09:40", "2025-07-26 09:41", "2025-07-26 09:42", "2025-07-26 09:43", "2025-07-26 09:44", "2025-07-26 09:45", "2025-07-26 09:46", "2025-07-26 09:47", "2025-07-26 09:48", "2025-07-26 09:49", "2025-07-26 09:50", "2025-07-26 09:51", "2025-07-26 09:52", "2025-07-26 09:53", "2025-07-26 09:54", "2025-07-26 09:55", "2025-07-26 09:56", "2025-07-26 09:57", "2025-07-26 09:58", "2025-07-26 09:59", "2025-07-26 10:00", "2025-07-26 10:01", "2025-07-26 10:02", "2025-07-26 10:03", "2025-07-26 10:04", "2025-07-26 10:05", "2025-07-26 10:06", "2025-07-26 10:07", "2025-07-26 10:08", "2025-07-26 10:09", "2025-07-26 10:10", "2025-07-26 10:11", "2025-07-26 10:12", "2025-07-26 10:13", "2025-07-26 10:14", "2025-07-26 10:15", "2025-07-26 10:16", "2025-07-26 10:17", "2025-07-26 10:18", "2025-07-26 10:19", "2025-07-26 10:20", "2025-07-26 10:21", "2025-07-26 10:22", "2025-07-26 10:23", "2025-07-26 10:24", "2025-07-26 10:25", "2025-07-26 10:26", "2025-07-26 10:27", "2025-07-26 10:28", "2025-07-26 10:29", "2025-07-26 10:30", "2025-07-26 10:31", "2025-07-26 10:32", "2025-07-26 10:33", "2025-07-26 10:34", "2025-07-26 10:35", "2025-07-26 10:36", "2025-07-26 10:37", "2025-07-26 10:38", "2025-07-26 10:39", "2025-07-26 10:40", "2025-07-26 10:41", "2025-07-26 10:42", "2025-07-26 10:43", "2025-07-26 10:44", "2025-07-26 10:45", "2025-07-26 10:46", "2025-07-26 10:47", "2025-07-26 10:48", "2025-07-26 10:49", "2025-07-26 10:50", "2025-07-26 10:51", "2025-07-26 10:52", "2025-07-26 10:53", "2025-07-26 10:54", "2025-07-26 10:55", "2025-07-26 10:56", "2025-07-26 10:57", "2025-07-26 10:58", "2025-07-26 10:59", "2025-07-26 11:00", "2025-07-26 11:01", "2025-07-26 11:02", "2025-07-26 11:03", "2025-07-26 11:04", "2025-07-26 11:05", "2025-07-26 11:06", "2025-07-26 11:07", "2025-07-26 11:08", "2025-07-26 11:09", "2025-07-26 11:10", "2025-07-26 11:11", "2025-07-26 11:12", "2025-07-26 11:13", "2025-07-26 11:14", "2025-07-26 11:15", "2025-07-26 11:16", "2025-07-26 11:17", "2025-07-26 11:18", "2025-07-26 11:19", "2025-07-26 11:20", "2025-07-26 11:21", "2025-07-26 11:22", "2025-07-26 11:23", "2025-07-26 11:24", "2025-07-26 11:25", "2025-07-26 11:26", "2025-07-26 11:27", "2025-07-26 11:28", "2025-07-26 11:29", "2025-07-26 11:30", "2025-07-26 11:31", "2025-07-26 11:32", "2025-07-26 11:33", "2025-07-26 11:34", "2025-07-26 11:35", "2025-07-26 11:36", "2025-07-26 11:37", "2025-07-26 11:38", "2025-07-26 11:39", "2025-07-26 11:40", "2025-07-26 11:41", "2025-07-26 11:42", "2025-07-26 11:43", "2025-07-26 11:44", "2025-07-26 11:45", "2025-07-26 11:46", "2025-07-26 11:47", "2025-07-26 11:48", "2025-07-26 11:49", "2025-07-26 11:50", "2025-07-26 11:51", "2025-07-26 11:52", "2025-07-26 11:53", "2025-07-26 11:54", "2025-07-26 11:55", "2025-07-26 11:56", "2025-07-26 11:57", "2025-07-26 11:58", "2025-07-26 11:59", "2025-07-26 12:00", "2025-07-26 12:01", "2025-07-26 12:02", "2025-07-26 12:03", "2025-07-26 12:04", "2025-07-26 12:05", "2025-07-26 12:06", "2025-07-26 12:07", "2025-07-26 12:08", "2025-07-26 12:09", "2025-07-26 12:10", "2025-07-26 12:11", "2025-07-26 12:12", "2025-07-26 12:13", "2025-07-26 12:14", "2025-07-26 12:15", "2025-07-26 12:16", "2025-07-26 12:17", "2025-07-26 12:18", "2025-07-26 12:19", "2025-07-26 12:20", "2025-07-26 12:21", "2025-07-26 12:22", "2025-07-26 12:23", "2025-07-26 12:24", "2025-07-26 12:25", "2025-07-26 12:26", "2025-07-26 12:27", "2025-07-26 12:28", "2025-07-26 12:29", "2025-07-26 12:30", "2025-07-26 12:31", "2025-07-26 12:32", "2025-07-26 12:33", "2025-07-26 12:34", "2025-07-26 12:35", "2025-07-26 12:36", "2025-07-26 12:37", "2025-07-26 12:38", "2025-07-26 12:39", "2025-07-26 12:40", "2025-07-26 12:41", "2025-07-26 12:42", "2025-07-26 12:43", "2025-07-26 12:44", "2025-07-26 12:45", "2025-07-26 12:46", "2025-07-26 12:47", "2025-07-26 12:48", "2025-07-26 12:49", "2025-07-26 12:50", "2025-07-26 12:51", "2025-07-26 12:52", "2025-07-26 12:53", "2025-07-26 12:54", "2025-07-26 12:55", "2025-07-26 12:56", "2025-07-26 12:57", "2025-07-26 12:58", "2025-07-26 12:59", "2025-07-26 13:00", "2025-07-26 13:01", "2025-07-26 13:02", "2025-07-26 13:03", "2025-07-26 13:04", "2025-07-26 13:05", "2025-07-26 13:06", "2025-07-26 13:07", "2025-07-26 13:08", "2025-07-26 13:09", "2025-07-26 13:10", "2025-07-26 13:11", "2025-07-26 13:12", "2025-07-26 13:13", "2025-07-26 13:14", "2025-07-26 13:15", "2025-07-26 13:16", "2025-07-26 13:17", "2025-07-26 13:18", "2025-07-26 13:19", "2025-07-26 13:20", "2025-07-26 13:21", "2025-07-26 13:22", "2025-07-26 13:23", "2025-07-26 13:24", "2025-07-26 13:25", "2025-07-26 13:26", "2025-07-26 13:27", "2025-07-26 13:28", "2025-07-26 13:29", "2025-07-26 13:30", "2025-07-26 13:31", "2025-07-26 13:32", "2025-07-26 13:33", "2025-07-26 13:34", "2025-07-26 13:35", "2025-07-26 13:36", "2025-07-26 13:37", "2025-07-26 13:38", "2025-07-26 13:39", "2025-07-26 13:40", "2025-07-26 13:41", "2025-07-26 13:42", "2025-07-26 13:43", "2025-07-26 13:44", "2025-07-26 13:45", "2025-07-26 13:46", "2025-07-26 13:47", "2025-07-26 13:48", "2025-07-26 13:49", "2025-07-26 13:50", "2025-07-26 13:51", "2025-07-26 13:52", "2025-07-26 13:53", "2025-07-26 13:54", "2025-07-26 13:55", "2025-07-26 13:56", "2025-07-26 13:57", "2025-07-26 13:58", "2025-07-26 13:59", "2025-07-26 14:00", "2025-07-26 14:01", "2025-07-26 14:02", "2025-07-26 14:03", "2025-07-26 14:04", "2025-07-26 14:05", "2025-07-26 14:06", "2025-07-26 14:07", "2025-07-26 14:08", "2025-07-26 14:09", "2025-07-26 14:10", "2025-07-26 14:11", "2025-07-26 14:12", "2025-07-26 14:13", "2025-07-26 14:14", "2025-07-26 14:15", "2025-07-26 14:16", "2025-07-26 14:17", "2025-07-26 14:18", "2025-07-26 14:19", "2025-07-26 14:20", "2025-07-26 14:21", "2025-07-26 14:22", "2025-07-26 14:23", "2025-07-26 14:24", "2025-07-26 14:25", "2025-07-26 14:26", "2025-07-26 14:27", "2025-07-26 14:28", "2025-07-26 14:29", "2025-07-26 14:30", "2025-07-26 14:31", "2025-07-26 14:32", "2025-07-26 14:33", "2025-07-26 14:34", "2025-07-26 14:35", "2025-07-26 14:36", "2025-07-26 14:37", "2025-07-26 14:38", "2025-07-26 14:39", "2025-07-26 14:40", "2025-07-26 14:41", "2025-07-26 14:42", "2025-07-26 14:43", "2025-07-26 14:44", "2025-07-26 14:45", "2025-07-26 14:46", "2025-07-26 14:47", "2025-07-26 14:48", "2025-07-26 14:49", "2025-07-26 14:50", "2025-07-26 14:51", "2025-07-26 14:52", "2025-07-26 14:53", "2025-07-26 14:54", "2025-07-26 14:55", "2025-07-26 14:56", "2025-07-26 14:57", "2025-07-26 14:58", "2025-07-26 14:59", "2025-07-26 15:00", "2025-07-26 15:01", "2025-07-26 15:02", "2025-07-26 15:03", "2025-07-26 15:04", "2025-07-26 15:05", "2025-07-26 15:06", "2025-07-26 15:07", "2025-07-26 15:08", "2025-07-26 15:09", "2025-07-26 15:10", "2025-07-26 15:11", "2025-07-26 15:12", "2025-07-26 15:13", "2025-07-26 15:14", "2025-07-26 15:15", "2025-07-26 15:16", "2025-07-26 15:17", "2025-07-26 15:18", "2025-07-26 15:19", "2025-07-26 15:20", "2025-07-26 15:21", "2025-07-26 15:22", "2025-07-26 15:23", "2025-07-26 15:24", "2025-07-26 15:25", "2025-07-26 15:26", "2025-07-26 15:27", "2025-07-26 15:28", "2025-07-26 15:29", "2025-07-26 15:30", "2025-07-26 15:31", "2025-07-26 15:32", "2025-07-26 15:33", "2025-07-26 15:34", "2025-07-26 15:35", "2025-07-26 15:36", "2025-07-26 15:37", "2025-07-26 15:38", "2025-07-26 15:39", "2025-07-26 15:40", "2025-07-26 15:41", "2025-07-26 15:42", "2025-07-26 15:43", "2025-07-26 15:44", "2025-07-26 15:45", "2025-07-26 15:46", "2025-07-26 15:47", "2025-07-26 15:48", "2025-07-26 15:49", "2025-07-26 15:50", "2025-07-26 15:51", "2025-07-26 15:52", "2025-07-26 15:53", "2025-07-26 15:54", "2025-07-26 15:55", "2025-07-26 15:56", "2025-07-26 15:57", "2025-07-26 15:58", "2025-07-26 15:59", "2025-07-26 16:00", "2025-07-26 16:01", "2025-07-26 16:02", "2025-07-26 16:03", "2025-07-26 16:04", "2025-07-26 16:05", "2025-07-26 16:06", "2025-07-26 16:07", "2025-07-26 16:08", "2025-07-26 16:09", "2025-07-26 16:10", "2025-07-26 16:11", "2025-07-26 16:12", "2025-07-26 16:13", "2025-07-26 16:14", "2025-07-26 16:15", "2025-07-26 16:16", "2025-07-26 16:17", "2025-07-26 16:18", "2025-07-26 16:19", "2025-07-26 16:20", "2025-07-26 16:21", "2025-07-26 16:22", "2025-07-26 16:23", "2025-07-26 16:24", "2025-07-26 16:25", "2025-07-26 16:26", "2025-07-26 16:27", "2025-07-26 16:28", "2025-07-26 16:29", "2025-07-26 16:30", "2025-07-26 16:31", "2025-07-26 16:32", "2025-07-26 16:33", "2025-07-26 16:34", "2025-07-26 16:35", "2025-07-26 16:36", "2025-07-26 16:37", "2025-07-26 16:38", "2025-07-26 16:39", "2025-07-26 16:40", "2025-07-26 16:41", "2025-07-26 16:42", "2025-07-26 16:43", "2025-07-26 16:44", "2025-07-26 16:45", "2025-07-26 16:46", "2025-07-26 16:47", "2025-07-26 16:48", "2025-07-26 16:49", "2025-07-26 16:50", "2025-07-26 16:51", "2025-07-26 16:52", "2025-07-26 16:53", "2025-07-26 16:54", "2025-07-26 16:55", "2025-07-26 16:56", "2025-07-26 16:57", "2025-07-26 16:58", "2025-07-26 16:59", "2025-07-26 17:00", "2025-07-26 17:01", "2025-07-26 17:02", "2025-07-26 17:03", "2025-07-26 17:04", "2025-07-26 17:05", "2025-07-26 17:06", "2025-07-26 17:07", "2025-07-26 17:08", "2025-07-26 17:09", "2025-07-26 17:10", "2025-07-26 17:11", "2025-07-26 17:12", "2025-07-26 17:13", "2025-07-26 17:14", "2025-07-26 17:15", "2025-07-26 17:16", "2025-07-26 17:17", "2025-07-26 17:18", "2025-07-26 17:19", "2025-07-26 17:20", "2025-07-26 17:21", "2025-07-26 17:22", "2025-07-26 17:23", "2025-07-26 17:24", "2025-07-26 17:25", "2025-07-26 17:26", "2025-07-26 17:27", "2025-07-26 17:28", "2025-07-26 17:29", "2025-07-26 17:30", "2025-07-26 17:31", "2025-07-26 17:32", "2025-07-26 17:33", "2025-07-26 17:34", "2025-07-26 17:35", "2025-07-26 17:36", "2025-07-26 17:37", "2025-07-26 17:38", "2025-07-26 17:39", "2025-07-26 17:40", "2025-07-26 17:41", "2025-07-26 17:42", "2025-07-26 17:43", "2025-07-26 17:44", "2025-07-26 17:45", "2025-07-26 17:46", "2025-07-26 17:47", "2025-07-26 17:48", "2025-07-26 17:49", "2025-07-26 17:50", "2025-07-26 17:51", "2025-07-26 17:52", "2025-07-26 17:53", "2025-07-26 17:54", "2025-07-26 17:55", "2025-07-26 17:56", "2025-07-26 17:57", "2025-07-26 17:58", "2025-07-26 17:59", "2025-07-26 18:00", "2025-07-26 18:01", "2025-07-26 18:02", "2025-07-26 18:03", "2025-07-26 18:04", "2025-07-26 18:05", "2025-07-26 18:06", "2025-07-26 18:07", "2025-07-26 18:08", "2025-07-26 18:09", "2025-07-26 18:10", "2025-07-26 18:11", "2025-07-26 18:12", "2025-07-26 18:13", "2025-07-26 18:14", "2025-07-26 18:15", "2025-07-26 18:16", "2025-07-26 18:17", "2025-07-26 18:18", "2025-07-26 18:19", "2025-07-26 18:20", "2025-07-26 18:21", "2025-07-26 18:22", "2025-07-26 18:23", "2025-07-26 18:24", "2025-07-26 18:25", "2025-07-26 18:26", "2025-07-26 18:27", "2025-07-26 18:28", "2025-07-26 18:29", "2025-07-26 18:30", "2025-07-26 18:31", "2025-07-26 18:32", "2025-07-26 18:33", "2025-07-26 18:34", "2025-07-26 18:35", "2025-07-26 18:36", "2025-07-26 18:37", "2025-07-26 18:38", "2025-07-26 18:39", "2025-07-26 18:40", "2025-07-26 18:41", "2025-07-26 18:42", "2025-07-26 18:43", "2025-07-26 18:44", "2025-07-26 18:45", "2025-07-26 18:46", "2025-07-26 18:47", "2025-07-26 18:48", "2025-07-26 18:49", "2025-07-26 18:50", "2025-07-26 18:51", "2025-07-26 18:52", "2025-07-26 18:53", "2025-07-26 18:54", "2025-07-26 18:55", "2025-07-26 18:56", "2025-07-26 18:57", "2025-07-26 18:58", "2025-07-26 18:59", "2025-07-26 19:00", "2025-07-26 19:01", "2025-07-26 19:02", "2025-07-26 19:03", "2025-07-26 19:04", "2025-07-26 19:05", "2025-07-26 19:06", "2025-07-26 19:07", "2025-07-26 19:08", "2025-07-26 19:09", "2025-07-26 19:10", "2025-07-26 19:11", "2025-07-26 19:12", "2025-07-26 19:13", "2025-07-26 19:14", "2025-07-26 19:15", "2025-07-26 19:16", "2025-07-26 19:17", "2025-07-26 19:18", "2025-07-26 19:19", "2025-07-26 19:20", "2025-07-26 19:21", "2025-07-26 19:22", "2025-07-26 19:23", "2025-07-26 19:24", "2025-07-26 19:25", "2025-07-26 19:26", "2025-07-26 19:27", "2025-07-26 19:28", "2025-07-26 19:29", "2025-07-26 19:30", "2025-07-26 19:31", "2025-07-26 19:32", "2025-07-26 19:33", "2025-07-26 19:34", "2025-07-26 19:35", "2025-07-26 19:36", "2025-07-26 19:37", "2025-07-26 19:38", "2025-07-26 19:39", "2025-07-26 19:40", "2025-07-26 19:41", "2025-07-26 19:42", "2025-07-26 19:43", "2025-07-26 19:44", "2025-07-26 19:45", "2025-07-26 19:46", "2025-07-26 19:47", "2025-07-26 19:48", "2025-07-26 19:49", "2025-07-26 19:50", "2025-07-26 19:51", "2025-07-26 19:52", "2025-07-26 19:53", "2025-07-26 19:54", "2025-07-26 19:55", "2025-07-26 19:56", "2025-07-26 19:57", "2025-07-26 19:58", "2025-07-26 19:59", "2025-07-26 20:00", "2025-07-26 20:01", "2025-07-26 20:02", "2025-07-26 20:03", "2025-07-26 20:04", "2025-07-26 20:05", "2025-07-26 20:06", "2025-07-26 20:07", "2025-07-26 20:08", "2025-07-26 20:09", "2025-07-26 20:10", "2025-07-26 20:11", "2025-07-26 20:12", "2025-07-26 20:13", "2025-07-26 20:14", "2025-07-26 20:15", "2025-07-26 20:16", "2025-07-26 20:17", "2025-07-26 20:18", "2025-07-26 20:19", "2025-07-26 20:20", "2025-07-26 20:21", "2025-07-26 20:22", "2025-07-26 20:23", "2025-07-26 20:24", "2025-07-26 20:25", "2025-07-26 20:26", "2025-07-26 20:27", "2025-07-26 20:28", "2025-07-26 20:29", "2025-07-26 20:30", "2025-07-26 20:31", "2025-07-26 20:32", "2025-07-26 20:33", "2025-07-26 20:34", "2025-07-26 20:35", "2025-07-26 20:36", "2025-07-26 20:37", "2025-07-26 20:38", "2025-07-26 20:39", "2025-07-26 20:40", "2025-07-26 20:41", "2025-07-26 20:42", "2025-07-26 20:43", "2025-07-26 20:44", "2025-07-26 20:45", "2025-07-26 20:46", "2025-07-26 20:47", "2025-07-26 20:48", "2025-07-26 20:49", "2025-07-26 20:50", "2025-07-26 20:51", "2025-07-26 20:52", "2025-07-26 20:53", "2025-07-26 20:54", "2025-07-26 20:55", "2025-07-26 20:56", "2025-07-26 20:57", "2025-07-26 20:58", "2025-07-26 20:59", "2025-07-26 21:00", "2025-07-26 21:01", "2025-07-26 21:02", "2025-07-26 21:03", "2025-07-26 21:04", "2025-07-26 21:05", "2025-07-26 21:06", "2025-07-26 21:07", "2025-07-26 21:08", "2025-07-26 21:09", "2025-07-26 21:10", "2025-07-26 21:11", "2025-07-26 21:12", "2025-07-26 21:13", "2025-07-26 21:14", "2025-07-26 21:15", "2025-07-26 21:16", "2025-07-26 21:17", "2025-07-26 21:18", "2025-07-26 21:19", "2025-07-26 21:20", "2025-07-26 21:21", "2025-07-26 21:22", "2025-07-26 21:23", "2025-07-26 21:24", "2025-07-26 21:25", "2025-07-26 21:26", "2025-07-26 21:27", "2025-07-26 21:28", "2025-07-26 21:29", "2025-07-26 21:30", "2025-07-26 21:31", "2025-07-26 21:32", "2025-07-26 21:33", "2025-07-26 21:34", "2025-07-26 21:35", "2025-07-26 21:36", "2025-07-26 21:37", "2025-07-26 21:38", "2025-07-26 21:39", "2025-07-26 21:40", "2025-07-26 21:41", "2025-07-26 21:42", "2025-07-26 21:43", "2025-07-26 21:44", "2025-07-26 21:45", "2025-07-26 21:46", "2025-07-26 21:47", "2025-07-26 21:48", "2025-07-26 21:49", "2025-07-26 21:50", "2025-07-26 21:51", "2025-07-26 21:52", "2025-07-26 21:53", "2025-07-26 21:54", "2025-07-26 21:55", "2025-07-26 21:56", "2025-07-26 21:57", "2025-07-26 21:58", "2025-07-26 21:59", "2025-07-26 22:00", "2025-07-26 22:01", "2025-07-26 22:02", "2025-07-26 22:03", "2025-07-26 22:04", "2025-07-26 22:05", "2025-07-26 22:06", "2025-07-26 22:07", "2025-07-26 22:08", "2025-07-26 22:09", "2025-07-26 22:10", "2025-07-26 22:11", "2025-07-26 22:12", "2025-07-26 22:13", "2025-07-26 22:14", "2025-07-26 22:15", "2025-07-26 22:16", "2025-07-26 22:17", "2025-07-26 22:18", "2025-07-26 22:19", "2025-07-26 22:20", "2025-07-26 22:21", "2025-07-26 22:22", "2025-07-26 22:23", "2025-07-26 22:24", "2025-07-26 22:25", "2025-07-26 22:26", "2025-07-26 22:27", "2025-07-26 22:28", "2025-07-26 22:29", "2025-07-26 22:30", "2025-07-26 22:31", "2025-07-26 22:32", "2025-07-26 22:33", "2025-07-26 22:34", "2025-07-26 22:35", "2025-07-26 22:36", "2025-07-26 22:37", "2025-07-26 22:38", "2025-07-26 22:39", "2025-07-26 22:40", "2025-07-26 22:41", "2025-07-26 22:42", "2025-07-26 22:43", "2025-07-26 22:44", "2025-07-26 22:45", "2025-07-26 22:46", "2025-07-26 22:47", "2025-07-26 22:48", "2025-07-26 22:49", "2025-07-26 22:50", "2025-07-26 22:51", "2025-07-26 22:52", "2025-07-26 22:53", "2025-07-26 22:54", "2025-07-26 22:55", "2025-07-26 22:56", "2025-07-26 22:57", "2025-07-26 22:58", "2025-07-26 22:59", "2025-07-26 23:00", "2025-07-26 23:01", "2025-07-26 23:02", "2025-07-26 23:03", "2025-07-26 23:04", "2025-07-26 23:05", "2025-07-26 23:06", "2025-07-26 23:07", "2025-07-26 23:08", "2025-07-26 23:09", "2025-07-26 23:10", "2025-07-26 23:11", "2025-07-26 23:12", "2025-07-26 23:13", "2025-07-26 23:14", "2025-07-26 23:15", "2025-07-26 23:16", "2025-07-26 23:17", "2025-07-26 23:18", "2025-07-26 23:19", "2025-07-26 23:20", "2025-07-26 23:21", "2025-07-26 23:22", "2025-07-26 23:23", "2025-07-26 23:24", "2025-07-26 23:25", "2025-07-26 23:26", "2025-07-26 23:27", "2025-07-26 23:28", "2025-07-26 23:29", "2025-07-26 23:30", "2025-07-26 23:31", "2025-07-26 23:32", "2025-07-26 23:33", "2025-07-26 23:34", "2025-07-26 23:35", "2025-07-26 23:36", "2025-07-26 23:37", "2025-07-26 23:38", "2025-07-26 23:39", "2025-07-26 23:40", "2025-07-26 23:41", "2025-07-26 23:42", "2025-07-26 23:43", "2025-07-26 23:44", "2025-07-26 23:45", "2025-07-26 23:46", "2025-07-26 23:47", "2025-07-26 23:48", "2025-07-26 23:49", "2025-07-26 23:50", "2025-07-26 23:51", "2025-07-26 23:52", "2025-07-26 23:53", "2025-07-26 23:54", "2025-07-26 23:55", "2025-07-26 23:56", "2025-07-26 23:57", "2025-07-26 23:58", "2025-07-26 23:59", "2025-07-27 00:00", "2025-07-27 00:01", "2025-07-27 00:02", "2025-07-27 00:03", "2025-07-27 00:04", "2025-07-27 00:05", "2025-07-27 00:06", "2025-07-27 00:07", "2025-07-27 00:08", "2025-07-27 00:09", "2025-07-27 00:10", "2025-07-27 00:11", "2025-07-27 00:12", "2025-07-27 00:13", "2025-07-27 00:14", "2025-07-27 00:15", "2025-07-27 00:16", "2025-07-27 00:17", "2025-07-27 00:18", "2025-07-27 00:19", "2025-07-27 00:20", "2025-07-27 00:21", "2025-07-27 00:22", "2025-07-27 00:23", "2025-07-27 00:24", "2025-07-27 00:25", "2025-07-27 00:26", "2025-07-27 00:27", "2025-07-27 00:28", "2025-07-27 00:29", "2025-07-27 00:30", "2025-07-27 00:31", "2025-07-27 00:32", "2025-07-27 00:33", "2025-07-27 00:34", "2025-07-27 00:35", "2025-07-27 00:36", "2025-07-27 00:37", "2025-07-27 00:38", "2025-07-27 00:39", "2025-07-27 00:40", "2025-07-27 00:41", "2025-07-27 00:42", "2025-07-27 00:43", "2025-07-27 00:44", "2025-07-27 00:45", "2025-07-27 00:46", "2025-07-27 00:47", "2025-07-27 00:48", "2025-07-27 00:49", "2025-07-27 00:50", "2025-07-27 00:51", "2025-07-27 00:52", "2025-07-27 00:53", "2025-07-27 00:54", "2025-07-27 00:55", "2025-07-27 00:56", "2025-07-27 00:57", "2025-07-27 00:58", "2025-07-27 00:59", "2025-07-27 01:00", "2025-07-27 01:01", "2025-07-27 01:02", "2025-07-27 01:03", "2025-07-27 01:04", "2025-07-27 01:05", "2025-07-27 01:06", "2025-07-27 01:07", "2025-07-27 01:08", "2025-07-27 01:09", "2025-07-27 01:10", "2025-07-27 01:11", "2025-07-27 01:12", "2025-07-27 01:13", "2025-07-27 01:14", "2025-07-27 01:15", "2025-07-27 01:16", "2025-07-27 01:17", "2025-07-27 01:18", "2025-07-27 01:19", "2025-07-27 01:20", "2025-07-27 01:21", "2025-07-27 01:22", "2025-07-27 01:23", "2025-07-27 01:24", "2025-07-27 01:25", "2025-07-27 01:26", "2025-07-27 01:27", "2025-07-27 01:28", "2025-07-27 01:29", "2025-07-27 01:30", "2025-07-27 01:31", "2025-07-27 01:32", "2025-07-27 01:33", "2025-07-27 01:34", "2025-07-27 01:35", "2025-07-27 01:36", "2025-07-27 01:37", "2025-07-27 01:38", "2025-07-27 01:39", "2025-07-27 01:40", "2025-07-27 01:41", "2025-07-27 01:42", "2025-07-27 01:43", "2025-07-27 01:44", "2025-07-27 01:45", "2025-07-27 01:46", "2025-07-27 01:47", "2025-07-27 01:48", "2025-07-27 01:49", "2025-07-27 01:50", "2025-07-27 01:51", "2025-07-27 01:52", "2025-07-27 01:53", "2025-07-27 01:54", "2025-07-27 01:55", "2025-07-27 01:56", "2025-07-27 01:57", "2025-07-27 01:58", "2025-07-27 01:59", "2025-07-27 02:00", "2025-07-27 02:01", "2025-07-27 02:02", "2025-07-27 02:03", "2025-07-27 02:04", "2025-07-27 02:05", "2025-07-27 02:06", "2025-07-27 02:07", "2025-07-27 02:08", "2025-07-27 02:09", "2025-07-27 02:10", "2025-07-27 02:11", "2025-07-27 02:12", "2025-07-27 02:13", "2025-07-27 02:14", "2025-07-27 02:15", "2025-07-27 02:16", "2025-07-27 02:17", "2025-07-27 02:18", "2025-07-27 02:19", "2025-07-27 02:20", "2025-07-27 02:21", "2025-07-27 02:22", "2025-07-27 02:23", "2025-07-27 02:24", "2025-07-27 02:25", "2025-07-27 02:26", "2025-07-27 02:27", "2025-07-27 02:28", "2025-07-27 02:29", "2025-07-27 02:30", "2025-07-27 02:31", "2025-07-27 02:32", "2025-07-27 02:33", "2025-07-27 02:34", "2025-07-27 02:35", "2025-07-27 02:36", "2025-07-27 02:37", "2025-07-27 02:38", "2025-07-27 02:39", "2025-07-27 02:40", "2025-07-27 02:41", "2025-07-27 02:42", "2025-07-27 02:43", "2025-07-27 02:44", "2025-07-27 02:45", "2025-07-27 02:46", "2025-07-27 02:47", "2025-07-27 02:48", "2025-07-27 02:49", "2025-07-27 02:50", "2025-07-27 02:51", "2025-07-27 02:52", "2025-07-27 02:53", "2025-07-27 02:54", "2025-07-27 02:55", "2025-07-27 02:56", "2025-07-27 02:57", "2025-07-27 02:58", "2025-07-27 02:59", "2025-07-27 03:00", "2025-07-27 03:01", "2025-07-27 03:02", "2025-07-27 03:03", "2025-07-27 03:04", "2025-07-27 03:05", "2025-07-27 03:06", "2025-07-27 03:07", "2025-07-27 03:08", "2025-07-27 03:09", "2025-07-27 03:10", "2025-07-27 03:11", "2025-07-27 03:12", "2025-07-27 03:13", "2025-07-27 03:14", "2025-07-27 03:15", "2025-07-27 03:16", "2025-07-27 03:17", "2025-07-27 03:18", "2025-07-27 03:19", "2025-07-27 03:20", "2025-07-27 03:21", "2025-07-27 03:22", "2025-07-27 03:23", "2025-07-27 03:24", "2025-07-27 03:25", "2025-07-27 03:26", "2025-07-27 03:27", "2025-07-27 03:28", "2025-07-27 03:29", "2025-07-27 03:30", "2025-07-27 03:31", "2025-07-27 03:32", "2025-07-27 03:33", "2025-07-27 03:34", "2025-07-27 03:35", "2025-07-27 03:36", "2025-07-27 03:37", "2025-07-27 03:38", "2025-07-27 03:39", "2025-07-27 03:40", "2025-07-27 03:41", "2025-07-27 03:42", "2025-07-27 03:43", "2025-07-27 03:44", "2025-07-27 03:45", "2025-07-27 03:46", "2025-07-27 03:47", "2025-07-27 03:48", "2025-07-27 03:49", "2025-07-27 03:50", "2025-07-27 03:51", "2025-07-27 03:52", "2025-07-27 03:53", "2025-07-27 03:54", "2025-07-27 03:55", "2025-07-27 03:56", "2025-07-27 03:57", "2025-07-27 03:58", "2025-07-27 03:59", "2025-07-27 04:00", "2025-07-27 04:01", "2025-07-27 04:02", "2025-07-27 04:03", "2025-07-27 04:04", "2025-07-27 04:05", "2025-07-27 04:06", "2025-07-27 04:07", "2025-07-27 04:08", "2025-07-27 04:09", "2025-07-27 04:10", "2025-07-27 04:11", "2025-07-27 04:12", "2025-07-27 04:13", "2025-07-27 04:14", "2025-07-27 04:15", "2025-07-27 04:16", "2025-07-27 04:17", "2025-07-27 04:18", "2025-07-27 04:19", "2025-07-27 04:20", "2025-07-27 04:21", "2025-07-27 04:22", "2025-07-27 04:23", "2025-07-27 04:24", "2025-07-27 04:25", "2025-07-27 04:26", "2025-07-27 04:27", "2025-07-27 04:28", "2025-07-27 04:29", "2025-07-27 04:30", "2025-07-27 04:31", "2025-07-27 04:32", "2025-07-27 04:33", "2025-07-27 04:34", "2025-07-27 04:35", "2025-07-27 04:36", "2025-07-27 04:37", "2025-07-27 04:38", "2025-07-27 04:39", "2025-07-27 04:40", "2025-07-27 04:41", "2025-07-27 04:42", "2025-07-27 04:43", "2025-07-27 04:44", "2025-07-27 04:45", "2025-07-27 04:46", "2025-07-27 04:47", "2025-07-27 04:48", "2025-07-27 04:49", "2025-07-27 04:50", "2025-07-27 04:51", "2025-07-27 04:52", "2025-07-27 04:53", "2025-07-27 04:54", "2025-07-27 04:55", "2025-07-27 04:56", "2025-07-27 04:57", "2025-07-27 04:58", "2025-07-27 04:59", "2025-07-27 05:00", "2025-07-27 05:01", "2025-07-27 05:02", "2025-07-27 05:03", "2025-07-27 05:04", "2025-07-27 05:05", "2025-07-27 05:06", "2025-07-27 05:07", "2025-07-27 05:08", "2025-07-27 05:09", "2025-07-27 05:10", "2025-07-27 05:11", "2025-07-27 05:12", "2025-07-27 05:13", "2025-07-27 05:14", "2025-07-27 05:15", "2025-07-27 05:16", "2025-07-27 05:17", "2025-07-27 05:18", "2025-07-27 05:19", "2025-07-27 05:20", "2025-07-27 05:21", "2025-07-27 05:22", "2025-07-27 05:23", "2025-07-27 05:24", "2025-07-27 05:25", "2025-07-27 05:26", "2025-07-27 05:27", "2025-07-27 05:28", "2025-07-27 05:29", "2025-07-27 05:30", "2025-07-27 05:31", "2025-07-27 05:32", "2025-07-27 05:33", "2025-07-27 05:34", "2025-07-27 05:35", "2025-07-27 05:36", "2025-07-27 05:37", "2025-07-27 05:38", "2025-07-27 05:39", "2025-07-27 05:40", "2025-07-27 05:41", "2025-07-27 05:42", "2025-07-27 05:43", "2025-07-27 05:44", "2025-07-27 05:45", "2025-07-27 05:46", "2025-07-27 05:47", "2025-07-27 05:48", "2025-07-27 05:49", "2025-07-27 05:50", "2025-07-27 05:51", "2025-07-27 05:52", "2025-07-27 05:53", "2025-07-27 05:54", "2025-07-27 05:55", "2025-07-27 05:56", "2025-07-27 05:57", "2025-07-27 05:58", "2025-07-27 05:59", "2025-07-27 06:00", "2025-07-27 06:01", "2025-07-27 06:02", "2025-07-27 06:03", "2025-07-27 06:04", "2025-07-27 06:05", "2025-07-27 06:06", "2025-07-27 06:07", "2025-07-27 06:08", "2025-07-27 06:09", "2025-07-27 06:10", "2025-07-27 06:11", "2025-07-27 06:12", "2025-07-27 06:13", "2025-07-27 06:14", "2025-07-27 06:15", "2025-07-27 06:16", "2025-07-27 06:17", "2025-07-27 06:18", "2025-07-27 06:19", "2025-07-27 06:20", "2025-07-27 06:21", "2025-07-27 06:22", "2025-07-27 06:23", "2025-07-27 06:24", "2025-07-27 06:25", "2025-07-27 06:26", "2025-07-27 06:27", "2025-07-27 06:28", "2025-07-27 06:29", "2025-07-27 06:30", "2025-07-27 06:31", "2025-07-27 06:32", "2025-07-27 06:33", "2025-07-27 06:34", "2025-07-27 06:35", "2025-07-27 06:36", "2025-07-27 06:37", "2025-07-27 06:38", "2025-07-27 06:39", "2025-07-27 06:40", "2025-07-27 06:41", "2025-07-27 06:42", "2025-07-27 06:43", "2025-07-27 06:44", "2025-07-27 06:45", "2025-07-27 06:46", "2025-07-27 06:47", "2025-07-27 06:48", "2025-07-27 06:49", "2025-07-27 06:50", "2025-07-27 06:51", "2025-07-27 06:52", "2025-07-27 06:53", "2025-07-27 06:54", "2025-07-27 06:55", "2025-07-27 06:56", "2025-07-27 06:57", "2025-07-27 06:58", "2025-07-27 06:59", "2025-07-27 07:00", "2025-07-27 07:01", "2025-07-27 07:02", "2025-07-27 07:03", "2025-07-27 07:04", "2025-07-27 07:05", "2025-07-27 07:06", "2025-07-27 07:07", "2025-07-27 07:08", "2025-07-27 07:09", "2025-07-27 07:10", "2025-07-27 07:11", "2025-07-27 07:12", "2025-07-27 07:13", "2025-07-27 07:14", "2025-07-27 07:15", "2025-07-27 07:16", "2025-07-27 07:17", "2025-07-27 07:18", "2025-07-27 07:19", "2025-07-27 07:20", "2025-07-27 07:21", "2025-07-27 07:22", "2025-07-27 07:23", "2025-07-27 07:24", "2025-07-27 07:25", "2025-07-27 07:26", "2025-07-27 07:27", "2025-07-27 07:28", "2025-07-27 07:29", "2025-07-27 07:30", "2025-07-27 07:31", "2025-07-27 07:32", "2025-07-27 07:33", "2025-07-27 07:34", "2025-07-27 07:35", "2025-07-27 07:36", "2025-07-27 07:37", "2025-07-27 07:38", "2025-07-27 07:39", "2025-07-27 07:40", "2025-07-27 07:41", "2025-07-27 07:42", "2025-07-27 07:43", "2025-07-27 07:44", "2025-07-27 07:45", "2025-07-27 07:46", "2025-07-27 07:47", "2025-07-27 07:48", "2025-07-27 07:49", "2025-07-27 07:50", "2025-07-27 07:51", "2025-07-27 07:52", "2025-07-27 07:53", "2025-07-27 07:54", "2025-07-27 07:55", "2025-07-27 07:56", "2025-07-27 07:57", "2025-07-27 07:58", "2025-07-27 07:59", "2025-07-27 08:00", "2025-07-27 08:01", "2025-07-27 08:02", "2025-07-27 08:03", "2025-07-27 08:04", "2025-07-27 08:05", "2025-07-27 08:06", "2025-07-27 08:07", "2025-07-27 08:08", "2025-07-27 08:09", "2025-07-27 08:10", "2025-07-27 08:11", "2025-07-27 08:12", "2025-07-27 08:13", "2025-07-27 08:14", "2025-07-27 08:15", "2025-07-27 08:16", "2025-07-27 08:17", "2025-07-27 08:18", "2025-07-27 08:19", "2025-07-27 08:20", "2025-07-27 08:21", "2025-07-27 08:22", "2025-07-27 08:23", "2025-07-27 08:24", "2025-07-27 08:25", "2025-07-27 08:26", "2025-07-27 08:27", "2025-07-27 08:28", "2025-07-27 08:29", "2025-07-27 08:30", "2025-07-27 08:31", "2025-07-27 08:32", "2025-07-27 08:33", "2025-07-27 08:34", "2025-07-27 08:35", "2025-07-27 08:36", "2025-07-27 08:37", "2025-07-27 08:38", "2025-07-27 08:39", "2025-07-27 08:40", "2025-07-27 08:41", "2025-07-27 08:42", "2025-07-27 08:43", "2025-07-27 08:44", "2025-07-27 08:45", "2025-07-27 08:46", "2025-07-27 08:47", "2025-07-27 08:48", "2025-07-27 08:49", "2025-07-27 08:50", "2025-07-27 08:51", "2025-07-27 08:52", "2025-07-27 08:53", "2025-07-27 08:54", "2025-07-27 08:55", "2025-07-27 08:56", "2025-07-27 08:57", "2025-07-27 08:58", "2025-07-27 08:59", "2025-07-27 09:00", "2025-07-27 09:01", "2025-07-27 09:02", "2025-07-27 09:03", "2025-07-27 09:04", "2025-07-27 09:05", "2025-07-27 09:06", "2025-07-27 09:07", "2025-07-27 09:08", "2025-07-27 09:09", "2025-07-27 09:10", "2025-07-27 09:11", "2025-07-27 09:12", "2025-07-27 09:13", "2025-07-27 09:14", "2025-07-27 09:15", "2025-07-27 09:16", "2025-07-27 09:17", "2025-07-27 09:18", "2025-07-27 09:19", "2025-07-27 09:20", "2025-07-27 09:21", "2025-07-27 09:22", "2025-07-27 09:23", "2025-07-27 09:24", "2025-07-27 09:25", "2025-07-27 09:26", "2025-07-27 09:27", "2025-07-27 09:28", "2025-07-27 09:29", "2025-07-27 09:30", "2025-07-27 09:31", "2025-07-27 09:32", "2025-07-27 09:33", "2025-07-27 09:34", "2025-07-27 09:35", "2025-07-27 09:36", "2025-07-27 09:37", "2025-07-27 09:38", "2025-07-27 09:39", "2025-07-27 09:40", "2025-07-27 09:41", "2025-07-27 09:42", "2025-07-27 09:43", "2025-07-27 09:44", "2025-07-27 09:45", "2025-07-27 09:46", "2025-07-27 09:47", "2025-07-27 09:48", "2025-07-27 09:49", "2025-07-27 09:50", "2025-07-27 09:51", "2025-07-27 09:52", "2025-07-27 09:53", "2025-07-27 09:54", "2025-07-27 09:55", "2025-07-27 09:56", "2025-07-27 09:57", "2025-07-27 09:58", "2025-07-27 09:59", "2025-07-27 10:00", "2025-07-27 10:01", "2025-07-27 10:02", "2025-07-27 10:03", "2025-07-27 10:04", "2025-07-27 10:05", "2025-07-27 10:06", "2025-07-27 10:07", "2025-07-27 10:08", "2025-07-27 10:09", "2025-07-27 10:10", "2025-07-27 10:11", "2025-07-27 10:12", "2025-07-27 10:13", "2025-07-27 10:14", "2025-07-27 10:15", "2025-07-27 10:16", "2025-07-27 10:17", "2025-07-27 10:18", "2025-07-27 10:19", "2025-07-27 10:20", "2025-07-27 10:21", "2025-07-27 10:22", "2025-07-27 10:23", "2025-07-27 10:24", "2025-07-27 10:25", "2025-07-27 10:26", "2025-07-27 10:27", "2025-07-27 10:28", "2025-07-27 10:29", "2025-07-27 10:30", "2025-07-27 10:31", "2025-07-27 10:32", "2025-07-27 10:33", "2025-07-27 10:34", "2025-07-27 10:35", "2025-07-27 10:36", "2025-07-27 10:37", "2025-07-27 10:38", "2025-07-27 10:39", "2025-07-27 10:40", "2025-07-27 10:41", "2025-07-27 10:42", "2025-07-27 10:43", "2025-07-27 10:44", "2025-07-27 10:45", "2025-07-27 10:46", "2025-07-27 10:47", "2025-07-27 10:48", "2025-07-27 10:49", "2025-07-27 10:50", "2025-07-27 10:51", "2025-07-27 10:52", "2025-07-27 10:53", "2025-07-27 10:54", "2025-07-27 10:55", "2025-07-27 10:56", "2025-07-27 10:57", "2025-07-27 10:58", "2025-07-27 10:59", "2025-07-27 11:00", "2025-07-27 11:01", "2025-07-27 11:02", "2025-07-27 11:03", "2025-07-27 11:04", "2025-07-27 11:05", "2025-07-27 11:06", "2025-07-27 11:07", "2025-07-27 11:08", "2025-07-27 11:09", "2025-07-27 11:10", "2025-07-27 11:11", "2025-07-27 11:12", "2025-07-27 11:13", "2025-07-27 11:14", "2025-07-27 11:15", "2025-07-27 11:16", "2025-07-27 11:17", "2025-07-27 11:18", "2025-07-27 11:19", "2025-07-27 11:20", "2025-07-27 11:21", "2025-07-27 11:22", "2025-07-27 11:23", "2025-07-27 11:24", "2025-07-27 11:25", "2025-07-27 11:26", "2025-07-27 11:27", "2025-07-27 11:28", "2025-07-27 11:29", "2025-07-27 11:30", "2025-07-27 11:31", "2025-07-27 11:32", "2025-07-27 11:33", "2025-07-27 11:34", "2025-07-27 11:35", "2025-07-27 11:36", "2025-07-27 11:37", "2025-07-27 11:38", "2025-07-27 11:39", "2025-07-27 11:40", "2025-07-27 11:41", "2025-07-27 11:42", "2025-07-27 11:43", "2025-07-27 11:44", "2025-07-27 11:45", "2025-07-27 11:46", "2025-07-27 11:47", "2025-07-27 11:48", "2025-07-27 11:49", "2025-07-27 11:50", "2025-07-27 11:51", "2025-07-27 11:52", "2025-07-27 11:53", "2025-07-27 11:54", "2025-07-27 11:55", "2025-07-27 11:56", "2025-07-27 11:57", "2025-07-27 11:58", "2025-07-27 11:59", "2025-07-27 12:00", "2025-07-27 12:01", "2025-07-27 12:02", "2025-07-27 12:03", "2025-07-27 12:04", "2025-07-27 12:05", "2025-07-27 12:06", "2025-07-27 12:07", "2025-07-27 12:08", "2025-07-27 12:09", "2025-07-27 12:10", "2025-07-27 12:11", "2025-07-27 12:12", "2025-07-27 12:13", "2025-07-27 12:14", "2025-07-27 12:15", "2025-07-27 12:16", "2025-07-27 12:17", "2025-07-27 12:18", "2025-07-27 12:19", "2025-07-27 12:20", "2025-07-27 12:21", "2025-07-27 12:22", "2025-07-27 12:23", "2025-07-27 12:24", "2025-07-27 12:25", "2025-07-27 12:26", "2025-07-27 12:27", "2025-07-27 12:28", "2025-07-27 12:29", "2025-07-27 12:30", "2025-07-27 12:31", "2025-07-27 12:32", "2025-07-27 12:33", "2025-07-27 12:34", "2025-07-27 12:35", "2025-07-27 12:36", "2025-07-27 12:37", "2025-07-27 12:38", "2025-07-27 12:39", "2025-07-27 12:40", "2025-07-27 12:41", "2025-07-27 12:42", "2025-07-27 12:43", "2025-07-27 12:44", "2025-07-27 12:45", "2025-07-27 12:46", "2025-07-27 12:47", "2025-07-27 12:48", "2025-07-27 12:49", "2025-07-27 12:50", "2025-07-27 12:51", "2025-07-27 12:52", "2025-07-27 12:53", "2025-07-27 12:54", "2025-07-27 12:55", "2025-07-27 12:56", "2025-07-27 12:57", "2025-07-27 12:58", "2025-07-27 12:59", "2025-07-27 13:00", "2025-07-27 13:01", "2025-07-27 13:02", "2025-07-27 13:03", "2025-07-27 13:04", "2025-07-27 13:05", "2025-07-27 13:06", "2025-07-27 13:07", "2025-07-27 13:08", "2025-07-27 13:09", "2025-07-27 13:10", "2025-07-27 13:11", "2025-07-27 13:12", "2025-07-27 13:13", "2025-07-27 13:14", "2025-07-27 13:15", "2025-07-27 13:16", "2025-07-27 13:17", "2025-07-27 13:18", "2025-07-27 13:19", "2025-07-27 13:20", "2025-07-27 13:21", "2025-07-27 13:22", "2025-07-27 13:23", "2025-07-27 13:24", "2025-07-27 13:25", "2025-07-27 13:26", "2025-07-27 13:27", "2025-07-27 13:28", "2025-07-27 13:29", "2025-07-27 13:30", "2025-07-27 13:31", "2025-07-27 13:32", "2025-07-27 13:33", "2025-07-27 13:34", "2025-07-27 13:35", "2025-07-27 13:36", "2025-07-27 13:37", "2025-07-27 13:38", "2025-07-27 13:39", "2025-07-27 13:40", "2025-07-27 13:41", "2025-07-27 13:42", "2025-07-27 13:43", "2025-07-27 13:44", "2025-07-27 13:45", "2025-07-27 13:46", "2025-07-27 13:47", "2025-07-27 13:48", "2025-07-27 13:49", "2025-07-27 13:50", "2025-07-27 13:51", "2025-07-27 13:52", "2025-07-27 13:53", "2025-07-27 13:54", "2025-07-27 13:55", "2025-07-27 13:56", "2025-07-27 13:57", "2025-07-27 13:58", "2025-07-27 13:59", "2025-07-27 14:00", "2025-07-27 14:01", "2025-07-27 14:02", "2025-07-27 14:03", "2025-07-27 14:04", "2025-07-27 14:05", "2025-07-27 14:06", "2025-07-27 14:07", "2025-07-27 14:08", "2025-07-27 14:09", "2025-07-27 14:10", "2025-07-27 14:11", "2025-07-27 14:12", "2025-07-27 14:13", "2025-07-27 14:14", "2025-07-27 14:15", "2025-07-27 14:16", "2025-07-27 14:17", "2025-07-27 14:18", "2025-07-27 14:19", "2025-07-27 14:20", "2025-07-27 14:21", "2025-07-27 14:22", "2025-07-27 14:23", "2025-07-27 14:24", "2025-07-27 14:25", "2025-07-27 14:26", "2025-07-27 14:27", "2025-07-27 14:28", "2025-07-27 14:29", "2025-07-27 14:30", "2025-07-27 14:31", "2025-07-27 14:32", "2025-07-27 14:33", "2025-07-27 14:34", "2025-07-27 14:35", "2025-07-27 14:36", "2025-07-27 14:37", "2025-07-27 14:38", "2025-07-27 14:39", "2025-07-27 14:40", "2025-07-27 14:41", "2025-07-27 14:42", "2025-07-27 14:43", "2025-07-27 14:44", "2025-07-27 14:45", "2025-07-27 14:46", "2025-07-27 14:47", "2025-07-27 14:48", "2025-07-27 14:49", "2025-07-27 14:50", "2025-07-27 14:51", "2025-07-27 14:52", "2025-07-27 14:53", "2025-07-27 14:54", "2025-07-27 14:55", "2025-07-27 14:56", "2025-07-27 14:57", "2025-07-27 14:58", "2025-07-27 14:59", "2025-07-27 15:00", "2025-07-27 15:01", "2025-07-27 15:02", "2025-07-27 15:03", "2025-07-27 15:04", "2025-07-27 15:05", "2025-07-27 15:06", "2025-07-27 15:07", "2025-07-27 15:08", "2025-07-27 15:09", "2025-07-27 15:10", "2025-07-27 15:11", "2025-07-27 15:12", "2025-07-27 15:13", "2025-07-27 15:14", "2025-07-27 15:15", "2025-07-27 15:16", "2025-07-27 15:17", "2025-07-27 15:18", "2025-07-27 15:19", "2025-07-27 15:20", "2025-07-27 15:21", "2025-07-27 15:22", "2025-07-27 15:23", "2025-07-27 15:24", "2025-07-27 15:25", "2025-07-27 15:26", "2025-07-27 15:27", "2025-07-27 15:28", "2025-07-27 15:29", "2025-07-27 15:30", "2025-07-27 15:31", "2025-07-27 15:32", "2025-07-27 15:33", "2025-07-27 15:34", "2025-07-27 15:35", "2025-07-27 15:36", "2025-07-27 15:37", "2025-07-27 15:38", "2025-07-27 15:39", "2025-07-27 15:40", "2025-07-27 15:41", "2025-07-27 15:42", "2025-07-27 15:43", "2025-07-27 15:44", "2025-07-27 15:45", "2025-07-27 15:46", "2025-07-27 15:47", "2025-07-27 15:48", "2025-07-27 15:49", "2025-07-27 15:50", "2025-07-27 15:51", "2025-07-27 15:52", "2025-07-27 15:53", "2025-07-27 15:54", "2025-07-27 15:55", "2025-07-27 15:56", "2025-07-27 15:57", "2025-07-27 15:58", "2025-07-27 15:59", "2025-07-27 16:00", "2025-07-27 16:01", "2025-07-27 16:02", "2025-07-27 16:03", "2025-07-27 16:04", "2025-07-27 16:05", "2025-07-27 16:06", "2025-07-27 16:07", "2025-07-27 16:08", "2025-07-27 16:09", "2025-07-27 16:10", "2025-07-27 16:11", "2025-07-27 16:12", "2025-07-27 16:13", "2025-07-27 16:14", "2025-07-27 16:15", "2025-07-27 16:16", "2025-07-27 16:17", "2025-07-27 16:18", "2025-07-27 16:19", "2025-07-27 16:20", "2025-07-27 16:21", "2025-07-27 16:22", "2025-07-27 16:23", "2025-07-27 16:24", "2025-07-27 16:25", "2025-07-27 16:26", "2025-07-27 16:27", "2025-07-27 16:28", "2025-07-27 16:29", "2025-07-27 16:30", "2025-07-27 16:31", "2025-07-27 16:32", "2025-07-27 16:33", "2025-07-27 16:34", "2025-07-27 16:35", "2025-07-27 16:36", "2025-07-27 16:37", "2025-07-27 16:38", "2025-07-27 16:39", "2025-07-27 16:40", "2025-07-27 16:41", "2025-07-27 16:42", "2025-07-27 16:43", "2025-07-27 16:44", "2025-07-27 16:45", "2025-07-27 16:46", "2025-07-27 16:47", "2025-07-27 16:48", "2025-07-27 16:49", "2025-07-27 16:50", "2025-07-27 16:51", "2025-07-27 16:52", "2025-07-27 16:53", "2025-07-27 16:54", "2025-07-27 16:55", "2025-07-27 16:56", "2025-07-27 16:57", "2025-07-27 16:58", "2025-07-27 16:59", "2025-07-27 17:00", "2025-07-27 17:01", "2025-07-27 17:02", "2025-07-27 17:03", "2025-07-27 17:04", "2025-07-27 17:05", "2025-07-27 17:06", "2025-07-27 17:07", "2025-07-27 17:08", "2025-07-27 17:09", "2025-07-27 17:10", "2025-07-27 17:11", "2025-07-27 17:12", "2025-07-27 17:13", "2025-07-27 17:14", "2025-07-27 17:15", "2025-07-27 17:16", "2025-07-27 17:17", "2025-07-27 17:18", "2025-07-27 17:19", "2025-07-27 17:20", "2025-07-27 17:21", "2025-07-27 17:22", "2025-07-27 17:23", "2025-07-27 17:24", "2025-07-27 17:25", "2025-07-27 17:26", "2025-07-27 17:27", "2025-07-27 17:28", "2025-07-27 17:29", "2025-07-27 17:30", "2025-07-27 17:31", "2025-07-27 17:32", "2025-07-27 17:33", "2025-07-27 17:34", "2025-07-27 17:35", "2025-07-27 17:36", "2025-07-27 17:37", "2025-07-27 17:38", "2025-07-27 17:39", "2025-07-27 17:40", "2025-07-27 17:41", "2025-07-27 17:42", "2025-07-27 17:43", "2025-07-27 17:44", "2025-07-27 17:45", "2025-07-27 17:46", "2025-07-27 17:47", "2025-07-27 17:48", "2025-07-27 17:49", "2025-07-27 17:50", "2025-07-27 17:51", "2025-07-27 17:52", "2025-07-27 17:53", "2025-07-27 17:54", "2025-07-27 17:55", "2025-07-27 17:56", "2025-07-27 17:57", "2025-07-27 17:58", "2025-07-27 17:59", "2025-07-27 18:00", "2025-07-27 18:01", "2025-07-27 18:02", "2025-07-27 18:03", "2025-07-27 18:04", "2025-07-27 18:05", "2025-07-27 18:06", "2025-07-27 18:07", "2025-07-27 18:08", "2025-07-27 18:09", "2025-07-27 18:10", "2025-07-27 18:11", "2025-07-27 18:12", "2025-07-27 18:13", "2025-07-27 18:14", "2025-07-27 18:15", "2025-07-27 18:16", "2025-07-27 18:17", "2025-07-27 18:18", "2025-07-27 18:19", "2025-07-27 18:20", "2025-07-27 18:21", "2025-07-27 18:22", "2025-07-27 18:23", "2025-07-27 18:24", "2025-07-27 18:25", "2025-07-27 18:26", "2025-07-27 18:27", "2025-07-27 18:28", "2025-07-27 18:29", "2025-07-27 18:30", "2025-07-27 18:31", "2025-07-27 18:32", "2025-07-27 18:33", "2025-07-27 18:34", "2025-07-27 18:35", "2025-07-27 18:36", "2025-07-27 18:37", "2025-07-27 18:38", "2025-07-27 18:39", "2025-07-27 18:40", "2025-07-27 18:41", "2025-07-27 18:42", "2025-07-27 18:43", "2025-07-27 18:44", "2025-07-27 18:45", "2025-07-27 18:46", "2025-07-27 18:47", "2025-07-27 18:48", "2025-07-27 18:49", "2025-07-27 18:50", "2025-07-27 18:51", "2025-07-27 18:52", "2025-07-27 18:53", "2025-07-27 18:54", "2025-07-27 18:55", "2025-07-27 18:56", "2025-07-27 18:57", "2025-07-27 18:58", "2025-07-27 18:59", "2025-07-27 19:00", "2025-07-27 19:01", "2025-07-27 19:02", "2025-07-27 19:03", "2025-07-27 19:04", "2025-07-27 19:05", "2025-07-27 19:06", "2025-07-27 19:07", "2025-07-27 19:08", "2025-07-27 19:09", "2025-07-27 19:10", "2025-07-27 19:11", "2025-07-27 19:12", "2025-07-27 19:13", "2025-07-27 19:14", "2025-07-27 19:15", "2025-07-27 19:16", "2025-07-27 19:17", "2025-07-27 19:18", "2025-07-27 19:19", "2025-07-27 19:20", "2025-07-27 19:21", "2025-07-27 19:22", "2025-07-27 19:23", "2025-07-27 19:24", "2025-07-27 19:25", "2025-07-27 19:26", "2025-07-27 19:27", "2025-07-27 19:28", "2025-07-27 19:29", "2025-07-27 19:30", "2025-07-27 19:31", "2025-07-27 19:32", "2025-07-27 19:33", "2025-07-27 19:34", "2025-07-27 19:35", "2025-07-27 19:36", "2025-07-27 19:37", "2025-07-27 19:38", "2025-07-27 19:39", "2025-07-27 19:40", "2025-07-27 19:41", "2025-07-27 19:42", "2025-07-27 19:43", "2025-07-27 19:44", "2025-07-27 19:45", "2025-07-27 19:46", "2025-07-27 19:47", "2025-07-27 19:48", "2025-07-27 19:49", "2025-07-27 19:50", "2025-07-27 19:51", "2025-07-27 19:52", "2025-07-27 19:53", "2025-07-27 19:54", "2025-07-27 19:55", "2025-07-27 19:56", "2025-07-27 19:57", "2025-07-27 19:58", "2025-07-27 19:59", "2025-07-27 20:00", "2025-07-27 20:01", "2025-07-27 20:02", "2025-07-27 20:03", "2025-07-27 20:04", "2025-07-27 20:05", "2025-07-27 20:06", "2025-07-27 20:07", "2025-07-27 20:08", "2025-07-27 20:09", "2025-07-27 20:10", "2025-07-27 20:11", "2025-07-27 20:12", "2025-07-27 20:13", "2025-07-27 20:14", "2025-07-27 20:15", "2025-07-27 20:16", "2025-07-27 20:17", "2025-07-27 20:18", "2025-07-27 20:19", "2025-07-27 20:20", "2025-07-27 20:21", "2025-07-27 20:22", "2025-07-27 20:23", "2025-07-27 20:24", "2025-07-27 20:25", "2025-07-27 20:26", "2025-07-27 20:27", "2025-07-27 20:28", "2025-07-27 20:29", "2025-07-27 20:30", "2025-07-27 20:31", "2025-07-27 20:32", "2025-07-27 20:33", "2025-07-27 20:34", "2025-07-27 20:35", "2025-07-27 20:36", "2025-07-27 20:37", "2025-07-27 20:38", "2025-07-27 20:39", "2025-07-27 20:40", "2025-07-27 20:41", "2025-07-27 20:42", "2025-07-27 20:43", "2025-07-27 20:44", "2025-07-27 20:45", "2025-07-27 20:46", "2025-07-27 20:47", "2025-07-27 20:48", "2025-07-27 20:49", "2025-07-27 20:50", "2025-07-27 20:51", "2025-07-27 20:52", "2025-07-27 20:53", "2025-07-27 20:54", "2025-07-27 20:55", "2025-07-27 20:56", "2025-07-27 20:57", "2025-07-27 20:58", "2025-07-27 20:59", "2025-07-27 21:00", "2025-07-27 21:01", "2025-07-27 21:02", "2025-07-27 21:03", "2025-07-27 21:04", "2025-07-27 21:05", "2025-07-27 21:06", "2025-07-27 21:07", "2025-07-27 21:08", "2025-07-27 21:09", "2025-07-27 21:10", "2025-07-27 21:11", "2025-07-27 21:12", "2025-07-27 21:13", "2025-07-27 21:14", "2025-07-27 21:15", "2025-07-27 21:16", "2025-07-27 21:17", "2025-07-27 21:18", "2025-07-27 21:19", "2025-07-27 21:20", "2025-07-27 21:21", "2025-07-27 21:22", "2025-07-27 21:23", "2025-07-27 21:24", "2025-07-27 21:25", "2025-07-27 21:26", "2025-07-27 21:27", "2025-07-27 21:28", "2025-07-27 21:29", "2025-07-27 21:30", "2025-07-27 21:31", "2025-07-27 21:32", "2025-07-27 21:33", "2025-07-27 21:34", "2025-07-27 21:35", "2025-07-27 21:36", "2025-07-27 21:37", "2025-07-27 21:38", "2025-07-27 21:39", "2025-07-27 21:40", "2025-07-27 21:41", "2025-07-27 21:42", "2025-07-27 21:43", "2025-07-27 21:44", "2025-07-27 21:45", "2025-07-27 21:46", "2025-07-27 21:47", "2025-07-27 21:48", "2025-07-27 21:49", "2025-07-27 21:50", "2025-07-27 21:51", "2025-07-27 21:52", "2025-07-27 21:53", "2025-07-27 21:54", "2025-07-27 21:55", "2025-07-27 21:56", "2025-07-27 21:57", "2025-07-27 21:58", "2025-07-27 21:59", "2025-07-27 22:00", "2025-07-27 22:01", "2025-07-27 22:02", "2025-07-27 22:03", "2025-07-27 22:04", "2025-07-27 22:05", "2025-07-27 22:06", "2025-07-27 22:07", "2025-07-27 22:08", "2025-07-27 22:09", "2025-07-27 22:10", "2025-07-27 22:11", "2025-07-27 22:12", "2025-07-27 22:13", "2025-07-27 22:14", "2025-07-27 22:15", "2025-07-27 22:16", "2025-07-27 22:17", "2025-07-27 22:18", "2025-07-27 22:19", "2025-07-27 22:20", "2025-07-27 22:21", "2025-07-27 22:22", "2025-07-27 22:23", "2025-07-27 22:24", "2025-07-27 22:25", "2025-07-27 22:26", "2025-07-27 22:27", "2025-07-27 22:28", "2025-07-27 22:29", "2025-07-27 22:30", "2025-07-27 22:31", "2025-07-27 22:32", "2025-07-27 22:33", "2025-07-27 22:34", "2025-07-27 22:35", "2025-07-27 22:36", "2025-07-27 22:37", "2025-07-27 22:38", "2025-07-27 22:39", "2025-07-27 22:40", "2025-07-27 22:41", "2025-07-27 22:42", "2025-07-27 22:43", "2025-07-27 22:44", "2025-07-27 22:45", "2025-07-27 22:46", "2025-07-27 22:47", "2025-07-27 22:48", "2025-07-27 22:49", "2025-07-27 22:50", "2025-07-27 22:51", "2025-07-27 22:52", "2025-07-27 22:53", "2025-07-27 22:54", "2025-07-27 22:55", "2025-07-27 22:56", "2025-07-27 22:57", "2025-07-27 22:58", "2025-07-27 22:59", "2025-07-27 23:00", "2025-07-27 23:01", "2025-07-27 23:02", "2025-07-27 23:03", "2025-07-27 23:04", "2025-07-27 23:05", "2025-07-27 23:06", "2025-07-27 23:07", "2025-07-27 23:08", "2025-07-27 23:09", "2025-07-27 23:10", "2025-07-27 23:11", "2025-07-27 23:12", "2025-07-27 23:13", "2025-07-27 23:14", "2025-07-27 23:15", "2025-07-27 23:16", "2025-07-27 23:17", "2025-07-27 23:18", "2025-07-27 23:19", "2025-07-27 23:20", "2025-07-27 23:21", "2025-07-27 23:22", "2025-07-27 23:23", "2025-07-27 23:24", "2025-07-27 23:25", "2025-07-27 23:26", "2025-07-27 23:27", "2025-07-27 23:28", "2025-07-27 23:29", "2025-07-27 23:30", "2025-07-27 23:31", "2025-07-27 23:32", "2025-07-27 23:33", "2025-07-27 23:34", "2025-07-27 23:35", "2025-07-27 23:36", "2025-07-27 23:37", "2025-07-27 23:38", "2025-07-27 23:39", "2025-07-27 23:40", "2025-07-27 23:41", "2025-07-27 23:42", "2025-07-27 23:43", "2025-07-27 23:44", "2025-07-27 23:45", "2025-07-27 23:46", "2025-07-27 23:47", "2025-07-27 23:48", "2025-07-27 23:49", "2025-07-27 23:50", "2025-07-27 23:51", "2025-07-27 23:52", "2025-07-27 23:53", "2025-07-27 23:54", "2025-07-27 23:55", "2025-07-27 23:56", "2025-07-27 23:57", "2025-07-27 23:58", "2025-07-27 23:59", "2025-07-28 00:00", "2025-07-28 00:01", "2025-07-28 00:02", "2025-07-28 00:03", "2025-07-28 00:04", "2025-07-28 00:05", "2025-07-28 00:06", "2025-07-28 00:07", "2025-07-28 00:08", "2025-07-28 00:09", "2025-07-28 00:10", "2025-07-28 00:11", "2025-07-28 00:12", "2025-07-28 00:13", "2025-07-28 00:14", "2025-07-28 00:15", "2025-07-28 00:16", "2025-07-28 00:17", "2025-07-28 00:18", "2025-07-28 00:19", "2025-07-28 00:20", "2025-07-28 00:21", "2025-07-28 00:22", "2025-07-28 00:23", "2025-07-28 00:24", "2025-07-28 00:25", "2025-07-28 00:26", "2025-07-28 00:27", "2025-07-28 00:28", "2025-07-28 00:29", "2025-07-28 00:30", "2025-07-28 00:31", "2025-07-28 00:32", "2025-07-28 00:33", "2025-07-28 00:34", "2025-07-28 00:35", "2025-07-28 00:36", "2025-07-28 00:37", "2025-07-28 00:38", "2025-07-28 00:39", "2025-07-28 00:40", "2025-07-28 00:41", "2025-07-28 00:42", "2025-07-28 00:43", "2025-07-28 00:44", "2025-07-28 00:45", "2025-07-28 00:46", "2025-07-28 00:47", "2025-07-28 00:48", "2025-07-28 00:49", "2025-07-28 00:50", "2025-07-28 00:51", "2025-07-28 00:52", "2025-07-28 00:53", "2025-07-28 00:54", "2025-07-28 00:55", "2025-07-28 00:56", "2025-07-28 00:57", "2025-07-28 00:58", "2025-07-28 00:59", "2025-07-28 01:00", "2025-07-28 01:01", "2025-07-28 01:02", "2025-07-28 01:03", "2025-07-28 01:04", "2025-07-28 01:05", "2025-07-28 01:06", "2025-07-28 01:07", "2025-07-28 01:08", "2025-07-28 01:09", "2025-07-28 01:10", "2025-07-28 01:11", "2025-07-28 01:12", "2025-07-28 01:13", "2025-07-28 01:14", "2025-07-28 01:15", "2025-07-28 01:16", "2025-07-28 01:17", "2025-07-28 01:18", "2025-07-28 01:19", "2025-07-28 01:20", "2025-07-28 01:21", "2025-07-28 01:22", "2025-07-28 01:23", "2025-07-28 01:24", "2025-07-28 01:25", "2025-07-28 01:26", "2025-07-28 01:27", "2025-07-28 01:28", "2025-07-28 01:29", "2025-07-28 01:30", "2025-07-28 01:31", "2025-07-28 01:32", "2025-07-28 01:33", "2025-07-28 01:34", "2025-07-28 01:35", "2025-07-28 01:36", "2025-07-28 01:37", "2025-07-28 01:38", "2025-07-28 01:39", "2025-07-28 01:40", "2025-07-28 01:41", "2025-07-28 01:42", "2025-07-28 01:43", "2025-07-28 01:44", "2025-07-28 01:45", "2025-07-28 01:46", "2025-07-28 01:47", "2025-07-28 01:48", "2025-07-28 01:49", "2025-07-28 01:50", "2025-07-28 01:51", "2025-07-28 01:52", "2025-07-28 01:53", "2025-07-28 01:54", "2025-07-28 01:55", "2025-07-28 01:56", "2025-07-28 01:57", "2025-07-28 01:58", "2025-07-28 01:59", "2025-07-28 02:00", "2025-07-28 02:01", "2025-07-28 02:02", "2025-07-28 02:03", "2025-07-28 02:04", "2025-07-28 02:05", "2025-07-28 02:06", "2025-07-28 02:07", "2025-07-28 02:08", "2025-07-28 02:09", "2025-07-28 02:10", "2025-07-28 02:11", "2025-07-28 02:12", "2025-07-28 02:13", "2025-07-28 02:14", "2025-07-28 02:15", "2025-07-28 02:16", "2025-07-28 02:17", "2025-07-28 02:18", "2025-07-28 02:19", "2025-07-28 02:20", "2025-07-28 02:21", "2025-07-28 02:22", "2025-07-28 02:23", "2025-07-28 02:24", "2025-07-28 02:25", "2025-07-28 02:26", "2025-07-28 02:27", "2025-07-28 02:28", "2025-07-28 02:29", "2025-07-28 02:30", "2025-07-28 02:31", "2025-07-28 02:32", "2025-07-28 02:33", "2025-07-28 02:34", "2025-07-28 02:35", "2025-07-28 02:36", "2025-07-28 02:37", "2025-07-28 02:38", "2025-07-28 02:39", "2025-07-28 02:40", "2025-07-28 02:41", "2025-07-28 02:42", "2025-07-28 02:43", "2025-07-28 02:44", "2025-07-28 02:45", "2025-07-28 02:46", "2025-07-28 02:47", "2025-07-28 02:48", "2025-07-28 02:49", "2025-07-28 02:50", "2025-07-28 02:51", "2025-07-28 02:52", "2025-07-28 02:53", "2025-07-28 02:54", "2025-07-28 02:55", "2025-07-28 02:56", "2025-07-28 02:57", "2025-07-28 02:58", "2025-07-28 02:59", "2025-07-28 03:00", "2025-07-28 03:01", "2025-07-28 03:02", "2025-07-28 03:03", "2025-07-28 03:04", "2025-07-28 03:05", "2025-07-28 03:06", "2025-07-28 03:07", "2025-07-28 03:08", "2025-07-28 03:09", "2025-07-28 03:10", "2025-07-28 03:11", "2025-07-28 03:12", "2025-07-28 03:13", "2025-07-28 03:14", "2025-07-28 03:15", "2025-07-28 03:16", "2025-07-28 03:17", "2025-07-28 03:18", "2025-07-28 03:19", "2025-07-28 03:20", "2025-07-28 03:21", "2025-07-28 03:22", "2025-07-28 03:23", "2025-07-28 03:24", "2025-07-28 03:25", "2025-07-28 03:26", "2025-07-28 03:27", "2025-07-28 03:28", "2025-07-28 03:29", "2025-07-28 03:30", "2025-07-28 03:31", "2025-07-28 03:32", "2025-07-28 03:33", "2025-07-28 03:34", "2025-07-28 03:35", "2025-07-28 03:36", "2025-07-28 03:37", "2025-07-28 03:38", "2025-07-28 03:39", "2025-07-28 03:40", "2025-07-28 03:41", "2025-07-28 03:42", "2025-07-28 03:43", "2025-07-28 03:44", "2025-07-28 03:45", "2025-07-28 03:46", "2025-07-28 03:47", "2025-07-28 03:48", "2025-07-28 03:49", "2025-07-28 03:50", "2025-07-28 03:51", "2025-07-28 03:52", "2025-07-28 03:53", "2025-07-28 03:54", "2025-07-28 03:55", "2025-07-28 03:56", "2025-07-28 03:57", "2025-07-28 03:58", "2025-07-28 03:59", "2025-07-28 04:00", "2025-07-28 04:01", "2025-07-28 04:02", "2025-07-28 04:03", "2025-07-28 04:04", "2025-07-28 04:05", "2025-07-28 04:06", "2025-07-28 04:07", "2025-07-28 04:08", "2025-07-28 04:09", "2025-07-28 04:10", "2025-07-28 04:11", "2025-07-28 04:12", "2025-07-28 04:13", "2025-07-28 04:14", "2025-07-28 04:15", "2025-07-28 04:16", "2025-07-28 04:17", "2025-07-28 04:18", "2025-07-28 04:19", "2025-07-28 04:20", "2025-07-28 04:21", "2025-07-28 04:22", "2025-07-28 04:23", "2025-07-28 04:24", "2025-07-28 04:25", "2025-07-28 04:26", "2025-07-28 04:27", "2025-07-28 04:28", "2025-07-28 04:29", "2025-07-28 04:30", "2025-07-28 04:31", "2025-07-28 04:32", "2025-07-28 04:33", "2025-07-28 04:34", "2025-07-28 04:35", "2025-07-28 04:36", "2025-07-28 04:37", "2025-07-28 04:38", "2025-07-28 04:39", "2025-07-28 04:40", "2025-07-28 04:41", "2025-07-28 04:42", "2025-07-28 04:43", "2025-07-28 04:44", "2025-07-28 04:45", "2025-07-28 04:46", "2025-07-28 04:47", "2025-07-28 04:48", "2025-07-28 04:49", "2025-07-28 04:50", "2025-07-28 04:51", "2025-07-28 04:52", "2025-07-28 04:53", "2025-07-28 04:54", "2025-07-28 04:55", "2025-07-28 04:56", "2025-07-28 04:57", "2025-07-28 04:58", "2025-07-28 04:59", "2025-07-28 05:00", "2025-07-28 05:01", "2025-07-28 05:02", "2025-07-28 05:03", "2025-07-28 05:04", "2025-07-28 05:05", "2025-07-28 05:06", "2025-07-28 05:07", "2025-07-28 05:08", "2025-07-28 05:09", "2025-07-28 05:10", "2025-07-28 05:11", "2025-07-28 05:12", "2025-07-28 05:13", "2025-07-28 05:14", "2025-07-28 05:15", "2025-07-28 05:16", "2025-07-28 05:17", "2025-07-28 05:18", "2025-07-28 05:19", "2025-07-28 05:20", "2025-07-28 05:21", "2025-07-28 05:22", "2025-07-28 05:23", "2025-07-28 05:24", "2025-07-28 05:25", "2025-07-28 05:26", "2025-07-28 05:27", "2025-07-28 05:28", "2025-07-28 05:29", "2025-07-28 05:30", "2025-07-28 05:31", "2025-07-28 05:32", "2025-07-28 05:33", "2025-07-28 05:34", "2025-07-28 05:35", "2025-07-28 05:36", "2025-07-28 05:37", "2025-07-28 05:38", "2025-07-28 05:39", "2025-07-28 05:40", "2025-07-28 05:41", "2025-07-28 05:42", "2025-07-28 05:43", "2025-07-28 05:44", "2025-07-28 05:45", "2025-07-28 05:46", "2025-07-28 05:47", "2025-07-28 05:48", "2025-07-28 05:49", "2025-07-28 05:50", "2025-07-28 05:51", "2025-07-28 05:52", "2025-07-28 05:53", "2025-07-28 05:54", "2025-07-28 05:55", "2025-07-28 05:56", "2025-07-28 05:57", "2025-07-28 05:58", "2025-07-28 05:59", "2025-07-28 06:00", "2025-07-28 06:01", "2025-07-28 06:02", "2025-07-28 06:03", "2025-07-28 06:04", "2025-07-28 06:05", "2025-07-28 06:06", "2025-07-28 06:07", "2025-07-28 06:08", "2025-07-28 06:09", "2025-07-28 06:10", "2025-07-28 06:11", "2025-07-28 06:12", "2025-07-28 06:13", "2025-07-28 06:14", "2025-07-28 06:15", "2025-07-28 06:16", "2025-07-28 06:17", "2025-07-28 06:18", "2025-07-28 06:19", "2025-07-28 06:20", "2025-07-28 06:21", "2025-07-28 06:22", "2025-07-28 06:23", "2025-07-28 06:24", "2025-07-28 06:25", "2025-07-28 06:26", "2025-07-28 06:27", "2025-07-28 06:28", "2025-07-28 06:29", "2025-07-28 06:30", "2025-07-28 06:31", "2025-07-28 06:32", "2025-07-28 06:33", "2025-07-28 06:34", "2025-07-28 06:35", "2025-07-28 06:36", "2025-07-28 06:37", "2025-07-28 06:38", "2025-07-28 06:39", "2025-07-28 06:40", "2025-07-28 06:41", "2025-07-28 06:42", "2025-07-28 06:43", "2025-07-28 06:44", "2025-07-28 06:45", "2025-07-28 06:46", "2025-07-28 06:47", "2025-07-28 06:48", "2025-07-28 06:49", "2025-07-28 06:50", "2025-07-28 06:51", "2025-07-28 06:52", "2025-07-28 06:53", "2025-07-28 06:54", "2025-07-28 06:55", "2025-07-28 06:56", "2025-07-28 06:57", "2025-07-28 06:58", "2025-07-28 06:59", "2025-07-28 07:00", "2025-07-28 07:01", "2025-07-28 07:02", "2025-07-28 07:03", "2025-07-28 07:04", "2025-07-28 07:05", "2025-07-28 07:06", "2025-07-28 07:07", "2025-07-28 07:08", "2025-07-28 07:09", "2025-07-28 07:10", "2025-07-28 07:11", "2025-07-28 07:12", "2025-07-28 07:13", "2025-07-28 07:14", "2025-07-28 07:15", "2025-07-28 07:16", "2025-07-28 07:17", "2025-07-28 07:18", "2025-07-28 07:19", "2025-07-28 07:20", "2025-07-28 07:21", "2025-07-28 07:22", "2025-07-28 07:23", "2025-07-28 07:24", "2025-07-28 07:25", "2025-07-28 07:26", "2025-07-28 07:27", "2025-07-28 07:28", "2025-07-28 07:29", "2025-07-28 07:30", "2025-07-28 07:31", "2025-07-28 07:32", "2025-07-28 07:33", "2025-07-28 07:34", "2025-07-28 07:35", "2025-07-28 07:36", "2025-07-28 07:37", "2025-07-28 07:38", "2025-07-28 07:39", "2025-07-28 07:40", "2025-07-28 07:41", "2025-07-28 07:42", "2025-07-28 07:43", "2025-07-28 07:44", "2025-07-28 07:45", "2025-07-28 07:46", "2025-07-28 07:47", "2025-07-28 07:48", "2025-07-28 07:49", "2025-07-28 07:50", "2025-07-28 07:51", "2025-07-28 07:52", "2025-07-28 07:53", "2025-07-28 07:54", "2025-07-28 07:55", "2025-07-28 07:56", "2025-07-28 07:57", "2025-07-28 07:58", "2025-07-28 07:59", "2025-07-28 08:00", "2025-07-28 08:01", "2025-07-28 08:02", "2025-07-28 08:03", "2025-07-28 08:04", "2025-07-28 08:05", "2025-07-28 08:06", "2025-07-28 08:07", "2025-07-28 08:08", "2025-07-28 08:09", "2025-07-28 08:10", "2025-07-28 08:11", "2025-07-28 08:12", "2025-07-28 08:13", "2025-07-28 08:14", "2025-07-28 08:15", "2025-07-28 08:16", "2025-07-28 08:17", "2025-07-28 08:18", "2025-07-28 08:19", "2025-07-28 08:20", "2025-07-28 08:21", "2025-07-28 08:22", "2025-07-28 08:23", "2025-07-28 08:24", "2025-07-28 08:25", "2025-07-28 08:26", "2025-07-28 08:27", "2025-07-28 08:28", "2025-07-28 08:29", "2025-07-28 08:30", "2025-07-28 08:31", "2025-07-28 08:32", "2025-07-28 08:33", "2025-07-28 08:34", "2025-07-28 08:35", "2025-07-28 08:36", "2025-07-28 08:37", "2025-07-28 08:38", "2025-07-28 08:39", "2025-07-28 08:40", "2025-07-28 08:41", "2025-07-28 08:42", "2025-07-28 08:43", "2025-07-28 08:44", "2025-07-28 08:45", "2025-07-28 08:46", "2025-07-28 08:47", "2025-07-28 08:48", "2025-07-28 08:49", "2025-07-28 08:50", "2025-07-28 08:51", "2025-07-28 08:52", "2025-07-28 08:53", "2025-07-28 08:54", "2025-07-28 08:55", "2025-07-28 08:56", "2025-07-28 08:57", "2025-07-28 08:58", "2025-07-28 08:59", "2025-07-28 09:00", "2025-07-28 09:01", "2025-07-28 09:02", "2025-07-28 09:03", "2025-07-28 09:04", "2025-07-28 09:05", "2025-07-28 09:06", "2025-07-28 09:07", "2025-07-28 09:08", "2025-07-28 09:09", "2025-07-28 09:10", "2025-07-28 09:11", "2025-07-28 09:12", "2025-07-28 09:13", "2025-07-28 09:14", "2025-07-28 09:15", "2025-07-28 09:16", "2025-07-28 09:17", "2025-07-28 09:18", "2025-07-28 09:19", "2025-07-28 09:20", "2025-07-28 09:21", "2025-07-28 09:22", "2025-07-28 09:23", "2025-07-28 09:24", "2025-07-28 09:25", "2025-07-28 09:26", "2025-07-28 09:27", "2025-07-28 09:28", "2025-07-28 09:29", "2025-07-28 09:30", "2025-07-28 09:31", "2025-07-28 09:32", "2025-07-28 09:33", "2025-07-28 09:34", "2025-07-28 09:35", "2025-07-28 09:36", "2025-07-28 09:37", "2025-07-28 09:38", "2025-07-28 09:39", "2025-07-28 09:40", "2025-07-28 09:41", "2025-07-28 09:42", "2025-07-28 09:43", "2025-07-28 09:44", "2025-07-28 09:45", "2025-07-28 09:46", "2025-07-28 09:47", "2025-07-28 09:48", "2025-07-28 09:49", "2025-07-28 09:50", "2025-07-28 09:51", "2025-07-28 09:52", "2025-07-28 09:53", "2025-07-28 09:54", "2025-07-28 09:55", "2025-07-28 09:56", "2025-07-28 09:57", "2025-07-28 09:58", "2025-07-28 09:59", "2025-07-28 10:00", "2025-07-28 10:01", "2025-07-28 10:02", "2025-07-28 10:03", "2025-07-28 10:04", "2025-07-28 10:05", "2025-07-28 10:06", "2025-07-28 10:07", "2025-07-28 10:08", "2025-07-28 10:09", "2025-07-28 10:10", "2025-07-28 10:11", "2025-07-28 10:12", "2025-07-28 10:13", "2025-07-28 10:14", "2025-07-28 10:15", "2025-07-28 10:16", "2025-07-28 10:17", "2025-07-28 10:18", "2025-07-28 10:19", "2025-07-28 10:20", "2025-07-28 10:21", "2025-07-28 10:22", "2025-07-28 10:23", "2025-07-28 10:24", "2025-07-28 10:25", "2025-07-28 10:26", "2025-07-28 10:27", "2025-07-28 10:28", "2025-07-28 10:29", "2025-07-28 10:30", "2025-07-28 10:31", "2025-07-28 10:32", "2025-07-28 10:33", "2025-07-28 10:34", "2025-07-28 10:35", "2025-07-28 10:36", "2025-07-28 10:37", "2025-07-28 10:38", "2025-07-28 10:39", "2025-07-28 10:40", "2025-07-28 10:41", "2025-07-28 10:42", "2025-07-28 10:43", "2025-07-28 10:44", "2025-07-28 10:45", "2025-07-28 10:46", "2025-07-28 10:47", "2025-07-28 10:48", "2025-07-28 10:49", "2025-07-28 10:50", "2025-07-28 10:51", "2025-07-28 10:52", "2025-07-28 10:53", "2025-07-28 10:54", "2025-07-28 10:55", "2025-07-28 10:56", "2025-07-28 10:57", "2025-07-28 10:58", "2025-07-28 10:59", "2025-07-28 11:00", "2025-07-28 11:01", "2025-07-28 11:02", "2025-07-28 11:03", "2025-07-28 11:04", "2025-07-28 11:05", "2025-07-28 11:06", "2025-07-28 11:07", "2025-07-28 11:08", "2025-07-28 11:09", "2025-07-28 11:10", "2025-07-28 11:11", "2025-07-28 11:12", "2025-07-28 11:13", "2025-07-28 11:14", "2025-07-28 11:15", "2025-07-28 11:16", "2025-07-28 11:17", "2025-07-28 11:18", "2025-07-28 11:19", "2025-07-28 11:20", "2025-07-28 11:21", "2025-07-28 11:22", "2025-07-28 11:23", "2025-07-28 11:24", "2025-07-28 11:25", "2025-07-28 11:26", "2025-07-28 11:27", "2025-07-28 11:28", "2025-07-28 11:29", "2025-07-28 11:30", "2025-07-28 11:31", "2025-07-28 11:32", "2025-07-28 11:33", "2025-07-28 11:34", "2025-07-28 11:35", "2025-07-28 11:36", "2025-07-28 11:37", "2025-07-28 11:38", "2025-07-28 11:39", "2025-07-28 11:40", "2025-07-28 11:41", "2025-07-28 11:42", "2025-07-28 11:43", "2025-07-28 11:44", "2025-07-28 11:45", "2025-07-28 11:46", "2025-07-28 11:47", "2025-07-28 11:48", "2025-07-28 11:49", "2025-07-28 11:50", "2025-07-28 11:51", "2025-07-28 11:52", "2025-07-28 11:53", "2025-07-28 11:54", "2025-07-28 11:55", "2025-07-28 11:56", "2025-07-28 11:57", "2025-07-28 11:58", "2025-07-28 11:59", "2025-07-28 12:00", "2025-07-28 12:01", "2025-07-28 12:02", "2025-07-28 12:03", "2025-07-28 12:04", "2025-07-28 12:05", "2025-07-28 12:06", "2025-07-28 12:07", "2025-07-28 12:08", "2025-07-28 12:09", "2025-07-28 12:10", "2025-07-28 12:11", "2025-07-28 12:12", "2025-07-28 12:13", "2025-07-28 12:14", "2025-07-28 12:15", "2025-07-28 12:16", "2025-07-28 12:17", "2025-07-28 12:18", "2025-07-28 12:19", "2025-07-28 12:20", "2025-07-28 12:21", "2025-07-28 12:22", "2025-07-28 12:23", "2025-07-28 12:24", "2025-07-28 12:25", "2025-07-28 12:26", "2025-07-28 12:27", "2025-07-28 12:28", "2025-07-28 12:29", "2025-07-28 12:30", "2025-07-28 12:31", "2025-07-28 12:32", "2025-07-28 12:33", "2025-07-28 12:34", "2025-07-28 12:35", "2025-07-28 12:36", "2025-07-28 12:37", "2025-07-28 12:38", "2025-07-28 12:39", "2025-07-28 12:40", "2025-07-28 12:41", "2025-07-28 12:42", "2025-07-28 12:43", "2025-07-28 12:44", "2025-07-28 12:45", "2025-07-28 12:46", "2025-07-28 12:47", "2025-07-28 12:48", "2025-07-28 12:49", "2025-07-28 12:50", "2025-07-28 12:51", "2025-07-28 12:52", "2025-07-28 12:53", "2025-07-28 12:54", "2025-07-28 12:55", "2025-07-28 12:56", "2025-07-28 12:57", "2025-07-28 12:58", "2025-07-28 12:59", "2025-07-28 13:00", "2025-07-28 13:01", "2025-07-28 13:02", "2025-07-28 13:03", "2025-07-28 13:04", "2025-07-28 13:05", "2025-07-28 13:06", "2025-07-28 13:07", "2025-07-28 13:08", "2025-07-28 13:09", "2025-07-28 13:10", "2025-07-28 13:11", "2025-07-28 13:12", "2025-07-28 13:13", "2025-07-28 13:14", "2025-07-28 13:15", "2025-07-28 13:16", "2025-07-28 13:17", "2025-07-28 13:18", "2025-07-28 13:19", "2025-07-28 13:20", "2025-07-28 13:21", "2025-07-28 13:22", "2025-07-28 13:23", "2025-07-28 13:24", "2025-07-28 13:25", "2025-07-28 13:26", "2025-07-28 13:27", "2025-07-28 13:28", "2025-07-28 13:29", "2025-07-28 13:30", "2025-07-28 13:31", "2025-07-28 13:32", "2025-07-28 13:33", "2025-07-28 13:34", "2025-07-28 13:35", "2025-07-28 13:36", "2025-07-28 13:37", "2025-07-28 13:38", "2025-07-28 13:39", "2025-07-28 13:40", "2025-07-28 13:41", "2025-07-28 13:42", "2025-07-28 13:43", "2025-07-28 13:44", "2025-07-28 13:45", "2025-07-28 13:46", "2025-07-28 13:47", "2025-07-28 13:48", "2025-07-28 13:49", "2025-07-28 13:50", "2025-07-28 13:51", "2025-07-28 13:52", "2025-07-28 13:53", "2025-07-28 13:54", "2025-07-28 13:55", "2025-07-28 13:56", "2025-07-28 13:57", "2025-07-28 13:58", "2025-07-28 13:59", "2025-07-28 14:00", "2025-07-28 14:01", "2025-07-28 14:02", "2025-07-28 14:03", "2025-07-28 14:04", "2025-07-28 14:05", "2025-07-28 14:06", "2025-07-28 14:07", "2025-07-28 14:08"], "metric_data": {"series": [{"label": "实时", "metric_val": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 113.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 4.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 49.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 55.0, 254.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.0, 4476.0, 17336.0, 25704.0, 12855.0, 6926.0, 1.0, 5941.0, 24.0, 6552.0, 2.0, 5012.0, 1.0, 6845.0, 1680.0, 2006.0, 2770.0, 5.0, 7729.0, 1.0, 4334.0, 1.0, 7432.0, 613.0, 6737.0, 2.0, 5632.0, 1.0, 5566.0, 138.0, 2882.0, 1750.0, 1064.0, 4214.0, 1.0, 8013.0, 1.0, 7446.0, 1.0, 5990.0, 1.0, 5826.0, 1.0, 5762.0, 1.0, 4688.0, 1414.0, 1899.0, 2976.0, 1.0, 27645.0, 23263.0, 7364.0, 1480.0, 1465.0, 6163.0, 3822.0, 15954.0, 648.0, 12709.0, 2295.0, 11957.0, 7777.0, 4373.0, 1541.0, 2650.0, 2597.0, 3045.0, 2349.0, 2453.0, 2310.0, 4873.0, 13288.0, 5527.0, 2671.0, 1747.0, 2486.0, 5157.0, 16189.0, 22410.0, 22742.0, 18871.0, 23082.0, 18314.0, 20040.0, 13323.0, 5565.0, 1910.0, 1432.0, 11830.0, 23333.0, 15319.0, 3189.0, 2634.0, 2439.0, 3178.0, 2264.0, 1631.0, 3832.0, 1601.0, 12953.0, 11331.0, 2093.0, 1502.0, 2581.0, 3186.0, 10054.0, 14197.0, 4311.0, 22097.0, 2151.0, 1818.0, 2810.0, 2235.0, 1532.0, 1990.0, 5527.0, 4585.0, 5858.0, 2730.0, 5242.0, 7954.0, 1558.0, 2555.0, 5910.0, 6030.0, 2341.0, 7559.0, 2586.0, 7389.0, 3806.0, 4883.0, 21010.0, 4156.0, 2044.0, 5232.0, 2254.0, 2356.0, 1760.0, 1917.0, 2603.0, 2093.0, 1582.0, 1510.0, 3930.0, 3615.0, 6984.0, 5506.0, 16925.0, 26129.0, 4723.0, 1364.0, 3203.0, 5563.0, 2304.0, 19036.0, 21511.0, 2095.0, 2126.0, 15641.0, 20919.0, 12702.0, 2443.0, 27609.0, 22604.0, 6400.0, 4031.0, 2584.0, 7252.0, 3449.0, 5338.0, 1708.0, 1572.0, 7679.0, 13843.0, 5527.0, 2459.0, 3389.0, 8015.0, 4160.0, 3145.0, 3587.0, 2063.0, 2402.0, 2423.0, 17603.0, 8975.0, 2170.0, 2488.0, 1736.0, 2211.0, 3282.0, 2100.0, 2351.0, 7476.0, 2809.0, 2631.0, 2097.0, 2793.0, 2479.0, 2971.0, 11915.0, 18902.0, 26458.0, 14540.0, 2281.0, 2504.0, 2875.0, 2832.0, 2462.0, 2140.0, 2203.0, 1900.0, 1515.0, 4715.0, 2895.0, 5008.0, 1937.0, 1450.0, 2076.0, 1503.0, 1678.0, 2382.0, 1735.0, 5807.0, 11129.0, 2164.0, 2261.0, 2712.0, 3258.0, 3686.0, 15565.0, 38680.0, 28328.0, 23133.0, 1911.0, 2394.0, 17869.0, 2795.0, 2338.0, 1630.0, 2069.0, 2421.0, 2367.0, 2057.0, 2594.0, 2063.0, 4101.0, 3549.0, 1758.0, 6811.0, 1640.0, 2242.0, 2051.0, 2568.0, 4509.0, 1820.0, 1642.0, 2111.0, 1403.0, 6519.0, 3037.0, 4612.0, 2565.0, 1886.0, 1551.0, 1690.0, 2487.0, 3294.0, 2135.0, 2400.0, 3599.0, 2312.0, 2104.0, 2670.0, 2511.0, 2560.0, 1967.0, 1498.0, 3732.0, 5289.0, 2587.0, 1405.0, 1020.0, 1118.0, 1163.0, 1158.0, 1316.0, 1984.0, 1940.0, 1823.0, 2389.0, 1418.0, 1892.0, 1530.0, 2094.0, 2396.0, 1296.0, 2254.0, 22362.0, 1686.0, 1610.0, 2423.0, 1823.0, 2306.0, 3274.0, 2008.0, 3547.0, 9936.0, 26996.0, 27175.0, 10214.0, 1758.0, 2262.0, 2095.0, 2313.0, 3786.0, 2042.0, 7756.0, 23461.0, 4060.0, 1919.0, 1990.0, 2392.0, 3600.0, 3123.0, 2200.0, 18258.0, 26926.0, 26731.0, 26945.0, 26408.0, 13133.0, 3138.0, 19566.0, 13605.0, 2072.0, 1817.0, 3092.0, 1940.0, 1478.0, 1310.0, 2434.0, 1415.0, 1471.0, 1771.0, 2055.0, 1671.0, 2388.0, 1522.0, 1984.0, 2188.0, 2724.0, 1862.0, 21709.0, 12762.0, 2284.0, 12249.0, 5234.0, 2029.0, 1.0, 2540.0, 1.0, 2519.0, 1.0, 3812.0, 1.0, 3023.0, 1423.0, 1543.0, 2981.0, 1.0, 5270.0, 1.0, 3834.0, 1.0, 4259.0, 33.0, 8115.0, 2645.0, 1860.0, 10538.0, 3304.0, 2641.0, 2633.0, 2037.0, 2659.0, 2956.0, 5004.0, 7505.0, 1841.0, 2111.0, 16907.0, 26405.0, 25064.0, 28750.0, 29678.0, 11901.0, 1.0, 3033.0, 1.0, 2626.0, 1.0, 4116.0, 1.0, 2940.0, 1290.0, 1133.0, 3187.0, 1.0, 3840.0, 1.0, 4725.0, 1.0, 6065.0, 9981.0, 2546.0, 2724.0, 2191.0, 1776.0, 1305.0, 1360.0, 1627.0, 1814.0, 2902.0, 2676.0, 1647.0, 3958.0, 7127.0, 2737.0, 2833.0, 2225.0, 3694.0, 3900.0, 2680.0, 2113.0, 7159.0, 1.0, 990.0, 2207.0, 19908.0, 27243.0, 28200.0, 4928.0, 1571.0, 2658.0, 2985.0, 2400.0, 2714.0, 21861.0, 33003.0, 28851.0, 27944.0, 28963.0, 29481.0, 30198.0, 27467.0, 29548.0, 27769.0, 30493.0, 33759.0, 29292.0, 28322.0, 32345.0, 35919.0, 45478.0, 27609.0, 29354.0, 29624.0, 34913.0, 39965.0, 45255.0, 41695.0, 26403.0, 32826.0, 44250.0, 30605.0, 45121.0, 46378.0, 48741.0, 35606.0, 28449.0, 26745.0, 30086.0, 37858.0, 45383.0, 47644.0, 37716.0, 26770.0, 31942.0, 35712.0, 26749.0, 28803.0, 30076.0, 30438.0, 27778.0, 28749.0, 46317.0, 31694.0, 25455.0, 34214.0, 31063.0, 34276.0, 45640.0, 45119.0, 36992.0, 28255.0, 28832.0, 28105.0, 41351.0, 31619.0, 36461.0, 32623.0, 33775.0, 8289.0, 7523.0, 8084.0, 22030.0, 19200.0, 19365.0, 46151.0, 46076.0, 38530.0, 36872.0, 37655.0, 28398.0, 28653.0, 39994.0, 34711.0, 30683.0, 41269.0, 30471.0, 29673.0, 28851.0, 34898.0, 28438.0, 30660.0, 27363.0, 28172.0, 43593.0, 31251.0, 29514.0, 29394.0, 29347.0, 28907.0, 28604.0, 29310.0, 30923.0, 31564.0, 27915.0, 8645.0, 7087.0, 19557.0, 11889.0, 12849.0, 3997.0, 3828.0, 3527.0, 14277.0, 28665.0, 29458.0, 29566.0, 28751.0, 32067.0, 30264.0, 29213.0, 29104.0, 31630.0, 29192.0, 29911.0, 30321.0, 38780.0, 39554.0, 26929.0, 30054.0, 28589.0, 29840.0, 26697.0, 32078.0, 27868.0, 29179.0, 32338.0, 40830.0, 36375.0, 32421.0, 28409.0, 38599.0, 30741.0, 32856.0, 28315.0, 28318.0, 28239.0, 30339.0, 45271.0, 48588.0, 42137.0, 27546.0, 43773.0, 48246.0, 47853.0, 29107.0, 26453.0, 14688.0, 3809.0, 3098.0, 4157.0, 9825.0, 5120.0, 2743.0, 13102.0, 32302.0, 29103.0, 29470.0, 30411.0, 28583.0, 30096.0, 11127.0, 7373.0, 28314.0, 28232.0, 29078.0, 31810.0, 26941.0, 28039.0, 28242.0, 31409.0, 38417.0, 42137.0, 26468.0, 42642.0, 45522.0, 45388.0, 33563.0, 28033.0, 26229.0, 28227.0, 25620.0, 28476.0, 28884.0, 31441.0, 26451.0, 27635.0, 28173.0, 24053.0, 4784.0, 4927.0, 4379.0, 7130.0, 5015.0, 6200.0, 6463.0, 40614.0, 31041.0, 27046.0, 31459.0, 27376.0, 26376.0, 25581.0, 29692.0, 28055.0, 38364.0, 31762.0, 21394.0, 42712.0, 47860.0, 46491.0, 36148.0, 28560.0, 28129.0, 28727.0, 28039.0, 29518.0, 27213.0, 27465.0, 21923.0, 29164.0, 30846.0, 27181.0, 45653.0, 48480.0, 31491.0, 31435.0, 30626.0, 28958.0, 27682.0, 39517.0, 31117.0, 31345.0, 28399.0, 27361.0, 28764.0, 27573.0, 27786.0, 28514.0, 29517.0, 29450.0, 28820.0, 30422.0, 29479.0, 25971.0, 26885.0, 29556.0, 29790.0, 26950.0, 30651.0, 45919.0, 47495.0, 45361.0, 43334.0, 45464.0, 44649.0, 41822.0, 28706.0, 27073.0, 34794.0, 31027.0, 26690.0, 45262.0, 49253.0, 47315.0, 42050.0, 35808.0, 26619.0, 26141.0, 30154.0, 29478.0, 42047.0, 45653.0, 48447.0, 44740.0, 44061.0, 33061.0, 32016.0, 30282.0, 26681.0, 28515.0, 46818.0, 24177.0, 30504.0, 29302.0, 28473.0, 39892.0, 45893.0, 45524.0, 29477.0, 28148.0, 40052.0, 26725.0, 27919.0, 28816.0, 30151.0, 27623.0, 31888.0, 28504.0, 29804.0, 28245.0, 28905.0, 32231.0, 27987.0, 28651.0, 28412.0, 28873.0, 29177.0, 27856.0, 50325.0, 47542.0, 31641.0, 29127.0, 26813.0, 38855.0, 30164.0, 30976.0, 27891.0, 28209.0, 28661.0, 28898.0, 29169.0, 44513.0, 31923.0, 27030.0, 27864.0, 28805.0, 42869.0, 32690.0, 27869.0, 29387.0, 37628.0, 30560.0, 32157.0, 27758.0, 28105.0, 29788.0, 28543.0, 27047.0, 26981.0, 31348.0, 27976.0, 28741.0, 29523.0, 30425.0, 26980.0, 38196.0, 45192.0, 30692.0, 28988.0, 29676.0, 27821.0, 40493.0, 59513.0, 52500.0, 35257.0, 41091.0, 46792.0, 45535.0, 48279.0, 49222.0, 22366.0, 29671.0, 32005.0, 43571.0, 43681.0, 30188.0, 28819.0, 29433.0, 27461.0, 28866.0, 36016.0, 31323.0, 27717.0, 46296.0, 45812.0, 36740.0, 49808.0, 31713.0, 26827.0, 28351.0, 28294.0, 29656.0, 30878.0, 42020.0, 26936.0, 27840.0, 29108.0, 27842.0, 37289.0, 26516.0, 29165.0, 32600.0, 27779.0, 26919.0, 34353.0, 36498.0, 29072.0, 29308.0, 28672.0, 43753.0, 28379.0, 28247.0, 27405.0, 28894.0, 28993.0, 29526.0, 28968.0, 29711.0, 30019.0, 28613.0, 33288.0, 40885.0, 48376.0, 42083.0, 32239.0, 27351.0, 27984.0, 30671.0, 28222.0, 36161.0, 28339.0, 34190.0, 46330.0, 44560.0, 43701.0, 28498.0, 27524.0, 31912.0, 31448.0, 28903.0, 28820.0, 29519.0, 28813.0, 29321.0, 31713.0, 29820.0, 32574.0, 27679.0, 28481.0, 30034.0, 30480.0, 26063.0, 30642.0, 32079.0, 28870.0, 32774.0, 27520.0, 28164.0, 30147.0, 29727.0, 44905.0, 46406.0, 33070.0, 34007.0, 27553.0, 28637.0, 28293.0, 28019.0, 32451.0, 36713.0, 33047.0, 29807.0, 28173.0, 28871.0, 40809.0, 41594.0, 33448.0, 33198.0, 43276.0, 28102.0, 30611.0, 28729.0, 27542.0, 27900.0, 29839.0, 26671.0, 29179.0, 28302.0, 28559.0, 25928.0, 30613.0, 30881.0, 45131.0, 45315.0, 46905.0, 46978.0, 45552.0, 628.0, 2004.0, 6515.0, 8812.0, 3301.0, 17261.0, 10168.0, 20808.0, 7052.0, 30863.0, 35282.0, 44931.0, 29737.0, 36236.0, 32074.0, 26036.0, 27570.0, 32381.0, 31841.0, 31217.0, 47200.0, 46669.0, 43233.0, 44967.0, 30351.0, 30198.0, 26851.0, 33539.0, 45569.0, 28294.0, 44592.0, 26147.0, 25985.0, 29805.0, 28956.0, 28361.0, 28508.0, 27213.0, 29454.0, 27563.0, 27665.0, 30781.0, 42582.0, 45745.0, 30692.0, 27865.0, 31148.0, 27992.0, 28604.0, 42631.0, 46569.0, 46355.0, 42138.0, 47538.0, 29506.0, 28234.0, 28281.0, 35883.0, 41292.0, 32025.0, 48719.0, 43863.0, 30531.0, 31359.0, 30690.0, 29299.0, 27584.0, 27633.0, 32283.0, 45097.0, 42245.0, 30216.0, 36779.0, 35629.0, 32493.0, 33042.0, 30957.0, 30153.0, 27519.0, 37322.0, 30215.0, 29908.0, 27141.0, 31976.0, 29285.0, 30222.0, 28195.0, 50407.0, 46192.0, 47790.0, 41160.0, 26695.0, 25880.0, 29087.0, 38870.0, 49798.0, 34883.0, 27547.0, 44651.0, 48833.0, 45477.0, 37048.0, 29034.0, 28033.0, 25471.0, 42142.0, 43759.0, 21472.0, 11982.0, 4903.0, 3714.0, 5115.0, 5483.0, 5169.0, 11106.0, 31713.0, 30176.0, 33385.0, 40798.0, 47311.0, 44008.0, 46701.0, 29665.0, 29322.0, 27527.0, 31031.0, 44645.0, 45042.0, 46571.0, 30189.0, 30241.0, 28425.0, 29990.0, 27143.0, 27490.0, 28441.0, 29196.0, 28076.0, 37627.0, 27917.0, 28461.0, 28474.0, 26493.0, 26887.0, 29111.0, 29533.0, 31087.0, 29965.0, 4579.0, 4792.0, 3614.0, 4332.0, 3848.0, 5355.0, 4594.0, 6365.0, 3749.0, 4133.0, 1.0, 1.0, 1.0, 606.0, 1.0, 218.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 156.0, 853.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1214.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3475.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1376.0, 2003.0, 2150.0, 2105.0, 2138.0, 2115.0, 2175.0, 2122.0, 2122.0, 2141.0, 2202.0, 2209.0, 2166.0, 2186.0, 2229.0, 2140.0, 2282.0, 2128.0, 2247.0, 2269.0, 2451.0, 2326.0, 2344.0, 2372.0, 2340.0, 2289.0, 2242.0, 2358.0, 2281.0, 2257.0, 2345.0, 2311.0, 2319.0, 2322.0, 2298.0, 2312.0, 2324.0, 2277.0, 2281.0, 2316.0, 2234.0, 2213.0, 2311.0, 2287.0, 2230.0, 2268.0, 2413.0, 2274.0, 2336.0, 2244.0, 2277.0, 2284.0, 2336.0, 2232.0, 2269.0, 2142.0, 2247.0, 2294.0, 2237.0, 2319.0, 2307.0, 2256.0, 2341.0, 2318.0, 2347.0, 2245.0, 2276.0, 2270.0, 2281.0, 2248.0, 2286.0, 2214.0, 2366.0, 2287.0, 2282.0, 2313.0, 2335.0, 2410.0, 2258.0, 2334.0, 2365.0, 2304.0, 2247.0, 2318.0, 2344.0, 2305.0, 2364.0, 2293.0, 2328.0, 2347.0, 2255.0, 2367.0, 2247.0, 2270.0, 2358.0, 2351.0, 2293.0, 2298.0, 2299.0, 2358.0, 2347.0, 2387.0, 2254.0, 2348.0, 2389.0, 2173.0, 2295.0, 2422.0, 2314.0, 2343.0, 2370.0, 2384.0, 2217.0, 2365.0, 2295.0, 2422.0, 2348.0, 2295.0, 2316.0, 2376.0, 2328.0, 2316.0, 2359.0, 2321.0, 2349.0, 2249.0, 2292.0, 2380.0, 2320.0, 2365.0, 2336.0, 2321.0, 2376.0, 2316.0, 2348.0, 2427.0, 2356.0, 2461.0, 2324.0, 2347.0, 2317.0, 2466.0, 2391.0, 2331.0, 2412.0, 2313.0, 2354.0, 2336.0, 2368.0, 2321.0, 2410.0, 2266.0, 2368.0, 2270.0, 2342.0, 2354.0, 2347.0, 2397.0, 2298.0, 2359.0, 2337.0, 2264.0, 2362.0, 2349.0, 2318.0, 2262.0, 2232.0, 2332.0, 2253.0, 2403.0, 2369.0, 2327.0, 2393.0, 2347.0, 2253.0, 2301.0, 2326.0, 2339.0, 2217.0, 2366.0, 2276.0, 2287.0, 2299.0, 2320.0, 2379.0, 2289.0, 2322.0, 2275.0, 2328.0, 2232.0, 2324.0, 2319.0, 2332.0, 2241.0, 2326.0, 2252.0, 2378.0, 2307.0, 2270.0, 2286.0, 2263.0, 2243.0, 2282.0, 2275.0, 2372.0, 2269.0, 2335.0, 2256.0, 2262.0, 2313.0, 2209.0, 2346.0, 2308.0, 2205.0, 2190.0, 2281.0, 2288.0, 2354.0, 2324.0, 2341.0, 2348.0, 2312.0, 2252.0, 2225.0, 2310.0, 2324.0, 2232.0, 2262.0, 2248.0, 2287.0, 2246.0, 2303.0, 2239.0, 2154.0, 2171.0, 2209.0, 2207.0, 2234.0, 2239.0, 2196.0, 2023.0, 2625.0, 3457.0, 3665.0, 3719.0, 3666.0, 3793.0, 3740.0, 3609.0, 3677.0, 3751.0, 3757.0, 3683.0, 3681.0, 3658.0, 3626.0, 3665.0, 3690.0, 3672.0, 3683.0, 3737.0, 3560.0, 3110.0, 2124.0, 2436.0, 5621.0, 20552.0, 20977.0, 21319.0, 20915.0, 20902.0, 19477.0, 13591.0, 7806.0, 8688.0, 10390.0, 9697.0, 9575.0, 10417.0, 9328.0, 8946.0, 10016.0, 10407.0, 10169.0, 10885.0, 9176.0, 10170.0, 10347.0, 10440.0, 7865.0, 1492.0, 11393.0, 15038.0, 19981.0, 20185.0, 20185.0, 19910.0, 19843.0, 20249.0, 20184.0, 19728.0, 19959.0, 19462.0, 19314.0, 20028.0, 19516.0, 19891.0, 19267.0, 19965.0, 20136.0, 20029.0, 20108.0, 20265.0, 19999.0, 19761.0, 20380.0, 20183.0, 19431.0, 20470.0, 19950.0, 20339.0, 19927.0, 19632.0, 19757.0, 19771.0, 19598.0, 19663.0, 19488.0, 19972.0, 20401.0, 19916.0, 19949.0, 20277.0, 19857.0, 20022.0, 20062.0, 19812.0, 20332.0, 19844.0, 19860.0, 18870.0, 20323.0, 20178.0, 19927.0, 20164.0, 19964.0, 20212.0, 21963.0, 20144.0, 18500.0, 20094.0, 20966.0, 20093.0, 16948.0, 19450.0, 20410.0, 19963.0, 19759.0, 17562.0, 20164.0, 20163.0, 20321.0, 19643.0, 20121.0, 20038.0, 18647.0, 15839.0, 18472.0, 19726.0, 19666.0, 22436.0, 22626.0, 25581.0, 25691.0, 25793.0, 22765.0, 22660.0, 20436.0, 20560.0, 22017.0, 25146.0, 24493.0, 22996.0, 25092.0, 25451.0, 23324.0, 20137.0, 20018.0, 19878.0, 21149.0, 21037.0, 20040.0, 19548.0, 20765.0, 19778.0, 18996.0, 19744.0, 21472.0, 21839.0, 18649.0, 16974.0, 20699.0, 20644.0, 19954.0, 20139.0, 19932.0, 20196.0, 20567.0, 19989.0, 19949.0, 20093.0, 19899.0, 20240.0, 19964.0, 19522.0, 20168.0, 20379.0, 17639.0, 17319.0, 16971.0, 18438.0, 19462.0, 19928.0, 19634.0, 18716.0, 18079.0, 18539.0, 16889.0, 17438.0, 17933.0, 17217.0, 17894.0, 17461.0, 17287.0, 16682.0, 17365.0, 16786.0, 18771.0, 20778.0, 20463.0, 20366.0, 20214.0, 20399.0, 20314.0, 22331.0, 23353.0, 22819.0, 20685.0, 18981.0, 22050.0, 22366.0, 20745.0, 19264.0, 17526.0, 19516.0, 20519.0, 20511.0, 20416.0, 20017.0, 20031.0, 20096.0, 19878.0, 19848.0, 19975.0, 19300.0, 20620.0, 22921.0, 22749.0, 21188.0, 18749.0, 16538.0, 20325.0, 21005.0, 20458.0, 20031.0, 20570.0, 20023.0, 19262.0, 18417.0, 18604.0, 18548.0, 18959.0, 18425.0, 18617.0, 18561.0, 18398.0, 18524.0, 17950.0, 18803.0, 18643.0, 18486.0, 17831.0, 18676.0, 18759.0, 18363.0, 18436.0, 18609.0, 18062.0, 18616.0, 18361.0, 18555.0, 18518.0, 18829.0, 19064.0, 18726.0, 18750.0, 18660.0, 18407.0, 18579.0, 19533.0, 18758.0, 18583.0, 18407.0, 18368.0, 18701.0, 18476.0, 17997.0, 18322.0, 18639.0, 18280.0, 18159.0, 18106.0, 18500.0, 18618.0, 18516.0, 18631.0, 18595.0, 18745.0, 18941.0, 18413.0, 18527.0, 19544.0, 30012.0, 19337.0, 19818.0, 19565.0, 19392.0, 19412.0, 19466.0, 19050.0, 19430.0, 19840.0, 19672.0, 19389.0, 19998.0, 19648.0, 18899.0, 18687.0, 19596.0, 19602.0, 19604.0, 19594.0, 19713.0, 19901.0, 19607.0, 19960.0, 19913.0, 19610.0, 18696.0, 824.0, 336.0, 296.0, 343.0, 341.0, 322.0, 300.0, 602.0, 191.0, 263.0, 106.0, 673.0, 1076.0, 2.0, 11.0, 38.0, 38.0, 29.0, 139.0, 4120.0, 6212.0, 33.0, 34.0, 28.0, 24.0, 2150.0, 2232.0, 6520.0, 8426.0, 8497.0, 8340.0, 8206.0, 8253.0, 8260.0, 8322.0, 8054.0, 8195.0, 8346.0, 8324.0, 8328.0, 8302.0, 8144.0, 8106.0, 8214.0, 8262.0, 8097.0, 8250.0, 8310.0, 8281.0, 8004.0, 8122.0, 8745.0, 8791.0, 8488.0, 8492.0, 8150.0, 8308.0, 8319.0, 8362.0, 8357.0, 8337.0, 8127.0, 8262.0, 8327.0, 8110.0, 8218.0, 8288.0, 8349.0, 8258.0, 8116.0, 8170.0, 8313.0, 8240.0, 8355.0, 8260.0, 8131.0, 8323.0, 8338.0, 8376.0, 8259.0, 8122.0, 8187.0, 8092.0, 8219.0, 8085.0, 8334.0, 8170.0, 8240.0, 8101.0, 8360.0, 8247.0, 8107.0, 8317.0, 8261.0, 8225.0, 8288.0, 8125.0, 8383.0, 8162.0, 8686.0, 13276.0, 14863.0, 21346.0, 23932.0, 23936.0, 24045.0, 24690.0, 24347.0, 24061.0, 23977.0, 24450.0, 24492.0, 24755.0, 25005.0, 25382.0, 25495.0, 25216.0, 25327.0, 24303.0, 23448.0, 25138.0, 24743.0, 25284.0, 24260.0, 22683.0, 26148.0, 25283.0, 24742.0, 25018.0, 25203.0, 25390.0, 24233.0, 21448.0, 23992.0, 24525.0, 24127.0, 24824.0, 25673.0, 25907.0, 25517.0, 26033.0, 26060.0, 24597.0, 25785.0, 25747.0, 25966.0, 26261.0, 26221.0, 25104.0, 25417.0, 26051.0, 25864.0, 25810.0, 25849.0, 25616.0, 25850.0, 25407.0, 25790.0, 25469.0, 25727.0, 25657.0, 25860.0, 25469.0, 25601.0, 25313.0, 25534.0, 25575.0, 25897.0, 25526.0, 25734.0, 25611.0, 25444.0, 25838.0, 25950.0, 25156.0, 25588.0, 25746.0, 25901.0, 25491.0, 24245.0, 24971.0, 25559.0, 25855.0, 26076.0, 25662.0, 25476.0, 25464.0, 25228.0, 25648.0, 25946.0, 25581.0, 24873.0, 25730.0, 25559.0, 25735.0, 25859.0, 25619.0, 26115.0, 25817.0, 25832.0, 25715.0, 24440.0, 24289.0, 25631.0, 25798.0, 25762.0, 25257.0, 25246.0, 25194.0, 25633.0, 25839.0, 25211.0, 25563.0, 25887.0, 25970.0, 25634.0, 25607.0, 25539.0, 25864.0, 25896.0, 25525.0, 25654.0, 25902.0, 25530.0, 25689.0, 25894.0, 25512.0, 25679.0, 25284.0, 25671.0, 25689.0, 25550.0, 26639.0, 27691.0, 27588.0, 27514.0, 27219.0, 25490.0, 27477.0, 28100.0, 27851.0, 27764.0, 28041.0, 24982.0, 26902.0, 27914.0, 28018.0, 27932.0, 27649.0, 28018.0, 27650.0, 27800.0, 25910.0, 26860.0, 27641.0, 27486.0, 27618.0, 27623.0, 25157.0, 27581.0, 23392.0, 27225.0, 27851.0, 24404.0, 26454.0, 27670.0, 27813.0, 28028.0, 27838.0, 27401.0, 24474.0, 27690.0, 27963.0, 27589.0, 27792.0, 28085.0, 27595.0, 27899.0, 27534.0, 27826.0, 27831.0, 24522.0, 24208.0, 23577.0, 27499.0, 25804.0, 27423.0, 27706.0, 27955.0, 27480.0, 27979.0, 27824.0, 27779.0, 27950.0, 27698.0, 27537.0, 27719.0, 27656.0, 27715.0, 27685.0, 27717.0, 27911.0, 27680.0, 27234.0, 27635.0, 27686.0, 27941.0, 27778.0, 27752.0, 27747.0, 27777.0, 27844.0, 27626.0, 27745.0, 27463.0, 27733.0, 27885.0, 27480.0, 27761.0, 27711.0, 27535.0, 27843.0, 28072.0, 27555.0, 27703.0, 27651.0, 27660.0, 27651.0, 27755.0, 28026.0, 27743.0, 27295.0, 27894.0, 27898.0, 28191.0, 27860.0, 27924.0, 27784.0, 27858.0, 27969.0, 27563.0, 27702.0, 28115.0, 27863.0, 27775.0, 28118.0, 27937.0, 27598.0, 23312.0, 28067.0, 32093.0, 30933.0, 29824.0, 29240.0, 28546.0, 28469.0, 28467.0, 28237.0, 27962.0, 28370.0, 27622.0, 27918.0, 27800.0, 28040.0, 27844.0, 27892.0, 27835.0, 28029.0, 27845.0, 27729.0, 27762.0, 28103.0, 27436.0, 27474.0, 27702.0, 27118.0, 26250.0, 26693.0, 27387.0, 27608.0, 27674.0, 27539.0, 27762.0, 27350.0, 27497.0, 27756.0, 27388.0, 26928.0, 27705.0, 27632.0, 27442.0, 27502.0, 27236.0, 27584.0, 27194.0, 27287.0, 27603.0, 27100.0, 27084.0, 27363.0, 27705.0, 27261.0, 27463.0, 27487.0, 27515.0, 27305.0, 27197.0, 27370.0, 27490.0, 27360.0, 27111.0, 27468.0, 27425.0, 27759.0, 27635.0, 27271.0, 27555.0, 27234.0, 27423.0, 27337.0, 27375.0, 27419.0, 27241.0, 27410.0, 27302.0, 27641.0, 27594.0, 27578.0, 27534.0, 27849.0, 27532.0, 27423.0, 27517.0, 27468.0, 27509.0, 27484.0, 27075.0, 27624.0, 27112.0, 27620.0, 27667.0, 27765.0, 27961.0, 27801.0, 27978.0, 27553.0, 27380.0, 28109.0, 27440.0, 27885.0, 27174.0, 27208.0, 27738.0, 27437.0, 27494.0, 27733.0, 27747.0, 28309.0, 28867.0, 28169.0, 27842.0, 27610.0, 27736.0, 28092.0, 27438.0, 27822.0, 27732.0, 27426.0, 27687.0, 27480.0, 27527.0, 27529.0, 27527.0, 27523.0, 27672.0, 27277.0, 27486.0, 27900.0, 27280.0, 28614.0, 29898.0, 30222.0, 30208.0, 29997.0, 29948.0, 29757.0, 29985.0, 29937.0, 29701.0, 30287.0, 30001.0, 30138.0, 30204.0, 30162.0, 30169.0, 30233.0, 30277.0, 29927.0, 29980.0, 30053.0, 29904.0, 29742.0, 27305.0, 25477.0, 30932.0, 28630.0, 26991.0, 26855.0, 26836.0, 26064.0, 25610.0, 25345.0, 24925.0, 24955.0, 25081.0, 25567.0, 25412.0, 25749.0, 27408.0, 28155.0, 28353.0, 29373.0, 28508.0, 28470.0, 28223.0, 28189.0, 28312.0, 28280.0, 28436.0, 28156.0, 28710.0, 28114.0, 29161.0, 29517.0, 29285.0, 28625.0, 28952.0, 28698.0, 28605.0, 28376.0, 28255.0, 28269.0, 28063.0, 28665.0, 28887.0, 29228.0, 28724.0, 28750.0, 29907.0, 30207.0, 29815.0, 29562.0, 29563.0, 29480.0, 28641.0, 27890.0, 25625.0, 25059.0, 24349.0, 24122.0, 25241.0, 25869.0, 25509.0, 24781.0, 24642.0, 24645.0, 24889.0, 24545.0, 24020.0, 24494.0, 24219.0, 24628.0, 24863.0, 25430.0, 25593.0, 25522.0, 25442.0, 25056.0, 25606.0, 25545.0, 25917.0, 26256.0, 25876.0, 26502.0, 26523.0, 25854.0, 25732.0, 26012.0, 25303.0, 25457.0, 24272.0, 23707.0, 23816.0, 24005.0, 24235.0, 23367.0, 23751.0, 23320.0, 23608.0, 23982.0, 23868.0, 23612.0, 23237.0, 23230.0, 26592.0, 28358.0, 26035.0, 25335.0, 25033.0, 24812.0, 24448.0, 24332.0, 24545.0, 23950.0, 24284.0, 24066.0, 24769.0, 24779.0, 24238.0, 24539.0, 24292.0, 23907.0, 22688.0, 23803.0, 24932.0, 24338.0, 24118.0, 23403.0, 24194.0, 24444.0, 25200.0, 24702.0, 24212.0, 23702.0, 23546.0, 23113.0, 23031.0, 23091.0, 22626.0, 22882.0, 23005.0, 22976.0, 22598.0, 22509.0, 22539.0, 22711.0, 23027.0, 23154.0, 22870.0, 22724.0, 22879.0, 22912.0, 22578.0, 22922.0, 23593.0, 23570.0, 23981.0, 24315.0, 25228.0, 29491.0, 29394.0, 29854.0, 29395.0, 28989.0, 29217.0, 27912.0, 28298.0, 29000.0, 29286.0, 28487.0, 28201.0, 28683.0, 28184.0, 28403.0, 28489.0, 28005.0, 25254.0, 26179.0, 33751.0, 32649.0, 31705.0, 30902.0, 29252.0, 29201.0, 29136.0, 28474.0, 28380.0, 27898.0, 26573.0, 29273.0, 29894.0, 29524.0, 29087.0, 28908.0, 28956.0, 28215.0, 28690.0, 28283.0, 25261.0, 820.0, 239.0, 275.0, 251.0, 239.0, 265.0, 244.0, 239.0, 224.0, 258.0, 109.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 7.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 4.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 275.0, 153.0, 20.0, 1.0, 5.0, 431.0, 9.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 4.0, 253.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "val_status": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"label": "环比1天", "metric_val": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 253.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 9.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 113.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 4.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 49.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 55.0, 254.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.0, 4476.0, 17336.0, 25704.0, 12855.0, 6926.0, 1.0, 5941.0, 24.0, 6552.0, 2.0, 5012.0, 1.0, 6845.0, 1680.0, 2006.0, 2770.0, 5.0, 7729.0, 1.0, 4334.0, 1.0, 7432.0, 613.0, 6737.0, 2.0, 5632.0, 1.0, 5566.0, 138.0, 2882.0, 1750.0, 1064.0, 4214.0, 1.0, 8013.0, 1.0, 7446.0, 1.0, 5990.0, 1.0, 5826.0, 1.0, 5762.0, 1.0, 4688.0, 1414.0, 1899.0, 2976.0, 1.0, 27645.0, 23263.0, 7364.0, 1480.0, 1465.0, 6163.0, 3822.0, 15954.0, 648.0, 12709.0, 2295.0, 11957.0, 7777.0, 4373.0, 1541.0, 2650.0, 2597.0, 3045.0, 2349.0, 2453.0, 2310.0, 4873.0, 13288.0, 5527.0, 2671.0, 1747.0, 2486.0, 5157.0, 16189.0, 22410.0, 22742.0, 18871.0, 23082.0, 18314.0, 20040.0, 13323.0, 5565.0, 1910.0, 1432.0, 11830.0, 23333.0, 15319.0, 3189.0, 2634.0, 2439.0, 3178.0, 2264.0, 1631.0, 3832.0, 1601.0, 12953.0, 11331.0, 2093.0, 1502.0, 2581.0, 3186.0, 10054.0, 14197.0, 4311.0, 22097.0, 2151.0, 1818.0, 2810.0, 2235.0, 1532.0, 1990.0, 5527.0, 4585.0, 5858.0, 2730.0, 5242.0, 7954.0, 1558.0, 2555.0, 5910.0, 6030.0, 2341.0, 7559.0, 2586.0, 7389.0, 3806.0, 4883.0, 21010.0, 4156.0, 2044.0, 5232.0, 2254.0, 2356.0, 1760.0, 1917.0, 2603.0, 2093.0, 1582.0, 1510.0, 3930.0, 3615.0, 6984.0, 5506.0, 16925.0, 26129.0, 4723.0, 1364.0, 3203.0, 5563.0, 2304.0, 19036.0, 21511.0, 2095.0, 2126.0, 15641.0, 20919.0, 12702.0, 2443.0, 27609.0, 22604.0, 6400.0, 4031.0, 2584.0, 7252.0, 3449.0, 5338.0, 1708.0, 1572.0, 7679.0, 13843.0, 5527.0, 2459.0, 3389.0, 8015.0, 4160.0, 3145.0, 3587.0, 2063.0, 2402.0, 2423.0, 17603.0, 8975.0, 2170.0, 2488.0, 1736.0, 2211.0, 3282.0, 2100.0, 2351.0, 7476.0, 2809.0, 2631.0, 2097.0, 2793.0, 2479.0, 2971.0, 11915.0, 18902.0, 26458.0, 14540.0, 2281.0, 2504.0, 2875.0, 2832.0, 2462.0, 2140.0, 2203.0, 1900.0, 1515.0, 4715.0, 2895.0, 5008.0, 1937.0, 1450.0, 2076.0, 1503.0, 1678.0, 2382.0, 1735.0, 5807.0, 11129.0, 2164.0, 2261.0, 2712.0, 3258.0, 3686.0, 15565.0, 38680.0, 28328.0, 23133.0, 1911.0, 2394.0, 17869.0, 2795.0, 2338.0, 1630.0, 2069.0, 2421.0, 2367.0, 2057.0, 2594.0, 2063.0, 4101.0, 3549.0, 1758.0, 6811.0, 1640.0, 2242.0, 2051.0, 2568.0, 4509.0, 1820.0, 1642.0, 2111.0, 1403.0, 6519.0, 3037.0, 4612.0, 2565.0, 1886.0, 1551.0, 1690.0, 2487.0, 3294.0, 2135.0, 2400.0, 3599.0, 2312.0, 2104.0, 2670.0, 2511.0, 2560.0, 1967.0, 1498.0, 3732.0, 5289.0, 2587.0, 1405.0, 1020.0, 1118.0, 1163.0, 1158.0, 1316.0, 1984.0, 1940.0, 1823.0, 2389.0, 1418.0, 1892.0, 1530.0, 2094.0, 2396.0, 1296.0, 2254.0, 22362.0, 1686.0, 1610.0, 2423.0, 1823.0, 2306.0, 3274.0, 2008.0, 3547.0, 9936.0, 26996.0, 27175.0, 10214.0, 1758.0, 2262.0, 2095.0, 2313.0, 3786.0, 2042.0, 7756.0, 23461.0, 4060.0, 1919.0, 1990.0, 2392.0, 3600.0, 3123.0, 2200.0, 18258.0, 26926.0, 26731.0, 26945.0, 26408.0, 13133.0, 3138.0, 19566.0, 13605.0, 2072.0, 1817.0, 3092.0, 1940.0, 1478.0, 1310.0, 2434.0, 1415.0, 1471.0, 1771.0, 2055.0, 1671.0, 2388.0, 1522.0, 1984.0, 2188.0, 2724.0, 1862.0, 21709.0, 12762.0, 2284.0, 12249.0, 5234.0, 2029.0, 1.0, 2540.0, 1.0, 2519.0, 1.0, 3812.0, 1.0, 3023.0, 1423.0, 1543.0, 2981.0, 1.0, 5270.0, 1.0, 3834.0, 1.0, 4259.0, 33.0, 8115.0, 2645.0, 1860.0, 10538.0, 3304.0, 2641.0, 2633.0, 2037.0, 2659.0, 2956.0, 5004.0, 7505.0, 1841.0, 2111.0, 16907.0, 26405.0, 25064.0, 28750.0, 29678.0, 11901.0, 1.0, 3033.0, 1.0, 2626.0, 1.0, 4116.0, 1.0, 2940.0, 1290.0, 1133.0, 3187.0, 1.0, 3840.0, 1.0, 4725.0, 1.0, 6065.0, 9981.0, 2546.0, 2724.0, 2191.0, 1776.0, 1305.0, 1360.0, 1627.0, 1814.0, 2902.0, 2676.0, 1647.0, 3958.0, 7127.0, 2737.0, 2833.0, 2225.0, 3694.0, 3900.0, 2680.0, 2113.0, 7159.0, 1.0, 990.0, 2207.0, 19908.0, 27243.0, 28200.0, 4928.0, 1571.0, 2658.0, 2985.0, 2400.0, 2714.0, 21861.0, 33003.0, 28851.0, 27944.0, 28963.0, 29481.0, 30198.0, 27467.0, 29548.0, 27769.0, 30493.0, 33759.0, 29292.0, 28322.0, 32345.0, 35919.0, 45478.0, 27609.0, 29354.0, 29624.0, 34913.0, 39965.0, 45255.0, 41695.0, 26403.0, 32826.0, 44250.0, 30605.0, 45121.0, 46378.0, 48741.0, 35606.0, 28449.0, 26745.0, 30086.0, 37858.0, 45383.0, 47644.0, 37716.0, 26770.0, 31942.0, 35712.0, 26749.0, 28803.0, 30076.0, 30438.0, 27778.0, 28749.0, 46317.0, 31694.0, 25455.0, 34214.0, 31063.0, 34276.0, 45640.0, 45119.0, 36992.0, 28255.0, 28832.0, 28105.0, 41351.0, 31619.0, 36461.0, 32623.0, 33775.0, 8289.0, 7523.0, 8084.0, 22030.0, 19200.0, 19365.0, 46151.0, 46076.0, 38530.0, 36872.0, 37655.0, 28398.0, 28653.0, 39994.0, 34711.0, 30683.0, 41269.0, 30471.0, 29673.0, 28851.0, 34898.0, 28438.0, 30660.0, 27363.0, 28172.0, 43593.0, 31251.0, 29514.0, 29394.0, 29347.0, 28907.0, 28604.0, 29310.0, 30923.0, 31564.0, 27915.0, 8645.0, 7087.0, 19557.0, 11889.0, 12849.0, 3997.0, 3828.0, 3527.0, 14277.0, 28665.0, 29458.0, 29566.0, 28751.0, 32067.0, 30264.0, 29213.0, 29104.0, 31630.0, 29192.0, 29911.0, 30321.0, 38780.0, 39554.0, 26929.0, 30054.0, 28589.0, 29840.0, 26697.0, 32078.0, 27868.0, 29179.0, 32338.0, 40830.0, 36375.0, 32421.0, 28409.0, 38599.0, 30741.0, 32856.0, 28315.0, 28318.0, 28239.0, 30339.0, 45271.0, 48588.0, 42137.0, 27546.0, 43773.0, 48246.0, 47853.0, 29107.0, 26453.0, 14688.0, 3809.0, 3098.0, 4157.0, 9825.0, 5120.0, 2743.0, 13102.0, 32302.0, 29103.0, 29470.0, 30411.0, 28583.0, 30096.0, 11127.0, 7373.0, 28314.0, 28232.0, 29078.0, 31810.0, 26941.0, 28039.0, 28242.0, 31409.0, 38417.0, 42137.0, 26468.0, 42642.0, 45522.0, 45388.0, 33563.0, 28033.0, 26229.0, 28227.0, 25620.0, 28476.0, 28884.0, 31441.0, 26451.0, 27635.0, 28173.0, 24053.0, 4784.0, 4927.0, 4379.0, 7130.0, 5015.0, 6200.0, 6463.0, 40614.0, 31041.0, 27046.0, 31459.0, 27376.0, 26376.0, 25581.0, 29692.0, 28055.0, 38364.0, 31762.0, 21394.0, 42712.0, 47860.0, 46491.0, 36148.0, 28560.0, 28129.0, 28727.0, 28039.0, 29518.0, 27213.0, 27465.0, 21923.0, 29164.0, 30846.0, 27181.0, 45653.0, 48480.0, 31491.0, 31435.0, 30626.0, 28958.0, 27682.0, 39517.0, 31117.0, 31345.0, 28399.0, 27361.0, 28764.0, 27573.0, 27786.0, 28514.0, 29517.0, 29450.0, 28820.0, 30422.0, 29479.0, 25971.0, 26885.0, 29556.0, 29790.0, 26950.0, 30651.0, 45919.0, 47495.0, 45361.0, 43334.0, 45464.0, 44649.0, 41822.0, 28706.0, 27073.0, 34794.0, 31027.0, 26690.0, 45262.0, 49253.0, 47315.0, 42050.0, 35808.0, 26619.0, 26141.0, 30154.0, 29478.0, 42047.0, 45653.0, 48447.0, 44740.0, 44061.0, 33061.0, 32016.0, 30282.0, 26681.0, 28515.0, 46818.0, 24177.0, 30504.0, 29302.0, 28473.0, 39892.0, 45893.0, 45524.0, 29477.0, 28148.0, 40052.0, 26725.0, 27919.0, 28816.0, 30151.0, 27623.0, 31888.0, 28504.0, 29804.0, 28245.0, 28905.0, 32231.0, 27987.0, 28651.0, 28412.0, 28873.0, 29177.0, 27856.0, 50325.0, 47542.0, 31641.0, 29127.0, 26813.0, 38855.0, 30164.0, 30976.0, 27891.0, 28209.0, 28661.0, 28898.0, 29169.0, 44513.0, 31923.0, 27030.0, 27864.0, 28805.0, 42869.0, 32690.0, 27869.0, 29387.0, 37628.0, 30560.0, 32157.0, 27758.0, 28105.0, 29788.0, 28543.0, 27047.0, 26981.0, 31348.0, 27976.0, 28741.0, 29523.0, 30425.0, 26980.0, 38196.0, 45192.0, 30692.0, 28988.0, 29676.0, 27821.0, 40493.0, 59513.0, 52500.0, 35257.0, 41091.0, 46792.0, 45535.0, 48279.0, 49222.0, 22366.0, 29671.0, 32005.0, 43571.0, 43681.0, 30188.0, 28819.0, 29433.0, 27461.0, 28866.0, 36016.0, 31323.0, 27717.0, 46296.0, 45812.0, 36740.0, 49808.0, 31713.0, 26827.0, 28351.0, 28294.0, 29656.0, 30878.0, 42020.0, 26936.0, 27840.0, 29108.0, 27842.0, 37289.0, 26516.0, 29165.0, 32600.0, 27779.0, 26919.0, 34353.0, 36498.0, 29072.0, 29308.0, 28672.0, 43753.0, 28379.0, 28247.0, 27405.0, 28894.0, 28993.0, 29526.0, 28968.0, 29711.0, 30019.0, 28613.0, 33288.0, 40885.0, 48376.0, 42083.0, 32239.0, 27351.0, 27984.0, 30671.0, 28222.0, 36161.0, 28339.0, 34190.0, 46330.0, 44560.0, 43701.0, 28498.0, 27524.0, 31912.0, 31448.0, 28903.0, 28820.0, 29519.0, 28813.0, 29321.0, 31713.0, 29820.0, 32574.0, 27679.0, 28481.0, 30034.0, 30480.0, 26063.0, 30642.0, 32079.0, 28870.0, 32774.0, 27520.0, 28164.0, 30147.0, 29727.0, 44905.0, 46406.0, 33070.0, 34007.0, 27553.0, 28637.0, 28293.0, 28019.0, 32451.0, 36713.0, 33047.0, 29807.0, 28173.0, 28871.0, 40809.0, 41594.0, 33448.0, 33198.0, 43276.0, 28102.0, 30611.0, 28729.0, 27542.0, 27900.0, 29839.0, 26671.0, 29179.0, 28302.0, 28559.0, 25928.0, 30613.0, 30881.0, 45131.0, 45315.0, 46905.0, 46978.0, 45552.0, 628.0, 2004.0, 6515.0, 8812.0, 3301.0, 17261.0, 10168.0, 20808.0, 7052.0, 30863.0, 35282.0, 44931.0, 29737.0, 36236.0, 32074.0, 26036.0, 27570.0, 32381.0, 31841.0, 31217.0, 47200.0, 46669.0, 43233.0, 44967.0, 30351.0, 30198.0, 26851.0, 33539.0, 45569.0, 28294.0, 44592.0, 26147.0, 25985.0, 29805.0, 28956.0, 28361.0, 28508.0, 27213.0, 29454.0, 27563.0, 27665.0, 30781.0, 42582.0, 45745.0, 30692.0, 27865.0, 31148.0, 27992.0, 28604.0, 42631.0, 46569.0, 46355.0, 42138.0, 47538.0, 29506.0, 28234.0, 28281.0, 35883.0, 41292.0, 32025.0, 48719.0, 43863.0, 30531.0, 31359.0, 30690.0, 29299.0, 27584.0, 27633.0, 32283.0, 45097.0, 42245.0, 30216.0, 36779.0, 35629.0, 32493.0, 33042.0, 30957.0, 30153.0, 27519.0, 37322.0, 30215.0, 29908.0, 27141.0, 31976.0, 29285.0, 30222.0, 28195.0, 50407.0, 46192.0, 47790.0, 41160.0, 26695.0, 25880.0, 29087.0, 38870.0, 49798.0, 34883.0, 27547.0, 44651.0, 48833.0, 45477.0, 37048.0, 29034.0, 28033.0, 25471.0, 42142.0, 43759.0, 21472.0, 11982.0, 4903.0, 3714.0, 5115.0, 5483.0, 5169.0, 11106.0, 31713.0, 30176.0, 33385.0, 40798.0, 47311.0, 44008.0, 46701.0, 29665.0, 29322.0, 27527.0, 31031.0, 44645.0, 45042.0, 46571.0, 30189.0, 30241.0, 28425.0, 29990.0, 27143.0, 27490.0, 28441.0, 29196.0, 28076.0, 37627.0, 27917.0, 28461.0, 28474.0, 26493.0, 26887.0, 29111.0, 29533.0, 31087.0, 29965.0, 4579.0, 4792.0, 3614.0, 4332.0, 3848.0, 5355.0, 4594.0, 6365.0, 3749.0, 4133.0, 1.0, 1.0, 1.0, 606.0, 1.0, 218.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 156.0, 853.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1214.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3475.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1376.0, 2003.0, 2150.0, 2105.0, 2138.0, 2115.0, 2175.0, 2122.0, 2122.0, 2141.0, 2202.0, 2209.0, 2166.0, 2186.0, 2229.0, 2140.0, 2282.0, 2128.0, 2247.0, 2269.0, 2451.0, 2326.0, 2344.0, 2372.0, 2340.0, 2289.0, 2242.0, 2358.0, 2281.0, 2257.0, 2345.0, 2311.0, 2319.0, 2322.0, 2298.0, 2312.0, 2324.0, 2277.0, 2281.0, 2316.0, 2234.0, 2213.0, 2311.0, 2287.0, 2230.0, 2268.0, 2413.0, 2274.0, 2336.0, 2244.0, 2277.0, 2284.0, 2336.0, 2232.0, 2269.0, 2142.0, 2247.0, 2294.0, 2237.0, 2319.0, 2307.0, 2256.0, 2341.0, 2318.0, 2347.0, 2245.0, 2276.0, 2270.0, 2281.0, 2248.0, 2286.0, 2214.0, 2366.0, 2287.0, 2282.0, 2313.0, 2335.0, 2410.0, 2258.0, 2334.0, 2365.0, 2304.0, 2247.0, 2318.0, 2344.0, 2305.0, 2364.0, 2293.0, 2328.0, 2347.0, 2255.0, 2367.0, 2247.0, 2270.0, 2358.0, 2351.0, 2293.0, 2298.0, 2299.0, 2358.0, 2347.0, 2387.0, 2254.0, 2348.0, 2389.0, 2173.0, 2295.0, 2422.0, 2314.0, 2343.0, 2370.0, 2384.0, 2217.0, 2365.0, 2295.0, 2422.0, 2348.0, 2295.0, 2316.0, 2376.0, 2328.0, 2316.0, 2359.0, 2321.0, 2349.0, 2249.0, 2292.0, 2380.0, 2320.0, 2365.0, 2336.0, 2321.0, 2376.0, 2316.0, 2348.0, 2427.0, 2356.0, 2461.0, 2324.0, 2347.0, 2317.0, 2466.0, 2391.0, 2331.0, 2412.0, 2313.0, 2354.0, 2336.0, 2368.0, 2321.0, 2410.0, 2266.0, 2368.0, 2270.0, 2342.0, 2354.0, 2347.0, 2397.0, 2298.0, 2359.0, 2337.0, 2264.0, 2362.0, 2349.0, 2318.0, 2262.0, 2232.0, 2332.0, 2253.0, 2403.0, 2369.0, 2327.0, 2393.0, 2347.0, 2253.0, 2301.0, 2326.0, 2339.0, 2217.0, 2366.0, 2276.0, 2287.0, 2299.0, 2320.0, 2379.0, 2289.0, 2322.0, 2275.0, 2328.0, 2232.0, 2324.0, 2319.0, 2332.0, 2241.0, 2326.0, 2252.0, 2378.0, 2307.0, 2270.0, 2286.0, 2263.0, 2243.0, 2282.0, 2275.0, 2372.0, 2269.0, 2335.0, 2256.0, 2262.0, 2313.0, 2209.0, 2346.0, 2308.0, 2205.0, 2190.0, 2281.0, 2288.0, 2354.0, 2324.0, 2341.0, 2348.0, 2312.0, 2252.0, 2225.0, 2310.0, 2324.0, 2232.0, 2262.0, 2248.0, 2287.0, 2246.0, 2303.0, 2239.0, 2154.0, 2171.0, 2209.0, 2207.0, 2234.0, 2239.0, 2196.0, 2023.0, 2625.0, 3457.0, 3665.0, 3719.0, 3666.0, 3793.0, 3740.0, 3609.0, 3677.0, 3751.0, 3757.0, 3683.0, 3681.0, 3658.0, 3626.0, 3665.0, 3690.0, 3672.0, 3683.0, 3737.0, 3560.0, 3110.0, 2124.0, 2436.0, 5621.0, 20552.0, 20977.0, 21319.0, 20915.0, 20902.0, 19477.0, 13591.0, 7806.0, 8688.0, 10390.0, 9697.0, 9575.0, 10417.0, 9328.0, 8946.0, 10016.0, 10407.0, 10169.0, 10885.0, 9176.0, 10170.0, 10347.0, 10440.0, 7865.0, 1492.0, 11393.0, 15038.0, 19981.0, 20185.0, 20185.0, 19910.0, 19843.0, 20249.0, 20184.0, 19728.0, 19959.0, 19462.0, 19314.0, 20028.0, 19516.0, 19891.0, 19267.0, 19965.0, 20136.0, 20029.0, 20108.0, 20265.0, 19999.0, 19761.0, 20380.0, 20183.0, 19431.0, 20470.0, 19950.0, 20339.0, 19927.0, 19632.0, 19757.0, 19771.0, 19598.0, 19663.0, 19488.0, 19972.0, 20401.0, 19916.0, 19949.0, 20277.0, 19857.0, 20022.0, 20062.0, 19812.0, 20332.0, 19844.0, 19860.0, 18870.0, 20323.0, 20178.0, 19927.0, 20164.0, 19964.0, 20212.0, 21963.0, 20144.0, 18500.0, 20094.0, 20966.0, 20093.0, 16948.0, 19450.0, 20410.0, 19963.0, 19759.0, 17562.0, 20164.0, 20163.0, 20321.0, 19643.0, 20121.0, 20038.0, 18647.0, 15839.0, 18472.0, 19726.0, 19666.0, 22436.0, 22626.0, 25581.0, 25691.0, 25793.0, 22765.0, 22660.0, 20436.0, 20560.0, 22017.0, 25146.0, 24493.0, 22996.0, 25092.0, 25451.0, 23324.0, 20137.0, 20018.0, 19878.0, 21149.0, 21037.0, 20040.0, 19548.0, 20765.0, 19778.0, 18996.0, 19744.0, 21472.0, 21839.0, 18649.0, 16974.0, 20699.0, 20644.0, 19954.0, 20139.0, 19932.0, 20196.0, 20567.0, 19989.0, 19949.0, 20093.0, 19899.0, 20240.0, 19964.0, 19522.0, 20168.0, 20379.0, 17639.0, 17319.0, 16971.0, 18438.0, 19462.0, 19928.0, 19634.0, 18716.0, 18079.0, 18539.0, 16889.0, 17438.0, 17933.0, 17217.0, 17894.0, 17461.0, 17287.0, 16682.0, 17365.0, 16786.0, 18771.0, 20778.0, 20463.0, 20366.0, 20214.0, 20399.0, 20314.0, 22331.0, 23353.0, 22819.0, 20685.0, 18981.0, 22050.0, 22366.0, 20745.0, 19264.0, 17526.0, 19516.0, 20519.0, 20511.0, 20416.0, 20017.0, 20031.0, 20096.0, 19878.0, 19848.0, 19975.0, 19300.0, 20620.0, 22921.0, 22749.0, 21188.0, 18749.0, 16538.0, 20325.0, 21005.0, 20458.0, 20031.0, 20570.0, 20023.0, 19262.0, 18417.0, 18604.0, 18548.0, 18959.0, 18425.0, 18617.0, 18561.0, 18398.0, 18524.0, 17950.0, 18803.0, 18643.0, 18486.0, 17831.0, 18676.0, 18759.0, 18363.0, 18436.0, 18609.0, 18062.0, 18616.0, 18361.0, 18555.0, 18518.0, 18829.0, 19064.0, 18726.0, 18750.0, 18660.0, 18407.0, 18579.0, 19533.0, 18758.0, 18583.0, 18407.0, 18368.0, 18701.0, 18476.0, 17997.0, 18322.0, 18639.0, 18280.0, 18159.0, 18106.0, 18500.0, 18618.0, 18516.0, 18631.0, 18595.0, 18745.0, 18941.0, 18413.0, 18527.0, 19544.0, 30012.0, 19337.0, 19818.0, 19565.0, 19392.0, 19412.0, 19466.0, 19050.0, 19430.0, 19840.0, 19672.0, 19389.0, 19998.0, 19648.0, 18899.0, 18687.0, 19596.0, 19602.0, 19604.0, 19594.0, 19713.0, 19901.0, 19607.0, 19960.0, 19913.0, 19610.0, 18696.0, 824.0, 336.0, 296.0, 343.0, 341.0, 322.0, 300.0, 602.0, 191.0, 263.0, 106.0, 673.0, 1076.0, 2.0, 11.0, 38.0, 38.0, 29.0, 139.0, 4120.0, 6212.0, 33.0, 34.0, 28.0, 24.0, 2150.0, 2232.0, 6520.0, 8426.0, 8497.0, 8340.0, 8206.0, 8253.0, 8260.0, 8322.0, 8054.0, 8195.0, 8346.0, 8324.0, 8328.0, 8302.0, 8144.0, 8106.0, 8214.0, 8262.0, 8097.0, 8250.0, 8310.0, 8281.0], "val_status": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]}, {"label": "环比7天", "metric_val": [3061.0, 3053.0, 3036.0, 2957.0, 3092.0, 3011.0, 3044.0, 3045.0, 3132.0, 2976.0, 2992.0, 2974.0, 3013.0, 2971.0, 2942.0, 3035.0, 2920.0, 2816.0, 3035.0, 3028.0, 3093.0, 3025.0, 3177.0, 3064.0, 3041.0, 3014.0, 3153.0, 3056.0, 2964.0, 3013.0, 3018.0, 3062.0, 3169.0, 3306.0, 3356.0, 3466.0, 3367.0, 3282.0, 3299.0, 3173.0, 3224.0, 3059.0, 3165.0, 3122.0, 2927.0, 3008.0, 3065.0, 3025.0, 3320.0, 3247.0, 3264.0, 3238.0, 3384.0, 3307.0, 3352.0, 3276.0, 3348.0, 3300.0, 3263.0, 3168.0, 3269.0, 3198.0, 3406.0, 3270.0, 3254.0, 3277.0, 3263.0, 3203.0, 3296.0, 3202.0, 3206.0, 3259.0, 3253.0, 3244.0, 3282.0, 3236.0, 3209.0, 3187.0, 3165.0, 3170.0, 3163.0, 3351.0, 3233.0, 3146.0, 3191.0, 3082.0, 3314.0, 3161.0, 3138.0, 3148.0, 2874.0, 2834.0, 2668.0, 2774.0, 2824.0, 2760.0, 2841.0, 3784.0, 5911.0, 6068.0, 6280.0, 6148.0, 5731.0, 6272.0, 6351.0, 6264.0, 9005.0, 8880.0, 8941.0, 9004.0, 9423.0, 8855.0, 11474.0, 11659.0, 11785.0, 10305.0, 12263.0, 10959.0, 12311.0, 12049.0, 12235.0, 12479.0, 12093.0, 11299.0, 12631.0, 11933.0, 12204.0, 11030.0, 11792.0, 12115.0, 12054.0, 12491.0, 10645.0, 12065.0, 12148.0, 11743.0, 12058.0, 11846.0, 10013.0, 11603.0, 11777.0, 11754.0, 11849.0, 10270.0, 11941.0, 11906.0, 12031.0, 12177.0, 11759.0, 12067.0, 11750.0, 12242.0, 11997.0, 11471.0, 11914.0, 10839.0, 11831.0, 11767.0, 12204.0, 12230.0, 12065.0, 12050.0, 11086.0, 11879.0, 12323.0, 12380.0, 12104.0, 10984.0, 11637.0, 11652.0, 11767.0, 11957.0, 11365.0, 11810.0, 12060.0, 10670.0, 12658.0, 12154.0, 11769.0, 11263.0, 11857.0, 11906.0, 12328.0, 13888.0, 19781.0, 21216.0, 21575.0, 21791.0, 19447.0, 17250.0, 21285.0, 18786.0, 16330.0, 13587.0, 11580.0, 13251.0, 13103.0, 13472.0, 13336.0, 13601.0, 13507.0, 13163.0, 11937.0, 13371.0, 12680.0, 13126.0, 12903.0, 12278.0, 12942.0, 11913.0, 12939.0, 12931.0, 13136.0, 13402.0, 12393.0, 12451.0, 13070.0, 13060.0, 12853.0, 12420.0, 12478.0, 10675.0, 12723.0, 12853.0, 12641.0, 11480.0, 12879.0, 11214.0, 14794.0, 17974.0, 18986.0, 19109.0, 13110.0, 14818.0, 13486.0, 14002.0, 13531.0, 13236.0, 13916.0, 13557.0, 13097.0, 13124.0, 13940.0, 13010.0, 13431.0, 13464.0, 12674.0, 12587.0, 12338.0, 11693.0, 11843.0, 12115.0, 13329.0, 13281.0, 13014.0, 12741.0, 12717.0, 12521.0, 12570.0, 13225.0, 12756.0, 13055.0, 11552.0, 13090.0, 12525.0, 12331.0, 13153.0, 12769.0, 11760.0, 13180.0, 12508.0, 12995.0, 12694.0, 12674.0, 11286.0, 11425.0, 12558.0, 12504.0, 12140.0, 11956.0, 11995.0, 12070.0, 12568.0, 12575.0, 12348.0, 12454.0, 11079.0, 12083.0, 12153.0, 12422.0, 12577.0, 13167.0, 12747.0, 12843.0, 12427.0, 12322.0, 12670.0, 12722.0, 12443.0, 12109.0, 12641.0, 12606.0, 13026.0, 12653.0, 12686.0, 11458.0, 12906.0, 11995.0, 11789.0, 12711.0, 11889.0, 12368.0, 13146.0, 12887.0, 11065.0, 11921.0, 11833.0, 12685.0, 13214.0, 13381.0, 12456.0, 12558.0, 11471.0, 12437.0, 12419.0, 12284.0, 13012.0, 12272.0, 11343.0, 12516.0, 12458.0, 13080.0, 12494.0, 12572.0, 12892.0, 12431.0, 12136.0, 13002.0, 12505.0, 11550.0, 11964.0, 13099.0, 12466.0, 12379.0, 12200.0, 11928.0, 12380.0, 13050.0, 12151.0, 13040.0, 12631.0, 12388.0, 13062.0, 12475.0, 12451.0, 11715.0, 12849.0, 12348.0, 11771.0, 12225.0, 12412.0, 12019.0, 12876.0, 12722.0, 12191.0, 12773.0, 11770.0, 12067.0, 12358.0, 12254.0, 12806.0, 14225.0, 15297.0, 18429.0, 19081.0, 17235.0, 19204.0, 16194.0, 14275.0, 14492.0, 14517.0, 12940.0, 13333.0, 13853.0, 13285.0, 12991.0, 12961.0, 12610.0, 12455.0, 13747.0, 13272.0, 13000.0, 13014.0, 12150.0, 14556.0, 12720.0, 12192.0, 12762.0, 13144.0, 12651.0, 12462.0, 12228.0, 13189.0, 12684.0, 11666.0, 12349.0, 12594.0, 12729.0, 13009.0, 13049.0, 12496.0, 12643.0, 12288.0, 12443.0, 12464.0, 12925.0, 12498.0, 11775.0, 13568.0, 13059.0, 12601.0, 12334.0, 12455.0, 12204.0, 12397.0, 12828.0, 12621.0, 12326.0, 12995.0, 12385.0, 13232.0, 11995.0, 11722.0, 12543.0, 11927.0, 12826.0, 12342.0, 12400.0, 11626.0, 12195.0, 11393.0, 10192.0, 10605.0, 11205.0, 11066.0, 9671.0, 9106.0, 8896.0, 9191.0, 9300.0, 10083.0, 10395.0, 9751.0, 10237.0, 10780.0, 10589.0, 11404.0, 11586.0, 12091.0, 11535.0, 11908.0, 11888.0, 12303.0, 12056.0, 11905.0, 12310.0, 12577.0, 12641.0, 12399.0, 12093.0, 12821.0, 12581.0, 12691.0, 12225.0, 12270.0, 12503.0, 12776.0, 12769.0, 12116.0, 10405.0, 10216.0, 9863.0, 10666.0, 10671.0, 9870.0, 10492.0, 11316.0, 11356.0, 11898.0, 12252.0, 11409.0, 11809.0, 11121.0, 12217.0, 12111.0, 11257.0, 10407.0, 8831.0, 11824.0, 11049.0, 10734.0, 9720.0, 11137.0, 12084.0, 12383.0, 12529.0, 12548.0, 11816.0, 12304.0, 12653.0, 12216.0, 12696.0, 12627.0, 13209.0, 18265.0, 19826.0, 18142.0, 17461.0, 16824.0, 18587.0, 17004.0, 14704.0, 14292.0, 13815.0, 13397.0, 13276.0, 12948.0, 13767.0, 13815.0, 15579.0, 16435.0, 16398.0, 18464.0, 17282.0, 17522.0, 16195.0, 14624.0, 14052.0, 13763.0, 13695.0, 13536.0, 13452.0, 13151.0, 13448.0, 13331.0, 13215.0, 13127.0, 13169.0, 13008.0, 13154.0, 13038.0, 13396.0, 12832.0, 13487.0, 12641.0, 13540.0, 12903.0, 12897.0, 13041.0, 12716.0, 12638.0, 12791.0, 12856.0, 12864.0, 12942.0, 12996.0, 12974.0, 13152.0, 12822.0, 12870.0, 12727.0, 11986.0, 12672.0, 12617.0, 12739.0, 12422.0, 12988.0, 12677.0, 12808.0, 12545.0, 13313.0, 9292.0, 6187.0, 7507.0, 7323.0, 7376.0, 7411.0, 7034.0, 7242.0, 7154.0, 8375.0, 5769.0, 6901.0, 7500.0, 6264.0, 6001.0, 5753.0, 7570.0, 6332.0, 7109.0, 6788.0, 6503.0, 6646.0, 7247.0, 6444.0, 6270.0, 6294.0, 4979.0, 7301.0, 6571.0, 6937.0, 6886.0, 7191.0, 7297.0, 6132.0, 4677.0, 7484.0, 6481.0, 4484.0, 4325.0, 7143.0, 7448.0, 7039.0, 7231.0, 5228.0, 5983.0, 7459.0, 7095.0, 7202.0, 4319.0, 4115.0, 6638.0, 6607.0, 5057.0, 6036.0, 3432.0, 9597.0, 11438.0, 11233.0, 6647.0, 5876.0, 6666.0, 7026.0, 6171.0, 6958.0, 7428.0, 5257.0, 6003.0, 6654.0, 7234.0, 5040.0, 6711.0, 4373.0, 4916.0, 7135.0, 6899.0, 7514.0, 7144.0, 7414.0, 7940.0, 4086.0, 7321.0, 8098.0, 7197.0, 6667.0, 6502.0, 7878.0, 6586.0, 7519.0, 6993.0, 7431.0, 5923.0, 7352.0, 6977.0, 6966.0, 4417.0, 6460.0, 6955.0, 7408.0, 6893.0, 7639.0, 6870.0, 7645.0, 7293.0, 7749.0, 7072.0, 6388.0, 6439.0, 7719.0, 8012.0, 7267.0, 6745.0, 7122.0, 4297.0, 7433.0, 7891.0, 6572.0, 7553.0, 7041.0, 6832.0, 7599.0, 6942.0, 6874.0, 6880.0, 7033.0, 7675.0, 7370.0, 7027.0, 7628.0, 7234.0, 7225.0, 6835.0, 6894.0, 8023.0, 7659.0, 7144.0, 6385.0, 7598.0, 6670.0, 7160.0, 7118.0, 6700.0, 7254.0, 7669.0, 7389.0, 6192.0, 7595.0, 7263.0, 7449.0, 7295.0, 7475.0, 7417.0, 7134.0, 7348.0, 7187.0, 7425.0, 7408.0, 6851.0, 6292.0, 6901.0, 7302.0, 7434.0, 6488.0, 7631.0, 7082.0, 7402.0, 7362.0, 7171.0, 7298.0, 6962.0, 7310.0, 6993.0, 7383.0, 7492.0, 6694.0, 6575.0, 7206.0, 7803.0, 7004.0, 6791.0, 7384.0, 6318.0, 7254.0, 7772.0, 6618.0, 7140.0, 6951.0, 7172.0, 7426.0, 6939.0, 7400.0, 6617.0, 6808.0, 6798.0, 7332.0, 7501.0, 6707.0, 7428.0, 7530.0, 6993.0, 5102.0, 6791.0, 6976.0, 7161.0, 7383.0, 6684.0, 7158.0, 7089.0, 7374.0, 7146.0, 7169.0, 7603.0, 7054.0, 6634.0, 6418.0, 7304.0, 7310.0, 6674.0, 4590.0, 6488.0, 7397.0, 7348.0, 7439.0, 6943.0, 7386.0, 7288.0, 7071.0, 6948.0, 7220.0, 6328.0, 7349.0, 6666.0, 7032.0, 5570.0, 6802.0, 7888.0, 7299.0, 6999.0, 6833.0, 7024.0, 7829.0, 6755.0, 6716.0, 6090.0, 6734.0, 7773.0, 7045.0, 6900.0, 7411.0, 7335.0, 7959.0, 6360.0, 7458.0, 7220.0, 6856.0, 7547.0, 7672.0, 7093.0, 6909.0, 6899.0, 6679.0, 7187.0, 7292.0, 7289.0, 7848.0, 6943.0, 7136.0, 7038.0, 7347.0, 7522.0, 7850.0, 7182.0, 7916.0, 5925.0, 7102.0, 7617.0, 7208.0, 7696.0, 7626.0, 8021.0, 9159.0, 10077.0, 10830.0, 9614.0, 8543.0, 7032.0, 7863.0, 7092.0, 6857.0, 7294.0, 7295.0, 6945.0, 5573.0, 7021.0, 7318.0, 7227.0, 6902.0, 6889.0, 7518.0, 7156.0, 7323.0, 6872.0, 7033.0, 7279.0, 6960.0, 7124.0, 6907.0, 6878.0, 7287.0, 6472.0, 6591.0, 7023.0, 6673.0, 7053.0, 7303.0, 6718.0, 7397.0, 6991.0, 4965.0, 6404.0, 6667.0, 7185.0, 6773.0, 6837.0, 6529.0, 6937.0, 7544.0, 6947.0, 7139.0, 6930.0, 6861.0, 7307.0, 6844.0, 6969.0, 6273.0, 6538.0, 6720.0, 7305.0, 6797.0, 7213.0, 7399.0, 6123.0, 7200.0, 7208.0, 7317.0, 6929.0, 6604.0, 7296.0, 6954.0, 7352.0, 6844.0, 6607.0, 6329.0, 6748.0, 7428.0, 6425.0, 6625.0, 6943.0, 7441.0, 7223.0, 6473.0, 7159.0, 6854.0, 7080.0, 7149.0, 7008.0, 6851.0, 7825.0, 7427.0, 6804.0, 5719.0, 7091.0, 6920.0, 7014.0, 6673.0, 6993.0, 7136.0, 6253.0, 7279.0, 7192.0, 7201.0, 7502.0, 7680.0, 6781.0, 730.0, 268.0, 363.0, 331.0, 348.0, 303.0, 330.0, 328.0, 230.0, 361.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 35.0, 63.0, 73.0, 30.0, 24.0, 73.0, 57.0, 100.0, 77.0, 10.0, 91.0, 111.0, 95.0, 55.0, 67.0, 141.0, 41.0, 74.0, 66.0, 72.0, 59.0, 113.0, 54.0, 86.0, 36.0, 35.0, 139.0, 124.0, 57.0, 175.0, 344.0, 188.0, 272.0, 95.0, 97.0, 1.0, 24.0, 1.0, 3.0, 17.0, 8.0, 1.0, 30.0, 15.0, 1.0, 1.0, 1.0, 1.0, 34.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 28.0, 4.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2322.0, 5379.0, 3703.0, 2605.0, 2430.0, 2614.0, 3195.0, 3622.0, 3707.0, 3718.0, 3692.0, 3862.0, 3828.0, 3581.0, 3841.0, 3806.0, 3687.0, 262.0, 130.0, 171.0, 251.0, 146.0, 168.0, 217.0, 155.0, 201.0, 100.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 803.0, 3774.0, 2146.0, 2251.0, 2268.0, 2270.0, 2111.0, 2049.0, 2287.0, 2123.0, 2423.0, 2389.0, 2341.0, 2178.0, 3265.0, 5819.0, 6344.0, 4850.0, 5606.0, 4567.0, 3231.0, 6192.0, 6874.0, 5154.0, 6890.0, 8544.0, 8026.0, 6499.0, 5530.0, 6222.0, 4253.0, 6700.0, 4011.0, 4689.0, 5195.0, 5180.0, 6335.0, 5890.0, 3378.0, 4623.0, 3051.0, 4924.0, 3781.0, 7215.0, 4402.0, 4197.0, 3875.0, 3986.0, 4846.0, 5169.0, 5233.0, 6648.0, 5615.0, 7079.0, 4099.0, 7841.0, 6428.0, 6495.0, 5472.0, 6277.0, 4312.0, 5732.0, 5819.0, 4635.0, 4622.0, 4948.0, 4084.0, 4440.0, 5865.0, 4963.0, 5159.0, 8022.0, 7278.0, 6664.0, 5945.0, 6058.0, 6245.0, 6807.0, 4857.0, 5861.0, 6171.0, 7824.0, 6323.0, 6277.0, 6365.0, 4495.0, 5774.0, 4973.0, 5390.0, 6976.0, 6133.0, 4540.0, 7185.0, 4521.0, 4691.0, 4870.0, 5769.0, 5453.0, 4233.0, 9077.0, 6174.0, 4652.0, 5206.0, 5960.0, 5385.0, 4758.0, 5391.0, 4890.0, 4636.0, 6453.0, 5133.0, 4680.0, 4829.0, 5504.0, 5408.0, 5026.0, 7317.0, 4558.0, 5627.0, 7116.0, 6985.0, 5575.0, 5696.0, 9259.0, 8701.0, 7567.0, 4642.0, 4416.0, 6078.0, 5791.0, 5104.0, 5473.0, 4879.0, 5549.0, 5156.0, 6275.0, 5243.0, 5246.0, 4488.0, 6115.0, 5075.0, 5147.0, 5424.0, 5449.0, 5930.0, 5580.0, 5123.0, 7124.0, 5415.0, 5619.0, 5323.0, 5324.0, 5709.0, 4926.0, 6473.0, 4449.0, 7767.0, 7231.0, 5714.0, 5973.0, 6631.0, 5774.0, 3758.0, 5146.0, 3610.0, 5085.0, 4849.0, 4787.0, 5258.0, 10196.0, 9298.0, 5523.0, 9775.0, 8408.0, 4841.0, 6288.0, 4967.0, 9242.0, 9177.0, 6866.0, 5826.0, 5142.0, 5494.0, 4851.0, 6035.0, 4983.0, 4795.0, 7122.0, 5603.0, 6680.0, 5240.0, 5900.0, 5805.0, 4485.0, 4956.0, 5741.0, 6411.0, 4340.0, 4481.0, 6250.0, 4977.0, 5920.0, 6236.0, 4971.0, 5192.0, 7896.0, 4298.0, 8579.0, 5328.0, 5430.0, 3961.0, 5156.0, 5200.0, 4727.0, 5223.0, 5746.0, 3585.0, 6256.0, 5486.0, 5105.0, 5245.0, 5657.0, 5458.0, 6189.0, 5378.0, 6207.0, 4522.0, 4795.0, 4782.0, 4695.0, 7042.0, 8739.0, 6074.0, 6100.0, 6502.0, 9343.0, 5639.0, 7334.0, 5017.0, 5930.0, 4427.0, 5796.0, 4713.0, 7519.0, 6970.0, 5258.0, 5172.0, 3137.0, 5202.0, 6327.0, 4659.0, 5082.0, 4363.0, 4561.0, 9794.0, 8502.0, 4305.0, 5747.0, 5510.0, 5692.0, 5710.0, 5412.0, 5295.0, 6285.0, 5461.0, 4967.0, 6139.0, 4316.0, 4054.0, 4507.0, 5777.0, 5119.0, 5556.0, 4821.0, 5420.0, 5543.0, 5568.0, 5021.0, 6856.0, 4461.0, 4746.0, 5067.0, 4584.0, 5568.0, 4910.0, 4588.0, 4908.0, 8745.0, 8898.0, 4523.0, 5580.0, 5578.0, 5126.0, 7404.0, 5989.0, 5250.0, 4848.0, 4914.0, 5541.0, 5691.0, 6395.0, 4846.0, 7137.0, 4943.0, 4907.0, 6363.0, 6107.0, 7133.0, 4742.0, 6319.0, 4978.0, 4945.0, 7421.0, 6405.0, 4571.0, 4022.0, 5465.0, 4123.0, 5569.0, 5492.0, 4210.0, 6457.0, 6427.0, 6023.0, 4777.0, 4805.0, 6259.0, 6448.0, 6578.0, 5131.0, 6397.0, 5987.0, 4665.0, 5046.0, 4059.0, 4308.0, 4477.0, 5168.0, 6714.0, 5256.0, 5367.0, 4948.0, 5636.0, 5030.0, 5141.0, 10314.0, 9514.0, 7518.0, 6767.0, 4602.0, 5847.0, 5373.0, 5150.0, 4567.0, 4154.0, 5886.0, 6446.0, 5984.0, 3997.0, 5181.0, 5013.0, 4724.0, 4595.0, 4199.0, 6961.0, 4694.0, 4981.0, 4897.0, 5112.0, 5460.0, 4484.0, 5497.0, 4941.0, 3408.0, 5122.0, 5836.0, 5746.0, 4716.0, 4333.0, 6749.0, 4603.0, 4762.0, 4382.0, 4428.0, 5369.0, 4748.0, 5426.0, 4323.0, 5731.0, 8951.0, 3683.0, 5338.0, 4321.0, 5220.0, 7162.0, 5647.0, 4076.0, 4758.0, 6988.0, 7428.0, 5886.0, 5039.0, 5377.0, 5762.0, 8422.0, 7880.0, 9474.0, 8858.0, 8446.0, 8474.0, 8840.0, 8313.0, 8389.0, 9561.0, 8323.0, 8979.0, 8648.0, 9092.0, 8911.0, 9532.0, 8941.0, 8587.0, 8488.0, 8697.0, 8389.0, 8680.0, 9372.0, 8965.0, 8642.0, 8702.0, 8985.0, 8785.0, 8833.0, 8874.0, 9515.0, 9275.0, 9218.0, 8650.0, 8853.0, 8958.0, 9429.0, 8949.0, 9129.0, 9521.0, 8560.0, 9061.0, 8399.0, 8502.0, 9068.0, 8810.0, 8877.0, 8425.0, 9235.0, 8963.0, 9090.0, 9017.0, 9018.0, 9565.0, 8534.0, 9196.0, 9300.0, 8656.0, 8839.0, 8869.0, 9483.0, 9404.0, 8856.0, 8882.0, 9355.0, 9155.0, 9028.0, 8376.0, 8442.0, 8620.0, 8981.0, 9339.0, 8959.0, 8606.0, 8789.0, 8308.0, 8287.0, 8648.0, 8971.0, 8461.0, 8913.0, 8361.0, 8694.0, 8946.0, 8545.0, 8764.0, 8673.0, 8751.0, 8675.0, 8726.0, 8377.0, 9381.0, 9152.0, 8305.0, 8071.0, 8399.0, 8217.0, 8306.0, 9313.0, 8695.0, 8320.0, 8420.0, 8983.0, 8525.0, 8608.0, 8734.0, 8862.0, 8624.0, 8510.0, 8085.0, 8971.0, 8799.0, 8260.0, 8550.0, 8443.0, 8259.0, 7964.0, 8491.0, 9148.0, 9019.0, 8681.0, 8314.0, 8393.0, 8490.0, 8962.0, 8439.0, 8369.0, 8638.0, 8618.0, 8464.0, 8778.0, 8691.0, 8634.0, 8888.0, 7876.0, 8690.0, 9046.0, 8676.0, 8419.0, 8319.0, 8288.0, 8450.0, 8638.0, 8438.0, 8747.0, 8811.0, 8722.0, 8345.0, 8648.0, 8680.0, 8771.0, 8853.0, 8396.0, 8815.0, 8742.0, 8597.0, 8550.0, 8412.0, 7412.0, 4802.0, 4613.0, 4900.0, 5026.0, 4950.0, 4737.0, 4700.0, 4826.0, 5277.0, 4613.0, 4828.0, 4912.0, 4673.0, 4564.0, 4488.0, 4674.0, 4853.0, 4560.0, 4714.0, 4581.0, 4727.0, 4722.0, 4446.0, 4545.0, 4478.0, 4665.0, 4746.0, 4515.0, 4683.0, 4810.0, 4960.0, 4626.0, 4743.0, 4638.0, 2835.0, 166.0, 185.0, 132.0, 177.0, 111.0, 111.0, 144.0, 142.0, 86.0, 109.0, 70.0, 46.0, 31.0, 32.0, 51.0, 53.0, 47.0, 36.0, 47.0, 30.0, 20.0, 33.0, 38.0, 36.0, 36.0, 37.0, 44.0, 37.0, 20.0, 35.0, 50.0, 50.0, 43.0, 45.0, 38.0, 59.0, 23.0, 63.0, 42.0, 43.0, 35.0, 23.0, 29.0, 32.0, 40.0, 45.0, 44.0, 41.0, 41.0, 49.0, 43.0, 45.0, 26.0, 44.0, 50.0, 72.0, 91.0, 91.0, 81.0, 63.0, 79.0, 84.0, 72.0, 71.0, 79.0, 67.0, 1228.0, 3653.0, 4405.0, 2783.0, 3107.0, 4622.0, 5371.0, 8511.0, 2723.0, 3413.0, 4477.0, 3488.0, 3350.0, 3081.0, 4637.0, 3208.0, 4219.0, 4295.0, 3336.0, 5069.0, 3587.0, 4471.0, 4250.0, 3699.0, 3767.0, 3615.0, 4473.0, 4818.0, 6703.0, 9091.0, 8163.0, 4711.0, 5145.0, 3931.0, 3070.0, 5500.0, 3268.0, 3228.0, 3121.0, 3774.0, 3374.0, 3381.0, 4496.0, 3698.0, 4979.0, 3561.0, 6013.0, 3070.0, 5578.0, 4857.0, 3523.0, 4813.0, 4465.0, 2019.0, 3993.0, 3725.0, 2567.0, 4005.0, 5915.0, 4741.0, 3570.0, 4483.0, 3308.0, 3894.0, 5959.0, 2970.0, 4956.0, 3667.0, 4195.0, 3697.0, 3463.0, 3612.0, 3594.0, 3261.0, 3806.0, 3626.0, 3911.0, 4735.0, 5584.0, 3315.0, 3947.0, 3602.0, 5147.0, 4343.0, 3666.0, 3043.0, 3875.0, 5038.0, 3268.0, 3389.0, 4468.0, 4599.0, 4229.0, 4845.0, 2282.0, 3020.0, 5185.0, 4505.0, 3171.0, 3043.0, 4387.0, 3576.0, 4189.0, 5626.0, 3138.0, 4230.0, 4569.0, 3627.0, 5518.0, 4685.0, 7456.0, 7574.0, 4533.0, 3239.0, 3618.0, 4883.0, 3406.0, 4384.0, 4217.0, 4118.0, 5156.0, 3284.0, 2948.0, 4649.0, 3135.0, 4389.0, 4319.0, 2904.0, 5319.0, 6353.0, 4874.0, 4576.0, 4191.0, 3506.0, 5228.0, 4124.0, 5885.0, 5018.0, 5494.0, 3912.0, 3048.0, 4804.0, 7446.0, 3903.0, 3687.0, 3939.0, 4259.0, 5339.0, 4905.0, 4514.0, 4818.0, 6832.0, 3083.0, 4030.0, 4331.0, 4770.0, 4357.0, 5358.0, 8321.0, 3778.0, 4074.0, 4984.0, 4249.0, 2840.0, 3837.0, 4074.0, 5309.0, 4877.0, 7158.0, 3035.0, 3649.0, 4203.0, 3646.0, 4369.0, 4143.0, 5007.0, 4943.0, 4129.0, 4700.0, 5062.0, 4382.0, 3693.0, 5634.0, 4400.0, 4271.0, 3790.0, 4843.0, 3748.0, 3946.0, 4245.0, 4504.0, 4012.0, 3939.0, 3923.0, 3800.0, 9930.0, 8925.0, 4397.0, 2881.0, 4943.0, 5714.0, 4430.0, 4523.0, 4208.0, 3616.0, 4740.0, 6432.0, 5841.0, 4069.0, 4265.0, 4886.0, 4103.0, 4493.0, 4977.0, 4264.0, 2967.0, 4030.0, 5405.0, 4109.0, 4127.0, 5457.0, 5571.0, 9268.0, 8761.0, 4358.0, 4358.0, 6039.0, 4539.0, 3414.0, 3664.0, 5695.0, 2873.0, 4433.0, 6883.0, 4048.0, 5651.0, 7410.0, 4118.0, 3873.0, 4169.0, 4273.0, 3882.0, 5519.0, 4844.0, 4566.0, 3587.0, 4921.0, 2811.0, 4269.0, 4157.0, 4933.0, 6320.0, 4852.0, 4484.0, 5155.0, 3982.0, 4023.0, 6756.0, 5358.0, 3998.0, 5316.0, 4751.0, 4051.0, 5354.0, 4721.0, 4313.0, 3947.0, 3756.0, 3387.0, 5920.0, 5112.0, 4948.0, 4538.0, 466.0, 738.0, 615.0, 537.0, 887.0, 265.0, 525.0, 508.0, 63.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 414.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 5.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 97.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3.0, 30.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 3.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 282.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 145.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 187.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.0, 6.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 117.0, 266.0, 1.0, 1.0, 1.0, 370.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 253.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 2.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "val_status": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}], "is_change_err": null, "change_period": true}, "metric_info": {"zhiyan_cal_method": 1, "project_id": 1, "app_mark": 1, "metric_name": "test", "metric_ch_name": "测试", "sec_lvl_name": 1, "env": "prod"}, "param": {"param_sensitive": 5}, "trace_id": 665}