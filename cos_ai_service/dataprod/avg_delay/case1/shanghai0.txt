{"timestamps": ["2025-07-08 15:06", "2025-07-08 15:07", "2025-07-08 15:08", "2025-07-08 15:09", "2025-07-08 15:10", "2025-07-08 15:11", "2025-07-08 15:12", "2025-07-08 15:13", "2025-07-08 15:14", "2025-07-08 15:15", "2025-07-08 15:16", "2025-07-08 15:17", "2025-07-08 15:18", "2025-07-08 15:19", "2025-07-08 15:20", "2025-07-08 15:21", "2025-07-08 15:22", "2025-07-08 15:23", "2025-07-08 15:24", "2025-07-08 15:25", "2025-07-08 15:26", "2025-07-08 15:27", "2025-07-08 15:28", "2025-07-08 15:29", "2025-07-08 15:30", "2025-07-08 15:31", "2025-07-08 15:32", "2025-07-08 15:33", "2025-07-08 15:34", "2025-07-08 15:35", "2025-07-08 15:36", "2025-07-08 15:37", "2025-07-08 15:38", "2025-07-08 15:39", "2025-07-08 15:40", "2025-07-08 15:41", "2025-07-08 15:42", "2025-07-08 15:43", "2025-07-08 15:44", "2025-07-08 15:45", "2025-07-08 15:46", "2025-07-08 15:47", "2025-07-08 15:48", "2025-07-08 15:49", "2025-07-08 15:50", "2025-07-08 15:51", "2025-07-08 15:52", "2025-07-08 15:53", "2025-07-08 15:54", "2025-07-08 15:55", "2025-07-08 15:56", "2025-07-08 15:57", "2025-07-08 15:58", "2025-07-08 15:59", "2025-07-08 16:00", "2025-07-08 16:01", "2025-07-08 16:02", "2025-07-08 16:03", "2025-07-08 16:04", "2025-07-08 16:05", "2025-07-08 16:06", "2025-07-08 16:07", "2025-07-08 16:08", "2025-07-08 16:09", "2025-07-08 16:10", "2025-07-08 16:11", "2025-07-08 16:12", "2025-07-08 16:13", "2025-07-08 16:14", "2025-07-08 16:15", "2025-07-08 16:16", "2025-07-08 16:17", "2025-07-08 16:18", "2025-07-08 16:19", "2025-07-08 16:20", "2025-07-08 16:21", "2025-07-08 16:22", "2025-07-08 16:23", "2025-07-08 16:24", "2025-07-08 16:25", "2025-07-08 16:26", "2025-07-08 16:27", "2025-07-08 16:28", "2025-07-08 16:29", "2025-07-08 16:30", "2025-07-08 16:31", "2025-07-08 16:32", "2025-07-08 16:33", "2025-07-08 16:34", "2025-07-08 16:35", "2025-07-08 16:36", "2025-07-08 16:37", "2025-07-08 16:38", "2025-07-08 16:39", "2025-07-08 16:40", "2025-07-08 16:41", "2025-07-08 16:42", "2025-07-08 16:43", "2025-07-08 16:44", "2025-07-08 16:45", "2025-07-08 16:46", "2025-07-08 16:47", "2025-07-08 16:48", "2025-07-08 16:49", "2025-07-08 16:50", "2025-07-08 16:51", "2025-07-08 16:52", "2025-07-08 16:53", "2025-07-08 16:54", "2025-07-08 16:55", "2025-07-08 16:56", "2025-07-08 16:57", "2025-07-08 16:58", "2025-07-08 16:59", "2025-07-08 17:00", "2025-07-08 17:01", "2025-07-08 17:02", "2025-07-08 17:03", "2025-07-08 17:04", "2025-07-08 17:05", "2025-07-08 17:06", "2025-07-08 17:07", "2025-07-08 17:08", "2025-07-08 17:09", "2025-07-08 17:10", "2025-07-08 17:11", "2025-07-08 17:12", "2025-07-08 17:13", "2025-07-08 17:14", "2025-07-08 17:15", "2025-07-08 17:16", "2025-07-08 17:17", "2025-07-08 17:18", "2025-07-08 17:19", "2025-07-08 17:20", "2025-07-08 17:21", "2025-07-08 17:22", "2025-07-08 17:23", "2025-07-08 17:24", "2025-07-08 17:25", "2025-07-08 17:26", "2025-07-08 17:27", "2025-07-08 17:28", "2025-07-08 17:29", "2025-07-08 17:30", "2025-07-08 17:31", "2025-07-08 17:32", "2025-07-08 17:33", "2025-07-08 17:34", "2025-07-08 17:35", "2025-07-08 17:36", "2025-07-08 17:37", "2025-07-08 17:38", "2025-07-08 17:39", "2025-07-08 17:40", "2025-07-08 17:41", "2025-07-08 17:42", "2025-07-08 17:43", "2025-07-08 17:44", "2025-07-08 17:45", "2025-07-08 17:46", "2025-07-08 17:47", "2025-07-08 17:48", "2025-07-08 17:49", "2025-07-08 17:50", "2025-07-08 17:51", "2025-07-08 17:52", "2025-07-08 17:53", "2025-07-08 17:54", "2025-07-08 17:55", "2025-07-08 17:56", "2025-07-08 17:57", "2025-07-08 17:58", "2025-07-08 17:59", "2025-07-08 18:00", "2025-07-08 18:01", "2025-07-08 18:02", "2025-07-08 18:03", "2025-07-08 18:04", "2025-07-08 18:05", "2025-07-08 18:06", "2025-07-08 18:07", "2025-07-08 18:08", "2025-07-08 18:09", "2025-07-08 18:10", "2025-07-08 18:11", "2025-07-08 18:12", "2025-07-08 18:13", "2025-07-08 18:14", "2025-07-08 18:15", "2025-07-08 18:16", "2025-07-08 18:17", "2025-07-08 18:18", "2025-07-08 18:19", "2025-07-08 18:20", "2025-07-08 18:21", "2025-07-08 18:22", "2025-07-08 18:23", "2025-07-08 18:24", "2025-07-08 18:25", "2025-07-08 18:26", "2025-07-08 18:27", "2025-07-08 18:28", "2025-07-08 18:29", "2025-07-08 18:30", "2025-07-08 18:31", "2025-07-08 18:32", "2025-07-08 18:33", "2025-07-08 18:34", "2025-07-08 18:35", "2025-07-08 18:36", "2025-07-08 18:37", "2025-07-08 18:38", "2025-07-08 18:39", "2025-07-08 18:40", "2025-07-08 18:41", "2025-07-08 18:42", "2025-07-08 18:43", "2025-07-08 18:44", "2025-07-08 18:45", "2025-07-08 18:46", "2025-07-08 18:47", "2025-07-08 18:48", "2025-07-08 18:49", "2025-07-08 18:50", "2025-07-08 18:51", "2025-07-08 18:52", "2025-07-08 18:53", "2025-07-08 18:54", "2025-07-08 18:55", "2025-07-08 18:56", "2025-07-08 18:57", "2025-07-08 18:58", "2025-07-08 18:59", "2025-07-08 19:00", "2025-07-08 19:01", "2025-07-08 19:02", "2025-07-08 19:03", "2025-07-08 19:04", "2025-07-08 19:05", "2025-07-08 19:06", "2025-07-08 19:07", "2025-07-08 19:08", "2025-07-08 19:09", "2025-07-08 19:10", "2025-07-08 19:11", "2025-07-08 19:12", "2025-07-08 19:13", "2025-07-08 19:14", "2025-07-08 19:15", "2025-07-08 19:16", "2025-07-08 19:17", "2025-07-08 19:18", "2025-07-08 19:19", "2025-07-08 19:20", "2025-07-08 19:21", "2025-07-08 19:22", "2025-07-08 19:23", "2025-07-08 19:24", "2025-07-08 19:25", "2025-07-08 19:26", "2025-07-08 19:27", "2025-07-08 19:28", "2025-07-08 19:29", "2025-07-08 19:30", "2025-07-08 19:31", "2025-07-08 19:32", "2025-07-08 19:33", "2025-07-08 19:34", "2025-07-08 19:35", "2025-07-08 19:36", "2025-07-08 19:37", "2025-07-08 19:38", "2025-07-08 19:39", "2025-07-08 19:40", "2025-07-08 19:41", "2025-07-08 19:42", "2025-07-08 19:43", "2025-07-08 19:44", "2025-07-08 19:45", "2025-07-08 19:46", "2025-07-08 19:47", "2025-07-08 19:48", "2025-07-08 19:49", "2025-07-08 19:50", "2025-07-08 19:51", "2025-07-08 19:52", "2025-07-08 19:53", "2025-07-08 19:54", "2025-07-08 19:55", "2025-07-08 19:56", "2025-07-08 19:57", "2025-07-08 19:58", "2025-07-08 19:59", "2025-07-08 20:00", "2025-07-08 20:01", "2025-07-08 20:02", "2025-07-08 20:03", "2025-07-08 20:04", "2025-07-08 20:05", "2025-07-08 20:06", "2025-07-08 20:07", "2025-07-08 20:08", "2025-07-08 20:09", "2025-07-08 20:10", "2025-07-08 20:11", "2025-07-08 20:12", "2025-07-08 20:13", "2025-07-08 20:14", "2025-07-08 20:15", "2025-07-08 20:16", "2025-07-08 20:17", "2025-07-08 20:18", "2025-07-08 20:19", "2025-07-08 20:20", "2025-07-08 20:21", "2025-07-08 20:22", "2025-07-08 20:23", "2025-07-08 20:24", "2025-07-08 20:25", "2025-07-08 20:26", "2025-07-08 20:27", "2025-07-08 20:28", "2025-07-08 20:29", "2025-07-08 20:30", "2025-07-08 20:31", "2025-07-08 20:32", "2025-07-08 20:33", "2025-07-08 20:34", "2025-07-08 20:35", "2025-07-08 20:36", "2025-07-08 20:37", "2025-07-08 20:38", "2025-07-08 20:39", "2025-07-08 20:40", "2025-07-08 20:41", "2025-07-08 20:42", "2025-07-08 20:43", "2025-07-08 20:44", "2025-07-08 20:45", "2025-07-08 20:46", "2025-07-08 20:47", "2025-07-08 20:48", "2025-07-08 20:49", "2025-07-08 20:50", "2025-07-08 20:51", "2025-07-08 20:52", "2025-07-08 20:53", "2025-07-08 20:54", "2025-07-08 20:55", "2025-07-08 20:56", "2025-07-08 20:57", "2025-07-08 20:58", "2025-07-08 20:59", "2025-07-08 21:00", "2025-07-08 21:01", "2025-07-08 21:02", "2025-07-08 21:03", "2025-07-08 21:04", "2025-07-08 21:05", "2025-07-08 21:06", "2025-07-08 21:07", "2025-07-08 21:08", "2025-07-08 21:09", "2025-07-08 21:10", "2025-07-08 21:11", "2025-07-08 21:12", "2025-07-08 21:13", "2025-07-08 21:14", "2025-07-08 21:15", "2025-07-08 21:16", "2025-07-08 21:17", "2025-07-08 21:18", "2025-07-08 21:19", "2025-07-08 21:20", "2025-07-08 21:21", "2025-07-08 21:22", "2025-07-08 21:23", "2025-07-08 21:24", "2025-07-08 21:25", "2025-07-08 21:26", "2025-07-08 21:27", "2025-07-08 21:28", "2025-07-08 21:29", "2025-07-08 21:30", "2025-07-08 21:31", "2025-07-08 21:32", "2025-07-08 21:33", "2025-07-08 21:34", "2025-07-08 21:35", "2025-07-08 21:36", "2025-07-08 21:37", "2025-07-08 21:38", "2025-07-08 21:39", "2025-07-08 21:40", "2025-07-08 21:41", "2025-07-08 21:42", "2025-07-08 21:43", "2025-07-08 21:44", "2025-07-08 21:45", "2025-07-08 21:46", "2025-07-08 21:47", "2025-07-08 21:48", "2025-07-08 21:49", "2025-07-08 21:50", "2025-07-08 21:51", "2025-07-08 21:52", "2025-07-08 21:53", "2025-07-08 21:54", "2025-07-08 21:55", "2025-07-08 21:56", "2025-07-08 21:57", "2025-07-08 21:58", "2025-07-08 21:59", "2025-07-08 22:00", "2025-07-08 22:01", "2025-07-08 22:02", "2025-07-08 22:03", "2025-07-08 22:04", "2025-07-08 22:05", "2025-07-08 22:06", "2025-07-08 22:07", "2025-07-08 22:08", "2025-07-08 22:09", "2025-07-08 22:10", "2025-07-08 22:11", "2025-07-08 22:12", "2025-07-08 22:13", "2025-07-08 22:14", "2025-07-08 22:15", "2025-07-08 22:16", "2025-07-08 22:17", "2025-07-08 22:18", "2025-07-08 22:19", "2025-07-08 22:20", "2025-07-08 22:21", "2025-07-08 22:22", "2025-07-08 22:23", "2025-07-08 22:24", "2025-07-08 22:25", "2025-07-08 22:26", "2025-07-08 22:27", "2025-07-08 22:28", "2025-07-08 22:29", "2025-07-08 22:30", "2025-07-08 22:31", "2025-07-08 22:32", "2025-07-08 22:33", "2025-07-08 22:34", "2025-07-08 22:35", "2025-07-08 22:36", "2025-07-08 22:37", "2025-07-08 22:38", "2025-07-08 22:39", "2025-07-08 22:40", "2025-07-08 22:41", "2025-07-08 22:42", "2025-07-08 22:43", "2025-07-08 22:44", "2025-07-08 22:45", "2025-07-08 22:46", "2025-07-08 22:47", "2025-07-08 22:48", "2025-07-08 22:49", "2025-07-08 22:50", "2025-07-08 22:51", "2025-07-08 22:52", "2025-07-08 22:53", "2025-07-08 22:54", "2025-07-08 22:55", "2025-07-08 22:56", "2025-07-08 22:57", "2025-07-08 22:58", "2025-07-08 22:59", "2025-07-08 23:00", "2025-07-08 23:01", "2025-07-08 23:02", "2025-07-08 23:03", "2025-07-08 23:04", "2025-07-08 23:05", "2025-07-08 23:06", "2025-07-08 23:07", "2025-07-08 23:08", "2025-07-08 23:09", "2025-07-08 23:10", "2025-07-08 23:11", "2025-07-08 23:12", "2025-07-08 23:13", "2025-07-08 23:14", "2025-07-08 23:15", "2025-07-08 23:16", "2025-07-08 23:17", "2025-07-08 23:18", "2025-07-08 23:19", "2025-07-08 23:20", "2025-07-08 23:21", "2025-07-08 23:22", "2025-07-08 23:23", "2025-07-08 23:24", "2025-07-08 23:25", "2025-07-08 23:26", "2025-07-08 23:27", "2025-07-08 23:28", "2025-07-08 23:29", "2025-07-08 23:30", "2025-07-08 23:31", "2025-07-08 23:32", "2025-07-08 23:33", "2025-07-08 23:34", "2025-07-08 23:35", "2025-07-08 23:36", "2025-07-08 23:37", "2025-07-08 23:38", "2025-07-08 23:39", "2025-07-08 23:40", "2025-07-08 23:41", "2025-07-08 23:42", "2025-07-08 23:43", "2025-07-08 23:44", "2025-07-08 23:45", "2025-07-08 23:46", "2025-07-08 23:47", "2025-07-08 23:48", "2025-07-08 23:49", "2025-07-08 23:50", "2025-07-08 23:51", "2025-07-08 23:52", "2025-07-08 23:53", "2025-07-08 23:54", "2025-07-08 23:55", "2025-07-08 23:56", "2025-07-08 23:57", "2025-07-08 23:58", "2025-07-08 23:59", "2025-07-09 00:00", "2025-07-09 00:01", "2025-07-09 00:02", "2025-07-09 00:03", "2025-07-09 00:04", "2025-07-09 00:05", "2025-07-09 00:06", "2025-07-09 00:07", "2025-07-09 00:08", "2025-07-09 00:09", "2025-07-09 00:10", "2025-07-09 00:11", "2025-07-09 00:12", "2025-07-09 00:13", "2025-07-09 00:14", "2025-07-09 00:15", "2025-07-09 00:16", "2025-07-09 00:17", "2025-07-09 00:18", "2025-07-09 00:19", "2025-07-09 00:20", "2025-07-09 00:21", "2025-07-09 00:22", "2025-07-09 00:23", "2025-07-09 00:24", "2025-07-09 00:25", "2025-07-09 00:26", "2025-07-09 00:27", "2025-07-09 00:28", "2025-07-09 00:29", "2025-07-09 00:30", "2025-07-09 00:31", "2025-07-09 00:32", "2025-07-09 00:33", "2025-07-09 00:34", "2025-07-09 00:35", "2025-07-09 00:36", "2025-07-09 00:37", "2025-07-09 00:38", "2025-07-09 00:39", "2025-07-09 00:40", "2025-07-09 00:41", "2025-07-09 00:42", "2025-07-09 00:43", "2025-07-09 00:44", "2025-07-09 00:45", "2025-07-09 00:46", "2025-07-09 00:47", "2025-07-09 00:48", "2025-07-09 00:49", "2025-07-09 00:50", "2025-07-09 00:51", "2025-07-09 00:52", "2025-07-09 00:53", "2025-07-09 00:54", "2025-07-09 00:55", "2025-07-09 00:56", "2025-07-09 00:57", "2025-07-09 00:58", "2025-07-09 00:59", "2025-07-09 01:00", "2025-07-09 01:01", "2025-07-09 01:02", "2025-07-09 01:03", "2025-07-09 01:04", "2025-07-09 01:05", "2025-07-09 01:06", "2025-07-09 01:07", "2025-07-09 01:08", "2025-07-09 01:09", "2025-07-09 01:10", "2025-07-09 01:11", "2025-07-09 01:12", "2025-07-09 01:13", "2025-07-09 01:14", "2025-07-09 01:15", "2025-07-09 01:16", "2025-07-09 01:17", "2025-07-09 01:18", "2025-07-09 01:19", "2025-07-09 01:20", "2025-07-09 01:21", "2025-07-09 01:22", "2025-07-09 01:23", "2025-07-09 01:24", "2025-07-09 01:25", "2025-07-09 01:26", "2025-07-09 01:27", "2025-07-09 01:28", "2025-07-09 01:29", "2025-07-09 01:30", "2025-07-09 01:31", "2025-07-09 01:32", "2025-07-09 01:33", "2025-07-09 01:34", "2025-07-09 01:35", "2025-07-09 01:36", "2025-07-09 01:37", "2025-07-09 01:38", "2025-07-09 01:39", "2025-07-09 01:40", "2025-07-09 01:41", "2025-07-09 01:42", "2025-07-09 01:43", "2025-07-09 01:44", "2025-07-09 01:45", "2025-07-09 01:46", "2025-07-09 01:47", "2025-07-09 01:48", "2025-07-09 01:49", "2025-07-09 01:50", "2025-07-09 01:51", "2025-07-09 01:52", "2025-07-09 01:53", "2025-07-09 01:54", "2025-07-09 01:55", "2025-07-09 01:56", "2025-07-09 01:57", "2025-07-09 01:58", "2025-07-09 01:59", "2025-07-09 02:00", "2025-07-09 02:01", "2025-07-09 02:02", "2025-07-09 02:03", "2025-07-09 02:04", "2025-07-09 02:05", "2025-07-09 02:06", "2025-07-09 02:07", "2025-07-09 02:08", "2025-07-09 02:09", "2025-07-09 02:10", "2025-07-09 02:11", "2025-07-09 02:12", "2025-07-09 02:13", "2025-07-09 02:14", "2025-07-09 02:15", "2025-07-09 02:16", "2025-07-09 02:17", "2025-07-09 02:18", "2025-07-09 02:19", "2025-07-09 02:20", "2025-07-09 02:21", "2025-07-09 02:22", "2025-07-09 02:23", "2025-07-09 02:24", "2025-07-09 02:25", "2025-07-09 02:26", "2025-07-09 02:27", "2025-07-09 02:28", "2025-07-09 02:29", "2025-07-09 02:30", "2025-07-09 02:31", "2025-07-09 02:32", "2025-07-09 02:33", "2025-07-09 02:34", "2025-07-09 02:35", "2025-07-09 02:36", "2025-07-09 02:37", "2025-07-09 02:38", "2025-07-09 02:39", "2025-07-09 02:40", "2025-07-09 02:41", "2025-07-09 02:42", "2025-07-09 02:43", "2025-07-09 02:44", "2025-07-09 02:45", "2025-07-09 02:46", "2025-07-09 02:47", "2025-07-09 02:48", "2025-07-09 02:49", "2025-07-09 02:50", "2025-07-09 02:51", "2025-07-09 02:52", "2025-07-09 02:53", "2025-07-09 02:54", "2025-07-09 02:55", "2025-07-09 02:56", "2025-07-09 02:57", "2025-07-09 02:58", "2025-07-09 02:59", "2025-07-09 03:00", "2025-07-09 03:01", "2025-07-09 03:02", "2025-07-09 03:03", "2025-07-09 03:04", "2025-07-09 03:05", "2025-07-09 03:06", "2025-07-09 03:07", "2025-07-09 03:08", "2025-07-09 03:09", "2025-07-09 03:10", "2025-07-09 03:11", "2025-07-09 03:12", "2025-07-09 03:13", "2025-07-09 03:14", "2025-07-09 03:15", "2025-07-09 03:16", "2025-07-09 03:17", "2025-07-09 03:18", "2025-07-09 03:19", "2025-07-09 03:20", "2025-07-09 03:21", "2025-07-09 03:22", "2025-07-09 03:23", "2025-07-09 03:24", "2025-07-09 03:25", "2025-07-09 03:26", "2025-07-09 03:27", "2025-07-09 03:28", "2025-07-09 03:29", "2025-07-09 03:30", "2025-07-09 03:31", "2025-07-09 03:32", "2025-07-09 03:33", "2025-07-09 03:34", "2025-07-09 03:35", "2025-07-09 03:36", "2025-07-09 03:37", "2025-07-09 03:38", "2025-07-09 03:39", "2025-07-09 03:40", "2025-07-09 03:41", "2025-07-09 03:42", "2025-07-09 03:43", "2025-07-09 03:44", "2025-07-09 03:45", "2025-07-09 03:46", "2025-07-09 03:47", "2025-07-09 03:48", "2025-07-09 03:49", "2025-07-09 03:50", "2025-07-09 03:51", "2025-07-09 03:52", "2025-07-09 03:53", "2025-07-09 03:54", "2025-07-09 03:55", "2025-07-09 03:56", "2025-07-09 03:57", "2025-07-09 03:58", "2025-07-09 03:59", "2025-07-09 04:00", "2025-07-09 04:01", "2025-07-09 04:02", "2025-07-09 04:03", "2025-07-09 04:04", "2025-07-09 04:05", "2025-07-09 04:06", "2025-07-09 04:07", "2025-07-09 04:08", "2025-07-09 04:09", "2025-07-09 04:10", "2025-07-09 04:11", "2025-07-09 04:12", "2025-07-09 04:13", "2025-07-09 04:14", "2025-07-09 04:15", "2025-07-09 04:16", "2025-07-09 04:17", "2025-07-09 04:18", "2025-07-09 04:19", "2025-07-09 04:20", "2025-07-09 04:21", "2025-07-09 04:22", "2025-07-09 04:23", "2025-07-09 04:24", "2025-07-09 04:25", "2025-07-09 04:26", "2025-07-09 04:27", "2025-07-09 04:28", "2025-07-09 04:29", "2025-07-09 04:30", "2025-07-09 04:31", "2025-07-09 04:32", "2025-07-09 04:33", "2025-07-09 04:34", "2025-07-09 04:35", "2025-07-09 04:36", "2025-07-09 04:37", "2025-07-09 04:38", "2025-07-09 04:39", "2025-07-09 04:40", "2025-07-09 04:41", "2025-07-09 04:42", "2025-07-09 04:43", "2025-07-09 04:44", "2025-07-09 04:45", "2025-07-09 04:46", "2025-07-09 04:47", "2025-07-09 04:48", "2025-07-09 04:49", "2025-07-09 04:50", "2025-07-09 04:51", "2025-07-09 04:52", "2025-07-09 04:53", "2025-07-09 04:54", "2025-07-09 04:55", "2025-07-09 04:56", "2025-07-09 04:57", "2025-07-09 04:58", "2025-07-09 04:59", "2025-07-09 05:00", "2025-07-09 05:01", "2025-07-09 05:02", "2025-07-09 05:03", "2025-07-09 05:04", "2025-07-09 05:05", "2025-07-09 05:06", "2025-07-09 05:07", "2025-07-09 05:08", "2025-07-09 05:09", "2025-07-09 05:10", "2025-07-09 05:11", "2025-07-09 05:12", "2025-07-09 05:13", "2025-07-09 05:14", "2025-07-09 05:15", "2025-07-09 05:16", "2025-07-09 05:17", "2025-07-09 05:18", "2025-07-09 05:19", "2025-07-09 05:20", "2025-07-09 05:21", "2025-07-09 05:22", "2025-07-09 05:23", "2025-07-09 05:24", "2025-07-09 05:25", "2025-07-09 05:26", "2025-07-09 05:27", "2025-07-09 05:28", "2025-07-09 05:29", "2025-07-09 05:30", "2025-07-09 05:31", "2025-07-09 05:32", "2025-07-09 05:33", "2025-07-09 05:34", "2025-07-09 05:35", "2025-07-09 05:36", "2025-07-09 05:37", "2025-07-09 05:38", "2025-07-09 05:39", "2025-07-09 05:40", "2025-07-09 05:41", "2025-07-09 05:42", "2025-07-09 05:43", "2025-07-09 05:44", "2025-07-09 05:45", "2025-07-09 05:46", "2025-07-09 05:47", "2025-07-09 05:48", "2025-07-09 05:49", "2025-07-09 05:50", "2025-07-09 05:51", "2025-07-09 05:52", "2025-07-09 05:53", "2025-07-09 05:54", "2025-07-09 05:55", "2025-07-09 05:56", "2025-07-09 05:57", "2025-07-09 05:58", "2025-07-09 05:59", "2025-07-09 06:00", "2025-07-09 06:01", "2025-07-09 06:02", "2025-07-09 06:03", "2025-07-09 06:04", "2025-07-09 06:05", "2025-07-09 06:06", "2025-07-09 06:07", "2025-07-09 06:08", "2025-07-09 06:09", "2025-07-09 06:10", "2025-07-09 06:11", "2025-07-09 06:12", "2025-07-09 06:13", "2025-07-09 06:14", "2025-07-09 06:15", "2025-07-09 06:16", "2025-07-09 06:17", "2025-07-09 06:18", "2025-07-09 06:19", "2025-07-09 06:20", "2025-07-09 06:21", "2025-07-09 06:22", "2025-07-09 06:23", "2025-07-09 06:24", "2025-07-09 06:25", "2025-07-09 06:26", "2025-07-09 06:27", "2025-07-09 06:28", "2025-07-09 06:29", "2025-07-09 06:30", "2025-07-09 06:31", "2025-07-09 06:32", "2025-07-09 06:33", "2025-07-09 06:34", "2025-07-09 06:35", "2025-07-09 06:36", "2025-07-09 06:37", "2025-07-09 06:38", "2025-07-09 06:39", "2025-07-09 06:40", "2025-07-09 06:41", "2025-07-09 06:42", "2025-07-09 06:43", "2025-07-09 06:44", "2025-07-09 06:45", "2025-07-09 06:46", "2025-07-09 06:47", "2025-07-09 06:48", "2025-07-09 06:49", "2025-07-09 06:50", "2025-07-09 06:51", "2025-07-09 06:52", "2025-07-09 06:53", "2025-07-09 06:54", "2025-07-09 06:55", "2025-07-09 06:56", "2025-07-09 06:57", "2025-07-09 06:58", "2025-07-09 06:59", "2025-07-09 07:00", "2025-07-09 07:01", "2025-07-09 07:02", "2025-07-09 07:03", "2025-07-09 07:04", "2025-07-09 07:05", "2025-07-09 07:06", "2025-07-09 07:07", "2025-07-09 07:08", "2025-07-09 07:09", "2025-07-09 07:10", "2025-07-09 07:11", "2025-07-09 07:12", "2025-07-09 07:13", "2025-07-09 07:14", "2025-07-09 07:15", "2025-07-09 07:16", "2025-07-09 07:17", "2025-07-09 07:18", "2025-07-09 07:19", "2025-07-09 07:20", "2025-07-09 07:21", "2025-07-09 07:22", "2025-07-09 07:23", "2025-07-09 07:24", "2025-07-09 07:25", "2025-07-09 07:26", "2025-07-09 07:27", "2025-07-09 07:28", "2025-07-09 07:29", "2025-07-09 07:30", "2025-07-09 07:31", "2025-07-09 07:32", "2025-07-09 07:33", "2025-07-09 07:34", "2025-07-09 07:35", "2025-07-09 07:36", "2025-07-09 07:37", "2025-07-09 07:38", "2025-07-09 07:39", "2025-07-09 07:40", "2025-07-09 07:41", "2025-07-09 07:42", "2025-07-09 07:43", "2025-07-09 07:44", "2025-07-09 07:45", "2025-07-09 07:46", "2025-07-09 07:47", "2025-07-09 07:48", "2025-07-09 07:49", "2025-07-09 07:50", "2025-07-09 07:51", "2025-07-09 07:52", "2025-07-09 07:53", "2025-07-09 07:54", "2025-07-09 07:55", "2025-07-09 07:56", "2025-07-09 07:57", "2025-07-09 07:58", "2025-07-09 07:59", "2025-07-09 08:00", "2025-07-09 08:01", "2025-07-09 08:02", "2025-07-09 08:03", "2025-07-09 08:04", "2025-07-09 08:05", "2025-07-09 08:06", "2025-07-09 08:07", "2025-07-09 08:08", "2025-07-09 08:09", "2025-07-09 08:10", "2025-07-09 08:11", "2025-07-09 08:12", "2025-07-09 08:13", "2025-07-09 08:14", "2025-07-09 08:15", "2025-07-09 08:16", "2025-07-09 08:17", "2025-07-09 08:18", "2025-07-09 08:19", "2025-07-09 08:20", "2025-07-09 08:21", "2025-07-09 08:22", "2025-07-09 08:23", "2025-07-09 08:24", "2025-07-09 08:25", "2025-07-09 08:26", "2025-07-09 08:27", "2025-07-09 08:28", "2025-07-09 08:29", "2025-07-09 08:30", "2025-07-09 08:31", "2025-07-09 08:32", "2025-07-09 08:33", "2025-07-09 08:34", "2025-07-09 08:35", "2025-07-09 08:36", "2025-07-09 08:37", "2025-07-09 08:38", "2025-07-09 08:39", "2025-07-09 08:40", "2025-07-09 08:41", "2025-07-09 08:42", "2025-07-09 08:43", "2025-07-09 08:44", "2025-07-09 08:45", "2025-07-09 08:46", "2025-07-09 08:47", "2025-07-09 08:48", "2025-07-09 08:49", "2025-07-09 08:50", "2025-07-09 08:51", "2025-07-09 08:52", "2025-07-09 08:53", "2025-07-09 08:54", "2025-07-09 08:55", "2025-07-09 08:56", "2025-07-09 08:57", "2025-07-09 08:58", "2025-07-09 08:59", "2025-07-09 09:00", "2025-07-09 09:01", "2025-07-09 09:02", "2025-07-09 09:03", "2025-07-09 09:04", "2025-07-09 09:05", "2025-07-09 09:06", "2025-07-09 09:07", "2025-07-09 09:08", "2025-07-09 09:09", "2025-07-09 09:10", "2025-07-09 09:11", "2025-07-09 09:12", "2025-07-09 09:13", "2025-07-09 09:14", "2025-07-09 09:15", "2025-07-09 09:16", "2025-07-09 09:17", "2025-07-09 09:18", "2025-07-09 09:19", "2025-07-09 09:20", "2025-07-09 09:21", "2025-07-09 09:22", "2025-07-09 09:23", "2025-07-09 09:24", "2025-07-09 09:25", "2025-07-09 09:26", "2025-07-09 09:27", "2025-07-09 09:28", "2025-07-09 09:29", "2025-07-09 09:30", "2025-07-09 09:31", "2025-07-09 09:32", "2025-07-09 09:33", "2025-07-09 09:34", "2025-07-09 09:35", "2025-07-09 09:36", "2025-07-09 09:37", "2025-07-09 09:38", "2025-07-09 09:39", "2025-07-09 09:40", "2025-07-09 09:41", "2025-07-09 09:42", "2025-07-09 09:43", "2025-07-09 09:44", "2025-07-09 09:45", "2025-07-09 09:46", "2025-07-09 09:47", "2025-07-09 09:48", "2025-07-09 09:49", "2025-07-09 09:50", "2025-07-09 09:51", "2025-07-09 09:52", "2025-07-09 09:53", "2025-07-09 09:54", "2025-07-09 09:55", "2025-07-09 09:56", "2025-07-09 09:57", "2025-07-09 09:58", "2025-07-09 09:59", "2025-07-09 10:00", "2025-07-09 10:01", "2025-07-09 10:02", "2025-07-09 10:03", "2025-07-09 10:04", "2025-07-09 10:05", "2025-07-09 10:06", "2025-07-09 10:07", "2025-07-09 10:08", "2025-07-09 10:09", "2025-07-09 10:10", "2025-07-09 10:11", "2025-07-09 10:12", "2025-07-09 10:13", "2025-07-09 10:14", "2025-07-09 10:15", "2025-07-09 10:16", "2025-07-09 10:17", "2025-07-09 10:18", "2025-07-09 10:19", "2025-07-09 10:20", "2025-07-09 10:21", "2025-07-09 10:22", "2025-07-09 10:23", "2025-07-09 10:24", "2025-07-09 10:25", "2025-07-09 10:26", "2025-07-09 10:27", "2025-07-09 10:28", "2025-07-09 10:29", "2025-07-09 10:30", "2025-07-09 10:31", "2025-07-09 10:32", "2025-07-09 10:33", "2025-07-09 10:34", "2025-07-09 10:35", "2025-07-09 10:36", "2025-07-09 10:37", "2025-07-09 10:38", "2025-07-09 10:39", "2025-07-09 10:40", "2025-07-09 10:41", "2025-07-09 10:42", "2025-07-09 10:43", "2025-07-09 10:44", "2025-07-09 10:45", "2025-07-09 10:46", "2025-07-09 10:47", "2025-07-09 10:48", "2025-07-09 10:49", "2025-07-09 10:50", "2025-07-09 10:51", "2025-07-09 10:52", "2025-07-09 10:53", "2025-07-09 10:54", "2025-07-09 10:55", "2025-07-09 10:56", "2025-07-09 10:57", "2025-07-09 10:58", "2025-07-09 10:59", "2025-07-09 11:00", "2025-07-09 11:01", "2025-07-09 11:02", "2025-07-09 11:03", "2025-07-09 11:04", "2025-07-09 11:05", "2025-07-09 11:06", "2025-07-09 11:07", "2025-07-09 11:08", "2025-07-09 11:09", "2025-07-09 11:10", "2025-07-09 11:11", "2025-07-09 11:12", "2025-07-09 11:13", "2025-07-09 11:14", "2025-07-09 11:15", "2025-07-09 11:16", "2025-07-09 11:17", "2025-07-09 11:18", "2025-07-09 11:19", "2025-07-09 11:20", "2025-07-09 11:21", "2025-07-09 11:22", "2025-07-09 11:23", "2025-07-09 11:24", "2025-07-09 11:25", "2025-07-09 11:26", "2025-07-09 11:27", "2025-07-09 11:28", "2025-07-09 11:29", "2025-07-09 11:30", "2025-07-09 11:31", "2025-07-09 11:32", "2025-07-09 11:33", "2025-07-09 11:34", "2025-07-09 11:35", "2025-07-09 11:36", "2025-07-09 11:37", "2025-07-09 11:38", "2025-07-09 11:39", "2025-07-09 11:40", "2025-07-09 11:41", "2025-07-09 11:42", "2025-07-09 11:43", "2025-07-09 11:44", "2025-07-09 11:45", "2025-07-09 11:46", "2025-07-09 11:47", "2025-07-09 11:48", "2025-07-09 11:49", "2025-07-09 11:50", "2025-07-09 11:51", "2025-07-09 11:52", "2025-07-09 11:53", "2025-07-09 11:54", "2025-07-09 11:55", "2025-07-09 11:56", "2025-07-09 11:57", "2025-07-09 11:58", "2025-07-09 11:59", "2025-07-09 12:00", "2025-07-09 12:01", "2025-07-09 12:02", "2025-07-09 12:03", "2025-07-09 12:04", "2025-07-09 12:05", "2025-07-09 12:06", "2025-07-09 12:07", "2025-07-09 12:08", "2025-07-09 12:09", "2025-07-09 12:10", "2025-07-09 12:11", "2025-07-09 12:12", "2025-07-09 12:13", "2025-07-09 12:14", "2025-07-09 12:15", "2025-07-09 12:16", "2025-07-09 12:17", "2025-07-09 12:18", "2025-07-09 12:19", "2025-07-09 12:20", "2025-07-09 12:21", "2025-07-09 12:22", "2025-07-09 12:23", "2025-07-09 12:24", "2025-07-09 12:25", "2025-07-09 12:26", "2025-07-09 12:27", "2025-07-09 12:28", "2025-07-09 12:29", "2025-07-09 12:30", "2025-07-09 12:31", "2025-07-09 12:32", "2025-07-09 12:33", "2025-07-09 12:34", "2025-07-09 12:35", "2025-07-09 12:36", "2025-07-09 12:37", "2025-07-09 12:38", "2025-07-09 12:39", "2025-07-09 12:40", "2025-07-09 12:41", "2025-07-09 12:42", "2025-07-09 12:43", "2025-07-09 12:44", "2025-07-09 12:45", "2025-07-09 12:46", "2025-07-09 12:47", "2025-07-09 12:48", "2025-07-09 12:49", "2025-07-09 12:50", "2025-07-09 12:51", "2025-07-09 12:52", "2025-07-09 12:53", "2025-07-09 12:54", "2025-07-09 12:55", "2025-07-09 12:56", "2025-07-09 12:57", "2025-07-09 12:58", "2025-07-09 12:59", "2025-07-09 13:00", "2025-07-09 13:01", "2025-07-09 13:02", "2025-07-09 13:03", "2025-07-09 13:04", "2025-07-09 13:05", "2025-07-09 13:06", "2025-07-09 13:07", "2025-07-09 13:08", "2025-07-09 13:09", "2025-07-09 13:10", "2025-07-09 13:11", "2025-07-09 13:12", "2025-07-09 13:13", "2025-07-09 13:14", "2025-07-09 13:15", "2025-07-09 13:16", "2025-07-09 13:17", "2025-07-09 13:18", "2025-07-09 13:19", "2025-07-09 13:20", "2025-07-09 13:21", "2025-07-09 13:22", "2025-07-09 13:23", "2025-07-09 13:24", "2025-07-09 13:25", "2025-07-09 13:26", "2025-07-09 13:27", "2025-07-09 13:28", "2025-07-09 13:29", "2025-07-09 13:30", "2025-07-09 13:31", "2025-07-09 13:32", "2025-07-09 13:33", "2025-07-09 13:34", "2025-07-09 13:35", "2025-07-09 13:36", "2025-07-09 13:37", "2025-07-09 13:38", "2025-07-09 13:39", "2025-07-09 13:40", "2025-07-09 13:41", "2025-07-09 13:42", "2025-07-09 13:43", "2025-07-09 13:44", "2025-07-09 13:45", "2025-07-09 13:46", "2025-07-09 13:47", "2025-07-09 13:48", "2025-07-09 13:49", "2025-07-09 13:50", "2025-07-09 13:51", "2025-07-09 13:52", "2025-07-09 13:53", "2025-07-09 13:54", "2025-07-09 13:55", "2025-07-09 13:56", "2025-07-09 13:57", "2025-07-09 13:58", "2025-07-09 13:59", "2025-07-09 14:00", "2025-07-09 14:01", "2025-07-09 14:02", "2025-07-09 14:03", "2025-07-09 14:04", "2025-07-09 14:05", "2025-07-09 14:06", "2025-07-09 14:07", "2025-07-09 14:08", "2025-07-09 14:09", "2025-07-09 14:10", "2025-07-09 14:11", "2025-07-09 14:12", "2025-07-09 14:13", "2025-07-09 14:14", "2025-07-09 14:15", "2025-07-09 14:16", "2025-07-09 14:17", "2025-07-09 14:18", "2025-07-09 14:19", "2025-07-09 14:20", "2025-07-09 14:21", "2025-07-09 14:22", "2025-07-09 14:23", "2025-07-09 14:24", "2025-07-09 14:25", "2025-07-09 14:26", "2025-07-09 14:27", "2025-07-09 14:28", "2025-07-09 14:29", "2025-07-09 14:30", "2025-07-09 14:31", "2025-07-09 14:32", "2025-07-09 14:33", "2025-07-09 14:34", "2025-07-09 14:35", "2025-07-09 14:36", "2025-07-09 14:37", "2025-07-09 14:38", "2025-07-09 14:39", "2025-07-09 14:40", "2025-07-09 14:41", "2025-07-09 14:42", "2025-07-09 14:43", "2025-07-09 14:44", "2025-07-09 14:45", "2025-07-09 14:46", "2025-07-09 14:47", "2025-07-09 14:48", "2025-07-09 14:49", "2025-07-09 14:50", "2025-07-09 14:51", "2025-07-09 14:52", "2025-07-09 14:53", "2025-07-09 14:54", "2025-07-09 14:55", "2025-07-09 14:56", "2025-07-09 14:57", "2025-07-09 14:58", "2025-07-09 14:59", "2025-07-09 15:00", "2025-07-09 15:01", "2025-07-09 15:02", "2025-07-09 15:03", "2025-07-09 15:04", "2025-07-09 15:05", "2025-07-09 15:06", "2025-07-09 15:07", "2025-07-09 15:08", "2025-07-09 15:09", "2025-07-09 15:10", "2025-07-09 15:11", "2025-07-09 15:12", "2025-07-09 15:13", "2025-07-09 15:14", "2025-07-09 15:15", "2025-07-09 15:16", "2025-07-09 15:17", "2025-07-09 15:18", "2025-07-09 15:19", "2025-07-09 15:20", "2025-07-09 15:21", "2025-07-09 15:22", "2025-07-09 15:23", "2025-07-09 15:24", "2025-07-09 15:25", "2025-07-09 15:26", "2025-07-09 15:27", "2025-07-09 15:28", "2025-07-09 15:29", "2025-07-09 15:30", "2025-07-09 15:31", "2025-07-09 15:32", "2025-07-09 15:33", "2025-07-09 15:34", "2025-07-09 15:35", "2025-07-09 15:36", "2025-07-09 15:37", "2025-07-09 15:38", "2025-07-09 15:39", "2025-07-09 15:40", "2025-07-09 15:41", "2025-07-09 15:42", "2025-07-09 15:43", "2025-07-09 15:44", "2025-07-09 15:45", "2025-07-09 15:46", "2025-07-09 15:47", "2025-07-09 15:48", "2025-07-09 15:49", "2025-07-09 15:50", "2025-07-09 15:51", "2025-07-09 15:52", "2025-07-09 15:53", "2025-07-09 15:54", "2025-07-09 15:55", "2025-07-09 15:56", "2025-07-09 15:57", "2025-07-09 15:58", "2025-07-09 15:59", "2025-07-09 16:00", "2025-07-09 16:01", "2025-07-09 16:02", "2025-07-09 16:03", "2025-07-09 16:04", "2025-07-09 16:05", "2025-07-09 16:06", "2025-07-09 16:07", "2025-07-09 16:08", "2025-07-09 16:09", "2025-07-09 16:10", "2025-07-09 16:11", "2025-07-09 16:12", "2025-07-09 16:13", "2025-07-09 16:14", "2025-07-09 16:15", "2025-07-09 16:16", "2025-07-09 16:17", "2025-07-09 16:18", "2025-07-09 16:19", "2025-07-09 16:20", "2025-07-09 16:21", "2025-07-09 16:22", "2025-07-09 16:23", "2025-07-09 16:24", "2025-07-09 16:25", "2025-07-09 16:26", "2025-07-09 16:27", "2025-07-09 16:28", "2025-07-09 16:29", "2025-07-09 16:30", "2025-07-09 16:31", "2025-07-09 16:32", "2025-07-09 16:33", "2025-07-09 16:34", "2025-07-09 16:35", "2025-07-09 16:36", "2025-07-09 16:37", "2025-07-09 16:38", "2025-07-09 16:39", "2025-07-09 16:40", "2025-07-09 16:41", "2025-07-09 16:42", "2025-07-09 16:43", "2025-07-09 16:44", "2025-07-09 16:45", "2025-07-09 16:46", "2025-07-09 16:47", "2025-07-09 16:48", "2025-07-09 16:49", "2025-07-09 16:50", "2025-07-09 16:51", "2025-07-09 16:52", "2025-07-09 16:53", "2025-07-09 16:54", "2025-07-09 16:55", "2025-07-09 16:56", "2025-07-09 16:57", "2025-07-09 16:58", "2025-07-09 16:59", "2025-07-09 17:00", "2025-07-09 17:01", "2025-07-09 17:02", "2025-07-09 17:03", "2025-07-09 17:04", "2025-07-09 17:05", "2025-07-09 17:06", "2025-07-09 17:07", "2025-07-09 17:08", "2025-07-09 17:09", "2025-07-09 17:10", "2025-07-09 17:11", "2025-07-09 17:12", "2025-07-09 17:13", "2025-07-09 17:14", "2025-07-09 17:15", "2025-07-09 17:16", "2025-07-09 17:17", "2025-07-09 17:18", "2025-07-09 17:19", "2025-07-09 17:20", "2025-07-09 17:21", "2025-07-09 17:22", "2025-07-09 17:23", "2025-07-09 17:24", "2025-07-09 17:25", "2025-07-09 17:26", "2025-07-09 17:27", "2025-07-09 17:28", "2025-07-09 17:29", "2025-07-09 17:30", "2025-07-09 17:31", "2025-07-09 17:32", "2025-07-09 17:33", "2025-07-09 17:34", "2025-07-09 17:35", "2025-07-09 17:36", "2025-07-09 17:37", "2025-07-09 17:38", "2025-07-09 17:39", "2025-07-09 17:40", "2025-07-09 17:41", "2025-07-09 17:42", "2025-07-09 17:43", "2025-07-09 17:44", "2025-07-09 17:45", "2025-07-09 17:46", "2025-07-09 17:47", "2025-07-09 17:48", "2025-07-09 17:49", "2025-07-09 17:50", "2025-07-09 17:51", "2025-07-09 17:52", "2025-07-09 17:53", "2025-07-09 17:54", "2025-07-09 17:55", "2025-07-09 17:56", "2025-07-09 17:57", "2025-07-09 17:58", "2025-07-09 17:59", "2025-07-09 18:00", "2025-07-09 18:01", "2025-07-09 18:02", "2025-07-09 18:03", "2025-07-09 18:04", "2025-07-09 18:05", "2025-07-09 18:06", "2025-07-09 18:07", "2025-07-09 18:08", "2025-07-09 18:09", "2025-07-09 18:10", "2025-07-09 18:11", "2025-07-09 18:12", "2025-07-09 18:13", "2025-07-09 18:14", "2025-07-09 18:15", "2025-07-09 18:16", "2025-07-09 18:17", "2025-07-09 18:18", "2025-07-09 18:19", "2025-07-09 18:20", "2025-07-09 18:21", "2025-07-09 18:22", "2025-07-09 18:23", "2025-07-09 18:24", "2025-07-09 18:25", "2025-07-09 18:26", "2025-07-09 18:27", "2025-07-09 18:28", "2025-07-09 18:29", "2025-07-09 18:30", "2025-07-09 18:31", "2025-07-09 18:32", "2025-07-09 18:33", "2025-07-09 18:34", "2025-07-09 18:35", "2025-07-09 18:36", "2025-07-09 18:37", "2025-07-09 18:38", "2025-07-09 18:39", "2025-07-09 18:40", "2025-07-09 18:41", "2025-07-09 18:42", "2025-07-09 18:43", "2025-07-09 18:44", "2025-07-09 18:45", "2025-07-09 18:46", "2025-07-09 18:47", "2025-07-09 18:48", "2025-07-09 18:49", "2025-07-09 18:50", "2025-07-09 18:51", "2025-07-09 18:52", "2025-07-09 18:53", "2025-07-09 18:54", "2025-07-09 18:55", "2025-07-09 18:56", "2025-07-09 18:57", "2025-07-09 18:58", "2025-07-09 18:59", "2025-07-09 19:00", "2025-07-09 19:01", "2025-07-09 19:02", "2025-07-09 19:03", "2025-07-09 19:04", "2025-07-09 19:05", "2025-07-09 19:06", "2025-07-09 19:07", "2025-07-09 19:08", "2025-07-09 19:09", "2025-07-09 19:10", "2025-07-09 19:11", "2025-07-09 19:12", "2025-07-09 19:13", "2025-07-09 19:14", "2025-07-09 19:15", "2025-07-09 19:16", "2025-07-09 19:17", "2025-07-09 19:18", "2025-07-09 19:19", "2025-07-09 19:20", "2025-07-09 19:21", "2025-07-09 19:22", "2025-07-09 19:23", "2025-07-09 19:24", "2025-07-09 19:25", "2025-07-09 19:26", "2025-07-09 19:27", "2025-07-09 19:28", "2025-07-09 19:29", "2025-07-09 19:30", "2025-07-09 19:31", "2025-07-09 19:32", "2025-07-09 19:33", "2025-07-09 19:34", "2025-07-09 19:35", "2025-07-09 19:36", "2025-07-09 19:37", "2025-07-09 19:38", "2025-07-09 19:39", "2025-07-09 19:40", "2025-07-09 19:41", "2025-07-09 19:42", "2025-07-09 19:43", "2025-07-09 19:44", "2025-07-09 19:45", "2025-07-09 19:46", "2025-07-09 19:47", "2025-07-09 19:48", "2025-07-09 19:49", "2025-07-09 19:50", "2025-07-09 19:51", "2025-07-09 19:52", "2025-07-09 19:53", "2025-07-09 19:54", "2025-07-09 19:55", "2025-07-09 19:56", "2025-07-09 19:57", "2025-07-09 19:58", "2025-07-09 19:59", "2025-07-09 20:00", "2025-07-09 20:01", "2025-07-09 20:02", "2025-07-09 20:03", "2025-07-09 20:04", "2025-07-09 20:05", "2025-07-09 20:06", "2025-07-09 20:07", "2025-07-09 20:08", "2025-07-09 20:09", "2025-07-09 20:10", "2025-07-09 20:11", "2025-07-09 20:12", "2025-07-09 20:13", "2025-07-09 20:14", "2025-07-09 20:15", "2025-07-09 20:16", "2025-07-09 20:17", "2025-07-09 20:18", "2025-07-09 20:19", "2025-07-09 20:20", "2025-07-09 20:21", "2025-07-09 20:22", "2025-07-09 20:23", "2025-07-09 20:24", "2025-07-09 20:25", "2025-07-09 20:26", "2025-07-09 20:27", "2025-07-09 20:28", "2025-07-09 20:29", "2025-07-09 20:30", "2025-07-09 20:31", "2025-07-09 20:32", "2025-07-09 20:33", "2025-07-09 20:34", "2025-07-09 20:35", "2025-07-09 20:36", "2025-07-09 20:37", "2025-07-09 20:38", "2025-07-09 20:39", "2025-07-09 20:40", "2025-07-09 20:41", "2025-07-09 20:42", "2025-07-09 20:43", "2025-07-09 20:44", "2025-07-09 20:45", "2025-07-09 20:46", "2025-07-09 20:47", "2025-07-09 20:48", "2025-07-09 20:49", "2025-07-09 20:50", "2025-07-09 20:51", "2025-07-09 20:52", "2025-07-09 20:53", "2025-07-09 20:54", "2025-07-09 20:55", "2025-07-09 20:56", "2025-07-09 20:57", "2025-07-09 20:58", "2025-07-09 20:59", "2025-07-09 21:00", "2025-07-09 21:01", "2025-07-09 21:02", "2025-07-09 21:03", "2025-07-09 21:04", "2025-07-09 21:05", "2025-07-09 21:06", "2025-07-09 21:07", "2025-07-09 21:08", "2025-07-09 21:09", "2025-07-09 21:10", "2025-07-09 21:11", "2025-07-09 21:12", "2025-07-09 21:13", "2025-07-09 21:14", "2025-07-09 21:15", "2025-07-09 21:16", "2025-07-09 21:17", "2025-07-09 21:18", "2025-07-09 21:19", "2025-07-09 21:20", "2025-07-09 21:21", "2025-07-09 21:22", "2025-07-09 21:23", "2025-07-09 21:24", "2025-07-09 21:25", "2025-07-09 21:26", "2025-07-09 21:27", "2025-07-09 21:28", "2025-07-09 21:29", "2025-07-09 21:30", "2025-07-09 21:31", "2025-07-09 21:32", "2025-07-09 21:33", "2025-07-09 21:34", "2025-07-09 21:35", "2025-07-09 21:36", "2025-07-09 21:37", "2025-07-09 21:38", "2025-07-09 21:39", "2025-07-09 21:40", "2025-07-09 21:41", "2025-07-09 21:42", "2025-07-09 21:43", "2025-07-09 21:44", "2025-07-09 21:45", "2025-07-09 21:46", "2025-07-09 21:47", "2025-07-09 21:48", "2025-07-09 21:49", "2025-07-09 21:50", "2025-07-09 21:51", "2025-07-09 21:52", "2025-07-09 21:53", "2025-07-09 21:54", "2025-07-09 21:55", "2025-07-09 21:56", "2025-07-09 21:57", "2025-07-09 21:58", "2025-07-09 21:59", "2025-07-09 22:00", "2025-07-09 22:01", "2025-07-09 22:02", "2025-07-09 22:03", "2025-07-09 22:04", "2025-07-09 22:05", "2025-07-09 22:06", "2025-07-09 22:07", "2025-07-09 22:08", "2025-07-09 22:09", "2025-07-09 22:10", "2025-07-09 22:11", "2025-07-09 22:12", "2025-07-09 22:13", "2025-07-09 22:14", "2025-07-09 22:15", "2025-07-09 22:16", "2025-07-09 22:17", "2025-07-09 22:18", "2025-07-09 22:19", "2025-07-09 22:20", "2025-07-09 22:21", "2025-07-09 22:22", "2025-07-09 22:23", "2025-07-09 22:24", "2025-07-09 22:25", "2025-07-09 22:26", "2025-07-09 22:27", "2025-07-09 22:28", "2025-07-09 22:29", "2025-07-09 22:30", "2025-07-09 22:31", "2025-07-09 22:32", "2025-07-09 22:33", "2025-07-09 22:34", "2025-07-09 22:35", "2025-07-09 22:36", "2025-07-09 22:37", "2025-07-09 22:38", "2025-07-09 22:39", "2025-07-09 22:40", "2025-07-09 22:41", "2025-07-09 22:42", "2025-07-09 22:43", "2025-07-09 22:44", "2025-07-09 22:45", "2025-07-09 22:46", "2025-07-09 22:47", "2025-07-09 22:48", "2025-07-09 22:49", "2025-07-09 22:50", "2025-07-09 22:51", "2025-07-09 22:52", "2025-07-09 22:53", "2025-07-09 22:54", "2025-07-09 22:55", "2025-07-09 22:56", "2025-07-09 22:57", "2025-07-09 22:58", "2025-07-09 22:59", "2025-07-09 23:00", "2025-07-09 23:01", "2025-07-09 23:02", "2025-07-09 23:03", "2025-07-09 23:04", "2025-07-09 23:05", "2025-07-09 23:06", "2025-07-09 23:07", "2025-07-09 23:08", "2025-07-09 23:09", "2025-07-09 23:10", "2025-07-09 23:11", "2025-07-09 23:12", "2025-07-09 23:13", "2025-07-09 23:14", "2025-07-09 23:15", "2025-07-09 23:16", "2025-07-09 23:17", "2025-07-09 23:18", "2025-07-09 23:19", "2025-07-09 23:20", "2025-07-09 23:21", "2025-07-09 23:22", "2025-07-09 23:23", "2025-07-09 23:24", "2025-07-09 23:25", "2025-07-09 23:26", "2025-07-09 23:27", "2025-07-09 23:28", "2025-07-09 23:29", "2025-07-09 23:30", "2025-07-09 23:31", "2025-07-09 23:32", "2025-07-09 23:33", "2025-07-09 23:34", "2025-07-09 23:35", "2025-07-09 23:36", "2025-07-09 23:37", "2025-07-09 23:38", "2025-07-09 23:39", "2025-07-09 23:40", "2025-07-09 23:41", "2025-07-09 23:42", "2025-07-09 23:43", "2025-07-09 23:44", "2025-07-09 23:45", "2025-07-09 23:46", "2025-07-09 23:47", "2025-07-09 23:48", "2025-07-09 23:49", "2025-07-09 23:50", "2025-07-09 23:51", "2025-07-09 23:52", "2025-07-09 23:53", "2025-07-09 23:54", "2025-07-09 23:55", "2025-07-09 23:56", "2025-07-09 23:57", "2025-07-09 23:58", "2025-07-09 23:59", "2025-07-10 00:00", "2025-07-10 00:01", "2025-07-10 00:02", "2025-07-10 00:03", "2025-07-10 00:04", "2025-07-10 00:05", "2025-07-10 00:06", "2025-07-10 00:07", "2025-07-10 00:08", "2025-07-10 00:09", "2025-07-10 00:10", "2025-07-10 00:11", "2025-07-10 00:12", "2025-07-10 00:13", "2025-07-10 00:14", "2025-07-10 00:15", "2025-07-10 00:16", "2025-07-10 00:17", "2025-07-10 00:18", "2025-07-10 00:19", "2025-07-10 00:20", "2025-07-10 00:21", "2025-07-10 00:22", "2025-07-10 00:23", "2025-07-10 00:24", "2025-07-10 00:25", "2025-07-10 00:26", "2025-07-10 00:27", "2025-07-10 00:28", "2025-07-10 00:29", "2025-07-10 00:30", "2025-07-10 00:31", "2025-07-10 00:32", "2025-07-10 00:33", "2025-07-10 00:34", "2025-07-10 00:35", "2025-07-10 00:36", "2025-07-10 00:37", "2025-07-10 00:38", "2025-07-10 00:39", "2025-07-10 00:40", "2025-07-10 00:41", "2025-07-10 00:42", "2025-07-10 00:43", "2025-07-10 00:44", "2025-07-10 00:45", "2025-07-10 00:46", "2025-07-10 00:47", "2025-07-10 00:48", "2025-07-10 00:49", "2025-07-10 00:50", "2025-07-10 00:51", "2025-07-10 00:52", "2025-07-10 00:53", "2025-07-10 00:54", "2025-07-10 00:55", "2025-07-10 00:56", "2025-07-10 00:57", "2025-07-10 00:58", "2025-07-10 00:59", "2025-07-10 01:00", "2025-07-10 01:01", "2025-07-10 01:02", "2025-07-10 01:03", "2025-07-10 01:04", "2025-07-10 01:05", "2025-07-10 01:06", "2025-07-10 01:07", "2025-07-10 01:08", "2025-07-10 01:09", "2025-07-10 01:10", "2025-07-10 01:11", "2025-07-10 01:12", "2025-07-10 01:13", "2025-07-10 01:14", "2025-07-10 01:15", "2025-07-10 01:16", "2025-07-10 01:17", "2025-07-10 01:18", "2025-07-10 01:19", "2025-07-10 01:20", "2025-07-10 01:21", "2025-07-10 01:22", "2025-07-10 01:23", "2025-07-10 01:24", "2025-07-10 01:25", "2025-07-10 01:26", "2025-07-10 01:27", "2025-07-10 01:28", "2025-07-10 01:29", "2025-07-10 01:30", "2025-07-10 01:31", "2025-07-10 01:32", "2025-07-10 01:33", "2025-07-10 01:34", "2025-07-10 01:35", "2025-07-10 01:36", "2025-07-10 01:37", "2025-07-10 01:38", "2025-07-10 01:39", "2025-07-10 01:40", "2025-07-10 01:41", "2025-07-10 01:42", "2025-07-10 01:43", "2025-07-10 01:44", "2025-07-10 01:45", "2025-07-10 01:46", "2025-07-10 01:47", "2025-07-10 01:48", "2025-07-10 01:49", "2025-07-10 01:50", "2025-07-10 01:51", "2025-07-10 01:52", "2025-07-10 01:53", "2025-07-10 01:54", "2025-07-10 01:55", "2025-07-10 01:56", "2025-07-10 01:57", "2025-07-10 01:58", "2025-07-10 01:59", "2025-07-10 02:00", "2025-07-10 02:01", "2025-07-10 02:02", "2025-07-10 02:03", "2025-07-10 02:04", "2025-07-10 02:05", "2025-07-10 02:06", "2025-07-10 02:07", "2025-07-10 02:08", "2025-07-10 02:09", "2025-07-10 02:10", "2025-07-10 02:11", "2025-07-10 02:12", "2025-07-10 02:13", "2025-07-10 02:14", "2025-07-10 02:15", "2025-07-10 02:16", "2025-07-10 02:17", "2025-07-10 02:18", "2025-07-10 02:19", "2025-07-10 02:20", "2025-07-10 02:21", "2025-07-10 02:22", "2025-07-10 02:23", "2025-07-10 02:24", "2025-07-10 02:25", "2025-07-10 02:26", "2025-07-10 02:27", "2025-07-10 02:28", "2025-07-10 02:29", "2025-07-10 02:30", "2025-07-10 02:31", "2025-07-10 02:32", "2025-07-10 02:33", "2025-07-10 02:34", "2025-07-10 02:35", "2025-07-10 02:36", "2025-07-10 02:37", "2025-07-10 02:38", "2025-07-10 02:39", "2025-07-10 02:40", "2025-07-10 02:41", "2025-07-10 02:42", "2025-07-10 02:43", "2025-07-10 02:44", "2025-07-10 02:45", "2025-07-10 02:46", "2025-07-10 02:47", "2025-07-10 02:48", "2025-07-10 02:49", "2025-07-10 02:50", "2025-07-10 02:51", "2025-07-10 02:52", "2025-07-10 02:53", "2025-07-10 02:54", "2025-07-10 02:55", "2025-07-10 02:56", "2025-07-10 02:57", "2025-07-10 02:58", "2025-07-10 02:59", "2025-07-10 03:00", "2025-07-10 03:01", "2025-07-10 03:02", "2025-07-10 03:03", "2025-07-10 03:04", "2025-07-10 03:05", "2025-07-10 03:06", "2025-07-10 03:07", "2025-07-10 03:08", "2025-07-10 03:09", "2025-07-10 03:10", "2025-07-10 03:11", "2025-07-10 03:12", "2025-07-10 03:13", "2025-07-10 03:14", "2025-07-10 03:15", "2025-07-10 03:16", "2025-07-10 03:17", "2025-07-10 03:18", "2025-07-10 03:19", "2025-07-10 03:20", "2025-07-10 03:21", "2025-07-10 03:22", "2025-07-10 03:23", "2025-07-10 03:24", "2025-07-10 03:25", "2025-07-10 03:26", "2025-07-10 03:27", "2025-07-10 03:28", "2025-07-10 03:29", "2025-07-10 03:30", "2025-07-10 03:31", "2025-07-10 03:32", "2025-07-10 03:33", "2025-07-10 03:34", "2025-07-10 03:35", "2025-07-10 03:36", "2025-07-10 03:37", "2025-07-10 03:38", "2025-07-10 03:39", "2025-07-10 03:40", "2025-07-10 03:41", "2025-07-10 03:42", "2025-07-10 03:43", "2025-07-10 03:44", "2025-07-10 03:45", "2025-07-10 03:46", "2025-07-10 03:47", "2025-07-10 03:48", "2025-07-10 03:49", "2025-07-10 03:50", "2025-07-10 03:51", "2025-07-10 03:52", "2025-07-10 03:53", "2025-07-10 03:54", "2025-07-10 03:55", "2025-07-10 03:56", "2025-07-10 03:57", "2025-07-10 03:58", "2025-07-10 03:59", "2025-07-10 04:00", "2025-07-10 04:01", "2025-07-10 04:02", "2025-07-10 04:03", "2025-07-10 04:04", "2025-07-10 04:05", "2025-07-10 04:06", "2025-07-10 04:07", "2025-07-10 04:08", "2025-07-10 04:09", "2025-07-10 04:10", "2025-07-10 04:11", "2025-07-10 04:12", "2025-07-10 04:13", "2025-07-10 04:14", "2025-07-10 04:15", "2025-07-10 04:16", "2025-07-10 04:17", "2025-07-10 04:18", "2025-07-10 04:19", "2025-07-10 04:20", "2025-07-10 04:21", "2025-07-10 04:22", "2025-07-10 04:23", "2025-07-10 04:24", "2025-07-10 04:25", "2025-07-10 04:26", "2025-07-10 04:27", "2025-07-10 04:28", "2025-07-10 04:29", "2025-07-10 04:30", "2025-07-10 04:31", "2025-07-10 04:32", "2025-07-10 04:33", "2025-07-10 04:34", "2025-07-10 04:35", "2025-07-10 04:36", "2025-07-10 04:37", "2025-07-10 04:38", "2025-07-10 04:39", "2025-07-10 04:40", "2025-07-10 04:41", "2025-07-10 04:42", "2025-07-10 04:43", "2025-07-10 04:44", "2025-07-10 04:45", "2025-07-10 04:46", "2025-07-10 04:47", "2025-07-10 04:48", "2025-07-10 04:49", "2025-07-10 04:50", "2025-07-10 04:51", "2025-07-10 04:52", "2025-07-10 04:53", "2025-07-10 04:54", "2025-07-10 04:55", "2025-07-10 04:56", "2025-07-10 04:57", "2025-07-10 04:58", "2025-07-10 04:59", "2025-07-10 05:00", "2025-07-10 05:01", "2025-07-10 05:02", "2025-07-10 05:03", "2025-07-10 05:04", "2025-07-10 05:05", "2025-07-10 05:06", "2025-07-10 05:07", "2025-07-10 05:08", "2025-07-10 05:09", "2025-07-10 05:10", "2025-07-10 05:11", "2025-07-10 05:12", "2025-07-10 05:13", "2025-07-10 05:14", "2025-07-10 05:15", "2025-07-10 05:16", "2025-07-10 05:17", "2025-07-10 05:18", "2025-07-10 05:19", "2025-07-10 05:20", "2025-07-10 05:21", "2025-07-10 05:22", "2025-07-10 05:23", "2025-07-10 05:24", "2025-07-10 05:25", "2025-07-10 05:26", "2025-07-10 05:27", "2025-07-10 05:28", "2025-07-10 05:29", "2025-07-10 05:30", "2025-07-10 05:31", "2025-07-10 05:32", "2025-07-10 05:33", "2025-07-10 05:34", "2025-07-10 05:35", "2025-07-10 05:36", "2025-07-10 05:37", "2025-07-10 05:38", "2025-07-10 05:39", "2025-07-10 05:40", "2025-07-10 05:41", "2025-07-10 05:42", "2025-07-10 05:43", "2025-07-10 05:44", "2025-07-10 05:45", "2025-07-10 05:46", "2025-07-10 05:47", "2025-07-10 05:48", "2025-07-10 05:49", "2025-07-10 05:50", "2025-07-10 05:51", "2025-07-10 05:52", "2025-07-10 05:53", "2025-07-10 05:54", "2025-07-10 05:55", "2025-07-10 05:56", "2025-07-10 05:57", "2025-07-10 05:58", "2025-07-10 05:59", "2025-07-10 06:00", "2025-07-10 06:01", "2025-07-10 06:02", "2025-07-10 06:03", "2025-07-10 06:04", "2025-07-10 06:05", "2025-07-10 06:06", "2025-07-10 06:07", "2025-07-10 06:08", "2025-07-10 06:09", "2025-07-10 06:10", "2025-07-10 06:11", "2025-07-10 06:12", "2025-07-10 06:13", "2025-07-10 06:14", "2025-07-10 06:15", "2025-07-10 06:16", "2025-07-10 06:17", "2025-07-10 06:18", "2025-07-10 06:19", "2025-07-10 06:20", "2025-07-10 06:21", "2025-07-10 06:22", "2025-07-10 06:23", "2025-07-10 06:24", "2025-07-10 06:25", "2025-07-10 06:26", "2025-07-10 06:27", "2025-07-10 06:28", "2025-07-10 06:29", "2025-07-10 06:30", "2025-07-10 06:31", "2025-07-10 06:32", "2025-07-10 06:33", "2025-07-10 06:34", "2025-07-10 06:35", "2025-07-10 06:36", "2025-07-10 06:37", "2025-07-10 06:38", "2025-07-10 06:39", "2025-07-10 06:40", "2025-07-10 06:41", "2025-07-10 06:42", "2025-07-10 06:43", "2025-07-10 06:44", "2025-07-10 06:45", "2025-07-10 06:46", "2025-07-10 06:47", "2025-07-10 06:48", "2025-07-10 06:49", "2025-07-10 06:50", "2025-07-10 06:51", "2025-07-10 06:52", "2025-07-10 06:53", "2025-07-10 06:54", "2025-07-10 06:55", "2025-07-10 06:56", "2025-07-10 06:57", "2025-07-10 06:58", "2025-07-10 06:59", "2025-07-10 07:00", "2025-07-10 07:01", "2025-07-10 07:02", "2025-07-10 07:03", "2025-07-10 07:04", "2025-07-10 07:05", "2025-07-10 07:06", "2025-07-10 07:07", "2025-07-10 07:08", "2025-07-10 07:09", "2025-07-10 07:10", "2025-07-10 07:11", "2025-07-10 07:12", "2025-07-10 07:13", "2025-07-10 07:14", "2025-07-10 07:15", "2025-07-10 07:16", "2025-07-10 07:17", "2025-07-10 07:18", "2025-07-10 07:19", "2025-07-10 07:20", "2025-07-10 07:21", "2025-07-10 07:22", "2025-07-10 07:23", "2025-07-10 07:24", "2025-07-10 07:25", "2025-07-10 07:26", "2025-07-10 07:27", "2025-07-10 07:28", "2025-07-10 07:29", "2025-07-10 07:30", "2025-07-10 07:31", "2025-07-10 07:32", "2025-07-10 07:33", "2025-07-10 07:34", "2025-07-10 07:35", "2025-07-10 07:36", "2025-07-10 07:37", "2025-07-10 07:38", "2025-07-10 07:39", "2025-07-10 07:40", "2025-07-10 07:41", "2025-07-10 07:42", "2025-07-10 07:43", "2025-07-10 07:44", "2025-07-10 07:45", "2025-07-10 07:46", "2025-07-10 07:47", "2025-07-10 07:48", "2025-07-10 07:49", "2025-07-10 07:50", "2025-07-10 07:51", "2025-07-10 07:52", "2025-07-10 07:53", "2025-07-10 07:54", "2025-07-10 07:55", "2025-07-10 07:56", "2025-07-10 07:57", "2025-07-10 07:58", "2025-07-10 07:59", "2025-07-10 08:00", "2025-07-10 08:01", "2025-07-10 08:02", "2025-07-10 08:03", "2025-07-10 08:04", "2025-07-10 08:05", "2025-07-10 08:06", "2025-07-10 08:07", "2025-07-10 08:08", "2025-07-10 08:09", "2025-07-10 08:10", "2025-07-10 08:11", "2025-07-10 08:12", "2025-07-10 08:13", "2025-07-10 08:14", "2025-07-10 08:15", "2025-07-10 08:16", "2025-07-10 08:17", "2025-07-10 08:18", "2025-07-10 08:19", "2025-07-10 08:20", "2025-07-10 08:21", "2025-07-10 08:22", "2025-07-10 08:23", "2025-07-10 08:24", "2025-07-10 08:25", "2025-07-10 08:26", "2025-07-10 08:27", "2025-07-10 08:28", "2025-07-10 08:29", "2025-07-10 08:30", "2025-07-10 08:31", "2025-07-10 08:32", "2025-07-10 08:33", "2025-07-10 08:34", "2025-07-10 08:35", "2025-07-10 08:36", "2025-07-10 08:37", "2025-07-10 08:38", "2025-07-10 08:39", "2025-07-10 08:40", "2025-07-10 08:41", "2025-07-10 08:42", "2025-07-10 08:43", "2025-07-10 08:44", "2025-07-10 08:45", "2025-07-10 08:46", "2025-07-10 08:47", "2025-07-10 08:48", "2025-07-10 08:49", "2025-07-10 08:50", "2025-07-10 08:51", "2025-07-10 08:52", "2025-07-10 08:53", "2025-07-10 08:54", "2025-07-10 08:55", "2025-07-10 08:56", "2025-07-10 08:57", "2025-07-10 08:58", "2025-07-10 08:59", "2025-07-10 09:00", "2025-07-10 09:01", "2025-07-10 09:02", "2025-07-10 09:03", "2025-07-10 09:04", "2025-07-10 09:05", "2025-07-10 09:06", "2025-07-10 09:07", "2025-07-10 09:08", "2025-07-10 09:09", "2025-07-10 09:10", "2025-07-10 09:11", "2025-07-10 09:12", "2025-07-10 09:13", "2025-07-10 09:14", "2025-07-10 09:15", "2025-07-10 09:16", "2025-07-10 09:17", "2025-07-10 09:18", "2025-07-10 09:19", "2025-07-10 09:20", "2025-07-10 09:21", "2025-07-10 09:22", "2025-07-10 09:23", "2025-07-10 09:24", "2025-07-10 09:25", "2025-07-10 09:26", "2025-07-10 09:27", "2025-07-10 09:28", "2025-07-10 09:29", "2025-07-10 09:30", "2025-07-10 09:31", "2025-07-10 09:32", "2025-07-10 09:33", "2025-07-10 09:34", "2025-07-10 09:35", "2025-07-10 09:36", "2025-07-10 09:37", "2025-07-10 09:38", "2025-07-10 09:39", "2025-07-10 09:40", "2025-07-10 09:41", "2025-07-10 09:42", "2025-07-10 09:43", "2025-07-10 09:44", "2025-07-10 09:45", "2025-07-10 09:46", "2025-07-10 09:47", "2025-07-10 09:48", "2025-07-10 09:49", "2025-07-10 09:50", "2025-07-10 09:51", "2025-07-10 09:52", "2025-07-10 09:53", "2025-07-10 09:54", "2025-07-10 09:55", "2025-07-10 09:56", "2025-07-10 09:57", "2025-07-10 09:58", "2025-07-10 09:59", "2025-07-10 10:00", "2025-07-10 10:01", "2025-07-10 10:02", "2025-07-10 10:03", "2025-07-10 10:04", "2025-07-10 10:05", "2025-07-10 10:06", "2025-07-10 10:07", "2025-07-10 10:08", "2025-07-10 10:09", "2025-07-10 10:10", "2025-07-10 10:11", "2025-07-10 10:12", "2025-07-10 10:13", "2025-07-10 10:14", "2025-07-10 10:15", "2025-07-10 10:16", "2025-07-10 10:17", "2025-07-10 10:18", "2025-07-10 10:19", "2025-07-10 10:20", "2025-07-10 10:21", "2025-07-10 10:22", "2025-07-10 10:23", "2025-07-10 10:24", "2025-07-10 10:25", "2025-07-10 10:26", "2025-07-10 10:27", "2025-07-10 10:28", "2025-07-10 10:29", "2025-07-10 10:30", "2025-07-10 10:31", "2025-07-10 10:32", "2025-07-10 10:33", "2025-07-10 10:34", "2025-07-10 10:35", "2025-07-10 10:36", "2025-07-10 10:37", "2025-07-10 10:38", "2025-07-10 10:39", "2025-07-10 10:40", "2025-07-10 10:41", "2025-07-10 10:42", "2025-07-10 10:43", "2025-07-10 10:44", "2025-07-10 10:45", "2025-07-10 10:46", "2025-07-10 10:47", "2025-07-10 10:48", "2025-07-10 10:49", "2025-07-10 10:50", "2025-07-10 10:51", "2025-07-10 10:52", "2025-07-10 10:53", "2025-07-10 10:54", "2025-07-10 10:55", "2025-07-10 10:56", "2025-07-10 10:57", "2025-07-10 10:58", "2025-07-10 10:59", "2025-07-10 11:00", "2025-07-10 11:01", "2025-07-10 11:02", "2025-07-10 11:03", "2025-07-10 11:04", "2025-07-10 11:05", "2025-07-10 11:06", "2025-07-10 11:07", "2025-07-10 11:08", "2025-07-10 11:09", "2025-07-10 11:10", "2025-07-10 11:11", "2025-07-10 11:12", "2025-07-10 11:13", "2025-07-10 11:14", "2025-07-10 11:15", "2025-07-10 11:16", "2025-07-10 11:17", "2025-07-10 11:18", "2025-07-10 11:19", "2025-07-10 11:20", "2025-07-10 11:21", "2025-07-10 11:22", "2025-07-10 11:23", "2025-07-10 11:24", "2025-07-10 11:25", "2025-07-10 11:26", "2025-07-10 11:27", "2025-07-10 11:28", "2025-07-10 11:29", "2025-07-10 11:30", "2025-07-10 11:31", "2025-07-10 11:32", "2025-07-10 11:33", "2025-07-10 11:34", "2025-07-10 11:35", "2025-07-10 11:36", "2025-07-10 11:37", "2025-07-10 11:38", "2025-07-10 11:39", "2025-07-10 11:40", "2025-07-10 11:41", "2025-07-10 11:42", "2025-07-10 11:43", "2025-07-10 11:44", "2025-07-10 11:45", "2025-07-10 11:46", "2025-07-10 11:47", "2025-07-10 11:48", "2025-07-10 11:49", "2025-07-10 11:50", "2025-07-10 11:51", "2025-07-10 11:52", "2025-07-10 11:53", "2025-07-10 11:54", "2025-07-10 11:55", "2025-07-10 11:56", "2025-07-10 11:57", "2025-07-10 11:58", "2025-07-10 11:59", "2025-07-10 12:00", "2025-07-10 12:01", "2025-07-10 12:02", "2025-07-10 12:03", "2025-07-10 12:04", "2025-07-10 12:05", "2025-07-10 12:06", "2025-07-10 12:07", "2025-07-10 12:08", "2025-07-10 12:09", "2025-07-10 12:10", "2025-07-10 12:11", "2025-07-10 12:12", "2025-07-10 12:13", "2025-07-10 12:14", "2025-07-10 12:15", "2025-07-10 12:16", "2025-07-10 12:17", "2025-07-10 12:18", "2025-07-10 12:19", "2025-07-10 12:20", "2025-07-10 12:21", "2025-07-10 12:22", "2025-07-10 12:23", "2025-07-10 12:24", "2025-07-10 12:25", "2025-07-10 12:26", "2025-07-10 12:27", "2025-07-10 12:28", "2025-07-10 12:29", "2025-07-10 12:30", "2025-07-10 12:31", "2025-07-10 12:32", "2025-07-10 12:33", "2025-07-10 12:34", "2025-07-10 12:35", "2025-07-10 12:36", "2025-07-10 12:37", "2025-07-10 12:38", "2025-07-10 12:39", "2025-07-10 12:40", "2025-07-10 12:41", "2025-07-10 12:42", "2025-07-10 12:43", "2025-07-10 12:44", "2025-07-10 12:45", "2025-07-10 12:46", "2025-07-10 12:47", "2025-07-10 12:48", "2025-07-10 12:49", "2025-07-10 12:50", "2025-07-10 12:51", "2025-07-10 12:52", "2025-07-10 12:53", "2025-07-10 12:54", "2025-07-10 12:55", "2025-07-10 12:56", "2025-07-10 12:57", "2025-07-10 12:58", "2025-07-10 12:59", "2025-07-10 13:00", "2025-07-10 13:01", "2025-07-10 13:02", "2025-07-10 13:03", "2025-07-10 13:04", "2025-07-10 13:05", "2025-07-10 13:06", "2025-07-10 13:07", "2025-07-10 13:08", "2025-07-10 13:09", "2025-07-10 13:10", "2025-07-10 13:11", "2025-07-10 13:12", "2025-07-10 13:13", "2025-07-10 13:14", "2025-07-10 13:15", "2025-07-10 13:16", "2025-07-10 13:17", "2025-07-10 13:18", "2025-07-10 13:19", "2025-07-10 13:20", "2025-07-10 13:21", "2025-07-10 13:22", "2025-07-10 13:23", "2025-07-10 13:24", "2025-07-10 13:25", "2025-07-10 13:26", "2025-07-10 13:27", "2025-07-10 13:28", "2025-07-10 13:29", "2025-07-10 13:30", "2025-07-10 13:31", "2025-07-10 13:32", "2025-07-10 13:33", "2025-07-10 13:34", "2025-07-10 13:35", "2025-07-10 13:36", "2025-07-10 13:37", "2025-07-10 13:38", "2025-07-10 13:39", "2025-07-10 13:40", "2025-07-10 13:41", "2025-07-10 13:42", "2025-07-10 13:43", "2025-07-10 13:44", "2025-07-10 13:45", "2025-07-10 13:46", "2025-07-10 13:47", "2025-07-10 13:48", "2025-07-10 13:49", "2025-07-10 13:50", "2025-07-10 13:51", "2025-07-10 13:52", "2025-07-10 13:53", "2025-07-10 13:54", "2025-07-10 13:55", "2025-07-10 13:56", "2025-07-10 13:57", "2025-07-10 13:58", "2025-07-10 13:59", "2025-07-10 14:00", "2025-07-10 14:01", "2025-07-10 14:02", "2025-07-10 14:03", "2025-07-10 14:04", "2025-07-10 14:05", "2025-07-10 14:06", "2025-07-10 14:07", "2025-07-10 14:08", "2025-07-10 14:09", "2025-07-10 14:10", "2025-07-10 14:11", "2025-07-10 14:12", "2025-07-10 14:13", "2025-07-10 14:14", "2025-07-10 14:15", "2025-07-10 14:16", "2025-07-10 14:17", "2025-07-10 14:18", "2025-07-10 14:19", "2025-07-10 14:20", "2025-07-10 14:21", "2025-07-10 14:22", "2025-07-10 14:23", "2025-07-10 14:24", "2025-07-10 14:25", "2025-07-10 14:26", "2025-07-10 14:27", "2025-07-10 14:28", "2025-07-10 14:29", "2025-07-10 14:30", "2025-07-10 14:31", "2025-07-10 14:32", "2025-07-10 14:33", "2025-07-10 14:34", "2025-07-10 14:35", "2025-07-10 14:36", "2025-07-10 14:37", "2025-07-10 14:38", "2025-07-10 14:39", "2025-07-10 14:40", "2025-07-10 14:41", "2025-07-10 14:42", "2025-07-10 14:43", "2025-07-10 14:44", "2025-07-10 14:45", "2025-07-10 14:46", "2025-07-10 14:47", "2025-07-10 14:48", "2025-07-10 14:49", "2025-07-10 14:50", "2025-07-10 14:51", "2025-07-10 14:52", "2025-07-10 14:53", "2025-07-10 14:54", "2025-07-10 14:55", "2025-07-10 14:56", "2025-07-10 14:57", "2025-07-10 14:58", "2025-07-10 14:59", "2025-07-10 15:00", "2025-07-10 15:01", "2025-07-10 15:02", "2025-07-10 15:03", "2025-07-10 15:04", "2025-07-10 15:05", "2025-07-10 15:06", "2025-07-10 15:07", "2025-07-10 15:08", "2025-07-10 15:09", "2025-07-10 15:10", "2025-07-10 15:11", "2025-07-10 15:12", "2025-07-10 15:13", "2025-07-10 15:14", "2025-07-10 15:15", "2025-07-10 15:16", "2025-07-10 15:17", "2025-07-10 15:18", "2025-07-10 15:19", "2025-07-10 15:20", "2025-07-10 15:21", "2025-07-10 15:22", "2025-07-10 15:23", "2025-07-10 15:24", "2025-07-10 15:25", "2025-07-10 15:26", "2025-07-10 15:27", "2025-07-10 15:28", "2025-07-10 15:29", "2025-07-10 15:30", "2025-07-10 15:31", "2025-07-10 15:32", "2025-07-10 15:33", "2025-07-10 15:34", "2025-07-10 15:35", "2025-07-10 15:36", "2025-07-10 15:37", "2025-07-10 15:38", "2025-07-10 15:39", "2025-07-10 15:40", "2025-07-10 15:41", "2025-07-10 15:42", "2025-07-10 15:43", "2025-07-10 15:44", "2025-07-10 15:45", "2025-07-10 15:46", "2025-07-10 15:47", "2025-07-10 15:48", "2025-07-10 15:49", "2025-07-10 15:50", "2025-07-10 15:51", "2025-07-10 15:52", "2025-07-10 15:53", "2025-07-10 15:54", "2025-07-10 15:55", "2025-07-10 15:56", "2025-07-10 15:57", "2025-07-10 15:58", "2025-07-10 15:59", "2025-07-10 16:00", "2025-07-10 16:01", "2025-07-10 16:02", "2025-07-10 16:03", "2025-07-10 16:04", "2025-07-10 16:05", "2025-07-10 16:06", "2025-07-10 16:07", "2025-07-10 16:08", "2025-07-10 16:09", "2025-07-10 16:10", "2025-07-10 16:11", "2025-07-10 16:12", "2025-07-10 16:13", "2025-07-10 16:14", "2025-07-10 16:15", "2025-07-10 16:16", "2025-07-10 16:17", "2025-07-10 16:18", "2025-07-10 16:19", "2025-07-10 16:20", "2025-07-10 16:21", "2025-07-10 16:22", "2025-07-10 16:23", "2025-07-10 16:24", "2025-07-10 16:25", "2025-07-10 16:26", "2025-07-10 16:27", "2025-07-10 16:28", "2025-07-10 16:29", "2025-07-10 16:30", "2025-07-10 16:31", "2025-07-10 16:32", "2025-07-10 16:33", "2025-07-10 16:34", "2025-07-10 16:35", "2025-07-10 16:36", "2025-07-10 16:37", "2025-07-10 16:38", "2025-07-10 16:39", "2025-07-10 16:40", "2025-07-10 16:41", "2025-07-10 16:42", "2025-07-10 16:43", "2025-07-10 16:44", "2025-07-10 16:45", "2025-07-10 16:46", "2025-07-10 16:47", "2025-07-10 16:48", "2025-07-10 16:49", "2025-07-10 16:50", "2025-07-10 16:51", "2025-07-10 16:52", "2025-07-10 16:53", "2025-07-10 16:54", "2025-07-10 16:55", "2025-07-10 16:56", "2025-07-10 16:57", "2025-07-10 16:58", "2025-07-10 16:59", "2025-07-10 17:00", "2025-07-10 17:01", "2025-07-10 17:02", "2025-07-10 17:03", "2025-07-10 17:04", "2025-07-10 17:05", "2025-07-10 17:06", "2025-07-10 17:07", "2025-07-10 17:08", "2025-07-10 17:09", "2025-07-10 17:10", "2025-07-10 17:11", "2025-07-10 17:12", "2025-07-10 17:13", "2025-07-10 17:14", "2025-07-10 17:15", "2025-07-10 17:16", "2025-07-10 17:17", "2025-07-10 17:18", "2025-07-10 17:19", "2025-07-10 17:20", "2025-07-10 17:21", "2025-07-10 17:22", "2025-07-10 17:23", "2025-07-10 17:24", "2025-07-10 17:25", "2025-07-10 17:26", "2025-07-10 17:27", "2025-07-10 17:28", "2025-07-10 17:29", "2025-07-10 17:30", "2025-07-10 17:31", "2025-07-10 17:32", "2025-07-10 17:33", "2025-07-10 17:34", "2025-07-10 17:35", "2025-07-10 17:36", "2025-07-10 17:37", "2025-07-10 17:38", "2025-07-10 17:39", "2025-07-10 17:40", "2025-07-10 17:41", "2025-07-10 17:42", "2025-07-10 17:43", "2025-07-10 17:44", "2025-07-10 17:45", "2025-07-10 17:46", "2025-07-10 17:47", "2025-07-10 17:48", "2025-07-10 17:49", "2025-07-10 17:50", "2025-07-10 17:51", "2025-07-10 17:52", "2025-07-10 17:53", "2025-07-10 17:54", "2025-07-10 17:55", "2025-07-10 17:56", "2025-07-10 17:57", "2025-07-10 17:58", "2025-07-10 17:59", "2025-07-10 18:00", "2025-07-10 18:01", "2025-07-10 18:02", "2025-07-10 18:03", "2025-07-10 18:04", "2025-07-10 18:05", "2025-07-10 18:06", "2025-07-10 18:07", "2025-07-10 18:08", "2025-07-10 18:09", "2025-07-10 18:10", "2025-07-10 18:11", "2025-07-10 18:12", "2025-07-10 18:13", "2025-07-10 18:14", "2025-07-10 18:15", "2025-07-10 18:16", "2025-07-10 18:17", "2025-07-10 18:18", "2025-07-10 18:19", "2025-07-10 18:20", "2025-07-10 18:21", "2025-07-10 18:22", "2025-07-10 18:23", "2025-07-10 18:24", "2025-07-10 18:25", "2025-07-10 18:26", "2025-07-10 18:27", "2025-07-10 18:28", "2025-07-10 18:29", "2025-07-10 18:30", "2025-07-10 18:31", "2025-07-10 18:32", "2025-07-10 18:33", "2025-07-10 18:34", "2025-07-10 18:35", "2025-07-10 18:36", "2025-07-10 18:37", "2025-07-10 18:38", "2025-07-10 18:39", "2025-07-10 18:40", "2025-07-10 18:41", "2025-07-10 18:42", "2025-07-10 18:43", "2025-07-10 18:44", "2025-07-10 18:45", "2025-07-10 18:46", "2025-07-10 18:47", "2025-07-10 18:48", "2025-07-10 18:49", "2025-07-10 18:50", "2025-07-10 18:51", "2025-07-10 18:52", "2025-07-10 18:53", "2025-07-10 18:54", "2025-07-10 18:55", "2025-07-10 18:56", "2025-07-10 18:57", "2025-07-10 18:58", "2025-07-10 18:59", "2025-07-10 19:00", "2025-07-10 19:01", "2025-07-10 19:02", "2025-07-10 19:03", "2025-07-10 19:04", "2025-07-10 19:05", "2025-07-10 19:06", "2025-07-10 19:07", "2025-07-10 19:08", "2025-07-10 19:09", "2025-07-10 19:10", "2025-07-10 19:11", "2025-07-10 19:12", "2025-07-10 19:13", "2025-07-10 19:14", "2025-07-10 19:15", "2025-07-10 19:16", "2025-07-10 19:17", "2025-07-10 19:18", "2025-07-10 19:19", "2025-07-10 19:20", "2025-07-10 19:21", "2025-07-10 19:22", "2025-07-10 19:23", "2025-07-10 19:24", "2025-07-10 19:25", "2025-07-10 19:26", "2025-07-10 19:27", "2025-07-10 19:28", "2025-07-10 19:29", "2025-07-10 19:30", "2025-07-10 19:31", "2025-07-10 19:32", "2025-07-10 19:33", "2025-07-10 19:34", "2025-07-10 19:35", "2025-07-10 19:36", "2025-07-10 19:37", "2025-07-10 19:38", "2025-07-10 19:39", "2025-07-10 19:40", "2025-07-10 19:41", "2025-07-10 19:42", "2025-07-10 19:43", "2025-07-10 19:44", "2025-07-10 19:45", "2025-07-10 19:46", "2025-07-10 19:47", "2025-07-10 19:48", "2025-07-10 19:49", "2025-07-10 19:50", "2025-07-10 19:51", "2025-07-10 19:52", "2025-07-10 19:53", "2025-07-10 19:54", "2025-07-10 19:55", "2025-07-10 19:56", "2025-07-10 19:57", "2025-07-10 19:58", "2025-07-10 19:59", "2025-07-10 20:00", "2025-07-10 20:01", "2025-07-10 20:02", "2025-07-10 20:03", "2025-07-10 20:04", "2025-07-10 20:05", "2025-07-10 20:06", "2025-07-10 20:07", "2025-07-10 20:08", "2025-07-10 20:09", "2025-07-10 20:10", "2025-07-10 20:11", "2025-07-10 20:12", "2025-07-10 20:13", "2025-07-10 20:14", "2025-07-10 20:15", "2025-07-10 20:16", "2025-07-10 20:17", "2025-07-10 20:18", "2025-07-10 20:19", "2025-07-10 20:20", "2025-07-10 20:21", "2025-07-10 20:22", "2025-07-10 20:23", "2025-07-10 20:24", "2025-07-10 20:25", "2025-07-10 20:26", "2025-07-10 20:27", "2025-07-10 20:28", "2025-07-10 20:29", "2025-07-10 20:30", "2025-07-10 20:31", "2025-07-10 20:32", "2025-07-10 20:33", "2025-07-10 20:34", "2025-07-10 20:35", "2025-07-10 20:36", "2025-07-10 20:37", "2025-07-10 20:38", "2025-07-10 20:39", "2025-07-10 20:40", "2025-07-10 20:41", "2025-07-10 20:42", "2025-07-10 20:43", "2025-07-10 20:44", "2025-07-10 20:45", "2025-07-10 20:46", "2025-07-10 20:47", "2025-07-10 20:48", "2025-07-10 20:49", "2025-07-10 20:50", "2025-07-10 20:51", "2025-07-10 20:52", "2025-07-10 20:53", "2025-07-10 20:54", "2025-07-10 20:55", "2025-07-10 20:56", "2025-07-10 20:57", "2025-07-10 20:58", "2025-07-10 20:59", "2025-07-10 21:00", "2025-07-10 21:01", "2025-07-10 21:02", "2025-07-10 21:03", "2025-07-10 21:04", "2025-07-10 21:05", "2025-07-10 21:06", "2025-07-10 21:07", "2025-07-10 21:08", "2025-07-10 21:09", "2025-07-10 21:10", "2025-07-10 21:11", "2025-07-10 21:12", "2025-07-10 21:13", "2025-07-10 21:14", "2025-07-10 21:15", "2025-07-10 21:16", "2025-07-10 21:17", "2025-07-10 21:18", "2025-07-10 21:19", "2025-07-10 21:20", "2025-07-10 21:21", "2025-07-10 21:22", "2025-07-10 21:23", "2025-07-10 21:24", "2025-07-10 21:25", "2025-07-10 21:26", "2025-07-10 21:27", "2025-07-10 21:28", "2025-07-10 21:29", "2025-07-10 21:30", "2025-07-10 21:31", "2025-07-10 21:32", "2025-07-10 21:33", "2025-07-10 21:34", "2025-07-10 21:35", "2025-07-10 21:36", "2025-07-10 21:37", "2025-07-10 21:38", "2025-07-10 21:39", "2025-07-10 21:40", "2025-07-10 21:41", "2025-07-10 21:42", "2025-07-10 21:43", "2025-07-10 21:44", "2025-07-10 21:45", "2025-07-10 21:46", "2025-07-10 21:47", "2025-07-10 21:48", "2025-07-10 21:49", "2025-07-10 21:50", "2025-07-10 21:51", "2025-07-10 21:52", "2025-07-10 21:53", "2025-07-10 21:54", "2025-07-10 21:55", "2025-07-10 21:56", "2025-07-10 21:57", "2025-07-10 21:58", "2025-07-10 21:59", "2025-07-10 22:00", "2025-07-10 22:01", "2025-07-10 22:02", "2025-07-10 22:03", "2025-07-10 22:04", "2025-07-10 22:05", "2025-07-10 22:06", "2025-07-10 22:07", "2025-07-10 22:08", "2025-07-10 22:09", "2025-07-10 22:10", "2025-07-10 22:11", "2025-07-10 22:12", "2025-07-10 22:13", "2025-07-10 22:14", "2025-07-10 22:15", "2025-07-10 22:16", "2025-07-10 22:17", "2025-07-10 22:18", "2025-07-10 22:19", "2025-07-10 22:20", "2025-07-10 22:21", "2025-07-10 22:22", "2025-07-10 22:23", "2025-07-10 22:24", "2025-07-10 22:25", "2025-07-10 22:26", "2025-07-10 22:27", "2025-07-10 22:28", "2025-07-10 22:29", "2025-07-10 22:30", "2025-07-10 22:31", "2025-07-10 22:32", "2025-07-10 22:33", "2025-07-10 22:34", "2025-07-10 22:35", "2025-07-10 22:36", "2025-07-10 22:37", "2025-07-10 22:38", "2025-07-10 22:39", "2025-07-10 22:40", "2025-07-10 22:41", "2025-07-10 22:42", "2025-07-10 22:43", "2025-07-10 22:44", "2025-07-10 22:45", "2025-07-10 22:46", "2025-07-10 22:47", "2025-07-10 22:48", "2025-07-10 22:49", "2025-07-10 22:50", "2025-07-10 22:51", "2025-07-10 22:52", "2025-07-10 22:53", "2025-07-10 22:54", "2025-07-10 22:55", "2025-07-10 22:56", "2025-07-10 22:57", "2025-07-10 22:58", "2025-07-10 22:59", "2025-07-10 23:00", "2025-07-10 23:01", "2025-07-10 23:02", "2025-07-10 23:03", "2025-07-10 23:04", "2025-07-10 23:05", "2025-07-10 23:06", "2025-07-10 23:07", "2025-07-10 23:08", "2025-07-10 23:09", "2025-07-10 23:10", "2025-07-10 23:11", "2025-07-10 23:12", "2025-07-10 23:13", "2025-07-10 23:14", "2025-07-10 23:15", "2025-07-10 23:16", "2025-07-10 23:17", "2025-07-10 23:18", "2025-07-10 23:19", "2025-07-10 23:20", "2025-07-10 23:21", "2025-07-10 23:22", "2025-07-10 23:23", "2025-07-10 23:24", "2025-07-10 23:25", "2025-07-10 23:26", "2025-07-10 23:27", "2025-07-10 23:28", "2025-07-10 23:29", "2025-07-10 23:30", "2025-07-10 23:31", "2025-07-10 23:32", "2025-07-10 23:33", "2025-07-10 23:34", "2025-07-10 23:35", "2025-07-10 23:36", "2025-07-10 23:37", "2025-07-10 23:38", "2025-07-10 23:39", "2025-07-10 23:40", "2025-07-10 23:41", "2025-07-10 23:42", "2025-07-10 23:43", "2025-07-10 23:44", "2025-07-10 23:45", "2025-07-10 23:46", "2025-07-10 23:47", "2025-07-10 23:48", "2025-07-10 23:49", "2025-07-10 23:50", "2025-07-10 23:51", "2025-07-10 23:52", "2025-07-10 23:53", "2025-07-10 23:54", "2025-07-10 23:55", "2025-07-10 23:56", "2025-07-10 23:57", "2025-07-10 23:58", "2025-07-10 23:59", "2025-07-11 00:00", "2025-07-11 00:01", "2025-07-11 00:02", "2025-07-11 00:03", "2025-07-11 00:04", "2025-07-11 00:05", "2025-07-11 00:06", "2025-07-11 00:07", "2025-07-11 00:08", "2025-07-11 00:09", "2025-07-11 00:10", "2025-07-11 00:11", "2025-07-11 00:12", "2025-07-11 00:13", "2025-07-11 00:14", "2025-07-11 00:15", "2025-07-11 00:16", "2025-07-11 00:17", "2025-07-11 00:18", "2025-07-11 00:19", "2025-07-11 00:20", "2025-07-11 00:21", "2025-07-11 00:22", "2025-07-11 00:23", "2025-07-11 00:24", "2025-07-11 00:25", "2025-07-11 00:26", "2025-07-11 00:27", "2025-07-11 00:28", "2025-07-11 00:29", "2025-07-11 00:30", "2025-07-11 00:31", "2025-07-11 00:32", "2025-07-11 00:33", "2025-07-11 00:34", "2025-07-11 00:35", "2025-07-11 00:36", "2025-07-11 00:37", "2025-07-11 00:38", "2025-07-11 00:39", "2025-07-11 00:40", "2025-07-11 00:41", "2025-07-11 00:42", "2025-07-11 00:43", "2025-07-11 00:44", "2025-07-11 00:45", "2025-07-11 00:46", "2025-07-11 00:47", "2025-07-11 00:48", "2025-07-11 00:49", "2025-07-11 00:50", "2025-07-11 00:51", "2025-07-11 00:52", "2025-07-11 00:53", "2025-07-11 00:54", "2025-07-11 00:55", "2025-07-11 00:56", "2025-07-11 00:57", "2025-07-11 00:58", "2025-07-11 00:59", "2025-07-11 01:00", "2025-07-11 01:01", "2025-07-11 01:02", "2025-07-11 01:03", "2025-07-11 01:04", "2025-07-11 01:05", "2025-07-11 01:06", "2025-07-11 01:07", "2025-07-11 01:08", "2025-07-11 01:09", "2025-07-11 01:10", "2025-07-11 01:11", "2025-07-11 01:12", "2025-07-11 01:13", "2025-07-11 01:14", "2025-07-11 01:15", "2025-07-11 01:16", "2025-07-11 01:17", "2025-07-11 01:18", "2025-07-11 01:19", "2025-07-11 01:20", "2025-07-11 01:21", "2025-07-11 01:22", "2025-07-11 01:23", "2025-07-11 01:24", "2025-07-11 01:25", "2025-07-11 01:26", "2025-07-11 01:27", "2025-07-11 01:28", "2025-07-11 01:29", "2025-07-11 01:30", "2025-07-11 01:31", "2025-07-11 01:32", "2025-07-11 01:33", "2025-07-11 01:34", "2025-07-11 01:35", "2025-07-11 01:36", "2025-07-11 01:37", "2025-07-11 01:38", "2025-07-11 01:39", "2025-07-11 01:40", "2025-07-11 01:41", "2025-07-11 01:42", "2025-07-11 01:43", "2025-07-11 01:44", "2025-07-11 01:45", "2025-07-11 01:46", "2025-07-11 01:47", "2025-07-11 01:48", "2025-07-11 01:49", "2025-07-11 01:50", "2025-07-11 01:51", "2025-07-11 01:52", "2025-07-11 01:53", "2025-07-11 01:54", "2025-07-11 01:55", "2025-07-11 01:56", "2025-07-11 01:57", "2025-07-11 01:58", "2025-07-11 01:59", "2025-07-11 02:00", "2025-07-11 02:01", "2025-07-11 02:02", "2025-07-11 02:03", "2025-07-11 02:04", "2025-07-11 02:05", "2025-07-11 02:06", "2025-07-11 02:07", "2025-07-11 02:08", "2025-07-11 02:09", "2025-07-11 02:10", "2025-07-11 02:11", "2025-07-11 02:12", "2025-07-11 02:13", "2025-07-11 02:14", "2025-07-11 02:15", "2025-07-11 02:16", "2025-07-11 02:17", "2025-07-11 02:18", "2025-07-11 02:19", "2025-07-11 02:20", "2025-07-11 02:21", "2025-07-11 02:22", "2025-07-11 02:23", "2025-07-11 02:24", "2025-07-11 02:25", "2025-07-11 02:26", "2025-07-11 02:27", "2025-07-11 02:28", "2025-07-11 02:29", "2025-07-11 02:30", "2025-07-11 02:31", "2025-07-11 02:32", "2025-07-11 02:33", "2025-07-11 02:34", "2025-07-11 02:35", "2025-07-11 02:36", "2025-07-11 02:37", "2025-07-11 02:38", "2025-07-11 02:39", "2025-07-11 02:40", "2025-07-11 02:41", "2025-07-11 02:42", "2025-07-11 02:43", "2025-07-11 02:44", "2025-07-11 02:45", "2025-07-11 02:46", "2025-07-11 02:47", "2025-07-11 02:48", "2025-07-11 02:49", "2025-07-11 02:50", "2025-07-11 02:51", "2025-07-11 02:52", "2025-07-11 02:53", "2025-07-11 02:54", "2025-07-11 02:55", "2025-07-11 02:56", "2025-07-11 02:57", "2025-07-11 02:58", "2025-07-11 02:59", "2025-07-11 03:00", "2025-07-11 03:01", "2025-07-11 03:02", "2025-07-11 03:03", "2025-07-11 03:04", "2025-07-11 03:05", "2025-07-11 03:06", "2025-07-11 03:07", "2025-07-11 03:08", "2025-07-11 03:09", "2025-07-11 03:10", "2025-07-11 03:11", "2025-07-11 03:12", "2025-07-11 03:13", "2025-07-11 03:14", "2025-07-11 03:15", "2025-07-11 03:16", "2025-07-11 03:17", "2025-07-11 03:18", "2025-07-11 03:19", "2025-07-11 03:20", "2025-07-11 03:21", "2025-07-11 03:22", "2025-07-11 03:23", "2025-07-11 03:24", "2025-07-11 03:25", "2025-07-11 03:26", "2025-07-11 03:27", "2025-07-11 03:28", "2025-07-11 03:29", "2025-07-11 03:30", "2025-07-11 03:31", "2025-07-11 03:32", "2025-07-11 03:33", "2025-07-11 03:34", "2025-07-11 03:35", "2025-07-11 03:36", "2025-07-11 03:37", "2025-07-11 03:38", "2025-07-11 03:39", "2025-07-11 03:40", "2025-07-11 03:41", "2025-07-11 03:42", "2025-07-11 03:43", "2025-07-11 03:44", "2025-07-11 03:45", "2025-07-11 03:46", "2025-07-11 03:47", "2025-07-11 03:48", "2025-07-11 03:49", "2025-07-11 03:50", "2025-07-11 03:51", "2025-07-11 03:52", "2025-07-11 03:53", "2025-07-11 03:54", "2025-07-11 03:55", "2025-07-11 03:56", "2025-07-11 03:57", "2025-07-11 03:58", "2025-07-11 03:59", "2025-07-11 04:00", "2025-07-11 04:01", "2025-07-11 04:02", "2025-07-11 04:03", "2025-07-11 04:04", "2025-07-11 04:05", "2025-07-11 04:06", "2025-07-11 04:07", "2025-07-11 04:08", "2025-07-11 04:09", "2025-07-11 04:10", "2025-07-11 04:11", "2025-07-11 04:12", "2025-07-11 04:13", "2025-07-11 04:14", "2025-07-11 04:15", "2025-07-11 04:16", "2025-07-11 04:17", "2025-07-11 04:18", "2025-07-11 04:19", "2025-07-11 04:20", "2025-07-11 04:21", "2025-07-11 04:22", "2025-07-11 04:23", "2025-07-11 04:24", "2025-07-11 04:25", "2025-07-11 04:26", "2025-07-11 04:27", "2025-07-11 04:28", "2025-07-11 04:29", "2025-07-11 04:30", "2025-07-11 04:31", "2025-07-11 04:32", "2025-07-11 04:33", "2025-07-11 04:34", "2025-07-11 04:35", "2025-07-11 04:36", "2025-07-11 04:37", "2025-07-11 04:38", "2025-07-11 04:39", "2025-07-11 04:40", "2025-07-11 04:41", "2025-07-11 04:42", "2025-07-11 04:43", "2025-07-11 04:44", "2025-07-11 04:45", "2025-07-11 04:46", "2025-07-11 04:47", "2025-07-11 04:48", "2025-07-11 04:49", "2025-07-11 04:50", "2025-07-11 04:51", "2025-07-11 04:52", "2025-07-11 04:53", "2025-07-11 04:54", "2025-07-11 04:55", "2025-07-11 04:56", "2025-07-11 04:57", "2025-07-11 04:58", "2025-07-11 04:59", "2025-07-11 05:00", "2025-07-11 05:01", "2025-07-11 05:02", "2025-07-11 05:03", "2025-07-11 05:04", "2025-07-11 05:05", "2025-07-11 05:06", "2025-07-11 05:07", "2025-07-11 05:08", "2025-07-11 05:09", "2025-07-11 05:10", "2025-07-11 05:11", "2025-07-11 05:12", "2025-07-11 05:13", "2025-07-11 05:14", "2025-07-11 05:15", "2025-07-11 05:16", "2025-07-11 05:17", "2025-07-11 05:18", "2025-07-11 05:19", "2025-07-11 05:20", "2025-07-11 05:21", "2025-07-11 05:22", "2025-07-11 05:23", "2025-07-11 05:24", "2025-07-11 05:25", "2025-07-11 05:26", "2025-07-11 05:27", "2025-07-11 05:28", "2025-07-11 05:29", "2025-07-11 05:30", "2025-07-11 05:31", "2025-07-11 05:32", "2025-07-11 05:33", "2025-07-11 05:34", "2025-07-11 05:35", "2025-07-11 05:36", "2025-07-11 05:37", "2025-07-11 05:38", "2025-07-11 05:39", "2025-07-11 05:40", "2025-07-11 05:41", "2025-07-11 05:42", "2025-07-11 05:43", "2025-07-11 05:44", "2025-07-11 05:45", "2025-07-11 05:46", "2025-07-11 05:47", "2025-07-11 05:48", "2025-07-11 05:49", "2025-07-11 05:50", "2025-07-11 05:51", "2025-07-11 05:52", "2025-07-11 05:53", "2025-07-11 05:54", "2025-07-11 05:55", "2025-07-11 05:56", "2025-07-11 05:57", "2025-07-11 05:58", "2025-07-11 05:59", "2025-07-11 06:00", "2025-07-11 06:01", "2025-07-11 06:02", "2025-07-11 06:03", "2025-07-11 06:04", "2025-07-11 06:05", "2025-07-11 06:06", "2025-07-11 06:07", "2025-07-11 06:08", "2025-07-11 06:09", "2025-07-11 06:10", "2025-07-11 06:11", "2025-07-11 06:12", "2025-07-11 06:13", "2025-07-11 06:14", "2025-07-11 06:15", "2025-07-11 06:16", "2025-07-11 06:17", "2025-07-11 06:18", "2025-07-11 06:19", "2025-07-11 06:20", "2025-07-11 06:21", "2025-07-11 06:22", "2025-07-11 06:23", "2025-07-11 06:24", "2025-07-11 06:25", "2025-07-11 06:26", "2025-07-11 06:27", "2025-07-11 06:28", "2025-07-11 06:29", "2025-07-11 06:30", "2025-07-11 06:31", "2025-07-11 06:32", "2025-07-11 06:33", "2025-07-11 06:34", "2025-07-11 06:35", "2025-07-11 06:36", "2025-07-11 06:37", "2025-07-11 06:38", "2025-07-11 06:39", "2025-07-11 06:40", "2025-07-11 06:41", "2025-07-11 06:42", "2025-07-11 06:43", "2025-07-11 06:44", "2025-07-11 06:45", "2025-07-11 06:46", "2025-07-11 06:47", "2025-07-11 06:48", "2025-07-11 06:49", "2025-07-11 06:50", "2025-07-11 06:51", "2025-07-11 06:52", "2025-07-11 06:53", "2025-07-11 06:54", "2025-07-11 06:55", "2025-07-11 06:56", "2025-07-11 06:57", "2025-07-11 06:58", "2025-07-11 06:59", "2025-07-11 07:00", "2025-07-11 07:01", "2025-07-11 07:02", "2025-07-11 07:03", "2025-07-11 07:04", "2025-07-11 07:05", "2025-07-11 07:06", "2025-07-11 07:07", "2025-07-11 07:08", "2025-07-11 07:09", "2025-07-11 07:10", "2025-07-11 07:11", "2025-07-11 07:12", "2025-07-11 07:13", "2025-07-11 07:14", "2025-07-11 07:15", "2025-07-11 07:16", "2025-07-11 07:17", "2025-07-11 07:18", "2025-07-11 07:19", "2025-07-11 07:20", "2025-07-11 07:21", "2025-07-11 07:22", "2025-07-11 07:23", "2025-07-11 07:24", "2025-07-11 07:25", "2025-07-11 07:26", "2025-07-11 07:27", "2025-07-11 07:28", "2025-07-11 07:29", "2025-07-11 07:30", "2025-07-11 07:31", "2025-07-11 07:32", "2025-07-11 07:33", "2025-07-11 07:34", "2025-07-11 07:35", "2025-07-11 07:36", "2025-07-11 07:37", "2025-07-11 07:38", "2025-07-11 07:39", "2025-07-11 07:40", "2025-07-11 07:41", "2025-07-11 07:42", "2025-07-11 07:43", "2025-07-11 07:44", "2025-07-11 07:45", "2025-07-11 07:46", "2025-07-11 07:47", "2025-07-11 07:48", "2025-07-11 07:49", "2025-07-11 07:50", "2025-07-11 07:51", "2025-07-11 07:52", "2025-07-11 07:53", "2025-07-11 07:54", "2025-07-11 07:55", "2025-07-11 07:56", "2025-07-11 07:57", "2025-07-11 07:58", "2025-07-11 07:59", "2025-07-11 08:00", "2025-07-11 08:01", "2025-07-11 08:02", "2025-07-11 08:03", "2025-07-11 08:04", "2025-07-11 08:05", "2025-07-11 08:06", "2025-07-11 08:07", "2025-07-11 08:08", "2025-07-11 08:09", "2025-07-11 08:10", "2025-07-11 08:11", "2025-07-11 08:12", "2025-07-11 08:13", "2025-07-11 08:14", "2025-07-11 08:15", "2025-07-11 08:16", "2025-07-11 08:17", "2025-07-11 08:18", "2025-07-11 08:19", "2025-07-11 08:20", "2025-07-11 08:21", "2025-07-11 08:22", "2025-07-11 08:23", "2025-07-11 08:24", "2025-07-11 08:25", "2025-07-11 08:26", "2025-07-11 08:27", "2025-07-11 08:28", "2025-07-11 08:29", "2025-07-11 08:30", "2025-07-11 08:31", "2025-07-11 08:32", "2025-07-11 08:33", "2025-07-11 08:34", "2025-07-11 08:35", "2025-07-11 08:36", "2025-07-11 08:37", "2025-07-11 08:38", "2025-07-11 08:39", "2025-07-11 08:40", "2025-07-11 08:41", "2025-07-11 08:42", "2025-07-11 08:43", "2025-07-11 08:44", "2025-07-11 08:45", "2025-07-11 08:46", "2025-07-11 08:47", "2025-07-11 08:48", "2025-07-11 08:49", "2025-07-11 08:50", "2025-07-11 08:51", "2025-07-11 08:52", "2025-07-11 08:53", "2025-07-11 08:54", "2025-07-11 08:55", "2025-07-11 08:56", "2025-07-11 08:57", "2025-07-11 08:58", "2025-07-11 08:59", "2025-07-11 09:00", "2025-07-11 09:01", "2025-07-11 09:02", "2025-07-11 09:03", "2025-07-11 09:04", "2025-07-11 09:05", "2025-07-11 09:06", "2025-07-11 09:07", "2025-07-11 09:08", "2025-07-11 09:09", "2025-07-11 09:10", "2025-07-11 09:11", "2025-07-11 09:12", "2025-07-11 09:13", "2025-07-11 09:14", "2025-07-11 09:15", "2025-07-11 09:16", "2025-07-11 09:17", "2025-07-11 09:18", "2025-07-11 09:19", "2025-07-11 09:20", "2025-07-11 09:21", "2025-07-11 09:22", "2025-07-11 09:23", "2025-07-11 09:24", "2025-07-11 09:25", "2025-07-11 09:26", "2025-07-11 09:27", "2025-07-11 09:28", "2025-07-11 09:29", "2025-07-11 09:30", "2025-07-11 09:31", "2025-07-11 09:32", "2025-07-11 09:33", "2025-07-11 09:34", "2025-07-11 09:35", "2025-07-11 09:36", "2025-07-11 09:37", "2025-07-11 09:38", "2025-07-11 09:39", "2025-07-11 09:40", "2025-07-11 09:41", "2025-07-11 09:42", "2025-07-11 09:43", "2025-07-11 09:44", "2025-07-11 09:45", "2025-07-11 09:46", "2025-07-11 09:47", "2025-07-11 09:48", "2025-07-11 09:49", "2025-07-11 09:50", "2025-07-11 09:51", "2025-07-11 09:52", "2025-07-11 09:53", "2025-07-11 09:54", "2025-07-11 09:55", "2025-07-11 09:56", "2025-07-11 09:57", "2025-07-11 09:58", "2025-07-11 09:59", "2025-07-11 10:00", "2025-07-11 10:01", "2025-07-11 10:02", "2025-07-11 10:03", "2025-07-11 10:04", "2025-07-11 10:05", "2025-07-11 10:06", "2025-07-11 10:07", "2025-07-11 10:08", "2025-07-11 10:09", "2025-07-11 10:10", "2025-07-11 10:11", "2025-07-11 10:12", "2025-07-11 10:13", "2025-07-11 10:14", "2025-07-11 10:15", "2025-07-11 10:16", "2025-07-11 10:17", "2025-07-11 10:18", "2025-07-11 10:19", "2025-07-11 10:20", "2025-07-11 10:21", "2025-07-11 10:22", "2025-07-11 10:23", "2025-07-11 10:24", "2025-07-11 10:25", "2025-07-11 10:26", "2025-07-11 10:27", "2025-07-11 10:28", "2025-07-11 10:29", "2025-07-11 10:30", "2025-07-11 10:31", "2025-07-11 10:32", "2025-07-11 10:33", "2025-07-11 10:34", "2025-07-11 10:35", "2025-07-11 10:36", "2025-07-11 10:37", "2025-07-11 10:38", "2025-07-11 10:39", "2025-07-11 10:40", "2025-07-11 10:41", "2025-07-11 10:42", "2025-07-11 10:43", "2025-07-11 10:44", "2025-07-11 10:45", "2025-07-11 10:46", "2025-07-11 10:47", "2025-07-11 10:48", "2025-07-11 10:49", "2025-07-11 10:50", "2025-07-11 10:51", "2025-07-11 10:52", "2025-07-11 10:53", "2025-07-11 10:54", "2025-07-11 10:55", "2025-07-11 10:56", "2025-07-11 10:57", "2025-07-11 10:58", "2025-07-11 10:59", "2025-07-11 11:00", "2025-07-11 11:01", "2025-07-11 11:02", "2025-07-11 11:03", "2025-07-11 11:04", "2025-07-11 11:05", "2025-07-11 11:06", "2025-07-11 11:07", "2025-07-11 11:08", "2025-07-11 11:09", "2025-07-11 11:10", "2025-07-11 11:11", "2025-07-11 11:12", "2025-07-11 11:13", "2025-07-11 11:14", "2025-07-11 11:15", "2025-07-11 11:16", "2025-07-11 11:17", "2025-07-11 11:18", "2025-07-11 11:19", "2025-07-11 11:20", "2025-07-11 11:21", "2025-07-11 11:22", "2025-07-11 11:23", "2025-07-11 11:24", "2025-07-11 11:25", "2025-07-11 11:26", "2025-07-11 11:27", "2025-07-11 11:28", "2025-07-11 11:29", "2025-07-11 11:30", "2025-07-11 11:31", "2025-07-11 11:32", "2025-07-11 11:33", "2025-07-11 11:34", "2025-07-11 11:35", "2025-07-11 11:36", "2025-07-11 11:37", "2025-07-11 11:38", "2025-07-11 11:39", "2025-07-11 11:40", "2025-07-11 11:41", "2025-07-11 11:42", "2025-07-11 11:43", "2025-07-11 11:44", "2025-07-11 11:45", "2025-07-11 11:46", "2025-07-11 11:47", "2025-07-11 11:48", "2025-07-11 11:49", "2025-07-11 11:50", "2025-07-11 11:51", "2025-07-11 11:52", "2025-07-11 11:53", "2025-07-11 11:54", "2025-07-11 11:55", "2025-07-11 11:56", "2025-07-11 11:57", "2025-07-11 11:58", "2025-07-11 11:59", "2025-07-11 12:00", "2025-07-11 12:01", "2025-07-11 12:02", "2025-07-11 12:03", "2025-07-11 12:04", "2025-07-11 12:05", "2025-07-11 12:06", "2025-07-11 12:07", "2025-07-11 12:08", "2025-07-11 12:09", "2025-07-11 12:10", "2025-07-11 12:11", "2025-07-11 12:12", "2025-07-11 12:13", "2025-07-11 12:14", "2025-07-11 12:15", "2025-07-11 12:16", "2025-07-11 12:17", "2025-07-11 12:18", "2025-07-11 12:19", "2025-07-11 12:20", "2025-07-11 12:21", "2025-07-11 12:22", "2025-07-11 12:23", "2025-07-11 12:24", "2025-07-11 12:25", "2025-07-11 12:26", "2025-07-11 12:27", "2025-07-11 12:28", "2025-07-11 12:29", "2025-07-11 12:30", "2025-07-11 12:31", "2025-07-11 12:32", "2025-07-11 12:33", "2025-07-11 12:34", "2025-07-11 12:35", "2025-07-11 12:36", "2025-07-11 12:37", "2025-07-11 12:38", "2025-07-11 12:39", "2025-07-11 12:40", "2025-07-11 12:41", "2025-07-11 12:42", "2025-07-11 12:43", "2025-07-11 12:44", "2025-07-11 12:45", "2025-07-11 12:46", "2025-07-11 12:47", "2025-07-11 12:48", "2025-07-11 12:49", "2025-07-11 12:50", "2025-07-11 12:51", "2025-07-11 12:52", "2025-07-11 12:53", "2025-07-11 12:54", "2025-07-11 12:55", "2025-07-11 12:56", "2025-07-11 12:57", "2025-07-11 12:58", "2025-07-11 12:59", "2025-07-11 13:00", "2025-07-11 13:01", "2025-07-11 13:02", "2025-07-11 13:03", "2025-07-11 13:04", "2025-07-11 13:05", "2025-07-11 13:06", "2025-07-11 13:07", "2025-07-11 13:08", "2025-07-11 13:09", "2025-07-11 13:10", "2025-07-11 13:11", "2025-07-11 13:12", "2025-07-11 13:13", "2025-07-11 13:14", "2025-07-11 13:15", "2025-07-11 13:16", "2025-07-11 13:17", "2025-07-11 13:18", "2025-07-11 13:19", "2025-07-11 13:20", "2025-07-11 13:21", "2025-07-11 13:22", "2025-07-11 13:23", "2025-07-11 13:24", "2025-07-11 13:25", "2025-07-11 13:26", "2025-07-11 13:27", "2025-07-11 13:28", "2025-07-11 13:29", "2025-07-11 13:30", "2025-07-11 13:31", "2025-07-11 13:32", "2025-07-11 13:33", "2025-07-11 13:34", "2025-07-11 13:35", "2025-07-11 13:36", "2025-07-11 13:37", "2025-07-11 13:38", "2025-07-11 13:39", "2025-07-11 13:40", "2025-07-11 13:41", "2025-07-11 13:42", "2025-07-11 13:43", "2025-07-11 13:44", "2025-07-11 13:45", "2025-07-11 13:46", "2025-07-11 13:47", "2025-07-11 13:48", "2025-07-11 13:49", "2025-07-11 13:50", "2025-07-11 13:51", "2025-07-11 13:52", "2025-07-11 13:53", "2025-07-11 13:54", "2025-07-11 13:55", "2025-07-11 13:56", "2025-07-11 13:57", "2025-07-11 13:58", "2025-07-11 13:59", "2025-07-11 14:00", "2025-07-11 14:01", "2025-07-11 14:02", "2025-07-11 14:03", "2025-07-11 14:04", "2025-07-11 14:05", "2025-07-11 14:06", "2025-07-11 14:07", "2025-07-11 14:08", "2025-07-11 14:09", "2025-07-11 14:10", "2025-07-11 14:11", "2025-07-11 14:12", "2025-07-11 14:13", "2025-07-11 14:14", "2025-07-11 14:15", "2025-07-11 14:16", "2025-07-11 14:17", "2025-07-11 14:18", "2025-07-11 14:19", "2025-07-11 14:20", "2025-07-11 14:21", "2025-07-11 14:22", "2025-07-11 14:23", "2025-07-11 14:24", "2025-07-11 14:25", "2025-07-11 14:26", "2025-07-11 14:27", "2025-07-11 14:28", "2025-07-11 14:29", "2025-07-11 14:30", "2025-07-11 14:31", "2025-07-11 14:32", "2025-07-11 14:33", "2025-07-11 14:34", "2025-07-11 14:35", "2025-07-11 14:36", "2025-07-11 14:37", "2025-07-11 14:38", "2025-07-11 14:39", "2025-07-11 14:40", "2025-07-11 14:41", "2025-07-11 14:42", "2025-07-11 14:43", "2025-07-11 14:44", "2025-07-11 14:45", "2025-07-11 14:46", "2025-07-11 14:47", "2025-07-11 14:48", "2025-07-11 14:49", "2025-07-11 14:50", "2025-07-11 14:51", "2025-07-11 14:52", "2025-07-11 14:53", "2025-07-11 14:54", "2025-07-11 14:55", "2025-07-11 14:56", "2025-07-11 14:57", "2025-07-11 14:58", "2025-07-11 14:59", "2025-07-11 15:00", "2025-07-11 15:01", "2025-07-11 15:02", "2025-07-11 15:03", "2025-07-11 15:04", "2025-07-11 15:05", "2025-07-11 15:06", "2025-07-11 15:07", "2025-07-11 15:08", "2025-07-11 15:09", "2025-07-11 15:10", "2025-07-11 15:11", "2025-07-11 15:12", "2025-07-11 15:13", "2025-07-11 15:14", "2025-07-11 15:15", "2025-07-11 15:16", "2025-07-11 15:17", "2025-07-11 15:18", "2025-07-11 15:19", "2025-07-11 15:20", "2025-07-11 15:21", "2025-07-11 15:22", "2025-07-11 15:23", "2025-07-11 15:24", "2025-07-11 15:25", "2025-07-11 15:26", "2025-07-11 15:27", "2025-07-11 15:28", "2025-07-11 15:29", "2025-07-11 15:30", "2025-07-11 15:31", "2025-07-11 15:32", "2025-07-11 15:33", "2025-07-11 15:34", "2025-07-11 15:35", "2025-07-11 15:36", "2025-07-11 15:37", "2025-07-11 15:38", "2025-07-11 15:39", "2025-07-11 15:40", "2025-07-11 15:41", "2025-07-11 15:42", "2025-07-11 15:43", "2025-07-11 15:44", "2025-07-11 15:45", "2025-07-11 15:46", "2025-07-11 15:47", "2025-07-11 15:48", "2025-07-11 15:49", "2025-07-11 15:50", "2025-07-11 15:51", "2025-07-11 15:52", "2025-07-11 15:53", "2025-07-11 15:54", "2025-07-11 15:55", "2025-07-11 15:56", "2025-07-11 15:57", "2025-07-11 15:58", "2025-07-11 15:59", "2025-07-11 16:00", "2025-07-11 16:01", "2025-07-11 16:02", "2025-07-11 16:03", "2025-07-11 16:04", "2025-07-11 16:05", "2025-07-11 16:06", "2025-07-11 16:07", "2025-07-11 16:08", "2025-07-11 16:09", "2025-07-11 16:10", "2025-07-11 16:11", "2025-07-11 16:12", "2025-07-11 16:13", "2025-07-11 16:14", "2025-07-11 16:15", "2025-07-11 16:16", "2025-07-11 16:17", "2025-07-11 16:18", "2025-07-11 16:19", "2025-07-11 16:20", "2025-07-11 16:21", "2025-07-11 16:22", "2025-07-11 16:23", "2025-07-11 16:24", "2025-07-11 16:25", "2025-07-11 16:26", "2025-07-11 16:27", "2025-07-11 16:28", "2025-07-11 16:29", "2025-07-11 16:30", "2025-07-11 16:31", "2025-07-11 16:32", "2025-07-11 16:33", "2025-07-11 16:34", "2025-07-11 16:35", "2025-07-11 16:36", "2025-07-11 16:37", "2025-07-11 16:38", "2025-07-11 16:39", "2025-07-11 16:40", "2025-07-11 16:41", "2025-07-11 16:42", "2025-07-11 16:43", "2025-07-11 16:44", "2025-07-11 16:45", "2025-07-11 16:46", "2025-07-11 16:47", "2025-07-11 16:48", "2025-07-11 16:49", "2025-07-11 16:50", "2025-07-11 16:51", "2025-07-11 16:52", "2025-07-11 16:53", "2025-07-11 16:54", "2025-07-11 16:55", "2025-07-11 16:56", "2025-07-11 16:57", "2025-07-11 16:58", "2025-07-11 16:59", "2025-07-11 17:00", "2025-07-11 17:01", "2025-07-11 17:02", "2025-07-11 17:03", "2025-07-11 17:04", "2025-07-11 17:05", "2025-07-11 17:06", "2025-07-11 17:07", "2025-07-11 17:08", "2025-07-11 17:09", "2025-07-11 17:10", "2025-07-11 17:11", "2025-07-11 17:12", "2025-07-11 17:13", "2025-07-11 17:14", "2025-07-11 17:15", "2025-07-11 17:16", "2025-07-11 17:17", "2025-07-11 17:18", "2025-07-11 17:19", "2025-07-11 17:20", "2025-07-11 17:21", "2025-07-11 17:22", "2025-07-11 17:23", "2025-07-11 17:24", "2025-07-11 17:25", "2025-07-11 17:26", "2025-07-11 17:27", "2025-07-11 17:28", "2025-07-11 17:29", "2025-07-11 17:30", "2025-07-11 17:31", "2025-07-11 17:32", "2025-07-11 17:33", "2025-07-11 17:34", "2025-07-11 17:35", "2025-07-11 17:36", "2025-07-11 17:37", "2025-07-11 17:38", "2025-07-11 17:39", "2025-07-11 17:40", "2025-07-11 17:41", "2025-07-11 17:42", "2025-07-11 17:43", "2025-07-11 17:44", "2025-07-11 17:45", "2025-07-11 17:46", "2025-07-11 17:47", "2025-07-11 17:48", "2025-07-11 17:49", "2025-07-11 17:50", "2025-07-11 17:51", "2025-07-11 17:52", "2025-07-11 17:53", "2025-07-11 17:54", "2025-07-11 17:55", "2025-07-11 17:56", "2025-07-11 17:57", "2025-07-11 17:58", "2025-07-11 17:59", "2025-07-11 18:00", "2025-07-11 18:01", "2025-07-11 18:02", "2025-07-11 18:03", "2025-07-11 18:04", "2025-07-11 18:05", "2025-07-11 18:06", "2025-07-11 18:07", "2025-07-11 18:08", "2025-07-11 18:09", "2025-07-11 18:10", "2025-07-11 18:11", "2025-07-11 18:12", "2025-07-11 18:13", "2025-07-11 18:14", "2025-07-11 18:15", "2025-07-11 18:16", "2025-07-11 18:17", "2025-07-11 18:18", "2025-07-11 18:19", "2025-07-11 18:20", "2025-07-11 18:21", "2025-07-11 18:22", "2025-07-11 18:23", "2025-07-11 18:24", "2025-07-11 18:25", "2025-07-11 18:26", "2025-07-11 18:27", "2025-07-11 18:28", "2025-07-11 18:29", "2025-07-11 18:30", "2025-07-11 18:31", "2025-07-11 18:32", "2025-07-11 18:33", "2025-07-11 18:34", "2025-07-11 18:35", "2025-07-11 18:36", "2025-07-11 18:37", "2025-07-11 18:38", "2025-07-11 18:39", "2025-07-11 18:40", "2025-07-11 18:41", "2025-07-11 18:42", "2025-07-11 18:43", "2025-07-11 18:44", "2025-07-11 18:45", "2025-07-11 18:46", "2025-07-11 18:47", "2025-07-11 18:48", "2025-07-11 18:49", "2025-07-11 18:50", "2025-07-11 18:51", "2025-07-11 18:52", "2025-07-11 18:53", "2025-07-11 18:54", "2025-07-11 18:55", "2025-07-11 18:56", "2025-07-11 18:57", "2025-07-11 18:58", "2025-07-11 18:59", "2025-07-11 19:00", "2025-07-11 19:01", "2025-07-11 19:02", "2025-07-11 19:03", "2025-07-11 19:04", "2025-07-11 19:05", "2025-07-11 19:06", "2025-07-11 19:07", "2025-07-11 19:08", "2025-07-11 19:09", "2025-07-11 19:10", "2025-07-11 19:11", "2025-07-11 19:12", "2025-07-11 19:13", "2025-07-11 19:14", "2025-07-11 19:15", "2025-07-11 19:16", "2025-07-11 19:17", "2025-07-11 19:18", "2025-07-11 19:19", "2025-07-11 19:20", "2025-07-11 19:21", "2025-07-11 19:22", "2025-07-11 19:23", "2025-07-11 19:24", "2025-07-11 19:25", "2025-07-11 19:26", "2025-07-11 19:27", "2025-07-11 19:28", "2025-07-11 19:29", "2025-07-11 19:30", "2025-07-11 19:31", "2025-07-11 19:32", "2025-07-11 19:33", "2025-07-11 19:34", "2025-07-11 19:35", "2025-07-11 19:36", "2025-07-11 19:37", "2025-07-11 19:38", "2025-07-11 19:39", "2025-07-11 19:40", "2025-07-11 19:41", "2025-07-11 19:42", "2025-07-11 19:43", "2025-07-11 19:44", "2025-07-11 19:45", "2025-07-11 19:46", "2025-07-11 19:47", "2025-07-11 19:48", "2025-07-11 19:49", "2025-07-11 19:50", "2025-07-11 19:51", "2025-07-11 19:52", "2025-07-11 19:53", "2025-07-11 19:54", "2025-07-11 19:55", "2025-07-11 19:56", "2025-07-11 19:57", "2025-07-11 19:58", "2025-07-11 19:59", "2025-07-11 20:00", "2025-07-11 20:01", "2025-07-11 20:02", "2025-07-11 20:03", "2025-07-11 20:04", "2025-07-11 20:05", "2025-07-11 20:06", "2025-07-11 20:07", "2025-07-11 20:08", "2025-07-11 20:09", "2025-07-11 20:10", "2025-07-11 20:11", "2025-07-11 20:12", "2025-07-11 20:13", "2025-07-11 20:14", "2025-07-11 20:15", "2025-07-11 20:16", "2025-07-11 20:17", "2025-07-11 20:18", "2025-07-11 20:19", "2025-07-11 20:20", "2025-07-11 20:21", "2025-07-11 20:22", "2025-07-11 20:23", "2025-07-11 20:24", "2025-07-11 20:25", "2025-07-11 20:26", "2025-07-11 20:27", "2025-07-11 20:28", "2025-07-11 20:29", "2025-07-11 20:30", "2025-07-11 20:31", "2025-07-11 20:32", "2025-07-11 20:33", "2025-07-11 20:34", "2025-07-11 20:35", "2025-07-11 20:36", "2025-07-11 20:37", "2025-07-11 20:38", "2025-07-11 20:39", "2025-07-11 20:40", "2025-07-11 20:41", "2025-07-11 20:42", "2025-07-11 20:43", "2025-07-11 20:44", "2025-07-11 20:45", "2025-07-11 20:46", "2025-07-11 20:47", "2025-07-11 20:48", "2025-07-11 20:49", "2025-07-11 20:50", "2025-07-11 20:51", "2025-07-11 20:52", "2025-07-11 20:53", "2025-07-11 20:54", "2025-07-11 20:55", "2025-07-11 20:56", "2025-07-11 20:57", "2025-07-11 20:58", "2025-07-11 20:59", "2025-07-11 21:00", "2025-07-11 21:01", "2025-07-11 21:02", "2025-07-11 21:03", "2025-07-11 21:04", "2025-07-11 21:05", "2025-07-11 21:06", "2025-07-11 21:07", "2025-07-11 21:08", "2025-07-11 21:09", "2025-07-11 21:10", "2025-07-11 21:11", "2025-07-11 21:12", "2025-07-11 21:13", "2025-07-11 21:14", "2025-07-11 21:15", "2025-07-11 21:16", "2025-07-11 21:17", "2025-07-11 21:18", "2025-07-11 21:19", "2025-07-11 21:20", "2025-07-11 21:21", "2025-07-11 21:22", "2025-07-11 21:23", "2025-07-11 21:24", "2025-07-11 21:25", "2025-07-11 21:26", "2025-07-11 21:27", "2025-07-11 21:28", "2025-07-11 21:29", "2025-07-11 21:30", "2025-07-11 21:31", "2025-07-11 21:32", "2025-07-11 21:33", "2025-07-11 21:34", "2025-07-11 21:35", "2025-07-11 21:36", "2025-07-11 21:37", "2025-07-11 21:38", "2025-07-11 21:39", "2025-07-11 21:40", "2025-07-11 21:41", "2025-07-11 21:42", "2025-07-11 21:43", "2025-07-11 21:44", "2025-07-11 21:45", "2025-07-11 21:46", "2025-07-11 21:47", "2025-07-11 21:48", "2025-07-11 21:49", "2025-07-11 21:50", "2025-07-11 21:51", "2025-07-11 21:52", "2025-07-11 21:53", "2025-07-11 21:54", "2025-07-11 21:55", "2025-07-11 21:56", "2025-07-11 21:57", "2025-07-11 21:58", "2025-07-11 21:59", "2025-07-11 22:00", "2025-07-11 22:01", "2025-07-11 22:02", "2025-07-11 22:03", "2025-07-11 22:04", "2025-07-11 22:05", "2025-07-11 22:06", "2025-07-11 22:07", "2025-07-11 22:08", "2025-07-11 22:09", "2025-07-11 22:10", "2025-07-11 22:11", "2025-07-11 22:12", "2025-07-11 22:13", "2025-07-11 22:14", "2025-07-11 22:15", "2025-07-11 22:16", "2025-07-11 22:17", "2025-07-11 22:18", "2025-07-11 22:19", "2025-07-11 22:20", "2025-07-11 22:21", "2025-07-11 22:22", "2025-07-11 22:23", "2025-07-11 22:24", "2025-07-11 22:25", "2025-07-11 22:26", "2025-07-11 22:27", "2025-07-11 22:28", "2025-07-11 22:29", "2025-07-11 22:30", "2025-07-11 22:31", "2025-07-11 22:32", "2025-07-11 22:33", "2025-07-11 22:34", "2025-07-11 22:35", "2025-07-11 22:36", "2025-07-11 22:37", "2025-07-11 22:38", "2025-07-11 22:39", "2025-07-11 22:40", "2025-07-11 22:41", "2025-07-11 22:42", "2025-07-11 22:43", "2025-07-11 22:44", "2025-07-11 22:45", "2025-07-11 22:46", "2025-07-11 22:47", "2025-07-11 22:48", "2025-07-11 22:49", "2025-07-11 22:50", "2025-07-11 22:51", "2025-07-11 22:52", "2025-07-11 22:53", "2025-07-11 22:54", "2025-07-11 22:55", "2025-07-11 22:56", "2025-07-11 22:57", "2025-07-11 22:58", "2025-07-11 22:59", "2025-07-11 23:00", "2025-07-11 23:01", "2025-07-11 23:02", "2025-07-11 23:03", "2025-07-11 23:04", "2025-07-11 23:05", "2025-07-11 23:06", "2025-07-11 23:07", "2025-07-11 23:08", "2025-07-11 23:09", "2025-07-11 23:10", "2025-07-11 23:11", "2025-07-11 23:12", "2025-07-11 23:13", "2025-07-11 23:14", "2025-07-11 23:15", "2025-07-11 23:16", "2025-07-11 23:17", "2025-07-11 23:18", "2025-07-11 23:19", "2025-07-11 23:20", "2025-07-11 23:21", "2025-07-11 23:22", "2025-07-11 23:23", "2025-07-11 23:24", "2025-07-11 23:25", "2025-07-11 23:26", "2025-07-11 23:27", "2025-07-11 23:28", "2025-07-11 23:29", "2025-07-11 23:30", "2025-07-11 23:31", "2025-07-11 23:32", "2025-07-11 23:33", "2025-07-11 23:34", "2025-07-11 23:35", "2025-07-11 23:36", "2025-07-11 23:37", "2025-07-11 23:38", "2025-07-11 23:39", "2025-07-11 23:40", "2025-07-11 23:41", "2025-07-11 23:42", "2025-07-11 23:43", "2025-07-11 23:44", "2025-07-11 23:45", "2025-07-11 23:46", "2025-07-11 23:47", "2025-07-11 23:48", "2025-07-11 23:49", "2025-07-11 23:50", "2025-07-11 23:51", "2025-07-11 23:52", "2025-07-11 23:53", "2025-07-11 23:54", "2025-07-11 23:55", "2025-07-11 23:56", "2025-07-11 23:57", "2025-07-11 23:58", "2025-07-11 23:59", "2025-07-12 00:00", "2025-07-12 00:01", "2025-07-12 00:02", "2025-07-12 00:03", "2025-07-12 00:04", "2025-07-12 00:05", "2025-07-12 00:06", "2025-07-12 00:07", "2025-07-12 00:08", "2025-07-12 00:09", "2025-07-12 00:10", "2025-07-12 00:11", "2025-07-12 00:12", "2025-07-12 00:13", "2025-07-12 00:14", "2025-07-12 00:15", "2025-07-12 00:16", "2025-07-12 00:17", "2025-07-12 00:18", "2025-07-12 00:19", "2025-07-12 00:20", "2025-07-12 00:21", "2025-07-12 00:22", "2025-07-12 00:23", "2025-07-12 00:24", "2025-07-12 00:25", "2025-07-12 00:26", "2025-07-12 00:27", "2025-07-12 00:28", "2025-07-12 00:29", "2025-07-12 00:30", "2025-07-12 00:31", "2025-07-12 00:32", "2025-07-12 00:33", "2025-07-12 00:34", "2025-07-12 00:35", "2025-07-12 00:36", "2025-07-12 00:37", "2025-07-12 00:38", "2025-07-12 00:39", "2025-07-12 00:40", "2025-07-12 00:41", "2025-07-12 00:42", "2025-07-12 00:43", "2025-07-12 00:44", "2025-07-12 00:45", "2025-07-12 00:46", "2025-07-12 00:47", "2025-07-12 00:48", "2025-07-12 00:49", "2025-07-12 00:50", "2025-07-12 00:51", "2025-07-12 00:52", "2025-07-12 00:53", "2025-07-12 00:54", "2025-07-12 00:55", "2025-07-12 00:56", "2025-07-12 00:57", "2025-07-12 00:58", "2025-07-12 00:59", "2025-07-12 01:00", "2025-07-12 01:01", "2025-07-12 01:02", "2025-07-12 01:03", "2025-07-12 01:04", "2025-07-12 01:05", "2025-07-12 01:06", "2025-07-12 01:07", "2025-07-12 01:08", "2025-07-12 01:09", "2025-07-12 01:10", "2025-07-12 01:11", "2025-07-12 01:12", "2025-07-12 01:13", "2025-07-12 01:14", "2025-07-12 01:15", "2025-07-12 01:16", "2025-07-12 01:17", "2025-07-12 01:18", "2025-07-12 01:19", "2025-07-12 01:20", "2025-07-12 01:21", "2025-07-12 01:22", "2025-07-12 01:23", "2025-07-12 01:24", "2025-07-12 01:25", "2025-07-12 01:26", "2025-07-12 01:27", "2025-07-12 01:28", "2025-07-12 01:29", "2025-07-12 01:30", "2025-07-12 01:31", "2025-07-12 01:32", "2025-07-12 01:33", "2025-07-12 01:34", "2025-07-12 01:35", "2025-07-12 01:36", "2025-07-12 01:37", "2025-07-12 01:38", "2025-07-12 01:39", "2025-07-12 01:40", "2025-07-12 01:41", "2025-07-12 01:42", "2025-07-12 01:43", "2025-07-12 01:44", "2025-07-12 01:45", "2025-07-12 01:46", "2025-07-12 01:47", "2025-07-12 01:48", "2025-07-12 01:49", "2025-07-12 01:50", "2025-07-12 01:51", "2025-07-12 01:52", "2025-07-12 01:53", "2025-07-12 01:54", "2025-07-12 01:55", "2025-07-12 01:56", "2025-07-12 01:57", "2025-07-12 01:58", "2025-07-12 01:59", "2025-07-12 02:00", "2025-07-12 02:01", "2025-07-12 02:02", "2025-07-12 02:03", "2025-07-12 02:04", "2025-07-12 02:05", "2025-07-12 02:06", "2025-07-12 02:07", "2025-07-12 02:08", "2025-07-12 02:09", "2025-07-12 02:10", "2025-07-12 02:11", "2025-07-12 02:12", "2025-07-12 02:13", "2025-07-12 02:14", "2025-07-12 02:15", "2025-07-12 02:16", "2025-07-12 02:17", "2025-07-12 02:18", "2025-07-12 02:19", "2025-07-12 02:20", "2025-07-12 02:21", "2025-07-12 02:22", "2025-07-12 02:23", "2025-07-12 02:24", "2025-07-12 02:25", "2025-07-12 02:26", "2025-07-12 02:27", "2025-07-12 02:28", "2025-07-12 02:29", "2025-07-12 02:30", "2025-07-12 02:31", "2025-07-12 02:32", "2025-07-12 02:33", "2025-07-12 02:34", "2025-07-12 02:35", "2025-07-12 02:36", "2025-07-12 02:37", "2025-07-12 02:38", "2025-07-12 02:39", "2025-07-12 02:40", "2025-07-12 02:41", "2025-07-12 02:42", "2025-07-12 02:43", "2025-07-12 02:44", "2025-07-12 02:45", "2025-07-12 02:46", "2025-07-12 02:47", "2025-07-12 02:48", "2025-07-12 02:49", "2025-07-12 02:50", "2025-07-12 02:51", "2025-07-12 02:52", "2025-07-12 02:53", "2025-07-12 02:54", "2025-07-12 02:55", "2025-07-12 02:56", "2025-07-12 02:57", "2025-07-12 02:58", "2025-07-12 02:59", "2025-07-12 03:00", "2025-07-12 03:01", "2025-07-12 03:02", "2025-07-12 03:03", "2025-07-12 03:04", "2025-07-12 03:05", "2025-07-12 03:06", "2025-07-12 03:07", "2025-07-12 03:08", "2025-07-12 03:09", "2025-07-12 03:10", "2025-07-12 03:11", "2025-07-12 03:12", "2025-07-12 03:13", "2025-07-12 03:14", "2025-07-12 03:15", "2025-07-12 03:16", "2025-07-12 03:17", "2025-07-12 03:18", "2025-07-12 03:19", "2025-07-12 03:20", "2025-07-12 03:21", "2025-07-12 03:22", "2025-07-12 03:23", "2025-07-12 03:24", "2025-07-12 03:25", "2025-07-12 03:26", "2025-07-12 03:27", "2025-07-12 03:28", "2025-07-12 03:29", "2025-07-12 03:30", "2025-07-12 03:31", "2025-07-12 03:32", "2025-07-12 03:33", "2025-07-12 03:34", "2025-07-12 03:35", "2025-07-12 03:36", "2025-07-12 03:37", "2025-07-12 03:38", "2025-07-12 03:39", "2025-07-12 03:40", "2025-07-12 03:41", "2025-07-12 03:42", "2025-07-12 03:43", "2025-07-12 03:44", "2025-07-12 03:45", "2025-07-12 03:46", "2025-07-12 03:47", "2025-07-12 03:48", "2025-07-12 03:49", "2025-07-12 03:50", "2025-07-12 03:51", "2025-07-12 03:52", "2025-07-12 03:53", "2025-07-12 03:54", "2025-07-12 03:55", "2025-07-12 03:56", "2025-07-12 03:57", "2025-07-12 03:58", "2025-07-12 03:59", "2025-07-12 04:00", "2025-07-12 04:01", "2025-07-12 04:02", "2025-07-12 04:03", "2025-07-12 04:04", "2025-07-12 04:05", "2025-07-12 04:06", "2025-07-12 04:07", "2025-07-12 04:08", "2025-07-12 04:09", "2025-07-12 04:10", "2025-07-12 04:11", "2025-07-12 04:12", "2025-07-12 04:13", "2025-07-12 04:14", "2025-07-12 04:15", "2025-07-12 04:16", "2025-07-12 04:17", "2025-07-12 04:18", "2025-07-12 04:19", "2025-07-12 04:20", "2025-07-12 04:21", "2025-07-12 04:22", "2025-07-12 04:23", "2025-07-12 04:24", "2025-07-12 04:25", "2025-07-12 04:26", "2025-07-12 04:27", "2025-07-12 04:28", "2025-07-12 04:29", "2025-07-12 04:30", "2025-07-12 04:31", "2025-07-12 04:32", "2025-07-12 04:33", "2025-07-12 04:34", "2025-07-12 04:35", "2025-07-12 04:36", "2025-07-12 04:37", "2025-07-12 04:38", "2025-07-12 04:39", "2025-07-12 04:40", "2025-07-12 04:41", "2025-07-12 04:42", "2025-07-12 04:43", "2025-07-12 04:44", "2025-07-12 04:45", "2025-07-12 04:46", "2025-07-12 04:47", "2025-07-12 04:48", "2025-07-12 04:49", "2025-07-12 04:50", "2025-07-12 04:51", "2025-07-12 04:52", "2025-07-12 04:53", "2025-07-12 04:54", "2025-07-12 04:55", "2025-07-12 04:56", "2025-07-12 04:57", "2025-07-12 04:58", "2025-07-12 04:59", "2025-07-12 05:00", "2025-07-12 05:01", "2025-07-12 05:02", "2025-07-12 05:03", "2025-07-12 05:04", "2025-07-12 05:05", "2025-07-12 05:06", "2025-07-12 05:07", "2025-07-12 05:08", "2025-07-12 05:09", "2025-07-12 05:10", "2025-07-12 05:11", "2025-07-12 05:12", "2025-07-12 05:13", "2025-07-12 05:14", "2025-07-12 05:15", "2025-07-12 05:16", "2025-07-12 05:17", "2025-07-12 05:18", "2025-07-12 05:19", "2025-07-12 05:20", "2025-07-12 05:21", "2025-07-12 05:22", "2025-07-12 05:23", "2025-07-12 05:24", "2025-07-12 05:25", "2025-07-12 05:26", "2025-07-12 05:27", "2025-07-12 05:28", "2025-07-12 05:29", "2025-07-12 05:30", "2025-07-12 05:31", "2025-07-12 05:32", "2025-07-12 05:33", "2025-07-12 05:34", "2025-07-12 05:35", "2025-07-12 05:36", "2025-07-12 05:37", "2025-07-12 05:38", "2025-07-12 05:39", "2025-07-12 05:40", "2025-07-12 05:41", "2025-07-12 05:42", "2025-07-12 05:43", "2025-07-12 05:44", "2025-07-12 05:45", "2025-07-12 05:46", "2025-07-12 05:47", "2025-07-12 05:48", "2025-07-12 05:49", "2025-07-12 05:50", "2025-07-12 05:51", "2025-07-12 05:52", "2025-07-12 05:53", "2025-07-12 05:54", "2025-07-12 05:55", "2025-07-12 05:56", "2025-07-12 05:57", "2025-07-12 05:58", "2025-07-12 05:59", "2025-07-12 06:00", "2025-07-12 06:01", "2025-07-12 06:02", "2025-07-12 06:03", "2025-07-12 06:04", "2025-07-12 06:05", "2025-07-12 06:06", "2025-07-12 06:07", "2025-07-12 06:08", "2025-07-12 06:09", "2025-07-12 06:10", "2025-07-12 06:11", "2025-07-12 06:12", "2025-07-12 06:13", "2025-07-12 06:14", "2025-07-12 06:15", "2025-07-12 06:16", "2025-07-12 06:17", "2025-07-12 06:18", "2025-07-12 06:19", "2025-07-12 06:20", "2025-07-12 06:21", "2025-07-12 06:22", "2025-07-12 06:23", "2025-07-12 06:24", "2025-07-12 06:25", "2025-07-12 06:26", "2025-07-12 06:27", "2025-07-12 06:28", "2025-07-12 06:29", "2025-07-12 06:30", "2025-07-12 06:31", "2025-07-12 06:32", "2025-07-12 06:33", "2025-07-12 06:34", "2025-07-12 06:35", "2025-07-12 06:36", "2025-07-12 06:37", "2025-07-12 06:38", "2025-07-12 06:39", "2025-07-12 06:40", "2025-07-12 06:41", "2025-07-12 06:42", "2025-07-12 06:43", "2025-07-12 06:44", "2025-07-12 06:45", "2025-07-12 06:46", "2025-07-12 06:47", "2025-07-12 06:48", "2025-07-12 06:49", "2025-07-12 06:50", "2025-07-12 06:51", "2025-07-12 06:52", "2025-07-12 06:53", "2025-07-12 06:54", "2025-07-12 06:55", "2025-07-12 06:56", "2025-07-12 06:57", "2025-07-12 06:58", "2025-07-12 06:59", "2025-07-12 07:00", "2025-07-12 07:01", "2025-07-12 07:02", "2025-07-12 07:03", "2025-07-12 07:04", "2025-07-12 07:05", "2025-07-12 07:06", "2025-07-12 07:07", "2025-07-12 07:08", "2025-07-12 07:09", "2025-07-12 07:10", "2025-07-12 07:11", "2025-07-12 07:12", "2025-07-12 07:13", "2025-07-12 07:14", "2025-07-12 07:15", "2025-07-12 07:16", "2025-07-12 07:17", "2025-07-12 07:18", "2025-07-12 07:19", "2025-07-12 07:20", "2025-07-12 07:21", "2025-07-12 07:22", "2025-07-12 07:23", "2025-07-12 07:24", "2025-07-12 07:25", "2025-07-12 07:26", "2025-07-12 07:27", "2025-07-12 07:28", "2025-07-12 07:29", "2025-07-12 07:30", "2025-07-12 07:31", "2025-07-12 07:32", "2025-07-12 07:33", "2025-07-12 07:34", "2025-07-12 07:35", "2025-07-12 07:36", "2025-07-12 07:37", "2025-07-12 07:38", "2025-07-12 07:39", "2025-07-12 07:40", "2025-07-12 07:41", "2025-07-12 07:42", "2025-07-12 07:43", "2025-07-12 07:44", "2025-07-12 07:45", "2025-07-12 07:46", "2025-07-12 07:47", "2025-07-12 07:48", "2025-07-12 07:49", "2025-07-12 07:50", "2025-07-12 07:51", "2025-07-12 07:52", "2025-07-12 07:53", "2025-07-12 07:54", "2025-07-12 07:55", "2025-07-12 07:56", "2025-07-12 07:57", "2025-07-12 07:58", "2025-07-12 07:59", "2025-07-12 08:00", "2025-07-12 08:01", "2025-07-12 08:02", "2025-07-12 08:03", "2025-07-12 08:04", "2025-07-12 08:05", "2025-07-12 08:06", "2025-07-12 08:07", "2025-07-12 08:08", "2025-07-12 08:09", "2025-07-12 08:10", "2025-07-12 08:11", "2025-07-12 08:12", "2025-07-12 08:13", "2025-07-12 08:14", "2025-07-12 08:15", "2025-07-12 08:16", "2025-07-12 08:17", "2025-07-12 08:18", "2025-07-12 08:19", "2025-07-12 08:20", "2025-07-12 08:21", "2025-07-12 08:22", "2025-07-12 08:23", "2025-07-12 08:24", "2025-07-12 08:25", "2025-07-12 08:26", "2025-07-12 08:27", "2025-07-12 08:28", "2025-07-12 08:29", "2025-07-12 08:30", "2025-07-12 08:31", "2025-07-12 08:32", "2025-07-12 08:33", "2025-07-12 08:34", "2025-07-12 08:35", "2025-07-12 08:36", "2025-07-12 08:37", "2025-07-12 08:38", "2025-07-12 08:39", "2025-07-12 08:40", "2025-07-12 08:41", "2025-07-12 08:42", "2025-07-12 08:43", "2025-07-12 08:44", "2025-07-12 08:45", "2025-07-12 08:46", "2025-07-12 08:47", "2025-07-12 08:48", "2025-07-12 08:49", "2025-07-12 08:50", "2025-07-12 08:51", "2025-07-12 08:52", "2025-07-12 08:53", "2025-07-12 08:54", "2025-07-12 08:55", "2025-07-12 08:56", "2025-07-12 08:57", "2025-07-12 08:58", "2025-07-12 08:59", "2025-07-12 09:00", "2025-07-12 09:01", "2025-07-12 09:02", "2025-07-12 09:03", "2025-07-12 09:04", "2025-07-12 09:05", "2025-07-12 09:06", "2025-07-12 09:07", "2025-07-12 09:08", "2025-07-12 09:09", "2025-07-12 09:10", "2025-07-12 09:11", "2025-07-12 09:12", "2025-07-12 09:13", "2025-07-12 09:14", "2025-07-12 09:15", "2025-07-12 09:16", "2025-07-12 09:17", "2025-07-12 09:18", "2025-07-12 09:19", "2025-07-12 09:20", "2025-07-12 09:21", "2025-07-12 09:22", "2025-07-12 09:23", "2025-07-12 09:24", "2025-07-12 09:25", "2025-07-12 09:26", "2025-07-12 09:27", "2025-07-12 09:28", "2025-07-12 09:29", "2025-07-12 09:30", "2025-07-12 09:31", "2025-07-12 09:32", "2025-07-12 09:33", "2025-07-12 09:34", "2025-07-12 09:35", "2025-07-12 09:36", "2025-07-12 09:37", "2025-07-12 09:38", "2025-07-12 09:39", "2025-07-12 09:40", "2025-07-12 09:41", "2025-07-12 09:42", "2025-07-12 09:43", "2025-07-12 09:44", "2025-07-12 09:45", "2025-07-12 09:46", "2025-07-12 09:47", "2025-07-12 09:48", "2025-07-12 09:49", "2025-07-12 09:50", "2025-07-12 09:51", "2025-07-12 09:52", "2025-07-12 09:53", "2025-07-12 09:54", "2025-07-12 09:55", "2025-07-12 09:56", "2025-07-12 09:57", "2025-07-12 09:58", "2025-07-12 09:59", "2025-07-12 10:00", "2025-07-12 10:01", "2025-07-12 10:02", "2025-07-12 10:03", "2025-07-12 10:04", "2025-07-12 10:05", "2025-07-12 10:06", "2025-07-12 10:07", "2025-07-12 10:08", "2025-07-12 10:09", "2025-07-12 10:10", "2025-07-12 10:11", "2025-07-12 10:12", "2025-07-12 10:13", "2025-07-12 10:14", "2025-07-12 10:15", "2025-07-12 10:16", "2025-07-12 10:17", "2025-07-12 10:18", "2025-07-12 10:19", "2025-07-12 10:20", "2025-07-12 10:21", "2025-07-12 10:22", "2025-07-12 10:23", "2025-07-12 10:24", "2025-07-12 10:25", "2025-07-12 10:26", "2025-07-12 10:27", "2025-07-12 10:28", "2025-07-12 10:29", "2025-07-12 10:30", "2025-07-12 10:31", "2025-07-12 10:32", "2025-07-12 10:33", "2025-07-12 10:34", "2025-07-12 10:35", "2025-07-12 10:36", "2025-07-12 10:37", "2025-07-12 10:38", "2025-07-12 10:39", "2025-07-12 10:40", "2025-07-12 10:41", "2025-07-12 10:42", "2025-07-12 10:43", "2025-07-12 10:44", "2025-07-12 10:45", "2025-07-12 10:46", "2025-07-12 10:47", "2025-07-12 10:48", "2025-07-12 10:49", "2025-07-12 10:50", "2025-07-12 10:51", "2025-07-12 10:52", "2025-07-12 10:53", "2025-07-12 10:54", "2025-07-12 10:55", "2025-07-12 10:56", "2025-07-12 10:57", "2025-07-12 10:58", "2025-07-12 10:59", "2025-07-12 11:00", "2025-07-12 11:01", "2025-07-12 11:02", "2025-07-12 11:03", "2025-07-12 11:04", "2025-07-12 11:05", "2025-07-12 11:06", "2025-07-12 11:07", "2025-07-12 11:08", "2025-07-12 11:09", "2025-07-12 11:10", "2025-07-12 11:11", "2025-07-12 11:12", "2025-07-12 11:13", "2025-07-12 11:14", "2025-07-12 11:15", "2025-07-12 11:16", "2025-07-12 11:17", "2025-07-12 11:18", "2025-07-12 11:19", "2025-07-12 11:20", "2025-07-12 11:21", "2025-07-12 11:22", "2025-07-12 11:23", "2025-07-12 11:24", "2025-07-12 11:25", "2025-07-12 11:26", "2025-07-12 11:27", "2025-07-12 11:28", "2025-07-12 11:29", "2025-07-12 11:30", "2025-07-12 11:31", "2025-07-12 11:32", "2025-07-12 11:33", "2025-07-12 11:34", "2025-07-12 11:35", "2025-07-12 11:36", "2025-07-12 11:37", "2025-07-12 11:38", "2025-07-12 11:39", "2025-07-12 11:40", "2025-07-12 11:41", "2025-07-12 11:42", "2025-07-12 11:43", "2025-07-12 11:44", "2025-07-12 11:45", "2025-07-12 11:46", "2025-07-12 11:47", "2025-07-12 11:48", "2025-07-12 11:49", "2025-07-12 11:50", "2025-07-12 11:51", "2025-07-12 11:52", "2025-07-12 11:53", "2025-07-12 11:54", "2025-07-12 11:55", "2025-07-12 11:56", "2025-07-12 11:57", "2025-07-12 11:58", "2025-07-12 11:59", "2025-07-12 12:00", "2025-07-12 12:01", "2025-07-12 12:02", "2025-07-12 12:03", "2025-07-12 12:04", "2025-07-12 12:05", "2025-07-12 12:06", "2025-07-12 12:07", "2025-07-12 12:08", "2025-07-12 12:09", "2025-07-12 12:10", "2025-07-12 12:11", "2025-07-12 12:12", "2025-07-12 12:13", "2025-07-12 12:14", "2025-07-12 12:15", "2025-07-12 12:16", "2025-07-12 12:17", "2025-07-12 12:18", "2025-07-12 12:19", "2025-07-12 12:20", "2025-07-12 12:21", "2025-07-12 12:22", "2025-07-12 12:23", "2025-07-12 12:24", "2025-07-12 12:25", "2025-07-12 12:26", "2025-07-12 12:27", "2025-07-12 12:28", "2025-07-12 12:29", "2025-07-12 12:30", "2025-07-12 12:31", "2025-07-12 12:32", "2025-07-12 12:33", "2025-07-12 12:34", "2025-07-12 12:35", "2025-07-12 12:36", "2025-07-12 12:37", "2025-07-12 12:38", "2025-07-12 12:39", "2025-07-12 12:40", "2025-07-12 12:41", "2025-07-12 12:42", "2025-07-12 12:43", "2025-07-12 12:44", "2025-07-12 12:45", "2025-07-12 12:46", "2025-07-12 12:47", "2025-07-12 12:48", "2025-07-12 12:49", "2025-07-12 12:50", "2025-07-12 12:51", "2025-07-12 12:52", "2025-07-12 12:53", "2025-07-12 12:54", "2025-07-12 12:55", "2025-07-12 12:56", "2025-07-12 12:57", "2025-07-12 12:58", "2025-07-12 12:59", "2025-07-12 13:00", "2025-07-12 13:01", "2025-07-12 13:02", "2025-07-12 13:03", "2025-07-12 13:04", "2025-07-12 13:05", "2025-07-12 13:06", "2025-07-12 13:07", "2025-07-12 13:08", "2025-07-12 13:09", "2025-07-12 13:10", "2025-07-12 13:11", "2025-07-12 13:12", "2025-07-12 13:13", "2025-07-12 13:14", "2025-07-12 13:15", "2025-07-12 13:16", "2025-07-12 13:17", "2025-07-12 13:18", "2025-07-12 13:19", "2025-07-12 13:20", "2025-07-12 13:21", "2025-07-12 13:22", "2025-07-12 13:23", "2025-07-12 13:24", "2025-07-12 13:25", "2025-07-12 13:26", "2025-07-12 13:27", "2025-07-12 13:28", "2025-07-12 13:29", "2025-07-12 13:30", "2025-07-12 13:31", "2025-07-12 13:32", "2025-07-12 13:33", "2025-07-12 13:34", "2025-07-12 13:35", "2025-07-12 13:36", "2025-07-12 13:37", "2025-07-12 13:38", "2025-07-12 13:39", "2025-07-12 13:40", "2025-07-12 13:41", "2025-07-12 13:42", "2025-07-12 13:43", "2025-07-12 13:44", "2025-07-12 13:45", "2025-07-12 13:46", "2025-07-12 13:47", "2025-07-12 13:48", "2025-07-12 13:49", "2025-07-12 13:50", "2025-07-12 13:51", "2025-07-12 13:52", "2025-07-12 13:53", "2025-07-12 13:54", "2025-07-12 13:55", "2025-07-12 13:56", "2025-07-12 13:57", "2025-07-12 13:58", "2025-07-12 13:59", "2025-07-12 14:00", "2025-07-12 14:01", "2025-07-12 14:02", "2025-07-12 14:03", "2025-07-12 14:04", "2025-07-12 14:05", "2025-07-12 14:06", "2025-07-12 14:07", "2025-07-12 14:08", "2025-07-12 14:09", "2025-07-12 14:10", "2025-07-12 14:11", "2025-07-12 14:12", "2025-07-12 14:13", "2025-07-12 14:14", "2025-07-12 14:15", "2025-07-12 14:16", "2025-07-12 14:17", "2025-07-12 14:18", "2025-07-12 14:19", "2025-07-12 14:20", "2025-07-12 14:21", "2025-07-12 14:22", "2025-07-12 14:23", "2025-07-12 14:24", "2025-07-12 14:25", "2025-07-12 14:26", "2025-07-12 14:27", "2025-07-12 14:28", "2025-07-12 14:29", "2025-07-12 14:30", "2025-07-12 14:31", "2025-07-12 14:32", "2025-07-12 14:33", "2025-07-12 14:34", "2025-07-12 14:35", "2025-07-12 14:36", "2025-07-12 14:37", "2025-07-12 14:38", "2025-07-12 14:39", "2025-07-12 14:40", "2025-07-12 14:41", "2025-07-12 14:42", "2025-07-12 14:43", "2025-07-12 14:44", "2025-07-12 14:45", "2025-07-12 14:46", "2025-07-12 14:47", "2025-07-12 14:48", "2025-07-12 14:49", "2025-07-12 14:50", "2025-07-12 14:51", "2025-07-12 14:52", "2025-07-12 14:53", "2025-07-12 14:54", "2025-07-12 14:55", "2025-07-12 14:56", "2025-07-12 14:57", "2025-07-12 14:58", "2025-07-12 14:59", "2025-07-12 15:00", "2025-07-12 15:01", "2025-07-12 15:02", "2025-07-12 15:03", "2025-07-12 15:04", "2025-07-12 15:05", "2025-07-12 15:06", "2025-07-12 15:07", "2025-07-12 15:08", "2025-07-12 15:09", "2025-07-12 15:10", "2025-07-12 15:11", "2025-07-12 15:12", "2025-07-12 15:13", "2025-07-12 15:14", "2025-07-12 15:15", "2025-07-12 15:16", "2025-07-12 15:17", "2025-07-12 15:18", "2025-07-12 15:19", "2025-07-12 15:20", "2025-07-12 15:21", "2025-07-12 15:22", "2025-07-12 15:23", "2025-07-12 15:24", "2025-07-12 15:25", "2025-07-12 15:26", "2025-07-12 15:27", "2025-07-12 15:28", "2025-07-12 15:29", "2025-07-12 15:30", "2025-07-12 15:31", "2025-07-12 15:32", "2025-07-12 15:33", "2025-07-12 15:34", "2025-07-12 15:35", "2025-07-12 15:36", "2025-07-12 15:37", "2025-07-12 15:38", "2025-07-12 15:39", "2025-07-12 15:40", "2025-07-12 15:41", "2025-07-12 15:42", "2025-07-12 15:43", "2025-07-12 15:44", "2025-07-12 15:45", "2025-07-12 15:46", "2025-07-12 15:47", "2025-07-12 15:48", "2025-07-12 15:49", "2025-07-12 15:50", "2025-07-12 15:51", "2025-07-12 15:52", "2025-07-12 15:53", "2025-07-12 15:54", "2025-07-12 15:55", "2025-07-12 15:56", "2025-07-12 15:57", "2025-07-12 15:58", "2025-07-12 15:59", "2025-07-12 16:00", "2025-07-12 16:01", "2025-07-12 16:02", "2025-07-12 16:03", "2025-07-12 16:04", "2025-07-12 16:05", "2025-07-12 16:06", "2025-07-12 16:07", "2025-07-12 16:08", "2025-07-12 16:09", "2025-07-12 16:10", "2025-07-12 16:11", "2025-07-12 16:12", "2025-07-12 16:13", "2025-07-12 16:14", "2025-07-12 16:15", "2025-07-12 16:16", "2025-07-12 16:17", "2025-07-12 16:18", "2025-07-12 16:19", "2025-07-12 16:20", "2025-07-12 16:21", "2025-07-12 16:22", "2025-07-12 16:23", "2025-07-12 16:24", "2025-07-12 16:25", "2025-07-12 16:26", "2025-07-12 16:27", "2025-07-12 16:28", "2025-07-12 16:29", "2025-07-12 16:30", "2025-07-12 16:31", "2025-07-12 16:32", "2025-07-12 16:33", "2025-07-12 16:34", "2025-07-12 16:35", "2025-07-12 16:36", "2025-07-12 16:37", "2025-07-12 16:38", "2025-07-12 16:39", "2025-07-12 16:40", "2025-07-12 16:41", "2025-07-12 16:42", "2025-07-12 16:43", "2025-07-12 16:44", "2025-07-12 16:45", "2025-07-12 16:46", "2025-07-12 16:47", "2025-07-12 16:48", "2025-07-12 16:49", "2025-07-12 16:50", "2025-07-12 16:51", "2025-07-12 16:52", "2025-07-12 16:53", "2025-07-12 16:54", "2025-07-12 16:55", "2025-07-12 16:56", "2025-07-12 16:57", "2025-07-12 16:58", "2025-07-12 16:59", "2025-07-12 17:00", "2025-07-12 17:01", "2025-07-12 17:02", "2025-07-12 17:03", "2025-07-12 17:04", "2025-07-12 17:05", "2025-07-12 17:06", "2025-07-12 17:07", "2025-07-12 17:08", "2025-07-12 17:09", "2025-07-12 17:10", "2025-07-12 17:11", "2025-07-12 17:12", "2025-07-12 17:13", "2025-07-12 17:14", "2025-07-12 17:15", "2025-07-12 17:16", "2025-07-12 17:17", "2025-07-12 17:18", "2025-07-12 17:19", "2025-07-12 17:20", "2025-07-12 17:21", "2025-07-12 17:22", "2025-07-12 17:23", "2025-07-12 17:24", "2025-07-12 17:25", "2025-07-12 17:26", "2025-07-12 17:27", "2025-07-12 17:28", "2025-07-12 17:29", "2025-07-12 17:30", "2025-07-12 17:31", "2025-07-12 17:32", "2025-07-12 17:33", "2025-07-12 17:34", "2025-07-12 17:35", "2025-07-12 17:36", "2025-07-12 17:37", "2025-07-12 17:38", "2025-07-12 17:39", "2025-07-12 17:40", "2025-07-12 17:41", "2025-07-12 17:42", "2025-07-12 17:43", "2025-07-12 17:44", "2025-07-12 17:45", "2025-07-12 17:46", "2025-07-12 17:47", "2025-07-12 17:48", "2025-07-12 17:49", "2025-07-12 17:50", "2025-07-12 17:51", "2025-07-12 17:52", "2025-07-12 17:53", "2025-07-12 17:54", "2025-07-12 17:55", "2025-07-12 17:56", "2025-07-12 17:57", "2025-07-12 17:58", "2025-07-12 17:59", "2025-07-12 18:00", "2025-07-12 18:01", "2025-07-12 18:02", "2025-07-12 18:03", "2025-07-12 18:04", "2025-07-12 18:05", "2025-07-12 18:06", "2025-07-12 18:07", "2025-07-12 18:08", "2025-07-12 18:09", "2025-07-12 18:10", "2025-07-12 18:11", "2025-07-12 18:12", "2025-07-12 18:13", "2025-07-12 18:14", "2025-07-12 18:15", "2025-07-12 18:16", "2025-07-12 18:17", "2025-07-12 18:18", "2025-07-12 18:19", "2025-07-12 18:20", "2025-07-12 18:21", "2025-07-12 18:22", "2025-07-12 18:23", "2025-07-12 18:24", "2025-07-12 18:25", "2025-07-12 18:26", "2025-07-12 18:27", "2025-07-12 18:28", "2025-07-12 18:29", "2025-07-12 18:30", "2025-07-12 18:31", "2025-07-12 18:32", "2025-07-12 18:33", "2025-07-12 18:34", "2025-07-12 18:35", "2025-07-12 18:36", "2025-07-12 18:37", "2025-07-12 18:38", "2025-07-12 18:39", "2025-07-12 18:40", "2025-07-12 18:41", "2025-07-12 18:42", "2025-07-12 18:43", "2025-07-12 18:44", "2025-07-12 18:45", "2025-07-12 18:46", "2025-07-12 18:47", "2025-07-12 18:48", "2025-07-12 18:49", "2025-07-12 18:50", "2025-07-12 18:51", "2025-07-12 18:52", "2025-07-12 18:53", "2025-07-12 18:54", "2025-07-12 18:55", "2025-07-12 18:56", "2025-07-12 18:57", "2025-07-12 18:58", "2025-07-12 18:59", "2025-07-12 19:00", "2025-07-12 19:01", "2025-07-12 19:02", "2025-07-12 19:03", "2025-07-12 19:04", "2025-07-12 19:05", "2025-07-12 19:06", "2025-07-12 19:07", "2025-07-12 19:08", "2025-07-12 19:09", "2025-07-12 19:10", "2025-07-12 19:11", "2025-07-12 19:12", "2025-07-12 19:13", "2025-07-12 19:14", "2025-07-12 19:15", "2025-07-12 19:16", "2025-07-12 19:17", "2025-07-12 19:18", "2025-07-12 19:19", "2025-07-12 19:20", "2025-07-12 19:21", "2025-07-12 19:22", "2025-07-12 19:23", "2025-07-12 19:24", "2025-07-12 19:25", "2025-07-12 19:26", "2025-07-12 19:27", "2025-07-12 19:28", "2025-07-12 19:29", "2025-07-12 19:30", "2025-07-12 19:31", "2025-07-12 19:32", "2025-07-12 19:33", "2025-07-12 19:34", "2025-07-12 19:35", "2025-07-12 19:36", "2025-07-12 19:37", "2025-07-12 19:38", "2025-07-12 19:39", "2025-07-12 19:40", "2025-07-12 19:41", "2025-07-12 19:42", "2025-07-12 19:43", "2025-07-12 19:44", "2025-07-12 19:45", "2025-07-12 19:46", "2025-07-12 19:47", "2025-07-12 19:48", "2025-07-12 19:49", "2025-07-12 19:50", "2025-07-12 19:51", "2025-07-12 19:52", "2025-07-12 19:53", "2025-07-12 19:54", "2025-07-12 19:55", "2025-07-12 19:56", "2025-07-12 19:57", "2025-07-12 19:58", "2025-07-12 19:59", "2025-07-12 20:00", "2025-07-12 20:01", "2025-07-12 20:02", "2025-07-12 20:03", "2025-07-12 20:04", "2025-07-12 20:05", "2025-07-12 20:06", "2025-07-12 20:07", "2025-07-12 20:08", "2025-07-12 20:09", "2025-07-12 20:10", "2025-07-12 20:11", "2025-07-12 20:12", "2025-07-12 20:13", "2025-07-12 20:14", "2025-07-12 20:15", "2025-07-12 20:16", "2025-07-12 20:17", "2025-07-12 20:18", "2025-07-12 20:19", "2025-07-12 20:20", "2025-07-12 20:21", "2025-07-12 20:22", "2025-07-12 20:23", "2025-07-12 20:24", "2025-07-12 20:25", "2025-07-12 20:26", "2025-07-12 20:27", "2025-07-12 20:28", "2025-07-12 20:29", "2025-07-12 20:30", "2025-07-12 20:31", "2025-07-12 20:32", "2025-07-12 20:33", "2025-07-12 20:34", "2025-07-12 20:35", "2025-07-12 20:36", "2025-07-12 20:37", "2025-07-12 20:38", "2025-07-12 20:39", "2025-07-12 20:40", "2025-07-12 20:41", "2025-07-12 20:42", "2025-07-12 20:43", "2025-07-12 20:44", "2025-07-12 20:45", "2025-07-12 20:46", "2025-07-12 20:47", "2025-07-12 20:48", "2025-07-12 20:49", "2025-07-12 20:50", "2025-07-12 20:51", "2025-07-12 20:52", "2025-07-12 20:53", "2025-07-12 20:54", "2025-07-12 20:55", "2025-07-12 20:56", "2025-07-12 20:57", "2025-07-12 20:58", "2025-07-12 20:59", "2025-07-12 21:00", "2025-07-12 21:01", "2025-07-12 21:02", "2025-07-12 21:03", "2025-07-12 21:04", "2025-07-12 21:05", "2025-07-12 21:06", "2025-07-12 21:07", "2025-07-12 21:08", "2025-07-12 21:09", "2025-07-12 21:10", "2025-07-12 21:11", "2025-07-12 21:12", "2025-07-12 21:13", "2025-07-12 21:14", "2025-07-12 21:15", "2025-07-12 21:16", "2025-07-12 21:17", "2025-07-12 21:18", "2025-07-12 21:19", "2025-07-12 21:20", "2025-07-12 21:21", "2025-07-12 21:22", "2025-07-12 21:23", "2025-07-12 21:24", "2025-07-12 21:25", "2025-07-12 21:26", "2025-07-12 21:27", "2025-07-12 21:28", "2025-07-12 21:29", "2025-07-12 21:30", "2025-07-12 21:31", "2025-07-12 21:32", "2025-07-12 21:33", "2025-07-12 21:34", "2025-07-12 21:35", "2025-07-12 21:36", "2025-07-12 21:37", "2025-07-12 21:38", "2025-07-12 21:39", "2025-07-12 21:40", "2025-07-12 21:41", "2025-07-12 21:42", "2025-07-12 21:43", "2025-07-12 21:44", "2025-07-12 21:45", "2025-07-12 21:46", "2025-07-12 21:47", "2025-07-12 21:48", "2025-07-12 21:49", "2025-07-12 21:50", "2025-07-12 21:51", "2025-07-12 21:52", "2025-07-12 21:53", "2025-07-12 21:54", "2025-07-12 21:55", "2025-07-12 21:56", "2025-07-12 21:57", "2025-07-12 21:58", "2025-07-12 21:59", "2025-07-12 22:00", "2025-07-12 22:01", "2025-07-12 22:02", "2025-07-12 22:03", "2025-07-12 22:04", "2025-07-12 22:05", "2025-07-12 22:06", "2025-07-12 22:07", "2025-07-12 22:08", "2025-07-12 22:09", "2025-07-12 22:10", "2025-07-12 22:11", "2025-07-12 22:12", "2025-07-12 22:13", "2025-07-12 22:14", "2025-07-12 22:15", "2025-07-12 22:16", "2025-07-12 22:17", "2025-07-12 22:18", "2025-07-12 22:19", "2025-07-12 22:20", "2025-07-12 22:21", "2025-07-12 22:22", "2025-07-12 22:23", "2025-07-12 22:24", "2025-07-12 22:25", "2025-07-12 22:26", "2025-07-12 22:27", "2025-07-12 22:28", "2025-07-12 22:29", "2025-07-12 22:30", "2025-07-12 22:31", "2025-07-12 22:32", "2025-07-12 22:33", "2025-07-12 22:34", "2025-07-12 22:35", "2025-07-12 22:36", "2025-07-12 22:37", "2025-07-12 22:38", "2025-07-12 22:39", "2025-07-12 22:40", "2025-07-12 22:41", "2025-07-12 22:42", "2025-07-12 22:43", "2025-07-12 22:44", "2025-07-12 22:45", "2025-07-12 22:46", "2025-07-12 22:47", "2025-07-12 22:48", "2025-07-12 22:49", "2025-07-12 22:50", "2025-07-12 22:51", "2025-07-12 22:52", "2025-07-12 22:53", "2025-07-12 22:54", "2025-07-12 22:55", "2025-07-12 22:56", "2025-07-12 22:57", "2025-07-12 22:58", "2025-07-12 22:59", "2025-07-12 23:00", "2025-07-12 23:01", "2025-07-12 23:02", "2025-07-12 23:03", "2025-07-12 23:04", "2025-07-12 23:05", "2025-07-12 23:06", "2025-07-12 23:07", "2025-07-12 23:08", "2025-07-12 23:09", "2025-07-12 23:10", "2025-07-12 23:11", "2025-07-12 23:12", "2025-07-12 23:13", "2025-07-12 23:14", "2025-07-12 23:15", "2025-07-12 23:16", "2025-07-12 23:17", "2025-07-12 23:18", "2025-07-12 23:19", "2025-07-12 23:20", "2025-07-12 23:21", "2025-07-12 23:22", "2025-07-12 23:23", "2025-07-12 23:24", "2025-07-12 23:25", "2025-07-12 23:26", "2025-07-12 23:27", "2025-07-12 23:28", "2025-07-12 23:29", "2025-07-12 23:30", "2025-07-12 23:31", "2025-07-12 23:32", "2025-07-12 23:33", "2025-07-12 23:34", "2025-07-12 23:35", "2025-07-12 23:36", "2025-07-12 23:37", "2025-07-12 23:38", "2025-07-12 23:39", "2025-07-12 23:40", "2025-07-12 23:41", "2025-07-12 23:42", "2025-07-12 23:43", "2025-07-12 23:44", "2025-07-12 23:45", "2025-07-12 23:46", "2025-07-12 23:47", "2025-07-12 23:48", "2025-07-12 23:49", "2025-07-12 23:50", "2025-07-12 23:51", "2025-07-12 23:52", "2025-07-12 23:53", "2025-07-12 23:54", "2025-07-12 23:55", "2025-07-12 23:56", "2025-07-12 23:57", "2025-07-12 23:58", "2025-07-12 23:59", "2025-07-13 00:00", "2025-07-13 00:01", "2025-07-13 00:02", "2025-07-13 00:03", "2025-07-13 00:04", "2025-07-13 00:05", "2025-07-13 00:06", "2025-07-13 00:07", "2025-07-13 00:08", "2025-07-13 00:09", "2025-07-13 00:10", "2025-07-13 00:11", "2025-07-13 00:12", "2025-07-13 00:13", "2025-07-13 00:14", "2025-07-13 00:15", "2025-07-13 00:16", "2025-07-13 00:17", "2025-07-13 00:18", "2025-07-13 00:19", "2025-07-13 00:20", "2025-07-13 00:21", "2025-07-13 00:22", "2025-07-13 00:23", "2025-07-13 00:24", "2025-07-13 00:25", "2025-07-13 00:26", "2025-07-13 00:27", "2025-07-13 00:28", "2025-07-13 00:29", "2025-07-13 00:30", "2025-07-13 00:31", "2025-07-13 00:32", "2025-07-13 00:33", "2025-07-13 00:34", "2025-07-13 00:35", "2025-07-13 00:36", "2025-07-13 00:37", "2025-07-13 00:38", "2025-07-13 00:39", "2025-07-13 00:40", "2025-07-13 00:41", "2025-07-13 00:42", "2025-07-13 00:43", "2025-07-13 00:44", "2025-07-13 00:45", "2025-07-13 00:46", "2025-07-13 00:47", "2025-07-13 00:48", "2025-07-13 00:49", "2025-07-13 00:50", "2025-07-13 00:51", "2025-07-13 00:52", "2025-07-13 00:53", "2025-07-13 00:54", "2025-07-13 00:55", "2025-07-13 00:56", "2025-07-13 00:57", "2025-07-13 00:58", "2025-07-13 00:59", "2025-07-13 01:00", "2025-07-13 01:01", "2025-07-13 01:02", "2025-07-13 01:03", "2025-07-13 01:04", "2025-07-13 01:05", "2025-07-13 01:06", "2025-07-13 01:07", "2025-07-13 01:08", "2025-07-13 01:09", "2025-07-13 01:10", "2025-07-13 01:11", "2025-07-13 01:12", "2025-07-13 01:13", "2025-07-13 01:14", "2025-07-13 01:15", "2025-07-13 01:16", "2025-07-13 01:17", "2025-07-13 01:18", "2025-07-13 01:19", "2025-07-13 01:20", "2025-07-13 01:21", "2025-07-13 01:22", "2025-07-13 01:23", "2025-07-13 01:24", "2025-07-13 01:25", "2025-07-13 01:26", "2025-07-13 01:27", "2025-07-13 01:28", "2025-07-13 01:29", "2025-07-13 01:30", "2025-07-13 01:31", "2025-07-13 01:32", "2025-07-13 01:33", "2025-07-13 01:34", "2025-07-13 01:35", "2025-07-13 01:36", "2025-07-13 01:37", "2025-07-13 01:38", "2025-07-13 01:39", "2025-07-13 01:40", "2025-07-13 01:41", "2025-07-13 01:42", "2025-07-13 01:43", "2025-07-13 01:44", "2025-07-13 01:45", "2025-07-13 01:46", "2025-07-13 01:47", "2025-07-13 01:48", "2025-07-13 01:49", "2025-07-13 01:50", "2025-07-13 01:51", "2025-07-13 01:52", "2025-07-13 01:53", "2025-07-13 01:54", "2025-07-13 01:55", "2025-07-13 01:56", "2025-07-13 01:57", "2025-07-13 01:58", "2025-07-13 01:59", "2025-07-13 02:00", "2025-07-13 02:01", "2025-07-13 02:02", "2025-07-13 02:03", "2025-07-13 02:04", "2025-07-13 02:05", "2025-07-13 02:06", "2025-07-13 02:07", "2025-07-13 02:08", "2025-07-13 02:09", "2025-07-13 02:10", "2025-07-13 02:11", "2025-07-13 02:12", "2025-07-13 02:13", "2025-07-13 02:14", "2025-07-13 02:15", "2025-07-13 02:16", "2025-07-13 02:17", "2025-07-13 02:18", "2025-07-13 02:19", "2025-07-13 02:20", "2025-07-13 02:21", "2025-07-13 02:22", "2025-07-13 02:23", "2025-07-13 02:24", "2025-07-13 02:25", "2025-07-13 02:26", "2025-07-13 02:27", "2025-07-13 02:28", "2025-07-13 02:29", "2025-07-13 02:30", "2025-07-13 02:31", "2025-07-13 02:32", "2025-07-13 02:33", "2025-07-13 02:34", "2025-07-13 02:35", "2025-07-13 02:36", "2025-07-13 02:37", "2025-07-13 02:38", "2025-07-13 02:39", "2025-07-13 02:40", "2025-07-13 02:41", "2025-07-13 02:42", "2025-07-13 02:43", "2025-07-13 02:44", "2025-07-13 02:45", "2025-07-13 02:46", "2025-07-13 02:47", "2025-07-13 02:48", "2025-07-13 02:49", "2025-07-13 02:50", "2025-07-13 02:51", "2025-07-13 02:52", "2025-07-13 02:53", "2025-07-13 02:54", "2025-07-13 02:55", "2025-07-13 02:56", "2025-07-13 02:57", "2025-07-13 02:58", "2025-07-13 02:59", "2025-07-13 03:00", "2025-07-13 03:01", "2025-07-13 03:02", "2025-07-13 03:03", "2025-07-13 03:04", "2025-07-13 03:05", "2025-07-13 03:06", "2025-07-13 03:07", "2025-07-13 03:08", "2025-07-13 03:09", "2025-07-13 03:10", "2025-07-13 03:11", "2025-07-13 03:12", "2025-07-13 03:13", "2025-07-13 03:14", "2025-07-13 03:15", "2025-07-13 03:16", "2025-07-13 03:17", "2025-07-13 03:18", "2025-07-13 03:19", "2025-07-13 03:20", "2025-07-13 03:21", "2025-07-13 03:22", "2025-07-13 03:23", "2025-07-13 03:24", "2025-07-13 03:25", "2025-07-13 03:26", "2025-07-13 03:27", "2025-07-13 03:28", "2025-07-13 03:29", "2025-07-13 03:30", "2025-07-13 03:31", "2025-07-13 03:32", "2025-07-13 03:33", "2025-07-13 03:34", "2025-07-13 03:35", "2025-07-13 03:36", "2025-07-13 03:37", "2025-07-13 03:38", "2025-07-13 03:39", "2025-07-13 03:40", "2025-07-13 03:41", "2025-07-13 03:42", "2025-07-13 03:43", "2025-07-13 03:44", "2025-07-13 03:45", "2025-07-13 03:46", "2025-07-13 03:47", "2025-07-13 03:48", "2025-07-13 03:49", "2025-07-13 03:50", "2025-07-13 03:51", "2025-07-13 03:52", "2025-07-13 03:53", "2025-07-13 03:54", "2025-07-13 03:55", "2025-07-13 03:56", "2025-07-13 03:57", "2025-07-13 03:58", "2025-07-13 03:59", "2025-07-13 04:00", "2025-07-13 04:01", "2025-07-13 04:02", "2025-07-13 04:03", "2025-07-13 04:04", "2025-07-13 04:05", "2025-07-13 04:06", "2025-07-13 04:07", "2025-07-13 04:08", "2025-07-13 04:09", "2025-07-13 04:10", "2025-07-13 04:11", "2025-07-13 04:12", "2025-07-13 04:13", "2025-07-13 04:14", "2025-07-13 04:15", "2025-07-13 04:16", "2025-07-13 04:17", "2025-07-13 04:18", "2025-07-13 04:19", "2025-07-13 04:20", "2025-07-13 04:21", "2025-07-13 04:22", "2025-07-13 04:23", "2025-07-13 04:24", "2025-07-13 04:25", "2025-07-13 04:26", "2025-07-13 04:27", "2025-07-13 04:28", "2025-07-13 04:29", "2025-07-13 04:30", "2025-07-13 04:31", "2025-07-13 04:32", "2025-07-13 04:33", "2025-07-13 04:34", "2025-07-13 04:35", "2025-07-13 04:36", "2025-07-13 04:37", "2025-07-13 04:38", "2025-07-13 04:39", "2025-07-13 04:40", "2025-07-13 04:41", "2025-07-13 04:42", "2025-07-13 04:43", "2025-07-13 04:44", "2025-07-13 04:45", "2025-07-13 04:46", "2025-07-13 04:47", "2025-07-13 04:48", "2025-07-13 04:49", "2025-07-13 04:50", "2025-07-13 04:51", "2025-07-13 04:52", "2025-07-13 04:53", "2025-07-13 04:54", "2025-07-13 04:55", "2025-07-13 04:56", "2025-07-13 04:57", "2025-07-13 04:58", "2025-07-13 04:59", "2025-07-13 05:00", "2025-07-13 05:01", "2025-07-13 05:02", "2025-07-13 05:03", "2025-07-13 05:04", "2025-07-13 05:05", "2025-07-13 05:06", "2025-07-13 05:07", "2025-07-13 05:08", "2025-07-13 05:09", "2025-07-13 05:10", "2025-07-13 05:11", "2025-07-13 05:12", "2025-07-13 05:13", "2025-07-13 05:14", "2025-07-13 05:15", "2025-07-13 05:16", "2025-07-13 05:17", "2025-07-13 05:18", "2025-07-13 05:19", "2025-07-13 05:20", "2025-07-13 05:21", "2025-07-13 05:22", "2025-07-13 05:23", "2025-07-13 05:24", "2025-07-13 05:25", "2025-07-13 05:26", "2025-07-13 05:27", "2025-07-13 05:28", "2025-07-13 05:29", "2025-07-13 05:30", "2025-07-13 05:31", "2025-07-13 05:32", "2025-07-13 05:33", "2025-07-13 05:34", "2025-07-13 05:35", "2025-07-13 05:36", "2025-07-13 05:37", "2025-07-13 05:38", "2025-07-13 05:39", "2025-07-13 05:40", "2025-07-13 05:41", "2025-07-13 05:42", "2025-07-13 05:43", "2025-07-13 05:44", "2025-07-13 05:45", "2025-07-13 05:46", "2025-07-13 05:47", "2025-07-13 05:48", "2025-07-13 05:49", "2025-07-13 05:50", "2025-07-13 05:51", "2025-07-13 05:52", "2025-07-13 05:53", "2025-07-13 05:54", "2025-07-13 05:55", "2025-07-13 05:56", "2025-07-13 05:57", "2025-07-13 05:58", "2025-07-13 05:59", "2025-07-13 06:00", "2025-07-13 06:01", "2025-07-13 06:02", "2025-07-13 06:03", "2025-07-13 06:04", "2025-07-13 06:05", "2025-07-13 06:06", "2025-07-13 06:07", "2025-07-13 06:08", "2025-07-13 06:09", "2025-07-13 06:10", "2025-07-13 06:11", "2025-07-13 06:12", "2025-07-13 06:13", "2025-07-13 06:14", "2025-07-13 06:15", "2025-07-13 06:16", "2025-07-13 06:17", "2025-07-13 06:18", "2025-07-13 06:19", "2025-07-13 06:20", "2025-07-13 06:21", "2025-07-13 06:22", "2025-07-13 06:23", "2025-07-13 06:24", "2025-07-13 06:25", "2025-07-13 06:26", "2025-07-13 06:27", "2025-07-13 06:28", "2025-07-13 06:29", "2025-07-13 06:30", "2025-07-13 06:31", "2025-07-13 06:32", "2025-07-13 06:33", "2025-07-13 06:34", "2025-07-13 06:35", "2025-07-13 06:36", "2025-07-13 06:37", "2025-07-13 06:38", "2025-07-13 06:39", "2025-07-13 06:40", "2025-07-13 06:41", "2025-07-13 06:42", "2025-07-13 06:43", "2025-07-13 06:44", "2025-07-13 06:45", "2025-07-13 06:46", "2025-07-13 06:47", "2025-07-13 06:48", "2025-07-13 06:49", "2025-07-13 06:50", "2025-07-13 06:51", "2025-07-13 06:52", "2025-07-13 06:53", "2025-07-13 06:54", "2025-07-13 06:55", "2025-07-13 06:56", "2025-07-13 06:57", "2025-07-13 06:58", "2025-07-13 06:59", "2025-07-13 07:00", "2025-07-13 07:01", "2025-07-13 07:02", "2025-07-13 07:03", "2025-07-13 07:04", "2025-07-13 07:05", "2025-07-13 07:06", "2025-07-13 07:07", "2025-07-13 07:08", "2025-07-13 07:09", "2025-07-13 07:10", "2025-07-13 07:11", "2025-07-13 07:12", "2025-07-13 07:13", "2025-07-13 07:14", "2025-07-13 07:15", "2025-07-13 07:16", "2025-07-13 07:17", "2025-07-13 07:18", "2025-07-13 07:19", "2025-07-13 07:20", "2025-07-13 07:21", "2025-07-13 07:22", "2025-07-13 07:23", "2025-07-13 07:24", "2025-07-13 07:25", "2025-07-13 07:26", "2025-07-13 07:27", "2025-07-13 07:28", "2025-07-13 07:29", "2025-07-13 07:30", "2025-07-13 07:31", "2025-07-13 07:32", "2025-07-13 07:33", "2025-07-13 07:34", "2025-07-13 07:35", "2025-07-13 07:36", "2025-07-13 07:37", "2025-07-13 07:38", "2025-07-13 07:39", "2025-07-13 07:40", "2025-07-13 07:41", "2025-07-13 07:42", "2025-07-13 07:43", "2025-07-13 07:44", "2025-07-13 07:45", "2025-07-13 07:46", "2025-07-13 07:47", "2025-07-13 07:48", "2025-07-13 07:49", "2025-07-13 07:50", "2025-07-13 07:51", "2025-07-13 07:52", "2025-07-13 07:53", "2025-07-13 07:54", "2025-07-13 07:55", "2025-07-13 07:56", "2025-07-13 07:57", "2025-07-13 07:58", "2025-07-13 07:59", "2025-07-13 08:00", "2025-07-13 08:01", "2025-07-13 08:02", "2025-07-13 08:03", "2025-07-13 08:04", "2025-07-13 08:05", "2025-07-13 08:06", "2025-07-13 08:07", "2025-07-13 08:08", "2025-07-13 08:09", "2025-07-13 08:10", "2025-07-13 08:11", "2025-07-13 08:12", "2025-07-13 08:13", "2025-07-13 08:14", "2025-07-13 08:15", "2025-07-13 08:16", "2025-07-13 08:17", "2025-07-13 08:18", "2025-07-13 08:19", "2025-07-13 08:20", "2025-07-13 08:21", "2025-07-13 08:22", "2025-07-13 08:23", "2025-07-13 08:24", "2025-07-13 08:25", "2025-07-13 08:26", "2025-07-13 08:27", "2025-07-13 08:28", "2025-07-13 08:29", "2025-07-13 08:30", "2025-07-13 08:31", "2025-07-13 08:32", "2025-07-13 08:33", "2025-07-13 08:34", "2025-07-13 08:35", "2025-07-13 08:36", "2025-07-13 08:37", "2025-07-13 08:38", "2025-07-13 08:39", "2025-07-13 08:40", "2025-07-13 08:41", "2025-07-13 08:42", "2025-07-13 08:43", "2025-07-13 08:44", "2025-07-13 08:45", "2025-07-13 08:46", "2025-07-13 08:47", "2025-07-13 08:48", "2025-07-13 08:49", "2025-07-13 08:50", "2025-07-13 08:51", "2025-07-13 08:52", "2025-07-13 08:53", "2025-07-13 08:54", "2025-07-13 08:55", "2025-07-13 08:56", "2025-07-13 08:57", "2025-07-13 08:58", "2025-07-13 08:59", "2025-07-13 09:00", "2025-07-13 09:01", "2025-07-13 09:02", "2025-07-13 09:03", "2025-07-13 09:04", "2025-07-13 09:05", "2025-07-13 09:06", "2025-07-13 09:07", "2025-07-13 09:08", "2025-07-13 09:09", "2025-07-13 09:10", "2025-07-13 09:11", "2025-07-13 09:12", "2025-07-13 09:13", "2025-07-13 09:14", "2025-07-13 09:15", "2025-07-13 09:16", "2025-07-13 09:17", "2025-07-13 09:18", "2025-07-13 09:19", "2025-07-13 09:20", "2025-07-13 09:21", "2025-07-13 09:22", "2025-07-13 09:23", "2025-07-13 09:24", "2025-07-13 09:25", "2025-07-13 09:26", "2025-07-13 09:27", "2025-07-13 09:28", "2025-07-13 09:29", "2025-07-13 09:30", "2025-07-13 09:31", "2025-07-13 09:32", "2025-07-13 09:33", "2025-07-13 09:34", "2025-07-13 09:35", "2025-07-13 09:36", "2025-07-13 09:37", "2025-07-13 09:38", "2025-07-13 09:39", "2025-07-13 09:40", "2025-07-13 09:41", "2025-07-13 09:42", "2025-07-13 09:43", "2025-07-13 09:44", "2025-07-13 09:45", "2025-07-13 09:46", "2025-07-13 09:47", "2025-07-13 09:48", "2025-07-13 09:49", "2025-07-13 09:50", "2025-07-13 09:51", "2025-07-13 09:52", "2025-07-13 09:53", "2025-07-13 09:54", "2025-07-13 09:55", "2025-07-13 09:56", "2025-07-13 09:57", "2025-07-13 09:58", "2025-07-13 09:59", "2025-07-13 10:00", "2025-07-13 10:01", "2025-07-13 10:02", "2025-07-13 10:03", "2025-07-13 10:04", "2025-07-13 10:05", "2025-07-13 10:06", "2025-07-13 10:07", "2025-07-13 10:08", "2025-07-13 10:09", "2025-07-13 10:10", "2025-07-13 10:11", "2025-07-13 10:12", "2025-07-13 10:13", "2025-07-13 10:14", "2025-07-13 10:15", "2025-07-13 10:16", "2025-07-13 10:17", "2025-07-13 10:18", "2025-07-13 10:19", "2025-07-13 10:20", "2025-07-13 10:21", "2025-07-13 10:22", "2025-07-13 10:23", "2025-07-13 10:24", "2025-07-13 10:25", "2025-07-13 10:26", "2025-07-13 10:27", "2025-07-13 10:28", "2025-07-13 10:29", "2025-07-13 10:30", "2025-07-13 10:31", "2025-07-13 10:32", "2025-07-13 10:33", "2025-07-13 10:34", "2025-07-13 10:35", "2025-07-13 10:36", "2025-07-13 10:37", "2025-07-13 10:38", "2025-07-13 10:39", "2025-07-13 10:40", "2025-07-13 10:41", "2025-07-13 10:42", "2025-07-13 10:43", "2025-07-13 10:44", "2025-07-13 10:45", "2025-07-13 10:46", "2025-07-13 10:47", "2025-07-13 10:48", "2025-07-13 10:49", "2025-07-13 10:50", "2025-07-13 10:51", "2025-07-13 10:52", "2025-07-13 10:53", "2025-07-13 10:54", "2025-07-13 10:55", "2025-07-13 10:56", "2025-07-13 10:57", "2025-07-13 10:58", "2025-07-13 10:59", "2025-07-13 11:00", "2025-07-13 11:01", "2025-07-13 11:02", "2025-07-13 11:03", "2025-07-13 11:04", "2025-07-13 11:05", "2025-07-13 11:06", "2025-07-13 11:07", "2025-07-13 11:08", "2025-07-13 11:09", "2025-07-13 11:10", "2025-07-13 11:11", "2025-07-13 11:12", "2025-07-13 11:13", "2025-07-13 11:14", "2025-07-13 11:15", "2025-07-13 11:16", "2025-07-13 11:17", "2025-07-13 11:18", "2025-07-13 11:19", "2025-07-13 11:20", "2025-07-13 11:21", "2025-07-13 11:22", "2025-07-13 11:23", "2025-07-13 11:24", "2025-07-13 11:25", "2025-07-13 11:26", "2025-07-13 11:27", "2025-07-13 11:28", "2025-07-13 11:29", "2025-07-13 11:30", "2025-07-13 11:31", "2025-07-13 11:32", "2025-07-13 11:33", "2025-07-13 11:34", "2025-07-13 11:35", "2025-07-13 11:36", "2025-07-13 11:37", "2025-07-13 11:38", "2025-07-13 11:39", "2025-07-13 11:40", "2025-07-13 11:41", "2025-07-13 11:42", "2025-07-13 11:43", "2025-07-13 11:44", "2025-07-13 11:45", "2025-07-13 11:46", "2025-07-13 11:47", "2025-07-13 11:48", "2025-07-13 11:49", "2025-07-13 11:50", "2025-07-13 11:51", "2025-07-13 11:52", "2025-07-13 11:53", "2025-07-13 11:54", "2025-07-13 11:55", "2025-07-13 11:56", "2025-07-13 11:57", "2025-07-13 11:58", "2025-07-13 11:59", "2025-07-13 12:00", "2025-07-13 12:01", "2025-07-13 12:02", "2025-07-13 12:03", "2025-07-13 12:04", "2025-07-13 12:05", "2025-07-13 12:06", "2025-07-13 12:07", "2025-07-13 12:08", "2025-07-13 12:09", "2025-07-13 12:10", "2025-07-13 12:11", "2025-07-13 12:12", "2025-07-13 12:13", "2025-07-13 12:14", "2025-07-13 12:15", "2025-07-13 12:16", "2025-07-13 12:17", "2025-07-13 12:18", "2025-07-13 12:19", "2025-07-13 12:20", "2025-07-13 12:21", "2025-07-13 12:22", "2025-07-13 12:23", "2025-07-13 12:24", "2025-07-13 12:25", "2025-07-13 12:26", "2025-07-13 12:27", "2025-07-13 12:28", "2025-07-13 12:29", "2025-07-13 12:30", "2025-07-13 12:31", "2025-07-13 12:32", "2025-07-13 12:33", "2025-07-13 12:34", "2025-07-13 12:35", "2025-07-13 12:36", "2025-07-13 12:37", "2025-07-13 12:38", "2025-07-13 12:39", "2025-07-13 12:40", "2025-07-13 12:41", "2025-07-13 12:42", "2025-07-13 12:43", "2025-07-13 12:44", "2025-07-13 12:45", "2025-07-13 12:46", "2025-07-13 12:47", "2025-07-13 12:48", "2025-07-13 12:49", "2025-07-13 12:50", "2025-07-13 12:51", "2025-07-13 12:52", "2025-07-13 12:53", "2025-07-13 12:54", "2025-07-13 12:55", "2025-07-13 12:56", "2025-07-13 12:57", "2025-07-13 12:58", "2025-07-13 12:59", "2025-07-13 13:00", "2025-07-13 13:01", "2025-07-13 13:02", "2025-07-13 13:03", "2025-07-13 13:04", "2025-07-13 13:05", "2025-07-13 13:06", "2025-07-13 13:07", "2025-07-13 13:08", "2025-07-13 13:09", "2025-07-13 13:10", "2025-07-13 13:11", "2025-07-13 13:12", "2025-07-13 13:13", "2025-07-13 13:14", "2025-07-13 13:15", "2025-07-13 13:16", "2025-07-13 13:17", "2025-07-13 13:18", "2025-07-13 13:19", "2025-07-13 13:20", "2025-07-13 13:21", "2025-07-13 13:22", "2025-07-13 13:23", "2025-07-13 13:24", "2025-07-13 13:25", "2025-07-13 13:26", "2025-07-13 13:27", "2025-07-13 13:28", "2025-07-13 13:29", "2025-07-13 13:30", "2025-07-13 13:31", "2025-07-13 13:32", "2025-07-13 13:33", "2025-07-13 13:34", "2025-07-13 13:35", "2025-07-13 13:36", "2025-07-13 13:37", "2025-07-13 13:38", "2025-07-13 13:39", "2025-07-13 13:40", "2025-07-13 13:41", "2025-07-13 13:42", "2025-07-13 13:43", "2025-07-13 13:44", "2025-07-13 13:45", "2025-07-13 13:46", "2025-07-13 13:47", "2025-07-13 13:48", "2025-07-13 13:49", "2025-07-13 13:50", "2025-07-13 13:51", "2025-07-13 13:52", "2025-07-13 13:53", "2025-07-13 13:54", "2025-07-13 13:55", "2025-07-13 13:56", "2025-07-13 13:57", "2025-07-13 13:58", "2025-07-13 13:59", "2025-07-13 14:00", "2025-07-13 14:01", "2025-07-13 14:02", "2025-07-13 14:03", "2025-07-13 14:04", "2025-07-13 14:05", "2025-07-13 14:06", "2025-07-13 14:07", "2025-07-13 14:08", "2025-07-13 14:09", "2025-07-13 14:10", "2025-07-13 14:11", "2025-07-13 14:12", "2025-07-13 14:13", "2025-07-13 14:14", "2025-07-13 14:15", "2025-07-13 14:16", "2025-07-13 14:17", "2025-07-13 14:18", "2025-07-13 14:19", "2025-07-13 14:20", "2025-07-13 14:21", "2025-07-13 14:22", "2025-07-13 14:23", "2025-07-13 14:24", "2025-07-13 14:25", "2025-07-13 14:26", "2025-07-13 14:27", "2025-07-13 14:28", "2025-07-13 14:29", "2025-07-13 14:30", "2025-07-13 14:31", "2025-07-13 14:32", "2025-07-13 14:33", "2025-07-13 14:34", "2025-07-13 14:35", "2025-07-13 14:36", "2025-07-13 14:37", "2025-07-13 14:38", "2025-07-13 14:39", "2025-07-13 14:40", "2025-07-13 14:41", "2025-07-13 14:42", "2025-07-13 14:43", "2025-07-13 14:44", "2025-07-13 14:45", "2025-07-13 14:46", "2025-07-13 14:47", "2025-07-13 14:48", "2025-07-13 14:49", "2025-07-13 14:50", "2025-07-13 14:51", "2025-07-13 14:52", "2025-07-13 14:53", "2025-07-13 14:54", "2025-07-13 14:55", "2025-07-13 14:56", "2025-07-13 14:57", "2025-07-13 14:58", "2025-07-13 14:59", "2025-07-13 15:00", "2025-07-13 15:01", "2025-07-13 15:02", "2025-07-13 15:03", "2025-07-13 15:04", "2025-07-13 15:05", "2025-07-13 15:06", "2025-07-13 15:07", "2025-07-13 15:08", "2025-07-13 15:09", "2025-07-13 15:10", "2025-07-13 15:11", "2025-07-13 15:12", "2025-07-13 15:13", "2025-07-13 15:14", "2025-07-13 15:15", "2025-07-13 15:16", "2025-07-13 15:17", "2025-07-13 15:18", "2025-07-13 15:19", "2025-07-13 15:20", "2025-07-13 15:21", "2025-07-13 15:22", "2025-07-13 15:23", "2025-07-13 15:24", "2025-07-13 15:25", "2025-07-13 15:26", "2025-07-13 15:27", "2025-07-13 15:28", "2025-07-13 15:29", "2025-07-13 15:30", "2025-07-13 15:31", "2025-07-13 15:32", "2025-07-13 15:33", "2025-07-13 15:34", "2025-07-13 15:35", "2025-07-13 15:36", "2025-07-13 15:37", "2025-07-13 15:38", "2025-07-13 15:39", "2025-07-13 15:40", "2025-07-13 15:41", "2025-07-13 15:42", "2025-07-13 15:43", "2025-07-13 15:44", "2025-07-13 15:45", "2025-07-13 15:46", "2025-07-13 15:47", "2025-07-13 15:48", "2025-07-13 15:49", "2025-07-13 15:50", "2025-07-13 15:51", "2025-07-13 15:52", "2025-07-13 15:53", "2025-07-13 15:54", "2025-07-13 15:55", "2025-07-13 15:56", "2025-07-13 15:57", "2025-07-13 15:58", "2025-07-13 15:59", "2025-07-13 16:00", "2025-07-13 16:01", "2025-07-13 16:02", "2025-07-13 16:03", "2025-07-13 16:04", "2025-07-13 16:05", "2025-07-13 16:06", "2025-07-13 16:07", "2025-07-13 16:08", "2025-07-13 16:09", "2025-07-13 16:10", "2025-07-13 16:11", "2025-07-13 16:12", "2025-07-13 16:13", "2025-07-13 16:14", "2025-07-13 16:15", "2025-07-13 16:16", "2025-07-13 16:17", "2025-07-13 16:18", "2025-07-13 16:19", "2025-07-13 16:20", "2025-07-13 16:21", "2025-07-13 16:22", "2025-07-13 16:23", "2025-07-13 16:24", "2025-07-13 16:25", "2025-07-13 16:26", "2025-07-13 16:27", "2025-07-13 16:28", "2025-07-13 16:29", "2025-07-13 16:30", "2025-07-13 16:31", "2025-07-13 16:32", "2025-07-13 16:33", "2025-07-13 16:34", "2025-07-13 16:35", "2025-07-13 16:36", "2025-07-13 16:37", "2025-07-13 16:38", "2025-07-13 16:39", "2025-07-13 16:40", "2025-07-13 16:41", "2025-07-13 16:42", "2025-07-13 16:43", "2025-07-13 16:44", "2025-07-13 16:45", "2025-07-13 16:46", "2025-07-13 16:47", "2025-07-13 16:48", "2025-07-13 16:49", "2025-07-13 16:50", "2025-07-13 16:51", "2025-07-13 16:52", "2025-07-13 16:53", "2025-07-13 16:54", "2025-07-13 16:55", "2025-07-13 16:56", "2025-07-13 16:57", "2025-07-13 16:58", "2025-07-13 16:59", "2025-07-13 17:00", "2025-07-13 17:01", "2025-07-13 17:02", "2025-07-13 17:03", "2025-07-13 17:04", "2025-07-13 17:05", "2025-07-13 17:06", "2025-07-13 17:07", "2025-07-13 17:08", "2025-07-13 17:09", "2025-07-13 17:10", "2025-07-13 17:11", "2025-07-13 17:12", "2025-07-13 17:13", "2025-07-13 17:14", "2025-07-13 17:15", "2025-07-13 17:16", "2025-07-13 17:17", "2025-07-13 17:18", "2025-07-13 17:19", "2025-07-13 17:20", "2025-07-13 17:21", "2025-07-13 17:22", "2025-07-13 17:23", "2025-07-13 17:24", "2025-07-13 17:25", "2025-07-13 17:26", "2025-07-13 17:27", "2025-07-13 17:28", "2025-07-13 17:29", "2025-07-13 17:30", "2025-07-13 17:31", "2025-07-13 17:32", "2025-07-13 17:33", "2025-07-13 17:34", "2025-07-13 17:35", "2025-07-13 17:36", "2025-07-13 17:37", "2025-07-13 17:38", "2025-07-13 17:39", "2025-07-13 17:40", "2025-07-13 17:41", "2025-07-13 17:42", "2025-07-13 17:43", "2025-07-13 17:44", "2025-07-13 17:45", "2025-07-13 17:46", "2025-07-13 17:47", "2025-07-13 17:48", "2025-07-13 17:49", "2025-07-13 17:50", "2025-07-13 17:51", "2025-07-13 17:52", "2025-07-13 17:53", "2025-07-13 17:54", "2025-07-13 17:55", "2025-07-13 17:56", "2025-07-13 17:57", "2025-07-13 17:58", "2025-07-13 17:59", "2025-07-13 18:00", "2025-07-13 18:01", "2025-07-13 18:02", "2025-07-13 18:03", "2025-07-13 18:04", "2025-07-13 18:05", "2025-07-13 18:06", "2025-07-13 18:07", "2025-07-13 18:08", "2025-07-13 18:09", "2025-07-13 18:10", "2025-07-13 18:11", "2025-07-13 18:12", "2025-07-13 18:13", "2025-07-13 18:14", "2025-07-13 18:15", "2025-07-13 18:16", "2025-07-13 18:17", "2025-07-13 18:18", "2025-07-13 18:19", "2025-07-13 18:20", "2025-07-13 18:21", "2025-07-13 18:22", "2025-07-13 18:23", "2025-07-13 18:24", "2025-07-13 18:25", "2025-07-13 18:26", "2025-07-13 18:27", "2025-07-13 18:28", "2025-07-13 18:29", "2025-07-13 18:30", "2025-07-13 18:31", "2025-07-13 18:32", "2025-07-13 18:33", "2025-07-13 18:34", "2025-07-13 18:35", "2025-07-13 18:36", "2025-07-13 18:37", "2025-07-13 18:38", "2025-07-13 18:39", "2025-07-13 18:40", "2025-07-13 18:41", "2025-07-13 18:42", "2025-07-13 18:43", "2025-07-13 18:44", "2025-07-13 18:45", "2025-07-13 18:46", "2025-07-13 18:47", "2025-07-13 18:48", "2025-07-13 18:49", "2025-07-13 18:50", "2025-07-13 18:51", "2025-07-13 18:52", "2025-07-13 18:53", "2025-07-13 18:54", "2025-07-13 18:55", "2025-07-13 18:56", "2025-07-13 18:57", "2025-07-13 18:58", "2025-07-13 18:59", "2025-07-13 19:00", "2025-07-13 19:01", "2025-07-13 19:02", "2025-07-13 19:03", "2025-07-13 19:04", "2025-07-13 19:05", "2025-07-13 19:06", "2025-07-13 19:07", "2025-07-13 19:08", "2025-07-13 19:09", "2025-07-13 19:10", "2025-07-13 19:11", "2025-07-13 19:12", "2025-07-13 19:13", "2025-07-13 19:14", "2025-07-13 19:15", "2025-07-13 19:16", "2025-07-13 19:17", "2025-07-13 19:18", "2025-07-13 19:19", "2025-07-13 19:20", "2025-07-13 19:21", "2025-07-13 19:22", "2025-07-13 19:23", "2025-07-13 19:24", "2025-07-13 19:25", "2025-07-13 19:26", "2025-07-13 19:27", "2025-07-13 19:28", "2025-07-13 19:29", "2025-07-13 19:30", "2025-07-13 19:31", "2025-07-13 19:32", "2025-07-13 19:33", "2025-07-13 19:34", "2025-07-13 19:35", "2025-07-13 19:36", "2025-07-13 19:37", "2025-07-13 19:38", "2025-07-13 19:39", "2025-07-13 19:40", "2025-07-13 19:41", "2025-07-13 19:42", "2025-07-13 19:43", "2025-07-13 19:44", "2025-07-13 19:45", "2025-07-13 19:46", "2025-07-13 19:47", "2025-07-13 19:48", "2025-07-13 19:49", "2025-07-13 19:50", "2025-07-13 19:51", "2025-07-13 19:52", "2025-07-13 19:53", "2025-07-13 19:54", "2025-07-13 19:55", "2025-07-13 19:56", "2025-07-13 19:57", "2025-07-13 19:58", "2025-07-13 19:59", "2025-07-13 20:00", "2025-07-13 20:01", "2025-07-13 20:02", "2025-07-13 20:03", "2025-07-13 20:04", "2025-07-13 20:05", "2025-07-13 20:06", "2025-07-13 20:07", "2025-07-13 20:08", "2025-07-13 20:09", "2025-07-13 20:10", "2025-07-13 20:11", "2025-07-13 20:12", "2025-07-13 20:13", "2025-07-13 20:14", "2025-07-13 20:15", "2025-07-13 20:16", "2025-07-13 20:17", "2025-07-13 20:18", "2025-07-13 20:19", "2025-07-13 20:20", "2025-07-13 20:21", "2025-07-13 20:22", "2025-07-13 20:23", "2025-07-13 20:24", "2025-07-13 20:25", "2025-07-13 20:26", "2025-07-13 20:27", "2025-07-13 20:28", "2025-07-13 20:29", "2025-07-13 20:30", "2025-07-13 20:31", "2025-07-13 20:32", "2025-07-13 20:33", "2025-07-13 20:34", "2025-07-13 20:35", "2025-07-13 20:36", "2025-07-13 20:37", "2025-07-13 20:38", "2025-07-13 20:39", "2025-07-13 20:40", "2025-07-13 20:41", "2025-07-13 20:42", "2025-07-13 20:43", "2025-07-13 20:44", "2025-07-13 20:45", "2025-07-13 20:46", "2025-07-13 20:47", "2025-07-13 20:48", "2025-07-13 20:49", "2025-07-13 20:50", "2025-07-13 20:51", "2025-07-13 20:52", "2025-07-13 20:53", "2025-07-13 20:54", "2025-07-13 20:55", "2025-07-13 20:56", "2025-07-13 20:57", "2025-07-13 20:58", "2025-07-13 20:59", "2025-07-13 21:00", "2025-07-13 21:01", "2025-07-13 21:02", "2025-07-13 21:03", "2025-07-13 21:04", "2025-07-13 21:05", "2025-07-13 21:06", "2025-07-13 21:07", "2025-07-13 21:08", "2025-07-13 21:09", "2025-07-13 21:10", "2025-07-13 21:11", "2025-07-13 21:12", "2025-07-13 21:13", "2025-07-13 21:14", "2025-07-13 21:15", "2025-07-13 21:16", "2025-07-13 21:17", "2025-07-13 21:18", "2025-07-13 21:19", "2025-07-13 21:20", "2025-07-13 21:21", "2025-07-13 21:22", "2025-07-13 21:23", "2025-07-13 21:24", "2025-07-13 21:25", "2025-07-13 21:26", "2025-07-13 21:27", "2025-07-13 21:28", "2025-07-13 21:29", "2025-07-13 21:30", "2025-07-13 21:31", "2025-07-13 21:32", "2025-07-13 21:33", "2025-07-13 21:34", "2025-07-13 21:35", "2025-07-13 21:36", "2025-07-13 21:37", "2025-07-13 21:38", "2025-07-13 21:39", "2025-07-13 21:40", "2025-07-13 21:41", "2025-07-13 21:42", "2025-07-13 21:43", "2025-07-13 21:44", "2025-07-13 21:45", "2025-07-13 21:46", "2025-07-13 21:47", "2025-07-13 21:48", "2025-07-13 21:49", "2025-07-13 21:50", "2025-07-13 21:51", "2025-07-13 21:52", "2025-07-13 21:53", "2025-07-13 21:54", "2025-07-13 21:55", "2025-07-13 21:56", "2025-07-13 21:57", "2025-07-13 21:58", "2025-07-13 21:59", "2025-07-13 22:00", "2025-07-13 22:01", "2025-07-13 22:02", "2025-07-13 22:03", "2025-07-13 22:04", "2025-07-13 22:05", "2025-07-13 22:06", "2025-07-13 22:07", "2025-07-13 22:08", "2025-07-13 22:09", "2025-07-13 22:10", "2025-07-13 22:11", "2025-07-13 22:12", "2025-07-13 22:13", "2025-07-13 22:14", "2025-07-13 22:15", "2025-07-13 22:16", "2025-07-13 22:17", "2025-07-13 22:18", "2025-07-13 22:19", "2025-07-13 22:20", "2025-07-13 22:21", "2025-07-13 22:22", "2025-07-13 22:23", "2025-07-13 22:24", "2025-07-13 22:25", "2025-07-13 22:26", "2025-07-13 22:27", "2025-07-13 22:28", "2025-07-13 22:29", "2025-07-13 22:30", "2025-07-13 22:31", "2025-07-13 22:32", "2025-07-13 22:33", "2025-07-13 22:34", "2025-07-13 22:35", "2025-07-13 22:36", "2025-07-13 22:37", "2025-07-13 22:38", "2025-07-13 22:39", "2025-07-13 22:40", "2025-07-13 22:41", "2025-07-13 22:42", "2025-07-13 22:43", "2025-07-13 22:44", "2025-07-13 22:45", "2025-07-13 22:46", "2025-07-13 22:47", "2025-07-13 22:48", "2025-07-13 22:49", "2025-07-13 22:50", "2025-07-13 22:51", "2025-07-13 22:52", "2025-07-13 22:53", "2025-07-13 22:54", "2025-07-13 22:55", "2025-07-13 22:56", "2025-07-13 22:57", "2025-07-13 22:58", "2025-07-13 22:59", "2025-07-13 23:00", "2025-07-13 23:01", "2025-07-13 23:02", "2025-07-13 23:03", "2025-07-13 23:04", "2025-07-13 23:05", "2025-07-13 23:06", "2025-07-13 23:07", "2025-07-13 23:08", "2025-07-13 23:09", "2025-07-13 23:10", "2025-07-13 23:11", "2025-07-13 23:12", "2025-07-13 23:13", "2025-07-13 23:14", "2025-07-13 23:15", "2025-07-13 23:16", "2025-07-13 23:17", "2025-07-13 23:18", "2025-07-13 23:19", "2025-07-13 23:20", "2025-07-13 23:21", "2025-07-13 23:22", "2025-07-13 23:23", "2025-07-13 23:24", "2025-07-13 23:25", "2025-07-13 23:26", "2025-07-13 23:27", "2025-07-13 23:28", "2025-07-13 23:29", "2025-07-13 23:30", "2025-07-13 23:31", "2025-07-13 23:32", "2025-07-13 23:33", "2025-07-13 23:34", "2025-07-13 23:35", "2025-07-13 23:36", "2025-07-13 23:37", "2025-07-13 23:38", "2025-07-13 23:39", "2025-07-13 23:40", "2025-07-13 23:41", "2025-07-13 23:42", "2025-07-13 23:43", "2025-07-13 23:44", "2025-07-13 23:45", "2025-07-13 23:46", "2025-07-13 23:47", "2025-07-13 23:48", "2025-07-13 23:49", "2025-07-13 23:50", "2025-07-13 23:51", "2025-07-13 23:52", "2025-07-13 23:53", "2025-07-13 23:54", "2025-07-13 23:55", "2025-07-13 23:56", "2025-07-13 23:57", "2025-07-13 23:58", "2025-07-13 23:59", "2025-07-14 00:00", "2025-07-14 00:01", "2025-07-14 00:02", "2025-07-14 00:03", "2025-07-14 00:04", "2025-07-14 00:05", "2025-07-14 00:06", "2025-07-14 00:07", "2025-07-14 00:08", "2025-07-14 00:09", "2025-07-14 00:10", "2025-07-14 00:11", "2025-07-14 00:12", "2025-07-14 00:13", "2025-07-14 00:14", "2025-07-14 00:15", "2025-07-14 00:16", "2025-07-14 00:17", "2025-07-14 00:18", "2025-07-14 00:19", "2025-07-14 00:20", "2025-07-14 00:21", "2025-07-14 00:22", "2025-07-14 00:23", "2025-07-14 00:24", "2025-07-14 00:25", "2025-07-14 00:26", "2025-07-14 00:27", "2025-07-14 00:28", "2025-07-14 00:29", "2025-07-14 00:30", "2025-07-14 00:31", "2025-07-14 00:32", "2025-07-14 00:33", "2025-07-14 00:34", "2025-07-14 00:35", "2025-07-14 00:36", "2025-07-14 00:37", "2025-07-14 00:38", "2025-07-14 00:39", "2025-07-14 00:40", "2025-07-14 00:41", "2025-07-14 00:42", "2025-07-14 00:43", "2025-07-14 00:44", "2025-07-14 00:45", "2025-07-14 00:46", "2025-07-14 00:47", "2025-07-14 00:48", "2025-07-14 00:49", "2025-07-14 00:50", "2025-07-14 00:51", "2025-07-14 00:52", "2025-07-14 00:53", "2025-07-14 00:54", "2025-07-14 00:55", "2025-07-14 00:56", "2025-07-14 00:57", "2025-07-14 00:58", "2025-07-14 00:59", "2025-07-14 01:00", "2025-07-14 01:01", "2025-07-14 01:02", "2025-07-14 01:03", "2025-07-14 01:04", "2025-07-14 01:05", "2025-07-14 01:06", "2025-07-14 01:07", "2025-07-14 01:08", "2025-07-14 01:09", "2025-07-14 01:10", "2025-07-14 01:11", "2025-07-14 01:12", "2025-07-14 01:13", "2025-07-14 01:14", "2025-07-14 01:15", "2025-07-14 01:16", "2025-07-14 01:17", "2025-07-14 01:18", "2025-07-14 01:19", "2025-07-14 01:20", "2025-07-14 01:21", "2025-07-14 01:22", "2025-07-14 01:23", "2025-07-14 01:24", "2025-07-14 01:25", "2025-07-14 01:26", "2025-07-14 01:27", "2025-07-14 01:28", "2025-07-14 01:29", "2025-07-14 01:30", "2025-07-14 01:31", "2025-07-14 01:32", "2025-07-14 01:33", "2025-07-14 01:34", "2025-07-14 01:35", "2025-07-14 01:36", "2025-07-14 01:37", "2025-07-14 01:38", "2025-07-14 01:39", "2025-07-14 01:40", "2025-07-14 01:41", "2025-07-14 01:42", "2025-07-14 01:43", "2025-07-14 01:44", "2025-07-14 01:45", "2025-07-14 01:46", "2025-07-14 01:47", "2025-07-14 01:48", "2025-07-14 01:49", "2025-07-14 01:50", "2025-07-14 01:51", "2025-07-14 01:52", "2025-07-14 01:53", "2025-07-14 01:54", "2025-07-14 01:55", "2025-07-14 01:56", "2025-07-14 01:57", "2025-07-14 01:58", "2025-07-14 01:59", "2025-07-14 02:00", "2025-07-14 02:01", "2025-07-14 02:02", "2025-07-14 02:03", "2025-07-14 02:04", "2025-07-14 02:05", "2025-07-14 02:06", "2025-07-14 02:07", "2025-07-14 02:08", "2025-07-14 02:09", "2025-07-14 02:10", "2025-07-14 02:11", "2025-07-14 02:12", "2025-07-14 02:13", "2025-07-14 02:14", "2025-07-14 02:15", "2025-07-14 02:16", "2025-07-14 02:17", "2025-07-14 02:18", "2025-07-14 02:19", "2025-07-14 02:20", "2025-07-14 02:21", "2025-07-14 02:22", "2025-07-14 02:23", "2025-07-14 02:24", "2025-07-14 02:25", "2025-07-14 02:26", "2025-07-14 02:27", "2025-07-14 02:28", "2025-07-14 02:29", "2025-07-14 02:30", "2025-07-14 02:31", "2025-07-14 02:32", "2025-07-14 02:33", "2025-07-14 02:34", "2025-07-14 02:35", "2025-07-14 02:36", "2025-07-14 02:37", "2025-07-14 02:38", "2025-07-14 02:39", "2025-07-14 02:40", "2025-07-14 02:41", "2025-07-14 02:42", "2025-07-14 02:43", "2025-07-14 02:44", "2025-07-14 02:45", "2025-07-14 02:46", "2025-07-14 02:47", "2025-07-14 02:48", "2025-07-14 02:49", "2025-07-14 02:50", "2025-07-14 02:51", "2025-07-14 02:52", "2025-07-14 02:53", "2025-07-14 02:54", "2025-07-14 02:55", "2025-07-14 02:56", "2025-07-14 02:57", "2025-07-14 02:58", "2025-07-14 02:59", "2025-07-14 03:00", "2025-07-14 03:01", "2025-07-14 03:02", "2025-07-14 03:03", "2025-07-14 03:04", "2025-07-14 03:05", "2025-07-14 03:06", "2025-07-14 03:07", "2025-07-14 03:08", "2025-07-14 03:09", "2025-07-14 03:10", "2025-07-14 03:11", "2025-07-14 03:12", "2025-07-14 03:13", "2025-07-14 03:14", "2025-07-14 03:15", "2025-07-14 03:16", "2025-07-14 03:17", "2025-07-14 03:18", "2025-07-14 03:19", "2025-07-14 03:20", "2025-07-14 03:21", "2025-07-14 03:22", "2025-07-14 03:23", "2025-07-14 03:24", "2025-07-14 03:25", "2025-07-14 03:26", "2025-07-14 03:27", "2025-07-14 03:28", "2025-07-14 03:29", "2025-07-14 03:30", "2025-07-14 03:31", "2025-07-14 03:32", "2025-07-14 03:33", "2025-07-14 03:34", "2025-07-14 03:35", "2025-07-14 03:36", "2025-07-14 03:37", "2025-07-14 03:38", "2025-07-14 03:39", "2025-07-14 03:40", "2025-07-14 03:41", "2025-07-14 03:42", "2025-07-14 03:43", "2025-07-14 03:44", "2025-07-14 03:45", "2025-07-14 03:46", "2025-07-14 03:47", "2025-07-14 03:48", "2025-07-14 03:49", "2025-07-14 03:50", "2025-07-14 03:51", "2025-07-14 03:52", "2025-07-14 03:53", "2025-07-14 03:54", "2025-07-14 03:55", "2025-07-14 03:56", "2025-07-14 03:57", "2025-07-14 03:58", "2025-07-14 03:59", "2025-07-14 04:00", "2025-07-14 04:01", "2025-07-14 04:02", "2025-07-14 04:03", "2025-07-14 04:04", "2025-07-14 04:05", "2025-07-14 04:06", "2025-07-14 04:07", "2025-07-14 04:08", "2025-07-14 04:09", "2025-07-14 04:10", "2025-07-14 04:11", "2025-07-14 04:12", "2025-07-14 04:13", "2025-07-14 04:14", "2025-07-14 04:15", "2025-07-14 04:16", "2025-07-14 04:17", "2025-07-14 04:18", "2025-07-14 04:19", "2025-07-14 04:20", "2025-07-14 04:21", "2025-07-14 04:22", "2025-07-14 04:23", "2025-07-14 04:24", "2025-07-14 04:25", "2025-07-14 04:26", "2025-07-14 04:27", "2025-07-14 04:28", "2025-07-14 04:29", "2025-07-14 04:30", "2025-07-14 04:31", "2025-07-14 04:32", "2025-07-14 04:33", "2025-07-14 04:34", "2025-07-14 04:35", "2025-07-14 04:36", "2025-07-14 04:37", "2025-07-14 04:38", "2025-07-14 04:39", "2025-07-14 04:40", "2025-07-14 04:41", "2025-07-14 04:42", "2025-07-14 04:43", "2025-07-14 04:44", "2025-07-14 04:45", "2025-07-14 04:46", "2025-07-14 04:47", "2025-07-14 04:48", "2025-07-14 04:49", "2025-07-14 04:50", "2025-07-14 04:51", "2025-07-14 04:52", "2025-07-14 04:53", "2025-07-14 04:54", "2025-07-14 04:55", "2025-07-14 04:56", "2025-07-14 04:57", "2025-07-14 04:58", "2025-07-14 04:59", "2025-07-14 05:00", "2025-07-14 05:01", "2025-07-14 05:02", "2025-07-14 05:03", "2025-07-14 05:04", "2025-07-14 05:05", "2025-07-14 05:06", "2025-07-14 05:07", "2025-07-14 05:08", "2025-07-14 05:09", "2025-07-14 05:10", "2025-07-14 05:11", "2025-07-14 05:12", "2025-07-14 05:13", "2025-07-14 05:14", "2025-07-14 05:15", "2025-07-14 05:16", "2025-07-14 05:17", "2025-07-14 05:18", "2025-07-14 05:19", "2025-07-14 05:20", "2025-07-14 05:21", "2025-07-14 05:22", "2025-07-14 05:23", "2025-07-14 05:24", "2025-07-14 05:25", "2025-07-14 05:26", "2025-07-14 05:27", "2025-07-14 05:28", "2025-07-14 05:29", "2025-07-14 05:30", "2025-07-14 05:31", "2025-07-14 05:32", "2025-07-14 05:33", "2025-07-14 05:34", "2025-07-14 05:35", "2025-07-14 05:36", "2025-07-14 05:37", "2025-07-14 05:38", "2025-07-14 05:39", "2025-07-14 05:40", "2025-07-14 05:41", "2025-07-14 05:42", "2025-07-14 05:43", "2025-07-14 05:44", "2025-07-14 05:45", "2025-07-14 05:46", "2025-07-14 05:47", "2025-07-14 05:48", "2025-07-14 05:49", "2025-07-14 05:50", "2025-07-14 05:51", "2025-07-14 05:52", "2025-07-14 05:53", "2025-07-14 05:54", "2025-07-14 05:55", "2025-07-14 05:56", "2025-07-14 05:57", "2025-07-14 05:58", "2025-07-14 05:59", "2025-07-14 06:00", "2025-07-14 06:01", "2025-07-14 06:02", "2025-07-14 06:03", "2025-07-14 06:04", "2025-07-14 06:05", "2025-07-14 06:06", "2025-07-14 06:07", "2025-07-14 06:08", "2025-07-14 06:09", "2025-07-14 06:10", "2025-07-14 06:11", "2025-07-14 06:12", "2025-07-14 06:13", "2025-07-14 06:14", "2025-07-14 06:15", "2025-07-14 06:16", "2025-07-14 06:17", "2025-07-14 06:18", "2025-07-14 06:19", "2025-07-14 06:20", "2025-07-14 06:21", "2025-07-14 06:22", "2025-07-14 06:23", "2025-07-14 06:24", "2025-07-14 06:25", "2025-07-14 06:26", "2025-07-14 06:27", "2025-07-14 06:28", "2025-07-14 06:29", "2025-07-14 06:30", "2025-07-14 06:31", "2025-07-14 06:32", "2025-07-14 06:33", "2025-07-14 06:34", "2025-07-14 06:35", "2025-07-14 06:36", "2025-07-14 06:37", "2025-07-14 06:38", "2025-07-14 06:39", "2025-07-14 06:40", "2025-07-14 06:41", "2025-07-14 06:42", "2025-07-14 06:43", "2025-07-14 06:44", "2025-07-14 06:45", "2025-07-14 06:46", "2025-07-14 06:47", "2025-07-14 06:48", "2025-07-14 06:49", "2025-07-14 06:50", "2025-07-14 06:51", "2025-07-14 06:52", "2025-07-14 06:53", "2025-07-14 06:54", "2025-07-14 06:55", "2025-07-14 06:56", "2025-07-14 06:57", "2025-07-14 06:58", "2025-07-14 06:59", "2025-07-14 07:00", "2025-07-14 07:01", "2025-07-14 07:02", "2025-07-14 07:03", "2025-07-14 07:04", "2025-07-14 07:05", "2025-07-14 07:06", "2025-07-14 07:07", "2025-07-14 07:08", "2025-07-14 07:09", "2025-07-14 07:10", "2025-07-14 07:11", "2025-07-14 07:12", "2025-07-14 07:13", "2025-07-14 07:14", "2025-07-14 07:15", "2025-07-14 07:16", "2025-07-14 07:17", "2025-07-14 07:18", "2025-07-14 07:19", "2025-07-14 07:20", "2025-07-14 07:21", "2025-07-14 07:22", "2025-07-14 07:23", "2025-07-14 07:24", "2025-07-14 07:25", "2025-07-14 07:26", "2025-07-14 07:27", "2025-07-14 07:28", "2025-07-14 07:29", "2025-07-14 07:30", "2025-07-14 07:31", "2025-07-14 07:32", "2025-07-14 07:33", "2025-07-14 07:34", "2025-07-14 07:35", "2025-07-14 07:36", "2025-07-14 07:37", "2025-07-14 07:38", "2025-07-14 07:39", "2025-07-14 07:40", "2025-07-14 07:41", "2025-07-14 07:42", "2025-07-14 07:43", "2025-07-14 07:44", "2025-07-14 07:45", "2025-07-14 07:46", "2025-07-14 07:47", "2025-07-14 07:48", "2025-07-14 07:49", "2025-07-14 07:50", "2025-07-14 07:51", "2025-07-14 07:52", "2025-07-14 07:53", "2025-07-14 07:54", "2025-07-14 07:55", "2025-07-14 07:56", "2025-07-14 07:57", "2025-07-14 07:58", "2025-07-14 07:59", "2025-07-14 08:00", "2025-07-14 08:01", "2025-07-14 08:02", "2025-07-14 08:03", "2025-07-14 08:04", "2025-07-14 08:05", "2025-07-14 08:06", "2025-07-14 08:07", "2025-07-14 08:08", "2025-07-14 08:09", "2025-07-14 08:10", "2025-07-14 08:11", "2025-07-14 08:12", "2025-07-14 08:13", "2025-07-14 08:14", "2025-07-14 08:15", "2025-07-14 08:16", "2025-07-14 08:17", "2025-07-14 08:18", "2025-07-14 08:19", "2025-07-14 08:20", "2025-07-14 08:21", "2025-07-14 08:22", "2025-07-14 08:23", "2025-07-14 08:24", "2025-07-14 08:25", "2025-07-14 08:26", "2025-07-14 08:27", "2025-07-14 08:28", "2025-07-14 08:29", "2025-07-14 08:30", "2025-07-14 08:31", "2025-07-14 08:32", "2025-07-14 08:33", "2025-07-14 08:34", "2025-07-14 08:35", "2025-07-14 08:36", "2025-07-14 08:37", "2025-07-14 08:38", "2025-07-14 08:39", "2025-07-14 08:40", "2025-07-14 08:41", "2025-07-14 08:42", "2025-07-14 08:43", "2025-07-14 08:44", "2025-07-14 08:45", "2025-07-14 08:46", "2025-07-14 08:47", "2025-07-14 08:48", "2025-07-14 08:49", "2025-07-14 08:50", "2025-07-14 08:51", "2025-07-14 08:52", "2025-07-14 08:53", "2025-07-14 08:54", "2025-07-14 08:55", "2025-07-14 08:56", "2025-07-14 08:57", "2025-07-14 08:58", "2025-07-14 08:59", "2025-07-14 09:00", "2025-07-14 09:01", "2025-07-14 09:02", "2025-07-14 09:03", "2025-07-14 09:04", "2025-07-14 09:05", "2025-07-14 09:06", "2025-07-14 09:07", "2025-07-14 09:08", "2025-07-14 09:09", "2025-07-14 09:10", "2025-07-14 09:11", "2025-07-14 09:12", "2025-07-14 09:13", "2025-07-14 09:14", "2025-07-14 09:15", "2025-07-14 09:16", "2025-07-14 09:17", "2025-07-14 09:18", "2025-07-14 09:19", "2025-07-14 09:20", "2025-07-14 09:21", "2025-07-14 09:22", "2025-07-14 09:23", "2025-07-14 09:24", "2025-07-14 09:25", "2025-07-14 09:26", "2025-07-14 09:27", "2025-07-14 09:28", "2025-07-14 09:29", "2025-07-14 09:30", "2025-07-14 09:31", "2025-07-14 09:32", "2025-07-14 09:33", "2025-07-14 09:34", "2025-07-14 09:35", "2025-07-14 09:36", "2025-07-14 09:37", "2025-07-14 09:38", "2025-07-14 09:39", "2025-07-14 09:40", "2025-07-14 09:41", "2025-07-14 09:42", "2025-07-14 09:43", "2025-07-14 09:44", "2025-07-14 09:45", "2025-07-14 09:46", "2025-07-14 09:47", "2025-07-14 09:48", "2025-07-14 09:49", "2025-07-14 09:50", "2025-07-14 09:51", "2025-07-14 09:52", "2025-07-14 09:53", "2025-07-14 09:54", "2025-07-14 09:55", "2025-07-14 09:56", "2025-07-14 09:57", "2025-07-14 09:58", "2025-07-14 09:59", "2025-07-14 10:00", "2025-07-14 10:01", "2025-07-14 10:02", "2025-07-14 10:03", "2025-07-14 10:04", "2025-07-14 10:05", "2025-07-14 10:06", "2025-07-14 10:07", "2025-07-14 10:08", "2025-07-14 10:09", "2025-07-14 10:10", "2025-07-14 10:11", "2025-07-14 10:12", "2025-07-14 10:13", "2025-07-14 10:14", "2025-07-14 10:15", "2025-07-14 10:16", "2025-07-14 10:17", "2025-07-14 10:18", "2025-07-14 10:19", "2025-07-14 10:20", "2025-07-14 10:21", "2025-07-14 10:22", "2025-07-14 10:23", "2025-07-14 10:24", "2025-07-14 10:25", "2025-07-14 10:26", "2025-07-14 10:27", "2025-07-14 10:28", "2025-07-14 10:29", "2025-07-14 10:30", "2025-07-14 10:31", "2025-07-14 10:32", "2025-07-14 10:33", "2025-07-14 10:34", "2025-07-14 10:35", "2025-07-14 10:36", "2025-07-14 10:37", "2025-07-14 10:38", "2025-07-14 10:39", "2025-07-14 10:40", "2025-07-14 10:41", "2025-07-14 10:42", "2025-07-14 10:43", "2025-07-14 10:44", "2025-07-14 10:45", "2025-07-14 10:46", "2025-07-14 10:47", "2025-07-14 10:48", "2025-07-14 10:49", "2025-07-14 10:50", "2025-07-14 10:51", "2025-07-14 10:52", "2025-07-14 10:53", "2025-07-14 10:54", "2025-07-14 10:55", "2025-07-14 10:56", "2025-07-14 10:57", "2025-07-14 10:58", "2025-07-14 10:59", "2025-07-14 11:00", "2025-07-14 11:01", "2025-07-14 11:02", "2025-07-14 11:03", "2025-07-14 11:04", "2025-07-14 11:05", "2025-07-14 11:06", "2025-07-14 11:07", "2025-07-14 11:08", "2025-07-14 11:09", "2025-07-14 11:10", "2025-07-14 11:11", "2025-07-14 11:12", "2025-07-14 11:13", "2025-07-14 11:14", "2025-07-14 11:15", "2025-07-14 11:16", "2025-07-14 11:17", "2025-07-14 11:18", "2025-07-14 11:19", "2025-07-14 11:20", "2025-07-14 11:21", "2025-07-14 11:22", "2025-07-14 11:23", "2025-07-14 11:24", "2025-07-14 11:25", "2025-07-14 11:26", "2025-07-14 11:27", "2025-07-14 11:28", "2025-07-14 11:29", "2025-07-14 11:30", "2025-07-14 11:31", "2025-07-14 11:32", "2025-07-14 11:33", "2025-07-14 11:34", "2025-07-14 11:35", "2025-07-14 11:36", "2025-07-14 11:37", "2025-07-14 11:38", "2025-07-14 11:39", "2025-07-14 11:40", "2025-07-14 11:41", "2025-07-14 11:42", "2025-07-14 11:43", "2025-07-14 11:44", "2025-07-14 11:45", "2025-07-14 11:46", "2025-07-14 11:47", "2025-07-14 11:48", "2025-07-14 11:49", "2025-07-14 11:50", "2025-07-14 11:51", "2025-07-14 11:52", "2025-07-14 11:53", "2025-07-14 11:54", "2025-07-14 11:55", "2025-07-14 11:56", "2025-07-14 11:57", "2025-07-14 11:58", "2025-07-14 11:59", "2025-07-14 12:00", "2025-07-14 12:01", "2025-07-14 12:02", "2025-07-14 12:03", "2025-07-14 12:04", "2025-07-14 12:05", "2025-07-14 12:06", "2025-07-14 12:07", "2025-07-14 12:08", "2025-07-14 12:09", "2025-07-14 12:10", "2025-07-14 12:11", "2025-07-14 12:12", "2025-07-14 12:13", "2025-07-14 12:14", "2025-07-14 12:15", "2025-07-14 12:16", "2025-07-14 12:17", "2025-07-14 12:18", "2025-07-14 12:19", "2025-07-14 12:20", "2025-07-14 12:21", "2025-07-14 12:22", "2025-07-14 12:23", "2025-07-14 12:24", "2025-07-14 12:25", "2025-07-14 12:26", "2025-07-14 12:27", "2025-07-14 12:28", "2025-07-14 12:29", "2025-07-14 12:30", "2025-07-14 12:31", "2025-07-14 12:32", "2025-07-14 12:33", "2025-07-14 12:34", "2025-07-14 12:35", "2025-07-14 12:36", "2025-07-14 12:37", "2025-07-14 12:38", "2025-07-14 12:39", "2025-07-14 12:40", "2025-07-14 12:41", "2025-07-14 12:42", "2025-07-14 12:43", "2025-07-14 12:44", "2025-07-14 12:45", "2025-07-14 12:46", "2025-07-14 12:47", "2025-07-14 12:48", "2025-07-14 12:49", "2025-07-14 12:50", "2025-07-14 12:51", "2025-07-14 12:52", "2025-07-14 12:53", "2025-07-14 12:54", "2025-07-14 12:55", "2025-07-14 12:56", "2025-07-14 12:57", "2025-07-14 12:58", "2025-07-14 12:59", "2025-07-14 13:00", "2025-07-14 13:01", "2025-07-14 13:02", "2025-07-14 13:03", "2025-07-14 13:04", "2025-07-14 13:05", "2025-07-14 13:06", "2025-07-14 13:07", "2025-07-14 13:08", "2025-07-14 13:09", "2025-07-14 13:10", "2025-07-14 13:11", "2025-07-14 13:12", "2025-07-14 13:13", "2025-07-14 13:14", "2025-07-14 13:15", "2025-07-14 13:16", "2025-07-14 13:17", "2025-07-14 13:18", "2025-07-14 13:19", "2025-07-14 13:20", "2025-07-14 13:21", "2025-07-14 13:22", "2025-07-14 13:23", "2025-07-14 13:24", "2025-07-14 13:25", "2025-07-14 13:26", "2025-07-14 13:27", "2025-07-14 13:28", "2025-07-14 13:29", "2025-07-14 13:30", "2025-07-14 13:31", "2025-07-14 13:32", "2025-07-14 13:33", "2025-07-14 13:34", "2025-07-14 13:35", "2025-07-14 13:36", "2025-07-14 13:37", "2025-07-14 13:38", "2025-07-14 13:39", "2025-07-14 13:40", "2025-07-14 13:41", "2025-07-14 13:42", "2025-07-14 13:43", "2025-07-14 13:44", "2025-07-14 13:45", "2025-07-14 13:46", "2025-07-14 13:47", "2025-07-14 13:48", "2025-07-14 13:49", "2025-07-14 13:50", "2025-07-14 13:51", "2025-07-14 13:52", "2025-07-14 13:53", "2025-07-14 13:54", "2025-07-14 13:55", "2025-07-14 13:56", "2025-07-14 13:57", "2025-07-14 13:58", "2025-07-14 13:59", "2025-07-14 14:00", "2025-07-14 14:01", "2025-07-14 14:02", "2025-07-14 14:03", "2025-07-14 14:04", "2025-07-14 14:05", "2025-07-14 14:06", "2025-07-14 14:07", "2025-07-14 14:08", "2025-07-14 14:09", "2025-07-14 14:10", "2025-07-14 14:11", "2025-07-14 14:12", "2025-07-14 14:13", "2025-07-14 14:14", "2025-07-14 14:15", "2025-07-14 14:16", "2025-07-14 14:17", "2025-07-14 14:18", "2025-07-14 14:19", "2025-07-14 14:20", "2025-07-14 14:21", "2025-07-14 14:22", "2025-07-14 14:23", "2025-07-14 14:24", "2025-07-14 14:25", "2025-07-14 14:26", "2025-07-14 14:27", "2025-07-14 14:28", "2025-07-14 14:29", "2025-07-14 14:30", "2025-07-14 14:31", "2025-07-14 14:32", "2025-07-14 14:33", "2025-07-14 14:34", "2025-07-14 14:35", "2025-07-14 14:36", "2025-07-14 14:37", "2025-07-14 14:38", "2025-07-14 14:39", "2025-07-14 14:40", "2025-07-14 14:41", "2025-07-14 14:42", "2025-07-14 14:43", "2025-07-14 14:44", "2025-07-14 14:45", "2025-07-14 14:46", "2025-07-14 14:47", "2025-07-14 14:48", "2025-07-14 14:49", "2025-07-14 14:50", "2025-07-14 14:51", "2025-07-14 14:52", "2025-07-14 14:53", "2025-07-14 14:54", "2025-07-14 14:55", "2025-07-14 14:56", "2025-07-14 14:57", "2025-07-14 14:58", "2025-07-14 14:59", "2025-07-14 15:00", "2025-07-14 15:01", "2025-07-14 15:02", "2025-07-14 15:03", "2025-07-14 15:04", "2025-07-14 15:05", "2025-07-14 15:06", "2025-07-14 15:07", "2025-07-14 15:08", "2025-07-14 15:09", "2025-07-14 15:10", "2025-07-14 15:11", "2025-07-14 15:12", "2025-07-14 15:13", "2025-07-14 15:14", "2025-07-14 15:15", "2025-07-14 15:16", "2025-07-14 15:17", "2025-07-14 15:18", "2025-07-14 15:19", "2025-07-14 15:20", "2025-07-14 15:21", "2025-07-14 15:22", "2025-07-14 15:23", "2025-07-14 15:24", "2025-07-14 15:25", "2025-07-14 15:26", "2025-07-14 15:27", "2025-07-14 15:28", "2025-07-14 15:29", "2025-07-14 15:30", "2025-07-14 15:31", "2025-07-14 15:32", "2025-07-14 15:33", "2025-07-14 15:34", "2025-07-14 15:35", "2025-07-14 15:36", "2025-07-14 15:37", "2025-07-14 15:38", "2025-07-14 15:39", "2025-07-14 15:40", "2025-07-14 15:41", "2025-07-14 15:42", "2025-07-14 15:43", "2025-07-14 15:44", "2025-07-14 15:45", "2025-07-14 15:46", "2025-07-14 15:47", "2025-07-14 15:48", "2025-07-14 15:49", "2025-07-14 15:50", "2025-07-14 15:51", "2025-07-14 15:52", "2025-07-14 15:53", "2025-07-14 15:54", "2025-07-14 15:55", "2025-07-14 15:56", "2025-07-14 15:57", "2025-07-14 15:58", "2025-07-14 15:59", "2025-07-14 16:00", "2025-07-14 16:01", "2025-07-14 16:02", "2025-07-14 16:03", "2025-07-14 16:04", "2025-07-14 16:05", "2025-07-14 16:06", "2025-07-14 16:07", "2025-07-14 16:08", "2025-07-14 16:09", "2025-07-14 16:10", "2025-07-14 16:11", "2025-07-14 16:12", "2025-07-14 16:13", "2025-07-14 16:14", "2025-07-14 16:15", "2025-07-14 16:16", "2025-07-14 16:17", "2025-07-14 16:18", "2025-07-14 16:19", "2025-07-14 16:20", "2025-07-14 16:21", "2025-07-14 16:22", "2025-07-14 16:23", "2025-07-14 16:24", "2025-07-14 16:25", "2025-07-14 16:26", "2025-07-14 16:27", "2025-07-14 16:28", "2025-07-14 16:29", "2025-07-14 16:30", "2025-07-14 16:31", "2025-07-14 16:32", "2025-07-14 16:33", "2025-07-14 16:34", "2025-07-14 16:35", "2025-07-14 16:36", "2025-07-14 16:37", "2025-07-14 16:38", "2025-07-14 16:39", "2025-07-14 16:40", "2025-07-14 16:41", "2025-07-14 16:42", "2025-07-14 16:43", "2025-07-14 16:44", "2025-07-14 16:45", "2025-07-14 16:46", "2025-07-14 16:47", "2025-07-14 16:48", "2025-07-14 16:49", "2025-07-14 16:50", "2025-07-14 16:51", "2025-07-14 16:52", "2025-07-14 16:53", "2025-07-14 16:54", "2025-07-14 16:55", "2025-07-14 16:56", "2025-07-14 16:57", "2025-07-14 16:58", "2025-07-14 16:59", "2025-07-14 17:00", "2025-07-14 17:01", "2025-07-14 17:02", "2025-07-14 17:03", "2025-07-14 17:04", "2025-07-14 17:05", "2025-07-14 17:06", "2025-07-14 17:07", "2025-07-14 17:08", "2025-07-14 17:09", "2025-07-14 17:10", "2025-07-14 17:11", "2025-07-14 17:12", "2025-07-14 17:13", "2025-07-14 17:14", "2025-07-14 17:15", "2025-07-14 17:16", "2025-07-14 17:17", "2025-07-14 17:18", "2025-07-14 17:19", "2025-07-14 17:20", "2025-07-14 17:21", "2025-07-14 17:22", "2025-07-14 17:23", "2025-07-14 17:24", "2025-07-14 17:25", "2025-07-14 17:26", "2025-07-14 17:27", "2025-07-14 17:28", "2025-07-14 17:29", "2025-07-14 17:30", "2025-07-14 17:31", "2025-07-14 17:32", "2025-07-14 17:33", "2025-07-14 17:34", "2025-07-14 17:35", "2025-07-14 17:36", "2025-07-14 17:37", "2025-07-14 17:38", "2025-07-14 17:39", "2025-07-14 17:40", "2025-07-14 17:41", "2025-07-14 17:42", "2025-07-14 17:43", "2025-07-14 17:44", "2025-07-14 17:45", "2025-07-14 17:46", "2025-07-14 17:47", "2025-07-14 17:48", "2025-07-14 17:49", "2025-07-14 17:50", "2025-07-14 17:51", "2025-07-14 17:52", "2025-07-14 17:53", "2025-07-14 17:54", "2025-07-14 17:55", "2025-07-14 17:56", "2025-07-14 17:57", "2025-07-14 17:58", "2025-07-14 17:59", "2025-07-14 18:00", "2025-07-14 18:01", "2025-07-14 18:02", "2025-07-14 18:03", "2025-07-14 18:04", "2025-07-14 18:05", "2025-07-14 18:06", "2025-07-14 18:07", "2025-07-14 18:08", "2025-07-14 18:09", "2025-07-14 18:10", "2025-07-14 18:11", "2025-07-14 18:12", "2025-07-14 18:13", "2025-07-14 18:14", "2025-07-14 18:15", "2025-07-14 18:16", "2025-07-14 18:17", "2025-07-14 18:18", "2025-07-14 18:19", "2025-07-14 18:20", "2025-07-14 18:21", "2025-07-14 18:22", "2025-07-14 18:23", "2025-07-14 18:24", "2025-07-14 18:25", "2025-07-14 18:26", "2025-07-14 18:27", "2025-07-14 18:28", "2025-07-14 18:29", "2025-07-14 18:30", "2025-07-14 18:31", "2025-07-14 18:32", "2025-07-14 18:33", "2025-07-14 18:34", "2025-07-14 18:35", "2025-07-14 18:36", "2025-07-14 18:37", "2025-07-14 18:38", "2025-07-14 18:39", "2025-07-14 18:40", "2025-07-14 18:41", "2025-07-14 18:42", "2025-07-14 18:43", "2025-07-14 18:44", "2025-07-14 18:45", "2025-07-14 18:46", "2025-07-14 18:47", "2025-07-14 18:48", "2025-07-14 18:49", "2025-07-14 18:50", "2025-07-14 18:51", "2025-07-14 18:52", "2025-07-14 18:53", "2025-07-14 18:54", "2025-07-14 18:55", "2025-07-14 18:56", "2025-07-14 18:57", "2025-07-14 18:58", "2025-07-14 18:59", "2025-07-14 19:00", "2025-07-14 19:01", "2025-07-14 19:02", "2025-07-14 19:03", "2025-07-14 19:04", "2025-07-14 19:05", "2025-07-14 19:06", "2025-07-14 19:07", "2025-07-14 19:08", "2025-07-14 19:09", "2025-07-14 19:10", "2025-07-14 19:11", "2025-07-14 19:12", "2025-07-14 19:13", "2025-07-14 19:14", "2025-07-14 19:15", "2025-07-14 19:16", "2025-07-14 19:17", "2025-07-14 19:18", "2025-07-14 19:19", "2025-07-14 19:20", "2025-07-14 19:21", "2025-07-14 19:22", "2025-07-14 19:23", "2025-07-14 19:24", "2025-07-14 19:25", "2025-07-14 19:26", "2025-07-14 19:27", "2025-07-14 19:28", "2025-07-14 19:29", "2025-07-14 19:30", "2025-07-14 19:31", "2025-07-14 19:32", "2025-07-14 19:33", "2025-07-14 19:34", "2025-07-14 19:35", "2025-07-14 19:36", "2025-07-14 19:37", "2025-07-14 19:38", "2025-07-14 19:39", "2025-07-14 19:40", "2025-07-14 19:41", "2025-07-14 19:42", "2025-07-14 19:43", "2025-07-14 19:44", "2025-07-14 19:45", "2025-07-14 19:46", "2025-07-14 19:47", "2025-07-14 19:48", "2025-07-14 19:49", "2025-07-14 19:50", "2025-07-14 19:51", "2025-07-14 19:52", "2025-07-14 19:53", "2025-07-14 19:54", "2025-07-14 19:55", "2025-07-14 19:56", "2025-07-14 19:57", "2025-07-14 19:58", "2025-07-14 19:59", "2025-07-14 20:00", "2025-07-14 20:01", "2025-07-14 20:02", "2025-07-14 20:03", "2025-07-14 20:04", "2025-07-14 20:05", "2025-07-14 20:06", "2025-07-14 20:07", "2025-07-14 20:08", "2025-07-14 20:09", "2025-07-14 20:10", "2025-07-14 20:11", "2025-07-14 20:12", "2025-07-14 20:13", "2025-07-14 20:14", "2025-07-14 20:15", "2025-07-14 20:16", "2025-07-14 20:17", "2025-07-14 20:18", "2025-07-14 20:19", "2025-07-14 20:20", "2025-07-14 20:21", "2025-07-14 20:22", "2025-07-14 20:23", "2025-07-14 20:24", "2025-07-14 20:25", "2025-07-14 20:26", "2025-07-14 20:27", "2025-07-14 20:28", "2025-07-14 20:29", "2025-07-14 20:30", "2025-07-14 20:31", "2025-07-14 20:32", "2025-07-14 20:33", "2025-07-14 20:34", "2025-07-14 20:35", "2025-07-14 20:36", "2025-07-14 20:37", "2025-07-14 20:38", "2025-07-14 20:39", "2025-07-14 20:40", "2025-07-14 20:41", "2025-07-14 20:42", "2025-07-14 20:43", "2025-07-14 20:44", "2025-07-14 20:45", "2025-07-14 20:46", "2025-07-14 20:47", "2025-07-14 20:48", "2025-07-14 20:49", "2025-07-14 20:50", "2025-07-14 20:51", "2025-07-14 20:52", "2025-07-14 20:53", "2025-07-14 20:54", "2025-07-14 20:55", "2025-07-14 20:56", "2025-07-14 20:57", "2025-07-14 20:58", "2025-07-14 20:59", "2025-07-14 21:00", "2025-07-14 21:01", "2025-07-14 21:02", "2025-07-14 21:03", "2025-07-14 21:04", "2025-07-14 21:05", "2025-07-14 21:06", "2025-07-14 21:07", "2025-07-14 21:08", "2025-07-14 21:09", "2025-07-14 21:10", "2025-07-14 21:11", "2025-07-14 21:12", "2025-07-14 21:13", "2025-07-14 21:14", "2025-07-14 21:15", "2025-07-14 21:16", "2025-07-14 21:17", "2025-07-14 21:18", "2025-07-14 21:19", "2025-07-14 21:20", "2025-07-14 21:21", "2025-07-14 21:22", "2025-07-14 21:23", "2025-07-14 21:24", "2025-07-14 21:25", "2025-07-14 21:26", "2025-07-14 21:27", "2025-07-14 21:28", "2025-07-14 21:29", "2025-07-14 21:30", "2025-07-14 21:31", "2025-07-14 21:32", "2025-07-14 21:33", "2025-07-14 21:34", "2025-07-14 21:35", "2025-07-14 21:36", "2025-07-14 21:37", "2025-07-14 21:38", "2025-07-14 21:39", "2025-07-14 21:40", "2025-07-14 21:41", "2025-07-14 21:42", "2025-07-14 21:43", "2025-07-14 21:44", "2025-07-14 21:45", "2025-07-14 21:46", "2025-07-14 21:47", "2025-07-14 21:48", "2025-07-14 21:49", "2025-07-14 21:50", "2025-07-14 21:51", "2025-07-14 21:52", "2025-07-14 21:53", "2025-07-14 21:54", "2025-07-14 21:55", "2025-07-14 21:56", "2025-07-14 21:57", "2025-07-14 21:58", "2025-07-14 21:59", "2025-07-14 22:00", "2025-07-14 22:01", "2025-07-14 22:02", "2025-07-14 22:03", "2025-07-14 22:04", "2025-07-14 22:05", "2025-07-14 22:06", "2025-07-14 22:07", "2025-07-14 22:08", "2025-07-14 22:09", "2025-07-14 22:10", "2025-07-14 22:11", "2025-07-14 22:12", "2025-07-14 22:13", "2025-07-14 22:14", "2025-07-14 22:15", "2025-07-14 22:16", "2025-07-14 22:17", "2025-07-14 22:18", "2025-07-14 22:19", "2025-07-14 22:20", "2025-07-14 22:21", "2025-07-14 22:22", "2025-07-14 22:23", "2025-07-14 22:24", "2025-07-14 22:25", "2025-07-14 22:26", "2025-07-14 22:27", "2025-07-14 22:28", "2025-07-14 22:29", "2025-07-14 22:30", "2025-07-14 22:31", "2025-07-14 22:32", "2025-07-14 22:33", "2025-07-14 22:34", "2025-07-14 22:35", "2025-07-14 22:36", "2025-07-14 22:37", "2025-07-14 22:38", "2025-07-14 22:39", "2025-07-14 22:40", "2025-07-14 22:41", "2025-07-14 22:42", "2025-07-14 22:43", "2025-07-14 22:44", "2025-07-14 22:45", "2025-07-14 22:46", "2025-07-14 22:47", "2025-07-14 22:48", "2025-07-14 22:49", "2025-07-14 22:50", "2025-07-14 22:51", "2025-07-14 22:52", "2025-07-14 22:53", "2025-07-14 22:54", "2025-07-14 22:55", "2025-07-14 22:56", "2025-07-14 22:57", "2025-07-14 22:58", "2025-07-14 22:59", "2025-07-14 23:00", "2025-07-14 23:01", "2025-07-14 23:02", "2025-07-14 23:03", "2025-07-14 23:04", "2025-07-14 23:05", "2025-07-14 23:06", "2025-07-14 23:07", "2025-07-14 23:08", "2025-07-14 23:09", "2025-07-14 23:10", "2025-07-14 23:11", "2025-07-14 23:12", "2025-07-14 23:13", "2025-07-14 23:14", "2025-07-14 23:15", "2025-07-14 23:16", "2025-07-14 23:17", "2025-07-14 23:18", "2025-07-14 23:19", "2025-07-14 23:20", "2025-07-14 23:21", "2025-07-14 23:22", "2025-07-14 23:23", "2025-07-14 23:24", "2025-07-14 23:25", "2025-07-14 23:26", "2025-07-14 23:27", "2025-07-14 23:28", "2025-07-14 23:29", "2025-07-14 23:30", "2025-07-14 23:31", "2025-07-14 23:32", "2025-07-14 23:33", "2025-07-14 23:34", "2025-07-14 23:35", "2025-07-14 23:36", "2025-07-14 23:37", "2025-07-14 23:38", "2025-07-14 23:39", "2025-07-14 23:40", "2025-07-14 23:41", "2025-07-14 23:42", "2025-07-14 23:43", "2025-07-14 23:44", "2025-07-14 23:45", "2025-07-14 23:46", "2025-07-14 23:47", "2025-07-14 23:48", "2025-07-14 23:49", "2025-07-14 23:50", "2025-07-14 23:51", "2025-07-14 23:52", "2025-07-14 23:53", "2025-07-14 23:54", "2025-07-14 23:55", "2025-07-14 23:56", "2025-07-14 23:57", "2025-07-14 23:58", "2025-07-14 23:59", "2025-07-15 00:00", "2025-07-15 00:01", "2025-07-15 00:02", "2025-07-15 00:03", "2025-07-15 00:04", "2025-07-15 00:05", "2025-07-15 00:06", "2025-07-15 00:07", "2025-07-15 00:08", "2025-07-15 00:09", "2025-07-15 00:10", "2025-07-15 00:11", "2025-07-15 00:12", "2025-07-15 00:13", "2025-07-15 00:14", "2025-07-15 00:15", "2025-07-15 00:16", "2025-07-15 00:17", "2025-07-15 00:18", "2025-07-15 00:19", "2025-07-15 00:20", "2025-07-15 00:21", "2025-07-15 00:22", "2025-07-15 00:23", "2025-07-15 00:24", "2025-07-15 00:25", "2025-07-15 00:26", "2025-07-15 00:27", "2025-07-15 00:28", "2025-07-15 00:29", "2025-07-15 00:30", "2025-07-15 00:31", "2025-07-15 00:32", "2025-07-15 00:33", "2025-07-15 00:34", "2025-07-15 00:35", "2025-07-15 00:36", "2025-07-15 00:37", "2025-07-15 00:38", "2025-07-15 00:39", "2025-07-15 00:40", "2025-07-15 00:41", "2025-07-15 00:42", "2025-07-15 00:43", "2025-07-15 00:44", "2025-07-15 00:45", "2025-07-15 00:46", "2025-07-15 00:47", "2025-07-15 00:48", "2025-07-15 00:49", "2025-07-15 00:50", "2025-07-15 00:51", "2025-07-15 00:52", "2025-07-15 00:53", "2025-07-15 00:54", "2025-07-15 00:55", "2025-07-15 00:56", "2025-07-15 00:57", "2025-07-15 00:58", "2025-07-15 00:59", "2025-07-15 01:00", "2025-07-15 01:01", "2025-07-15 01:02", "2025-07-15 01:03", "2025-07-15 01:04", "2025-07-15 01:05", "2025-07-15 01:06", "2025-07-15 01:07", "2025-07-15 01:08", "2025-07-15 01:09", "2025-07-15 01:10", "2025-07-15 01:11", "2025-07-15 01:12", "2025-07-15 01:13", "2025-07-15 01:14", "2025-07-15 01:15", "2025-07-15 01:16", "2025-07-15 01:17", "2025-07-15 01:18", "2025-07-15 01:19", "2025-07-15 01:20", "2025-07-15 01:21", "2025-07-15 01:22", "2025-07-15 01:23", "2025-07-15 01:24", "2025-07-15 01:25", "2025-07-15 01:26", "2025-07-15 01:27", "2025-07-15 01:28", "2025-07-15 01:29", "2025-07-15 01:30", "2025-07-15 01:31", "2025-07-15 01:32", "2025-07-15 01:33", "2025-07-15 01:34", "2025-07-15 01:35", "2025-07-15 01:36", "2025-07-15 01:37", "2025-07-15 01:38", "2025-07-15 01:39", "2025-07-15 01:40", "2025-07-15 01:41", "2025-07-15 01:42", "2025-07-15 01:43", "2025-07-15 01:44", "2025-07-15 01:45", "2025-07-15 01:46", "2025-07-15 01:47", "2025-07-15 01:48", "2025-07-15 01:49", "2025-07-15 01:50", "2025-07-15 01:51", "2025-07-15 01:52", "2025-07-15 01:53", "2025-07-15 01:54", "2025-07-15 01:55", "2025-07-15 01:56", "2025-07-15 01:57", "2025-07-15 01:58", "2025-07-15 01:59", "2025-07-15 02:00", "2025-07-15 02:01", "2025-07-15 02:02", "2025-07-15 02:03", "2025-07-15 02:04", "2025-07-15 02:05", "2025-07-15 02:06", "2025-07-15 02:07", "2025-07-15 02:08", "2025-07-15 02:09", "2025-07-15 02:10", "2025-07-15 02:11", "2025-07-15 02:12", "2025-07-15 02:13", "2025-07-15 02:14", "2025-07-15 02:15", "2025-07-15 02:16", "2025-07-15 02:17", "2025-07-15 02:18", "2025-07-15 02:19", "2025-07-15 02:20", "2025-07-15 02:21", "2025-07-15 02:22", "2025-07-15 02:23", "2025-07-15 02:24", "2025-07-15 02:25", "2025-07-15 02:26", "2025-07-15 02:27", "2025-07-15 02:28", "2025-07-15 02:29", "2025-07-15 02:30", "2025-07-15 02:31", "2025-07-15 02:32", "2025-07-15 02:33", "2025-07-15 02:34", "2025-07-15 02:35", "2025-07-15 02:36", "2025-07-15 02:37", "2025-07-15 02:38", "2025-07-15 02:39", "2025-07-15 02:40", "2025-07-15 02:41", "2025-07-15 02:42", "2025-07-15 02:43", "2025-07-15 02:44", "2025-07-15 02:45", "2025-07-15 02:46", "2025-07-15 02:47", "2025-07-15 02:48", "2025-07-15 02:49", "2025-07-15 02:50", "2025-07-15 02:51", "2025-07-15 02:52", "2025-07-15 02:53", "2025-07-15 02:54", "2025-07-15 02:55", "2025-07-15 02:56", "2025-07-15 02:57", "2025-07-15 02:58", "2025-07-15 02:59", "2025-07-15 03:00", "2025-07-15 03:01", "2025-07-15 03:02", "2025-07-15 03:03", "2025-07-15 03:04", "2025-07-15 03:05", "2025-07-15 03:06", "2025-07-15 03:07", "2025-07-15 03:08", "2025-07-15 03:09", "2025-07-15 03:10", "2025-07-15 03:11", "2025-07-15 03:12", "2025-07-15 03:13", "2025-07-15 03:14", "2025-07-15 03:15", "2025-07-15 03:16", "2025-07-15 03:17", "2025-07-15 03:18", "2025-07-15 03:19", "2025-07-15 03:20", "2025-07-15 03:21", "2025-07-15 03:22", "2025-07-15 03:23", "2025-07-15 03:24", "2025-07-15 03:25", "2025-07-15 03:26", "2025-07-15 03:27", "2025-07-15 03:28", "2025-07-15 03:29", "2025-07-15 03:30", "2025-07-15 03:31", "2025-07-15 03:32", "2025-07-15 03:33", "2025-07-15 03:34", "2025-07-15 03:35", "2025-07-15 03:36", "2025-07-15 03:37", "2025-07-15 03:38", "2025-07-15 03:39", "2025-07-15 03:40", "2025-07-15 03:41", "2025-07-15 03:42", "2025-07-15 03:43", "2025-07-15 03:44", "2025-07-15 03:45", "2025-07-15 03:46", "2025-07-15 03:47", "2025-07-15 03:48", "2025-07-15 03:49", "2025-07-15 03:50", "2025-07-15 03:51", "2025-07-15 03:52", "2025-07-15 03:53", "2025-07-15 03:54", "2025-07-15 03:55", "2025-07-15 03:56", "2025-07-15 03:57", "2025-07-15 03:58", "2025-07-15 03:59", "2025-07-15 04:00", "2025-07-15 04:01", "2025-07-15 04:02", "2025-07-15 04:03", "2025-07-15 04:04", "2025-07-15 04:05", "2025-07-15 04:06", "2025-07-15 04:07", "2025-07-15 04:08", "2025-07-15 04:09", "2025-07-15 04:10", "2025-07-15 04:11", "2025-07-15 04:12", "2025-07-15 04:13", "2025-07-15 04:14", "2025-07-15 04:15", "2025-07-15 04:16", "2025-07-15 04:17", "2025-07-15 04:18", "2025-07-15 04:19", "2025-07-15 04:20", "2025-07-15 04:21", "2025-07-15 04:22", "2025-07-15 04:23", "2025-07-15 04:24", "2025-07-15 04:25", "2025-07-15 04:26", "2025-07-15 04:27", "2025-07-15 04:28", "2025-07-15 04:29", "2025-07-15 04:30", "2025-07-15 04:31", "2025-07-15 04:32", "2025-07-15 04:33", "2025-07-15 04:34", "2025-07-15 04:35", "2025-07-15 04:36", "2025-07-15 04:37", "2025-07-15 04:38", "2025-07-15 04:39", "2025-07-15 04:40", "2025-07-15 04:41", "2025-07-15 04:42", "2025-07-15 04:43", "2025-07-15 04:44", "2025-07-15 04:45", "2025-07-15 04:46", "2025-07-15 04:47", "2025-07-15 04:48", "2025-07-15 04:49", "2025-07-15 04:50", "2025-07-15 04:51", "2025-07-15 04:52", "2025-07-15 04:53", "2025-07-15 04:54", "2025-07-15 04:55", "2025-07-15 04:56", "2025-07-15 04:57", "2025-07-15 04:58", "2025-07-15 04:59", "2025-07-15 05:00", "2025-07-15 05:01", "2025-07-15 05:02", "2025-07-15 05:03", "2025-07-15 05:04", "2025-07-15 05:05", "2025-07-15 05:06", "2025-07-15 05:07", "2025-07-15 05:08", "2025-07-15 05:09", "2025-07-15 05:10", "2025-07-15 05:11", "2025-07-15 05:12", "2025-07-15 05:13", "2025-07-15 05:14", "2025-07-15 05:15", "2025-07-15 05:16", "2025-07-15 05:17", "2025-07-15 05:18", "2025-07-15 05:19", "2025-07-15 05:20", "2025-07-15 05:21", "2025-07-15 05:22", "2025-07-15 05:23", "2025-07-15 05:24", "2025-07-15 05:25", "2025-07-15 05:26", "2025-07-15 05:27", "2025-07-15 05:28", "2025-07-15 05:29", "2025-07-15 05:30", "2025-07-15 05:31", "2025-07-15 05:32", "2025-07-15 05:33", "2025-07-15 05:34", "2025-07-15 05:35", "2025-07-15 05:36", "2025-07-15 05:37", "2025-07-15 05:38", "2025-07-15 05:39", "2025-07-15 05:40", "2025-07-15 05:41", "2025-07-15 05:42", "2025-07-15 05:43", "2025-07-15 05:44", "2025-07-15 05:45", "2025-07-15 05:46", "2025-07-15 05:47", "2025-07-15 05:48", "2025-07-15 05:49", "2025-07-15 05:50", "2025-07-15 05:51", "2025-07-15 05:52", "2025-07-15 05:53", "2025-07-15 05:54", "2025-07-15 05:55", "2025-07-15 05:56", "2025-07-15 05:57", "2025-07-15 05:58", "2025-07-15 05:59", "2025-07-15 06:00", "2025-07-15 06:01", "2025-07-15 06:02", "2025-07-15 06:03", "2025-07-15 06:04", "2025-07-15 06:05", "2025-07-15 06:06", "2025-07-15 06:07", "2025-07-15 06:08", "2025-07-15 06:09", "2025-07-15 06:10", "2025-07-15 06:11", "2025-07-15 06:12", "2025-07-15 06:13", "2025-07-15 06:14", "2025-07-15 06:15", "2025-07-15 06:16", "2025-07-15 06:17", "2025-07-15 06:18", "2025-07-15 06:19", "2025-07-15 06:20", "2025-07-15 06:21", "2025-07-15 06:22", "2025-07-15 06:23", "2025-07-15 06:24", "2025-07-15 06:25", "2025-07-15 06:26", "2025-07-15 06:27", "2025-07-15 06:28", "2025-07-15 06:29", "2025-07-15 06:30", "2025-07-15 06:31", "2025-07-15 06:32", "2025-07-15 06:33", "2025-07-15 06:34", "2025-07-15 06:35", "2025-07-15 06:36", "2025-07-15 06:37", "2025-07-15 06:38", "2025-07-15 06:39", "2025-07-15 06:40", "2025-07-15 06:41", "2025-07-15 06:42", "2025-07-15 06:43", "2025-07-15 06:44", "2025-07-15 06:45", "2025-07-15 06:46", "2025-07-15 06:47", "2025-07-15 06:48", "2025-07-15 06:49", "2025-07-15 06:50", "2025-07-15 06:51", "2025-07-15 06:52", "2025-07-15 06:53", "2025-07-15 06:54", "2025-07-15 06:55", "2025-07-15 06:56", "2025-07-15 06:57", "2025-07-15 06:58", "2025-07-15 06:59", "2025-07-15 07:00", "2025-07-15 07:01", "2025-07-15 07:02", "2025-07-15 07:03", "2025-07-15 07:04", "2025-07-15 07:05", "2025-07-15 07:06", "2025-07-15 07:07", "2025-07-15 07:08", "2025-07-15 07:09", "2025-07-15 07:10", "2025-07-15 07:11", "2025-07-15 07:12", "2025-07-15 07:13", "2025-07-15 07:14", "2025-07-15 07:15", "2025-07-15 07:16", "2025-07-15 07:17", "2025-07-15 07:18", "2025-07-15 07:19", "2025-07-15 07:20", "2025-07-15 07:21", "2025-07-15 07:22", "2025-07-15 07:23", "2025-07-15 07:24", "2025-07-15 07:25", "2025-07-15 07:26", "2025-07-15 07:27", "2025-07-15 07:28", "2025-07-15 07:29", "2025-07-15 07:30", "2025-07-15 07:31", "2025-07-15 07:32", "2025-07-15 07:33", "2025-07-15 07:34", "2025-07-15 07:35", "2025-07-15 07:36", "2025-07-15 07:37", "2025-07-15 07:38", "2025-07-15 07:39", "2025-07-15 07:40", "2025-07-15 07:41", "2025-07-15 07:42", "2025-07-15 07:43", "2025-07-15 07:44", "2025-07-15 07:45", "2025-07-15 07:46", "2025-07-15 07:47", "2025-07-15 07:48", "2025-07-15 07:49", "2025-07-15 07:50", "2025-07-15 07:51", "2025-07-15 07:52", "2025-07-15 07:53", "2025-07-15 07:54", "2025-07-15 07:55", "2025-07-15 07:56", "2025-07-15 07:57", "2025-07-15 07:58", "2025-07-15 07:59", "2025-07-15 08:00", "2025-07-15 08:01", "2025-07-15 08:02", "2025-07-15 08:03", "2025-07-15 08:04", "2025-07-15 08:05", "2025-07-15 08:06", "2025-07-15 08:07", "2025-07-15 08:08", "2025-07-15 08:09", "2025-07-15 08:10", "2025-07-15 08:11", "2025-07-15 08:12", "2025-07-15 08:13", "2025-07-15 08:14", "2025-07-15 08:15", "2025-07-15 08:16", "2025-07-15 08:17", "2025-07-15 08:18", "2025-07-15 08:19", "2025-07-15 08:20", "2025-07-15 08:21", "2025-07-15 08:22", "2025-07-15 08:23", "2025-07-15 08:24", "2025-07-15 08:25", "2025-07-15 08:26", "2025-07-15 08:27", "2025-07-15 08:28", "2025-07-15 08:29", "2025-07-15 08:30", "2025-07-15 08:31", "2025-07-15 08:32", "2025-07-15 08:33", "2025-07-15 08:34", "2025-07-15 08:35", "2025-07-15 08:36", "2025-07-15 08:37", "2025-07-15 08:38", "2025-07-15 08:39", "2025-07-15 08:40", "2025-07-15 08:41", "2025-07-15 08:42", "2025-07-15 08:43", "2025-07-15 08:44", "2025-07-15 08:45", "2025-07-15 08:46", "2025-07-15 08:47", "2025-07-15 08:48", "2025-07-15 08:49", "2025-07-15 08:50", "2025-07-15 08:51", "2025-07-15 08:52", "2025-07-15 08:53", "2025-07-15 08:54", "2025-07-15 08:55", "2025-07-15 08:56", "2025-07-15 08:57", "2025-07-15 08:58", "2025-07-15 08:59", "2025-07-15 09:00", "2025-07-15 09:01", "2025-07-15 09:02", "2025-07-15 09:03", "2025-07-15 09:04", "2025-07-15 09:05", "2025-07-15 09:06", "2025-07-15 09:07", "2025-07-15 09:08", "2025-07-15 09:09", "2025-07-15 09:10", "2025-07-15 09:11", "2025-07-15 09:12", "2025-07-15 09:13", "2025-07-15 09:14", "2025-07-15 09:15", "2025-07-15 09:16", "2025-07-15 09:17", "2025-07-15 09:18", "2025-07-15 09:19", "2025-07-15 09:20", "2025-07-15 09:21", "2025-07-15 09:22", "2025-07-15 09:23", "2025-07-15 09:24", "2025-07-15 09:25", "2025-07-15 09:26", "2025-07-15 09:27", "2025-07-15 09:28", "2025-07-15 09:29", "2025-07-15 09:30", "2025-07-15 09:31", "2025-07-15 09:32", "2025-07-15 09:33", "2025-07-15 09:34", "2025-07-15 09:35", "2025-07-15 09:36", "2025-07-15 09:37", "2025-07-15 09:38", "2025-07-15 09:39", "2025-07-15 09:40", "2025-07-15 09:41", "2025-07-15 09:42", "2025-07-15 09:43", "2025-07-15 09:44", "2025-07-15 09:45", "2025-07-15 09:46", "2025-07-15 09:47", "2025-07-15 09:48", "2025-07-15 09:49", "2025-07-15 09:50", "2025-07-15 09:51", "2025-07-15 09:52", "2025-07-15 09:53", "2025-07-15 09:54", "2025-07-15 09:55", "2025-07-15 09:56", "2025-07-15 09:57", "2025-07-15 09:58", "2025-07-15 09:59", "2025-07-15 10:00", "2025-07-15 10:01", "2025-07-15 10:02", "2025-07-15 10:03", "2025-07-15 10:04", "2025-07-15 10:05", "2025-07-15 10:06", "2025-07-15 10:07", "2025-07-15 10:08", "2025-07-15 10:09", "2025-07-15 10:10", "2025-07-15 10:11", "2025-07-15 10:12", "2025-07-15 10:13", "2025-07-15 10:14", "2025-07-15 10:15", "2025-07-15 10:16", "2025-07-15 10:17", "2025-07-15 10:18", "2025-07-15 10:19", "2025-07-15 10:20", "2025-07-15 10:21", "2025-07-15 10:22", "2025-07-15 10:23", "2025-07-15 10:24", "2025-07-15 10:25", "2025-07-15 10:26", "2025-07-15 10:27", "2025-07-15 10:28", "2025-07-15 10:29", "2025-07-15 10:30", "2025-07-15 10:31", "2025-07-15 10:32", "2025-07-15 10:33", "2025-07-15 10:34", "2025-07-15 10:35", "2025-07-15 10:36", "2025-07-15 10:37", "2025-07-15 10:38", "2025-07-15 10:39", "2025-07-15 10:40", "2025-07-15 10:41", "2025-07-15 10:42", "2025-07-15 10:43", "2025-07-15 10:44", "2025-07-15 10:45", "2025-07-15 10:46", "2025-07-15 10:47", "2025-07-15 10:48", "2025-07-15 10:49", "2025-07-15 10:50", "2025-07-15 10:51", "2025-07-15 10:52", "2025-07-15 10:53", "2025-07-15 10:54", "2025-07-15 10:55", "2025-07-15 10:56", "2025-07-15 10:57", "2025-07-15 10:58", "2025-07-15 10:59", "2025-07-15 11:00", "2025-07-15 11:01", "2025-07-15 11:02", "2025-07-15 11:03", "2025-07-15 11:04", "2025-07-15 11:05", "2025-07-15 11:06", "2025-07-15 11:07", "2025-07-15 11:08", "2025-07-15 11:09", "2025-07-15 11:10", "2025-07-15 11:11", "2025-07-15 11:12", "2025-07-15 11:13", "2025-07-15 11:14", "2025-07-15 11:15", "2025-07-15 11:16", "2025-07-15 11:17", "2025-07-15 11:18", "2025-07-15 11:19", "2025-07-15 11:20", "2025-07-15 11:21", "2025-07-15 11:22", "2025-07-15 11:23", "2025-07-15 11:24", "2025-07-15 11:25", "2025-07-15 11:26", "2025-07-15 11:27", "2025-07-15 11:28", "2025-07-15 11:29", "2025-07-15 11:30", "2025-07-15 11:31", "2025-07-15 11:32", "2025-07-15 11:33", "2025-07-15 11:34", "2025-07-15 11:35", "2025-07-15 11:36", "2025-07-15 11:37", "2025-07-15 11:38", "2025-07-15 11:39", "2025-07-15 11:40", "2025-07-15 11:41", "2025-07-15 11:42", "2025-07-15 11:43", "2025-07-15 11:44", "2025-07-15 11:45", "2025-07-15 11:46", "2025-07-15 11:47", "2025-07-15 11:48", "2025-07-15 11:49", "2025-07-15 11:50", "2025-07-15 11:51", "2025-07-15 11:52", "2025-07-15 11:53", "2025-07-15 11:54", "2025-07-15 11:55", "2025-07-15 11:56", "2025-07-15 11:57", "2025-07-15 11:58", "2025-07-15 11:59", "2025-07-15 12:00", "2025-07-15 12:01", "2025-07-15 12:02", "2025-07-15 12:03", "2025-07-15 12:04", "2025-07-15 12:05", "2025-07-15 12:06", "2025-07-15 12:07", "2025-07-15 12:08", "2025-07-15 12:09", "2025-07-15 12:10", "2025-07-15 12:11", "2025-07-15 12:12", "2025-07-15 12:13", "2025-07-15 12:14", "2025-07-15 12:15", "2025-07-15 12:16", "2025-07-15 12:17", "2025-07-15 12:18", "2025-07-15 12:19", "2025-07-15 12:20", "2025-07-15 12:21", "2025-07-15 12:22", "2025-07-15 12:23", "2025-07-15 12:24", "2025-07-15 12:25", "2025-07-15 12:26", "2025-07-15 12:27", "2025-07-15 12:28", "2025-07-15 12:29", "2025-07-15 12:30", "2025-07-15 12:31", "2025-07-15 12:32", "2025-07-15 12:33", "2025-07-15 12:34", "2025-07-15 12:35", "2025-07-15 12:36", "2025-07-15 12:37", "2025-07-15 12:38", "2025-07-15 12:39", "2025-07-15 12:40", "2025-07-15 12:41", "2025-07-15 12:42", "2025-07-15 12:43", "2025-07-15 12:44", "2025-07-15 12:45", "2025-07-15 12:46", "2025-07-15 12:47", "2025-07-15 12:48", "2025-07-15 12:49", "2025-07-15 12:50", "2025-07-15 12:51", "2025-07-15 12:52", "2025-07-15 12:53", "2025-07-15 12:54", "2025-07-15 12:55", "2025-07-15 12:56", "2025-07-15 12:57", "2025-07-15 12:58", "2025-07-15 12:59", "2025-07-15 13:00", "2025-07-15 13:01", "2025-07-15 13:02", "2025-07-15 13:03", "2025-07-15 13:04", "2025-07-15 13:05", "2025-07-15 13:06", "2025-07-15 13:07", "2025-07-15 13:08", "2025-07-15 13:09", "2025-07-15 13:10", "2025-07-15 13:11", "2025-07-15 13:12", "2025-07-15 13:13", "2025-07-15 13:14", "2025-07-15 13:15", "2025-07-15 13:16", "2025-07-15 13:17", "2025-07-15 13:18", "2025-07-15 13:19", "2025-07-15 13:20", "2025-07-15 13:21", "2025-07-15 13:22", "2025-07-15 13:23", "2025-07-15 13:24", "2025-07-15 13:25", "2025-07-15 13:26", "2025-07-15 13:27", "2025-07-15 13:28", "2025-07-15 13:29", "2025-07-15 13:30", "2025-07-15 13:31", "2025-07-15 13:32", "2025-07-15 13:33", "2025-07-15 13:34", "2025-07-15 13:35", "2025-07-15 13:36", "2025-07-15 13:37", "2025-07-15 13:38", "2025-07-15 13:39", "2025-07-15 13:40", "2025-07-15 13:41", "2025-07-15 13:42", "2025-07-15 13:43", "2025-07-15 13:44", "2025-07-15 13:45", "2025-07-15 13:46", "2025-07-15 13:47", "2025-07-15 13:48", "2025-07-15 13:49", "2025-07-15 13:50", "2025-07-15 13:51", "2025-07-15 13:52", "2025-07-15 13:53", "2025-07-15 13:54", "2025-07-15 13:55", "2025-07-15 13:56", "2025-07-15 13:57", "2025-07-15 13:58", "2025-07-15 13:59", "2025-07-15 14:00", "2025-07-15 14:01", "2025-07-15 14:02", "2025-07-15 14:03", "2025-07-15 14:04", "2025-07-15 14:05", "2025-07-15 14:06", "2025-07-15 14:07", "2025-07-15 14:08", "2025-07-15 14:09", "2025-07-15 14:10", "2025-07-15 14:11", "2025-07-15 14:12", "2025-07-15 14:13", "2025-07-15 14:14", "2025-07-15 14:15", "2025-07-15 14:16", "2025-07-15 14:17", "2025-07-15 14:18", "2025-07-15 14:19", "2025-07-15 14:20", "2025-07-15 14:21", "2025-07-15 14:22", "2025-07-15 14:23", "2025-07-15 14:24", "2025-07-15 14:25", "2025-07-15 14:26", "2025-07-15 14:27", "2025-07-15 14:28", "2025-07-15 14:29", "2025-07-15 14:30", "2025-07-15 14:31", "2025-07-15 14:32", "2025-07-15 14:33", "2025-07-15 14:34", "2025-07-15 14:35", "2025-07-15 14:36", "2025-07-15 14:37", "2025-07-15 14:38", "2025-07-15 14:39", "2025-07-15 14:40", "2025-07-15 14:41", "2025-07-15 14:42", "2025-07-15 14:43", "2025-07-15 14:44", "2025-07-15 14:45", "2025-07-15 14:46", "2025-07-15 14:47", "2025-07-15 14:48", "2025-07-15 14:49", "2025-07-15 14:50", "2025-07-15 14:51", "2025-07-15 14:52", "2025-07-15 14:53", "2025-07-15 14:54", "2025-07-15 14:55", "2025-07-15 14:56", "2025-07-15 14:57", "2025-07-15 14:58", "2025-07-15 14:59", "2025-07-15 15:00", "2025-07-15 15:01", "2025-07-15 15:02", "2025-07-15 15:03", "2025-07-15 15:04"], "metric_data": {"series": [{"label": "实时", "metric_val": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 20.5, 1.0, 16.0, 1.0, 13.388889, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.055556, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.388889, 12.0, 11.777778, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 11.214575, 12.692118, 13.835106, 11.793195, 14.014881, 14.663333, 18.899628, 12.087273, 11.688576, 15.246269, 6.773743, 15.06993, 7.734375, 8.266355, 7.666667, 7.448276, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 25.043326, 21.772995, 19.460878, 20.717631, 6.271676, 7.8, 6.987603, 7.553763, 7.713043, 7.684783, 7.635965, 7.68, 7.625, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 6.333333, 5.333333, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 25.481445, 14.964912, 9.062044, 7.162069, 8.490566, 8.88587, 7.547297, 7.850427, 8.0, 8.304348, 8.494565, 7.745536, 7.583333, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 4.666667, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 20.742857, 12.386598, 12.4102, 6.918033, 7.698276, 8.085106, 8.064356, 7.330128, 8.536082, 8.184783, 8.192708, 7.5, 7.46875, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 5.833333, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "val_status": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"label": "环比1天", "metric_val": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 20.5, 1.0, 16.0, 1.0, 13.388889, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 14.055556, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 13.388889, 12.0, 11.777778, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "val_status": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"label": "环比7天", "metric_val": [1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "val_status": [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}], "is_change_err": null, "change_period": true}, "metric_info": {"zhiyan_cal_method": 1, "project_id": 1, "app_mark": 1, "metric_name": "test", "metric_ch_name": "测试", "sec_lvl_name": 1, "env": "prod"}, "param": {"param_sensitive": 5}, "trace_id": 665}