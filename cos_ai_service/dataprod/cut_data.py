# 为了和愚蠢的欧豆豆进行对比


import json

in_txt_path = 'case/n_9e064935-fc5f-45ec-9bcf-3b391b7135be.txt'
out_txt_path = 'case/cut_data.txt'

with open(in_txt_path, 'r') as f:
    data = json.load(f)

# 截取 timestamps
data['timestamps'] = data['timestamps'][:180]

# 处理每个 series 条目
for series in data['metric_data']['series']:
    series['metric_val'] = series['metric_val'][:180]
    if 'val_status' in series:  # 对"环比1天"特殊处理
        series['val_status'] = series['val_status'][:180]

with open(out_txt_path, 'w') as f:
    json.dump(data, f, indent=2, ensure_ascii=False)
