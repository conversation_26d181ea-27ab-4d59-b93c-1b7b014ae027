import numpy as np


# 待优化
def get_nsigma_cal_res(metric_today, low_limit, high_limit, sigma_n):
    # 根据n_sigma原理，反推当前值偏离多少个std，其实应该在计算时就处理。
    # 需要再思考要从偏离的角度，还是从区间的角度实现
    guess_avg = (high_limit + low_limit) / 2
    guess_std = (high_limit - low_limit) / 2 / sigma_n
    if guess_std == 0:
        return -1
    res = abs((metric_today - guess_avg) / guess_std)
    return res


def check_interval(data_dic, field="nsigma"):
    d_dic = data_dic
    metric_today, intervals = d_dic["metric_today"], d_dic["interval"]
    if field not in data_dic["log"]:
        data_dic["log"][field] = [0] * len(metric_today)
    for itname, itvl in intervals.items():
        for i in range(len(itvl["highlimit"])):
            if field and itname != field:
                continue
            if metric_today[i] < itvl["highlimit"][i] and metric_today[i] >= itvl["lowlimit"][i]:
                d_dic["is_change_err"][i] = 0  # 在区间内为正常
                d_dic["log"][field][i] = 0
            else:  # 全段
                d_dic["log"][field][i] = get_nsigma_cal_res(data_dic, field, i,
                                                            data_dic["param"]["param_list"]["param_sigma"])
    # for i in range(): # 仅关注最后的告警点
    #     d_dic["log"][field][i] = get_nsigma_cal_res(data_dic, field, i, data_dic["param"]["param_list"]["param_nsigma"])
    return d_dic


def analysis_nsigma(data_dic, param_sigma, time_window):
    # 考虑计算量
    metric_today, metric_yesterday, metric_lastweek = data_dic["metric_today"], data_dic["metric_yesterday"], data_dic[
        "metric_lastweek"]
    highlimit, lowlimit = [float("inf")] * len(metric_today), [float("-inf")] * len(metric_today)
    twf = data_dic["param"]["param_list"]["time_window_focus"]
    ini_focus = max(0, len(metric_today) - twf)

    avg_yesterday, std_yesterday = float(np.mean(metric_yesterday[ini_focus:])), float(
        np.std(metric_yesterday[ini_focus:]))
    avg_lastweek, std_lastweek = float(np.mean(metric_lastweek[ini_focus:])), float(
        np.std(metric_lastweek[ini_focus:]))

    # 直接做近似计算
    lo_yes, hg_yes = avg_yesterday - param_sigma * std_yesterday, avg_yesterday + param_sigma * std_yesterday
    lo_week, hg_week = avg_lastweek - param_sigma * std_lastweek, avg_lastweek + param_sigma * std_lastweek
    lo_lmt, hg_lmt = min(lo_yes, lo_week), max(hg_yes, hg_week)

    data_dic["log"]["nsigma"] = [0] * len(metric_today)
    for i in range(ini_focus, len(metric_today)):
        if metric_today[i] >= lo_lmt and metric_today[i] <= hg_lmt:
            data_dic["is_change_err"][i] = 0
            data_dic["log"]["nsigma"][i] = -2
        if metric_today[i] < lo_lmt or metric_today[i] > hg_lmt:
            data_dic["log"]["nsigma"][i] = get_nsigma_cal_res(metric_today[i], lo_lmt, hg_lmt,
                                                              data_dic["param"]["param_list"]["param_sigma"])
        lowlimit[i], highlimit[i] = lo_lmt, hg_lmt
    data_dic["interval"]["nsigma"] = {
        "highlimit": highlimit,
        "lowlimit": lowlimit
    }
    return data_dic


def analysis_nsigma_sparse(data_dic, param_sigma, time_window=-1):
    """
    time_window == -1 时不遗忘
    time_window == 0 关闭
    """
    if time_window == 0:
        return data_dic
    metric_tmp = data_dic["metric_lastweek"] + data_dic["metric_yesterday"][:1440]
    report_tmp = (data_dic["origin_data"]["metric_lastweek_report_status"] \
                  + data_dic["origin_data"]["metric_yesterday_report_status"][:1440])
    highlimit, lowlimit = [0] * len(data_dic["metric_today"]), [0] * len(data_dic["metric_today"])

    if time_window == -1:
        time_window = 0
    lo_lmt, hg_lmt = data_dic["metric_today"][0], data_dic["metric_today"][0]
    data_dic["log"]["nsigma"] = [0] * len(data_dic["metric_today"])
    for i in range(len(data_dic["metric_today"])):
        if data_dic["origin_data"]["metric_today_report_status"][i] == 1 and \
                data_dic["origin_data"]["metric_yesterday_report_status"][i] == 1 and \
                data_dic["origin_data"]["metric_lastweek_report_status"][i] == 1:  # 1是没上报
            highlimit[i], lowlimit[i] = highlimit[i - 1], lowlimit[i - 1]
            data_dic["is_change_err"][i] = 0
            continue

        metric_t = metric_tmp + data_dic["metric_today"][:i]
        report_t = report_tmp + data_dic["origin_data"]["metric_today_report_status"][:i]
        metric_valid = []
        for j, val in enumerate(metric_t):
            if report_t[j] == 0:
                metric_valid.append(val)

        if len(metric_valid):
            avg, std = float(np.mean(metric_valid)), float(np.std(metric_valid))
            highlimit[i], lowlimit[i] = avg + param_sigma * std, avg - param_sigma * std
            lo_lmt, hg_lmt = avg - param_sigma * std, avg + param_sigma * std

        if data_dic["origin_data"]["metric_today_report_status"][i] == 1:  # 若当天未上报，则保存上下限，不比较，直接判定为正常，然后跳出
            data_dic["is_change_err"][i] = 0
            continue

        if data_dic["metric_today"][i] >= lo_lmt and data_dic["metric_today"][i] <= hg_lmt:
            data_dic["is_change_err"][i] = 0
            data_dic["log"]["nsigma"][i] = -2
        else:
            val = data_dic["metric_today"][i]
            out_val = get_nsigma_cal_res(data_dic["metric_today"][i], lo_lmt, hg_lmt,
                                         data_dic["param"]["param_list"]["param_sigma"])
            data_dic["log"]["nsigma"][i] = out_val
            avg, std = (lo_lmt + hg_lmt) / 2, (-lo_lmt + hg_lmt) / 2 / param_sigma
            print(
                f"nsigma告警-当前值{val:.3f}-上下限[{lo_lmt:.3f},{hg_lmt:.3f}]-avg:{avg:.3f}-std:{std:.3f}-偏离度{out_val:.3f}")
    data_dic["interval"]["nsigma"] = {  # 用于可视化
        "highlimit": highlimit,
        "lowlimit": lowlimit
    }
    # data_dic = check_interval(data_dic, "nsigma")
    return data_dic

#################################################################
# def analysis_nsigma_v2(data_dic, param_sigma, time_window):
#     # if data_dic["log"]["is_sparse"]:
#     #     return analysis_nsigma_sparse(data_dic, param_sigma, time_window)
#     metric_today, metric_yesterday, metric_lastweek = data_dic["metric_today"], data_dic["metric_yesterday"], data_dic[
#         "metric_lastweek"]
#     # highlimit_yesterday, lowlimit_yesterday = [], []
#     # highlimit_lastweek, lowlimit_lastweek = [], []
#     highlimit, lowlimit = [], []
#     data_dic["log"]["nsigma"] = [0] * len(metric_today)
#     for i in range(len(metric_today)):
#         # 目前用时间窗均值和方差 time_window不得超过24*60
#         start_idx, end_idx = max(0, i - time_window), min(len(metric_today), i + time_window)
#         avg_yesterday, std_yesterday = float(np.mean(metric_yesterday[start_idx:end_idx])), float(
#             np.std(metric_yesterday[start_idx:end_idx]))
#         avg_lastweek, std_lastweek = float(np.mean(metric_lastweek[start_idx:end_idx])), float(
#             np.std(metric_lastweek[start_idx:end_idx]))
#         lo_yes, hg_yes = avg_yesterday - param_sigma * std_yesterday, avg_yesterday + param_sigma * std_yesterday
#         lo_week, hg_week = avg_lastweek - param_sigma * std_lastweek, avg_lastweek + param_sigma * std_lastweek
#         lo_lmt, hg_lmt = min(lo_yes, lo_week), max(hg_yes, hg_week)
#         if metric_today[i] >= lo_lmt and metric_today[i] <= hg_lmt:
#             data_dic["is_change_err"][i] = 0
#             data_dic["log"]["nsigma"][i] = -2
#         if metric_today[i] < lo_lmt or metric_today[i] > hg_lmt:
#             data_dic["log"]["nsigma"][i] = get_nsigma_cal_res(metric_today[i], lo_lmt, hg_lmt,
#                                                               data_dic["param"]["param_list"]["param_sigma"])
#         lowlimit.append(lo_lmt)
#         highlimit.append(hg_lmt)
#         # v1 写的太难看了
#     #     # todo 加权均值 加权方差
#     #     highlimit_yesterday.append(avg_yesterday + param_sigma * std_yesterday)
#     #     lowlimit_yesterday.append(avg_yesterday - param_sigma * std_yesterday)
#     #     highlimit_lastweek.append(avg_lastweek + param_sigma * std_lastweek)
#     #     lowlimit_lastweek.append(avg_lastweek - param_sigma * std_lastweek)
#     #     highlimit.append(max(avg_yesterday + param_sigma * std_yesterday, avg_lastweek + param_sigma * std_lastweek))
#     #     lowlimit.append(min(avg_yesterday - param_sigma * std_yesterday, avg_lastweek - param_sigma * std_lastweek))
#     data_dic["interval"]["nsigma"] = {
#         "highlimit": highlimit,
#         "lowlimit": lowlimit
#     }
#     # data_dic = check_interval(data_dic, "nsigma")
#     return data_dic
