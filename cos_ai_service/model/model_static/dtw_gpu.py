import numpy as np
from numba import cuda
import math
import torch
import time
import numba

max_val = 1e5


@cuda.jit
def dtw_cuda_batch_kernel(x_batch, y_batch, cost_matrices, batch_size, max_n, max_m, x_lengths, y_lengths):
    """
    批处理DTW CUDA kernel
    """
    # 获取批次索引
    batch_idx = cuda.blockIdx.z
    i = cuda.blockIdx.x * cuda.blockDim.x + cuda.threadIdx.x
    j = cuda.blockIdx.y * cuda.blockDim.y + cuda.threadIdx.y

    if batch_idx >= batch_size:
        return

    n = x_lengths[batch_idx]
    m = y_lengths[batch_idx]

    if i >= n or j >= m:
        return

    # 计算当前位置的距离
    if i < n and j < m:
        dist = (x_batch[batch_idx, i] - y_batch[batch_idx, j]) ** 2

        if i == 0 and j == 0:
            cost_matrices[batch_idx, i, j] = dist
        elif i == 0:
            cost_matrices[batch_idx, i, j] = dist + cost_matrices[batch_idx, i, j - 1]
        elif j == 0:
            cost_matrices[batch_idx, i, j] = dist + cost_matrices[batch_idx, i - 1, j]
        else:
            # 防止溢出检查
            min_cost = min(
                cost_matrices[batch_idx, i - 1, j],
                cost_matrices[batch_idx, i, j - 1],
                cost_matrices[batch_idx, i - 1, j - 1]
            )
            max_val = 1e10  # 定义最大值
            if min_cost < max_val - dist:
                cost_matrices[batch_idx, i, j] = dist + min_cost
            else:
                cost_matrices[batch_idx, i, j] = max_val


def dtw_cuda_batch(x_batch, y_batch, max_length=10000):
    """
    批处理DTW计算主函数
    
    Args:
        x_batch: 批次序列列表 (list of numpy arrays)
        y_batch: 批次序列列表 (list of numpy arrays)
        max_length: 最大序列长度限制
    
    Returns:
        DTW距离列表
    """
    import time
    import math
    import numpy as np
    from numba import cuda

    start_t = time.time()

    # 输入验证
    if len(x_batch) != len(y_batch):
        raise ValueError("批次大小不匹配")

    batch_size = len(x_batch)
    if batch_size == 0:
        return []

    # 获取最大长度用于填充
    max_n = max(len(x) for x in x_batch)
    max_m = max(len(y) for y in y_batch)

    if max_n > max_length or max_m > max_length:
        raise ValueError(f"序列长度超过限制: {max_length}")

    # 记录实际长度
    x_lengths = np.array([len(x) for x in x_batch], dtype=np.int32)
    y_lengths = np.array([len(y) for y in y_batch], dtype=np.int32)

    # 创建填充后的批次数组
    x_padded = np.zeros((batch_size, max_n), dtype=np.float32)
    y_padded = np.zeros((batch_size, max_m), dtype=np.float32)

    # 填充数据
    for i, (x, y) in enumerate(zip(x_batch, y_batch)):
        x_padded[i, :len(x)] = x.astype(np.float32)
        y_padded[i, :len(y)] = y.astype(np.float32)

    # 转移到GPU 可能默认0？
    x_gpu = cuda.to_device(x_padded)
    y_gpu = cuda.to_device(y_padded)
    x_lengths_gpu = cuda.to_device(x_lengths)
    y_lengths_gpu = cuda.to_device(y_lengths)

    # 初始化代价矩阵
    cost_matrices = np.full((batch_size, max_n, max_m), np.inf, dtype=np.float32)
    cost_matrices_gpu = cuda.to_device(cost_matrices)

    # 配置CUDA网格和块 - 添加批次维度
    threads_per_block = (8, 8, 1)  # 减少每个块的线程数以适应3D网格
    blocks_per_grid_x = math.ceil(max_n / threads_per_block[0])
    blocks_per_grid_y = math.ceil(max_m / threads_per_block[1])
    blocks_per_grid_z = batch_size
    blocks_per_grid = (blocks_per_grid_x, blocks_per_grid_y, blocks_per_grid_z)

    # 分阶段计算，避免竞争条件
    for phase in range(max_n + max_m - 1):
        dtw_cuda_batch_kernel[blocks_per_grid, threads_per_block](
            x_gpu, y_gpu, cost_matrices_gpu, batch_size, max_n, max_m, x_lengths_gpu, y_lengths_gpu
        )
        cuda.synchronize()

    # 获取结果
    result_matrices = cost_matrices_gpu.copy_to_host()

    # 提取每个批次的DTW距离
    dtw_distances = []
    max_val = 1e10

    for i in range(batch_size):
        n = x_lengths[i]
        m = y_lengths[i]
        final_cost = result_matrices[i, n - 1, m - 1]

        if final_cost == np.inf or final_cost >= max_val:
            dtw_distances.append(max_val)
        else:
            dtw_distances.append(math.sqrt(final_cost))

    return dtw_distances


@cuda.jit  # 核函数 重要 gpu
def dtw_cuda_kernel(x, y, result, n, m):
    """
    CUDA kernel for DTW computation
    """
    # 使用共享内存优化
    shared_size = 32  # 根据GPU内存调整

    # 获取线程索引 # 用于多线程
    tx = cuda.threadIdx.x
    ty = cuda.threadIdx.y
    bx = cuda.blockIdx.x
    by = cuda.blockIdx.y

    # 计算全局索引
    i = bx * cuda.blockDim.x + tx
    j = by * cuda.blockDim.y + ty

    # 边界检查
    if i >= n or j >= m:
        return

    # 分配共享内存用于存储部分结果 # 目的是多线程访问重复数据效率
    shared_prev = cuda.shared.array((33, 33), float32)
    shared_curr = cuda.shared.array((33, 33), float32)

    # 初始化
    if i == 0 and j == 0:
        result[0, 0] = 0.0
    elif i == 0 or j == 0:
        result[i, j] = max_val

    cuda.syncthreads()

    # 按对角线计算，避免竞争条件
    for diag in range(n + m - 1):
        if i + j == diag and i < n and j < m and i > 0 and j > 0:
            # 计算距离
            dist = (x[i - 1] - y[j - 1]) ** 2

            # 获取前面的值，使用原子操作确保数据一致性
            prev_i_j = result[i - 1, j - 1] if i > 0 and j > 0 else max_val
            prev_i_1 = result[i - 1, j] if i > 0 else max_val
            prev_j_1 = result[i, j - 1] if j > 0 else max_val

            # 防止溢出：使用较小的值进行比较
            min_prev = min(prev_i_j, prev_i_1, prev_j_1)

            # 检查溢出
            if min_prev != max_val and dist < max_val - min_prev:
                result[i, j] = dist + min_prev
            else:
                result[i, j] = max_val

        cuda.syncthreads()


@cuda.jit  # 核函数 重要 gpu
def dtw_cuda_optimized_kernel(x, y, cost_matrix, n, m):
    """
    优化的CUDA kernel，使用滑动窗口减少内存使用
    """
    i = cuda.blockIdx.x * cuda.blockDim.x + cuda.threadIdx.x
    j = cuda.blockIdx.y * cuda.blockDim.y + cuda.threadIdx.y

    if i >= n or j >= m:
        return

    # 计算当前位置的距离
    if i < n and j < m:
        dist = (x[i] - y[j]) ** 2

        if i == 0 and j == 0:
            cost_matrix[i, j] = dist
        elif i == 0:
            cost_matrix[i, j] = dist + cost_matrix[i, j - 1]
        elif j == 0:
            cost_matrix[i, j] = dist + cost_matrix[i - 1, j]
        else:
            # 防止溢出检查
            min_cost = min(cost_matrix[i - 1, j], cost_matrix[i, j - 1], cost_matrix[i - 1, j - 1])
            if min_cost < max_val - dist:
                cost_matrix[i, j] = dist + min_cost
            else:
                cost_matrix[i, j] = max_val


def dtw_cuda(x, y, max_length=10000):
    """
    使用CUDA计算DTW距离的主函数

    Args:
        x: 第一个序列 (numpy array)
        y: 第二个序列 (numpy array)
        max_length: 最大序列长度限制，防止内存溢出

    Returns:
        DTW距离
    """
    start_t = time.time()
    # 输入验证
    if len(x) == 0 or len(y) == 0 or len(x) > max_length or len(y) > max_length:
        raise ValueError(f"序列长度不符合要求")

    n, m = len(x), len(y)

    # 转换为float32减少内存使用
    x_gpu = cuda.to_device(x.astype(np.float32))  # 转不转32的必要性，转去gpu是必要的
    y_gpu = cuda.to_device(y.astype(np.float32))

    # 初始化代价矩阵
    cost_matrix = np.full((n, m), np.inf, dtype=np.float32)
    cost_matrix_gpu = cuda.to_device(cost_matrix)

    # 配置CUDA网格和块 # 用于提高GPU的流多处理器sm的利用率，避免县丞浪费
    threads_per_block = (16, 16)
    blocks_per_grid_x = math.ceil(n / threads_per_block[0])
    blocks_per_grid_y = math.ceil(m / threads_per_block[1])
    blocks_per_grid = (blocks_per_grid_x, blocks_per_grid_y)

    t1 = time.time() - start_t

    # 分阶段计算，避免竞争条件
    for phase in range(n + m - 1):
        dtw_cuda_optimized_kernel[blocks_per_grid, threads_per_block](
            x_gpu, y_gpu, cost_matrix_gpu, n, m
        )
        cuda.synchronize()

    # 获取结果
    result_matrix = cost_matrix_gpu.copy_to_host()

    # 返回DTW距离
    final_cost = result_matrix[n - 1, m - 1]
    if final_cost == np.inf:
        return max_val

    t2 = time.time() - t1
    return math.sqrt(final_cost)


# 测试
def test_dtw(seqlen=1000):
    """
    Test function for DTW CUDA implementation
    """
    timings = {}
    # Create test sequences
    x = np.random.random(seqlen).astype(np.float32)
    y = np.random.random(seqlen).astype(np.float32)
    z = np.random.random(seqlen).astype(np.float32)

    # Compute DTW distance
    numba_start = time.time()
    distance_xy = dtw_cuda(x, y)
    distance_xz = dtw_cuda(x, z)
    distance = min(distance_xy, distance_xz)
    timings['numba'] = time.time() - numba_start

    return timings, distance


def test_dtw_batch(seqlen=240, batch_size=100):
    """
    Test function for batch DTW CUDA implementation
    """
    timings = {}
    # Create test sequences
    x_batch = [np.random.random(seqlen).astype(np.float32) for _ in range(batch_size)]
    y_batch = [np.random.random(seqlen).astype(np.float32) for _ in range(batch_size)]

    # Compute DTW distances
    numba_start = time.time()
    distances = dtw_cuda_batch(x_batch, y_batch)
    timings['numba'] = time.time() - numba_start

    return timings, distances
