import cupy as cp

from trpc import task
import asyncio


# 考虑到时间复杂度，相似性的计算，不遍历所有历史，只计算当前时间窗，若小于一定的阈值，则一定是误告
def check_threshold_rooftop(data_dic, field, thre_rooftop, idx):
    t = data_dic["curve"]["low"][field]
    if "DTW" not in data_dic["log"]:
        data_dic["log"]["DTW"] = [0] * len(t)
    for i in range(len(t)):
        if t[i] < thre_rooftop:  # 比喻成rooftop屋顶
            data_dic["is_change_err"][i] = 0  # 不高于rooftop为正常
        else:  # 高于阈值（异常）则记录dtw值 格式为data_dic["log"]["DTW"][i]
            data_dic["log"]["DTW"][i] = t[i]
    # tt = sum(data_dic["log"]["DTW"])
    return data_dic


async def analysis_DTW(ctx, data):
    """分析DTW"""

    param_list = data["param"]["param_list"]
    time_window = int(param_list["time_window_dtw"])
    time_window_focus = int(param_list["time_window_focus"])
    metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], \
        data['smooth']['ewma'][
            "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
    if "DTW" not in data["log"]:
        data["log"]["DTW"] = [0] * len(metric_today)

    ini_focus = max(0, len(metric_today) - time_window_focus)
    start = max(0, len(metric_today) - time_window) if time_window != -1 else len(
        metric_today) - ini_focus

    slice_yesterday, slice_week = await asyncio.gather(
        task.send_to_task(ctx, "task_test", {"x": metric_today[start:], "y": metric_yesterday[start:]}),
        # dtw_cuda(s_t, s_y)
        task.send_to_task(ctx, "task_test", {"x": metric_today[start:], "y": metric_lastweek[start:]})
        # dtw_cuda(s_t, s_l)
    )
    slice = min(slice_yesterday, slice_week)
    thre = param_list["param_dtw"]
    if slice < thre:
        data["is_change_err"][-1] = 0
    else:
        data["log"]["DTW"][-1] = slice
    data["curve"]["low"]["DTW"] = slice
    return data


async def analysis_D2(ctx, data):
    dtw_yes, dtw_week = await asyncio.gather(
        task.send_to_task(ctx, "task_test", {"x": data['downsample']["metric_today"], "y": data['downsample'][
            "metric_yesterday"]}),
        task.send_to_task(ctx, "task_test",
                          {"x": data['downsample']["metric_today"], "y": data['downsample']["metric_lastweek"]})
    )
    dtw_all = min(dtw_yes, dtw_week)
    param_list = data["param"]["param_list"]
    thre_low, thre_high = param_list["param_dtw_low"], param_list["param_dtw_high"]
    need_alarm = "模糊区间"
    if dtw_all < thre_low:
        data["is_change_err"][-1] = 0
        need_alarm = "不告警"
    if dtw_all > thre_high:
        data["is_change_err"][-1] = 1
        need_alarm = "强制告警"
    data["log"]["DTW_all"] = f"兼顾时效，差异度{dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]，{need_alarm}"
    return data


async def analysis_D3(ctx, data):
    metric_today, metric_yesterday, metric_lastweek = data['downsample']["metric_today"], data['downsample'][
        "metric_yesterday"], data['downsample']["metric_lastweek"]
    data["is_change_err"] = [0] * len(data["is_change_err"])
    # 计算最近的相似度是否发生变化
    time_window = 300
    time_window = int(time_window / 4320 * data["param"]["param_list"]["time_window_dtw"])  # 注意降采样了
    l_to, l_yes, l_last = data['downsample']["metric_today"][-time_window:], data['downsample'][
                                                                                 "metric_yesterday"][-time_window:], \
        data['downsample']["metric_lastweek"][-time_window:]

    dtw_all_yes, dtw_all_last, dtw_yes, dtw_last = await asyncio.gather(
        task.send_to_task(ctx, "task_test", {"x": metric_today, "y": metric_yesterday}),
        task.send_to_task(ctx, "task_test", {"x": metric_today, "y": metric_lastweek}),
        task.send_to_task(ctx, "task_test", {"x": l_to, "y": l_yes}),
        task.send_to_task(ctx, "task_test", {"x": l_to, "y": l_last})
    )
    dtw_all = min(dtw_all_yes, dtw_all_last)
    dtw_l = min(dtw_yes, dtw_last)
    print("dtw_最近:", dtw_l)
    param_list = data["param"]["param_list"]
    thre_low, thre_high = param_list["param_dtw_low"], param_list["param_dtw_high"]
    if dtw_l > thre_low and dtw_all > thre_high:  # 若最近的差异度高，并且差异度不是周期偏移导致的（总体），告警
        data["is_change_err"][-1] = 1

    data["log"]["DTW_all"] = f"仅关注相似，{dtw_l:.3f}, {dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]"
    return data


################################这不是我的黑历史，这是我的来时路########################################
# def analysis_DTW_gpu_v2(data, thre, time_window=24 * 60, time_window_focus=3):
#     # gpu实现
#     metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], data['smooth']['ewma'][
#         "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
#     if "DTW" not in data["log"]:
#         data["log"]["DTW"] = [0] * len(metric_today)
#
#     ini_focus = max(0, len(metric_today) - time_window_focus)
#     start = max(0, len(metric_today) - time_window) if time_window != -1 else len(metric_today) - ini_focus
#
#     s_t = np.array(metric_today[start:], dtype=np.float32)
#     s_y = np.array(metric_yesterday[start:], dtype=np.float32)
#     s_l = np.array(metric_lastweek[start:], dtype=np.float32)
#     slice_yesterday = dtw_cuda(s_t, s_y)
#     slice_week = dtw_cuda(s_t, s_l)
#     slice = min(slice_yesterday, slice_week)
#
#     if slice < thre:
#         data["is_change_err"][-1] = 0
#     else:
#         data["log"]["DTW"][-1] = slice
#     data["curve"]["low"]["DTW"] = slice
#     return data
#
#
# def analysis_DTW_gpu_v1(data, thre, time_window=24 * 60, time_window_focus=3):
#     # gpu版本，但是三方库实现不合理，导致计算没有加快
#     metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], data['smooth']['ewma'][
#         "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
#     if "DTW" not in data["log"]:
#         data["log"]["DTW"] = [0] * len(metric_today)
#
#     ini_focus = max(0, len(metric_today) - time_window_focus)
#     start = max(0, len(metric_today) - time_window) if time_window != -1 else len(metric_today) - ini_focus
#
#     # tslearn h5py==3.11.0
#     import torch
#     from tslearn.metrics import dtw
#     start_t = time.time()
#     device = torch.device("cuda")  # if torch.cuda.is_available() else "cpu"
#     s_t = torch.tensor(metric_today[start:], dtype=torch.float32).reshape(-1, 1).to(device)
#     s_y = torch.tensor(metric_yesterday[start:], dtype=torch.float32).reshape(-1, 1).to(device)
#     s_l = torch.tensor(metric_lastweek[start:], dtype=torch.float32).reshape(-1, 1).to(device)
#     slice_yesterday = dtw(s_t, s_y, be="pytorch")  # 1440
#     slice_week = dtw(s_t, s_l, be="pytorch")
#     slice = min(slice_yesterday, slice_week)
#
#     if slice < thre:
#         data["is_change_err"][-1] = 0
#     else:
#         data["log"]["DTW"][-1] = slice
#     data["curve"]["low"]["DTW"] = slice
#     return data
#
#
# def analysis_DTW_cpu_v1(data, thre, time_window=24 * 60, time_window_focus=3):
#     from dtaidistance import dtw
#     # 加速策略：窗口 剪枝
#     metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], data['smooth']['ewma'][
#         "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
#     if "DTW" not in data["log"]:
#         data["log"]["DTW"] = [0] * len(metric_today)
#
#     ini_focus = max(0, len(metric_today) - time_window_focus)
#     start = max(0, len(metric_today) - time_window) if time_window != -1 else len(metric_today) - ini_focus
#     # 处理周期一天以内的波形相似问题
#     slice_yesterday = dtw.distance(metric_today[start:], metric_yesterday[start:])
#     slice_week = dtw.distance(metric_today[start:], metric_lastweek[start:])
#     slice = min(slice_yesterday, slice_week)
#
#     if slice < thre:
#         data["is_change_err"][-1] = 0
#     else:
#         data["log"]["DTW"][-1] = slice
#     data["curve"]["low"]["DTW"] = slice
#     return data
#
#
# def analysis_DTW_v0(data, thre, time_window=24 * 60, time_window_focus=3):
#     from dtaidistance import dtw
#     metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], data['smooth']['ewma'][
#         "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
#     slice_yesterday, slice_week, slice = [0] * len(metric_today), [0] * len(metric_today), [0] * len(metric_today)
#     ini_focus = max(0, len(metric_today) - time_window_focus)
#
#     if "DTW" not in data["log"]:
#         data["log"]["DTW"] = [0] * len(metric_today)
#
#     for i in range(ini_focus, len(metric_today)):
#         start = max(0, i - time_window) if time_window != -1 else i - ini_focus
#         end = i + 1
#         # 处理周期一天以内的波形相似问题
#         slice_yesterday[i] = dtw.distance(metric_today[start: end], metric_yesterday[start:end])
#         slice_week[i] = dtw.distance(metric_today[start: end], metric_lastweek[start: end])
#         slice[i] = min(slice_yesterday[i], slice_week[i])
#
#         #
#         if slice[i] < thre:
#             data["is_change_err"][i] = 0
#         else:
#             data["log"]["DTW"][i] = slice[i]
#     data["curve"]["low"]["DTW"] = slice
#     # data = check_threshold_rooftop(data, "DTW", thre, ini_focus)
#     return data
#
#
# def analysis_D2_cpu(data, thre_low, thre_high):
#     from dtaidistance import dtw
#     # 针对变更期间 计算整段
#     metric_today, metric_yesterday, metric_lastweek = data['downsample']["metric_today"], data['downsample'][
#         "metric_yesterday"], data['downsample']["metric_lastweek"]
#     # metric_today, metric_yesterday, metric_lastweek = data["metric_today"], data[
#     #     "metric_yesterday"], data["metric_lastweek"]
#     dtw_all = min(dtw.distance(metric_today, metric_yesterday), dtw.distance(metric_today, metric_lastweek))
#     need_alarm = "模糊区间"
#     if dtw_all < thre_low:
#         data["is_change_err"][-1] = 0
#         need_alarm = "不告警"
#     if dtw_all > thre_high:
#         data["is_change_err"][-1] = 1
#         need_alarm = "强制告警"
#     data["log"]["DTW_all"] = f"兼顾时效，差异度{dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]，{need_alarm}"
#     return data
#
#
# def analysis_D3_cpu(data, thre_low, thre_high):
#     from dtaidistance import dtw
#     # 针对日常 仅关注time_window时效内
#     metric_today, metric_yesterday, metric_lastweek = data['downsample']["metric_today"], data['downsample'][
#         "metric_yesterday"], data['downsample']["metric_lastweek"]
#     dtw_all = min(dtw.distance(metric_today, metric_yesterday), dtw.distance(metric_today, metric_lastweek))
#     data["is_change_err"] = [0] * len(data["is_change_err"])
#     # 计算最近的相似度是否发生变化
#     time_window = 300
#     time_window = int(time_window / 4320 * data["param"]["param_list"]["time_window_dtw"])  # 注意降采样了
#     l_to, l_yes, l_last = data['downsample']["metric_today"][-time_window:], data['downsample'][
#                                                                                  "metric_yesterday"][-time_window:], \
#         data['downsample']["metric_lastweek"][-time_window:]
#
#     l_to = np.array(l_to, dtype=np.float32)
#     l_yes = np.array(l_yes, dtype=np.float32)
#     l_last = np.array(l_last, dtype=np.float32)
#     dtw_l = min(dtw_cuda(l_to, l_yes), dtw_cuda(l_to, l_last))
#
#     print("dtw_最近:", dtw_l)
#     if dtw_l > thre_low and dtw_all > thre_high:  # 若最近的差异度高，并且差异度不是周期偏移导致的（总体），告警
#         data["is_change_err"][-1] = 1
#
#     data["log"]["DTW_all"] = f"仅关注相似，{dtw_l:.3f}, {dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]"
#     return data
#
#
# def analysis_D2_gpu_v1(data, thre_low, thre_high):
#     # 针对变更期间 计算整段
#     metric_today, metric_yesterday, metric_lastweek = data['downsample']["metric_today"], data['downsample'][
#         "metric_yesterday"], data['downsample']["metric_lastweek"]
#
#     metric_today = np.array(metric_today, dtype=np.float32)
#     metric_yesterday = np.array(metric_yesterday, dtype=np.float32)
#     metric_lastweek = np.array(metric_lastweek, dtype=np.float32)
#
#     dtw_all = min(dtw_cuda(metric_today, metric_yesterday), dtw_cuda(metric_today, metric_lastweek))
#     need_alarm = "模糊区间"
#     if dtw_all < thre_low:
#         data["is_change_err"][-1] = 0
#         need_alarm = "不告警"
#     if dtw_all > thre_high:
#         data["is_change_err"][-1] = 1
#         need_alarm = "强制告警"
#     data["log"]["DTW_all"] = f"兼顾时效，差异度{dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]，{need_alarm}"
#     return data
#

def analysis_cos_sim(data, thre):
    """
    用于整体分析。易误报，用于筛选
    thre = -1 关闭
    """
    if thre == -1:
        return data

    def optimized_cos_sim(x, y):
        x, y = cp.array(x), cp.array(y)
        xy_dot = cp.dot(x, y)
        x_norm = cp.linalg.norm(x)
        y_norm = cp.linalg.norm(y)
        return xy_dot / (x_norm * y_norm + 1e-10)

    sim_ab = optimized_cos_sim(data["metric_today"], data["metric_yesterday"])
    sim_ac = optimized_cos_sim(data["metric_today"], data["metric_lastweek"])

    similarity_normal = cp.any(cp.array([sim_ab, sim_ac]) >= thre)
    data["log"]["cos_sim"] = max(sim_ab, sim_ac)  # 取相似度最高

    if similarity_normal.item():  # 整体趋势相似
        data["is_change_err"][-1] = 0
        pass  # 记得还原
    return data
