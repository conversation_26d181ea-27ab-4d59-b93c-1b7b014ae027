import json
import math

import pandas as pd


def get_txt_data(txt_path):
    with open(txt_path, 'r') as file:
        data = file.read()
    return json.loads(data)


def get_norm_alpha(list_list_data):
    min_val, max_val = math.inf, -math.inf
    for list_data in list_list_data:
        min_val, max_val = min(min_val, min(list_data)), max(max_val, max(list_data))
    return min_val, max_val


def norm_list(min_val, max_val, list_list_data):
    for i, list_data in enumerate(list_list_data):
        list_list_data[i] = [(val - min_val) / (max_val - min_val) if val != 0 else 0 for val in list_data]
    return list_list_data


def is_sparse(data_dic, param_sparse=0.95):
    # 1 表示未上报
    series = data_dic["metric_data"].series
    mtoday, mt_report = series[0].metric_val, series[0].val_status
    if len(mt_report) == 0:  # 没带上报数据的字段，少数情况，直接判定为稀疏
        return True, -1  # -1 表示上报数据为空
    report_num = len(mt_report) - sum(mt_report)
    report_ratio = report_num / max(1, len(mtoday))
    if report_ratio > param_sparse:
        return False, report_ratio
    return True, report_ratio


def data_preprocess(data):
    param_percent = data["param"].param_list.param_percent
    series = data["metric_data"].series
    metric_today, metric_yesterday, metric_lastweek = series[0].metric_val, series[1].metric_val, series[2].metric_val
    mtoday_report, myesterday_report, mlastweek_report = series[0].val_status, series[1].val_status, series[
        2].val_status

    # 以天为周期，百分比内允许波动 这个好像没用 想办法干掉
    fluctuate_val = param_percent * (max(max(metric_lastweek), max(metric_today[:24 * 60])) \
                                     - min(min(metric_lastweek), min(metric_today[:24 * 60])))
    highlimit_day, lowlimit_day = [max(fluctuate_val, val + fluctuate_val) for val in metric_yesterday] \
        , [max(0, val - fluctuate_val) for val in metric_yesterday]  # 正常错误码最小值大于0
    highlimit_week, lowlimit_week = [max(fluctuate_val, val + fluctuate_val) for val in metric_lastweek] \
        , [max(0, val - fluctuate_val) for val in metric_lastweek]
    # 周期偏移 这个好像没用 想办法干掉
    offset_time = 3  # 偏移不得超过1天（24*60）
    offset_highlimit_day, offset_lowlimit_day, offset_highlimit_week, offset_lowlimit_week = [], [], [], []
    for i in range(len(metric_today)):
        strtidx, endidx = max(0, i - offset_time), min(i + offset_time, len(metric_today))
        offset_highlimit_day.append(max(metric_yesterday[strtidx:endidx]))
        offset_lowlimit_day.append(min(metric_yesterday[strtidx:endidx]))
        offset_highlimit_week.append(max(metric_lastweek[strtidx:endidx]))
        offset_lowlimit_week.append(min(metric_lastweek[strtidx:endidx]))

    minval, maxval = get_norm_alpha([metric_today, highlimit_day, lowlimit_day, highlimit_week, lowlimit_week])
    list_list_data = [metric_today, metric_yesterday, metric_lastweek \
        , highlimit_day, lowlimit_day, highlimit_week, lowlimit_week \
        , offset_highlimit_day, offset_lowlimit_day, offset_highlimit_week, offset_lowlimit_week]
    list_list_data = norm_list(minval, maxval, list_list_data)

    issparse, report_ratio = is_sparse(data, param_sparse=data["param"].param_list_sparse.param_sparse)
    change_period = data["metric_data"].change_period
    data_dic = {
        "trace_id": data["trace_id"],
        "timestamps": list(data["timestamps"]),
        "change_period": data["metric_data"].change_period if data["metric_data"].change_period != None else False,
        # todo
        "origin_data": {
            "metric_today": list(metric_today),
            "metric_today_report_status": list(series[0].val_status),
            "metric_yesterday": list(metric_yesterday),
            "metric_yesterday_report_status": list(series[1].val_status),
            "metric_lastweek": list(metric_lastweek),
            "metric_lastweek_report_status": list(series[2].val_status)
        },
        "metric_today": list_list_data[0],
        "metric_yesterday": list_list_data[1],
        "metric_lastweek": list_list_data[2],
        "interval": {  # 区间型 好像没用到 可以干掉说不定
            "day": {
                "highlimit": list_list_data[3],
                "lowlimit": list_list_data[4]
            },
            "week": {
                "highlimit": list_list_data[5],
                "lowlimit": list_list_data[6]
            },
            "offset_day": {
                "highlimit": list_list_data[7],
                "lowlimit": list_list_data[8]
            },
            "offset_week": {
                "highlimit": list_list_data[9],
                "lowlimit": list_list_data[10]
            },
        },
        "curve": {"high": {}, "low": {}},  # 干掉 暂时没想好数值型的怎么统一格式
        "is_change_err": [0] * (len(metric_today) - 1) + [1],  # 0 正常 历史遗留问题，为了提高计算效率，实际上只对最后一个点进行观测
        "log": {
            "is_sparse": issparse,
            "report_ratio": report_ratio,
            "in_valid_interval": in_valid_interval(metric_today, data["param"].param_valid_interval.param_lolmt,
                                                   data["param"].param_valid_interval.param_hglmt)
        },
        "param": {
            "param_list": {
                "param_sparse": data["param"].param_list.param_sparse,
                "param_percent": data["param"].param_list.param_percent,
                "param_smooth": data["param"].param_list.param_smooth,
                "param_dtw": data["param"].param_list.param_dtw,
                "param_downsample": data["param"].param_list.param_downsample,
                "param_dtw_low": data["param"].param_list.param_dtw_low,
                "param_dtw_high": data["param"].param_list.param_dtw_high,
                "param_sigma": data["param"].param_list.param_sigma,
                "param_cos_sim": data["param"].param_list.param_cos_sim,
                "time_window_focus": data["param"].param_list.time_window_focus,
                "time_window_dtw": data["param"].param_list.time_window_dtw,
                "time_window_nsigma": data["param"].param_list.time_window_nsigma
            },
            "param_valid_interval": {
                "param_lolmt": data["param"].param_valid_interval.param_lolmt,
                "param_hglmt": data["param"].param_valid_interval.param_hglmt
            },
            "param_list_sparse": {
                "param_sparse": data["param"].param_list_sparse.param_sparse,
                "param_percent": data["param"].param_list_sparse.param_percent,
                "param_smooth": data["param"].param_list_sparse.param_smooth,
                "param_dtw": data["param"].param_list_sparse.param_dtw,
                "param_downsample": data["param"].param_list_sparse.param_downsample,
                "param_dtw_low": data["param"].param_list_sparse.param_dtw_low,
                "param_dtw_high": data["param"].param_list_sparse.param_dtw_high,
                "param_sigma": data["param"].param_list_sparse.param_sigma,
                "param_cos_sim": data["param"].param_list_sparse.param_cos_sim,
                "time_window_focus": data["param"].param_list_sparse.time_window_focus,
                "time_window_dtw": data["param"].param_list_sparse.time_window_dtw,
                "time_window_nsigma": data["param"].param_list_sparse.time_window_nsigma
            },
            "param_list_no_change": {
                "param_sparse": data["param"].param_list_no_change.param_sparse,
                "param_percent": data["param"].param_list_no_change.param_percent,
                "param_smooth": data["param"].param_list_no_change.param_smooth,
                "param_dtw": data["param"].param_list_no_change.param_dtw,
                "param_downsample": data["param"].param_list_no_change.param_downsample,
                "param_dtw_low": data["param"].param_list_no_change.param_dtw_low,
                "param_dtw_high": data["param"].param_list_no_change.param_dtw_high,
                "param_sigma": data["param"].param_list_no_change.param_sigma,
                "param_cos_sim": data["param"].param_list_no_change.param_cos_sim,
                "time_window_focus": data["param"].param_list_no_change.time_window_focus,
                "time_window_dtw": data["param"].param_list_no_change.time_window_dtw,
                "time_window_nsigma": data["param"].param_list_no_change.time_window_nsigma
            }
        }
    }
    data_dic = check_report_status(data_dic)
    return data_dic


def smooth_ewma(data_dic, param_smooth=0.3, time_window=24 * 60, time_window_focus=3):
    # 考虑计算量
    # dtw只关注time_window内数据，则只对该部分数据做去噪
    metric_today, metric_yesterday, metric_lastweek = data_dic["metric_today"], data_dic["metric_yesterday"], data_dic[
        "metric_lastweek"]

    time_window_ewma = 3 * 60 + time_window + time_window_focus
    ini_focus = max(0, len(metric_today) - time_window_ewma)

    series_to, series_yes, series_l = pd.Series(metric_today[ini_focus:]), pd.Series(
        metric_yesterday[ini_focus:]), pd.Series(metric_lastweek[ini_focus:])
    sm_to, sm_yes, sm_l = series_to.ewm(span=param_smooth).mean().tolist(), series_yes.ewm(
        span=param_smooth).mean().tolist(), series_l.ewm(span=param_smooth).mean().tolist()

    t = time_window + time_window_focus
    data_dic["smooth"] = {
        "ewma": {
            "metric_today": metric_today[:-t] + sm_to[-t:],
            "metric_yesterday": metric_yesterday[:-t] + sm_yes[-t:],
            "metric_lastweek": metric_lastweek[:-t] + sm_l[-t:]
        }
    }
    return data_dic


def smooth_ewma_v1(data_dic, param_smooth=0.3, time_window_focus=3 * 60):
    d_dic = data_dic
    metric_today, metric_yesterday, metric_lastweek = d_dic["metric_today"], d_dic["metric_yesterday"], d_dic[
        "metric_lastweek"]
    smooth_today, smooth_yesterday, smooth_lastweek = [0] * len(metric_today), [0] * len(metric_lastweek), [0] * len(
        metric_lastweek)
    time_window_focus = -1
    ini_focus = max(0, len(metric_today) - time_window_focus) if time_window_focus != -1 else 0
    for i in range(ini_focus):
        smooth_today[i] = metric_today[i]
        smooth_yesterday[i] = metric_yesterday[i]
        smooth_lastweek[i] = metric_lastweek[i]

    # 设平滑因子（alpha）为0.3
    # span = 2 / (1 - alpha)
    series_today, series_yesterday, series_lastweek = pd.Series(metric_today[ini_focus:]), pd.Series(
        metric_yesterday[ini_focus:]), pd.Series(metric_lastweek[ini_focus:])
    sm_today, sm_yesterday, sm_lastweek = series_today.ewm(span=param_smooth).mean(), series_yesterday.ewm(
        span=param_smooth).mean(), series_lastweek.ewm(span=param_smooth).mean()

    for i in range(ini_focus, len(metric_today)):
        smooth_today[i] = float(sm_today[i - ini_focus])
        smooth_yesterday[i] = float(sm_yesterday[i - ini_focus])
        smooth_lastweek[i] = float(sm_lastweek[i - ini_focus])
    d_dic["smooth"] = {
        "ewma": {
            "metric_today": smooth_today,
            "metric_yesterday": smooth_yesterday,
            "metric_lastweek": smooth_lastweek
        }
    }
    return d_dic


def check_report_status(data_dic):
    for i in range(len(data_dic["origin_data"]["metric_today_report_status"])):
        if data_dic["origin_data"]["metric_today_report_status"][i] == 1:  # 1 表示未上报
            data_dic["is_change_err"][i] = 0
    return data_dic


def check_interval(data_dic, field=None):
    d_dic = data_dic
    metric_today, intervals = d_dic["metric_today"], d_dic["interval"]
    for itname, itvl in intervals.items():
        for i in range(len(itvl["highlimit"])):
            if field and itname != field:
                continue
            if metric_today[i] < itvl["highlimit"][i] and metric_today[i] >= itvl["lowlimit"][i]:
                d_dic["is_change_err"][i] = 0  # 在区间内为正常
    return d_dic


def timeseries_list2str(timestamps, is_change_err_list):
    is_change_err_str = []
    for i, ts in enumerate(timestamps):
        if is_change_err_list[i] == 1:
            is_change_err_str.append(ts)
    return is_change_err_str


def in_valid_interval(metric_today, lolmt, hglmt):
    for val in metric_today[-3:]:
        if lolmt >= -9007199254740000 and hglmt <= 9007199254740000 and val > lolmt and val < hglmt:
            return True
        if lolmt >= -9007199254740000 and val > lolmt:
            return True
        if hglmt <= 9007199254740000 and val < hglmt:
            return True
    return False


def downsample(data, target_len):
    # step = max(1, len(data["metric_today"]) // target_len)
    # data["downsample"] = {
    #     "metric_today": data["metric_today"][::step],
    #     "metric_yesterday": data["metric_yesterday"][::step],
    #     "metric_lastweek": data["metric_lastweek"][::step]
    # }
    # return data

    def ds(d, target_len):
        if target_len >= len(d):
            return data

        res = []
        len_interval = len(d) / target_len
        for i in range(target_len):
            start, end = int(i * len_interval), int((i + 1) * len_interval)
            if i == target_len - 1:
                end = len(d)
            # avg = np.mean(d[start:end])
            # res.append(avg)
            res.append(max(d[start:end]))
        return res

    data["downsample"] = {
        "metric_today": ds(data["metric_today"], target_len),
        "metric_yesterday": ds(data["metric_yesterday"], target_len),
        "metric_lastweek": ds(data["metric_lastweek"], target_len)
    }
    return data
