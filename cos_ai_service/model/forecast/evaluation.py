import matplotlib.pyplot as plt
import numpy as np


def eval_errors1(y_true, y_pred):
    mean = np.mean(y_pred[:len(y_true)])
    std = np.std(y_pred[:len(y_true)])

    y2u = y_pred + 4.5 * std
    y2l = y_pred - 4.5 * std

    y3u = y_pred + 3 * std
    y3l = y_pred - 3 * std
    return (y2u, y2l), (y3u, y3l)


def eval_errors(y_true, y_pred):
    res = y_pred[:len(y_true)] - y_true
    mean = np.mean(res)
    std = np.std(res)

    y2u = y_pred + mean + 2 * std
    y2l = y_pred + mean - 2 * std

    y3u = y_pred + mean + 3 * std
    y3l = y_pred + mean - 3 * std
    return (y2u, y2l), (y3u, y3l)


def eval_errors_std(y_pred, std):
    y2u = y_pred + 2 * std
    y2l = y_pred - 2 * std

    y3u = y_pred + 3 * std
    y3l = y_pred - 3 * std
    return (y2u, y2l), (y3u, y3l)


def plot_forecast_pred_std(val_series, pred_series, std, path_output):
    pred_series = pred_series[:len(val_series)]
    plt.figure(figsize=(16, 8))
    fig = plt.subplot(111)
    fig.grid(True, which='major', c='gray', ls='-', lw=1, alpha=0.2)
    fig.plot(val_series, label="test series", color="green", alpha=0.8)
    fig.plot(pred_series, label="forecast series", color="red")

    (y2u, y2l), (y3u, y3l) = eval_errors_std(pred_series, std)

    # 确保 x 轴索引与 y 轴数据匹配
    idx = np.arange(len(pred_series))
    fig.fill_between(idx, y2l, y2u, color="#0072B2", alpha=0.5, label="N-sigma")
    fig.fill_between(idx, y3l, y3u, color="#0072B2", alpha=0.2, label="3-sigma")

    # 标出超出范围的点
    out_of_bounds_idx = np.where((val_series > y2u) | (val_series < y2l))[0]
    fig.scatter(idx[out_of_bounds_idx], val_series[out_of_bounds_idx], color='red', label='out of N-sigma')

    out_of_bounds_idx_3sigma = np.where((val_series > y3u) | (val_series < y3l))[0]
    fig.scatter(idx[out_of_bounds_idx_3sigma], val_series[out_of_bounds_idx_3sigma], color='darkred',
                label='out of 3-sigma')

    box = fig.get_position()
    fig.set_position([box.x0, box.y0, box.width * 0.9, box.height])
    fig.legend(loc="upper left", bbox_to_anchor=(1, 0.5))
    plt.savefig(path_output, bbox_inches='tight', dpi=300)


def plot_forecast(series, val_series, pred_series, path_output):
    size = len(series)
    vsize = len(val_series)
    n_steps = len(pred_series)
    idx = np.arange(size + n_steps)

    plt.figure(figsize=(16, 8))
    fig = plt.subplot(111)
    fig.grid(True, which='major', c='gray', ls='-', lw=1, alpha=0.2)
    fig.axvline(idx[size], linestyle="dotted")
    fig.plot(idx[:size], series, label="train series", color="blue", alpha=0.8)
    fig.plot(idx[size:size + vsize], val_series, label="test series", color="green", alpha=0.8)
    fig.plot(idx[size:], pred_series, label="forecast series", color="red")

    (y2u, y2l), (y3u, y3l) = eval_errors(val_series, pred_series)
    fig.fill_between(idx[size:], y2l, y2u, color="#0072B2", alpha=0.5, label="2-sigma")
    fig.fill_between(idx[size:], y3l, y3u, color="#0072B2", alpha=0.2, label="3-sigma")

    # 标出超出范围的点
    out_of_bounds_idx = np.where((val_series > y2u) | (val_series < y2l))[0]
    fig.scatter(idx[size:size + vsize][out_of_bounds_idx], val_series[out_of_bounds_idx], color='red',
                label='out of 2-sigma')
    out_of_bounds_idx_3sigma = np.where((val_series > y3u) | (val_series < y3l))[0]
    fig.scatter(idx[size:size + vsize][out_of_bounds_idx_3sigma], val_series[out_of_bounds_idx_3sigma], color='darkred',
                label='out of 3-sigma')

    box = fig.get_position()
    fig.set_position([box.x0, box.y0, box.width * 0.9, box.height])
    fig.legend(loc="upper left", bbox_to_anchor=(1, 0.5))
    plt.savefig(path_output, bbox_inches='tight', dpi=300)

    # plt.show()


def plot_forecast_pred(val_series, pred_series, path_output):
    pred_series = pred_series[:len(val_series)]
    plt.figure(figsize=(16, 8))
    fig = plt.subplot(111)
    fig.grid(True, which='major', c='gray', ls='-', lw=1, alpha=0.2)
    fig.plot(val_series, label="test series", color="green", alpha=0.8)
    fig.plot(pred_series, label="forecast series", color="red")

    (y2u, y2l), (y3u, y3l) = eval_errors(val_series, pred_series)

    # 确保 x 轴索引与 y 轴数据匹配
    idx = np.arange(len(pred_series))
    fig.fill_between(idx, y2l, y2u, color="#0072B2", alpha=0.5, label="N-sigma")
    fig.fill_between(idx, y3l, y3u, color="#0072B2", alpha=0.2, label="3-sigma")

    # 标出超出范围的点
    out_of_bounds_idx = np.where((val_series > y2u) | (val_series < y2l))[0]
    fig.scatter(idx[out_of_bounds_idx], val_series[out_of_bounds_idx], color='red', label='out of N-sigma')

    out_of_bounds_idx_3sigma = np.where((val_series > y3u) | (val_series < y3l))[0]
    fig.scatter(idx[out_of_bounds_idx_3sigma], val_series[out_of_bounds_idx_3sigma], color='darkred',
                label='out of 3-sigma')

    box = fig.get_position()
    fig.set_position([box.x0, box.y0, box.width * 0.9, box.height])
    fig.legend(loc="upper left", bbox_to_anchor=(1, 0.5))
    plt.savefig(path_output, bbox_inches='tight', dpi=300)
