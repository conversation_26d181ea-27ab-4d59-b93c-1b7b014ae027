from sympy import false

from model.data_preprocess import data_preprocess, smooth_ewma, check_interval, timeseries_list2str, \
    in_valid_interval
from model.model_static.similarity import analysis_DTW, analysis_cos_sim, analysis_D2, analysis_D3
from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse
from model.data_postprocess import get_abnormal_points
import time
from trpc.metrics import report_attr, report_custom
from trpc.trpc_abc.metrics import StatPolicy, StatValue, CustomData
import os
from trpc.log import logger
from trpc.server import Context
from view.hello.stub.trpc_test_rpchttp import pb

from model.model_static.similarity import analysis_cos_sim
from model.data_postprocess import get_abnormal_msg
import json

from model.data_preprocess import downsample
from trpc import task

import asyncio
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import asyncio


def log_cpu_usage(step_name):
    import psutil
    print(f"{step_name}, CPU USG: {psutil.cpu_percent(interval=1)}")


async def algo_no_sparse(ctx, lp, data):
    in_change_period = data["change_period"]

    # # 测试
    # in_change_period = True
    if not in_change_period:
        logger.error("判定为稀疏数据")
        param_list = data["param"]["param_list_no_change"]
        future_nsigma = lp.run_in_executor(None, analysis_nsigma, data, param_list["param_sigma"],
                                           int(param_list["time_window_nsigma"]))
        future_downsample = lp.run_in_executor(None, downsample, data, int(param_list["param_downsample"]))
        data.update(await future_nsigma)
        data.update(await future_downsample)
        data = await analysis_D3(ctx, data)
    else:
        param_list = data["param"]["param_list"]
        # 所有任务共享事件循环的默认线程池，线程数量为默认配置。如果要资源隔离的独立线程池，可以使用显式ThreadPoolExecutor
        future_smooth = lp.run_in_executor(None, smooth_ewma, data, param_list["param_smooth"],
                                           int(param_list["time_window_dtw"]),
                                           int(param_list["time_window_focus"]))
        future_nsigma = lp.run_in_executor(None, analysis_nsigma, data, param_list["param_sigma"],
                                           int(param_list["time_window_nsigma"]))
        future_downsample = lp.run_in_executor(None, downsample, data, int(param_list["param_downsample"]))
        data.update(await future_smooth)
        data = await analysis_DTW(ctx, data)
        data.update(await future_downsample)
        data = await analysis_D2(ctx, data)
        data.update(await future_nsigma)

    return data


async def algo_sparse(ctx, lp, data):
    # todo 优化
    param_list = data["param"]["param_list_sparse"]
    future_nsigma = lp.run_in_executor(None, analysis_nsigma_sparse, data, param_list["param_sigma"],
                                       int(param_list["time_window_nsigma"]))
    future_downsample = lp.run_in_executor(None, downsample, data, int(param_list["param_downsample"]))
    data.update(await future_downsample)
    data.update(await future_nsigma)
    data = await analysis_D2(ctx, data)
    return data


async def change_analysis(ctx, data):
    # # 在这获取七彩石配置失败
    # from cos_ai_service.view.hello.config import global_conf
    # rainbow_config = global_conf()  # 获取七彩石参数配置
    print(f"[PID] 当前进程 ID: {os.getpid()}")

    is_prod_env = True  # false 显示所有的异常点

    trace_id = data["trace_id"]
    logmsg = f"trace_id: {trace_id}\n"
    start, prestop = time.time(), time.time()

    # # 默认参数 本地写死
    # if not data["param"].HasField("param_list"):
    #     param_list=pb.OMParamList(
    #                         param_percent=0.15,
    #                         param_smooth=8,
    #                         param_dtw=0.18,
    #                         param_sigma=3,
    #                         param_cos_sim=0.35, # -1 关闭
    #                         time_window_focus=3,
    #                         time_window_dtw=240, # 10
    #                         time_window_nsigma=45
    #                 )
    #     data["param"].param_list.CopyFrom(param_list)
    # if not data["param"].HasField("param_valid_interval"):
    #     param_valid_interval = pb.OMInterval(
    #                         param_window_interval=60,
    #                         param_lolmt=10,
    #                         param_hglmt=9007199254740000
    #     )
    #     data["param"].param_valid_interval.CopyFrom(param_valid_interval)

    # data = data_preprocess(data) # 0617 采用批处理 由于函数入参需要可序列化格式 因此将预处理函数转移到上层的ChangeAnal中

    start_t, prestop = time.time(), time.time()

    lp = asyncio.get_running_loop()
    data = await lp.run_in_executor(None, data_preprocess, data)
    # import cProfile
    import pstats

    if not data["log"]["in_valid_interval"]:
        logmsg += "不在生效区间\n"
        data["is_change_err"] = [0] * len(data["is_change_err"])  # 0个异常点
    else:
        if data["log"]["is_sparse"]:
            data = await algo_sparse(ctx, lp, data)
        else:
            # profiler = cProfile.Profile()
            # profiler.enable()

            data = await algo_no_sparse(ctx, lp, data)

            # profiler.disable()
            # stats = pstats.Stats(profiler).sort_stats('cumtime')  # 按累计时间排序
            # stats.print_stats(20)

    # 检查异常点
    is_change_err = timeseries_list2str(data["timestamps"],
                                        data["is_change_err"])  # 输出异常时间戳 ["2024-4-19-00:00","2024-5...",...]
    abn_points = get_abnormal_points(data, is_prod_env)  # 只返回关注区间内的报错点

    logmsg += "总耗时: {:.3f}s\n".format(time.time() - start)
    logmsg += str(get_abnormal_msg(data, len(data["is_change_err"]) - 1))
    # logger.info(f'计算耗时:{time.time() - start}')

    ctx = Context()
    logger.with_context_fields(ctx, {"trace_id": data["trace_id"]})
    logger.info_context(ctx, logmsg)

    # 监控宝
    # 属性上报
    report_attr("测试输出随机数", 666, StatPolicy.MAX)
    # 自定义上报
    cluster = os.environ.get("CLUSTER_ID", "cluster")
    namespace = os.environ.get("POD_NAMESPACE", "namespace")
    pod = os.environ.get("POD_NAME", "pod")
    if pod != 'pod':
        tapp = pod.rsplit('-', 1)[0]
    else:
        tapp = 'TApp'
    bid = 1
    service = "变更质检AI"
    tag_set = {
        'cluster': cluster,
        'namespace': namespace,
        'TApp': tapp,
        'pod': pod,
        'bid': bid,
        'service': service,
    }
    dims = ["post_process_err", "algo_err", "data_smooth_err", "pre_process_err", "data_read_err", "ttl_time"]
    stat_vals = []
    post_process_err = StatValue(value=0, policy=StatPolicy.MAX, count=1)
    algo_err = StatValue(value=0, policy=StatPolicy.MAX, count=1)
    data_smooth_err = StatValue(value=0, policy=StatPolicy.MAX, count=1)
    pre_process_err = StatValue(value=0, policy=StatPolicy.MAX, count=1)
    data_read_err = StatValue(value=0, policy=StatPolicy.MAX, count=1)
    ttl_time = StatValue(value=time.time() - start, policy=StatPolicy.MAX, count=1)
    stat_vals.append(post_process_err)
    stat_vals.append(algo_err)
    stat_vals.append(data_smooth_err)
    stat_vals.append(pre_process_err)
    stat_vals.append(data_read_err)
    stat_vals.append(ttl_time)
    data = CustomData(name=service, dimensions=dims, stat_values=stat_vals)
    setattr(data, "tag", tag_set)

    report_custom(data)

    return abn_points, logmsg
