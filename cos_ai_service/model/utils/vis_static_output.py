import os

import matplotlib.pyplot as plt


# import pywt


def check_interval(data_dic, field=None):
    for title, d_dic in data_dic.items():
        metric_today, intervals = d_dic["metric_today"], d_dic["interval"]
        for itname, itvl in intervals.items():
            for i in range(len(itvl["highlimit"])):
                if field and itname != field:
                    continue
                if metric_today[i] < itvl["highlimit"][i] and metric_today[i] >= itvl["lowlimit"][i]:
                    d_dic["abnormal_today"][i] = 0  # 在区间内为正常
    return data_dic


def vis_dtw_threshold(d_dic, dtw_val, output_path, figure_size=(10, 5), img_name=""):
    os.makedirs(output_path, exist_ok=True)
    alpha = 0.5
    field = "dtw"
    # 左图
    fig, ax = plt.subplots(1, 2, figsize=(2 * figure_size[0], figure_size[1]))
    ax[0].plot(d_dic['metric_today'], label="today")
    ax[0].plot(d_dic['metric_yesterday'], label="yesterday", alpha=alpha)
    ax[0].plot(d_dic["metric_lastweek"], label="lastweek", alpha=alpha)
    # ax[0].set_ylim(0, 1)
    ax[0].legend()

    # 右图
    ax[1].plot(d_dic['metric_today'], label="today")
    ax[1].plot(dtw_val, label=field, alpha=alpha)

    # isfirst = True
    # for i in range(len(d_dic['metric_today'])):
    #     if d_dic['is_change_err'][i] == 1:  # 异常
    #         if isfirst:
    #             ax[1].plot(i, d_dic['metric_today'][i], 'o', color='purple')
    #             isfirst = False
    #         else:
    #             ax[1].plot(i, d_dic['metric_today'][i], 'o', color='red')

    ax[1].legend()
    # ax[1].set_ylim(0, 1)
    plt.grid()
    plt.tight_layout()
    os.makedirs(output_path, exist_ok=True)
    plt.savefig(f'{output_path}/dtw_{img_name}.png')


if __name__ == '__main__':
    plt.rcParams['font.sans-serif'] = ['Hiragino Sans GB']
    time_window = 10
    figure_size = (10, 5)
    metric_data_path = '/data/workspace/cos_ai_service/view/stress_test/metric_data0.txt'
    output_path = '/data/workspace/cos_ai_service/waste/output/static/dtw'

    # 测试dtw、vis_dtw_threshold
    from vis_data_preprocess import get_txt_data, data_preprocess
    from model.data_preprocess import smooth_ewma
    from model.model_static.similarity import analysis_DTW

    data = get_txt_data(metric_data_path)
    param_list = data["param"]["param_list"]
    data = data_preprocess(data, param_list["param_percent"])
    data = smooth_ewma(data, param_smooth=param_list["param_smooth"])
    data = analysis_DTW(data, thre=param_list["param_dtw"], time_window=int(param_list["time_window_dtw"]),
                        time_window_focus=-1)  # 关注窗口为-1时，标出三天所有告警点
    vis_dtw_threshold(data, output_path, comb_mode=False)


def vis_nsigma(d_dic, lollimit, highlimit, output_path, figure_size, img_name):
    os.makedirs(output_path, exist_ok=True)
    alpha = 0.5
    plt.figure(figsize=figure_size)
    plt.plot(d_dic['metric_today'], label="today", alpha=alpha)
    plt.plot(d_dic['metric_yesterday'], label="yesterday", alpha=alpha)
    plt.plot(d_dic["metric_lastweek"], label="lastweek", alpha=alpha)
    plt.fill_between(range(len(d_dic['metric_today'])), highlimit,
                     lollimit \
                     , label='boundary', alpha=alpha)
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/nsigma_{img_name}.png')


def vis_input_data(d_dic, output_path, figure_size=(10, 5), img_name=""):
    plt.title("最终报错点")
    xlabel, ylabel = 'time', 'val'
    plt.title("")
    plt.figure(figsize=figure_size)
    plt.plot(d_dic['metric_data']['series'][0]['metric_val'], label="today当前")
    plt.plot(d_dic['metric_data']['series'][1]['metric_val'], label="yesterday同比(前一天)")
    plt.plot(d_dic['metric_data']['series'][2]['metric_val'], label="lastweek同比(前一周)")
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/input_{img_name}.png')


def vis_err_points(d_dic, output_path, figure_size=(10, 5), img_name=""):
    plt.title("最终报错点")
    xlabel, ylabel = 'time', 'val'
    plt.title("")
    plt.figure(figsize=figure_size)
    plt.plot(d_dic['metric_today'], label="today当前")
    plt.plot(d_dic['metric_yesterday'], label="yesterday同比(前一天)")
    plt.plot(d_dic["metric_lastweek"], label="lastweek同比(前一周)")

    # 标注异常点
    err_indices = [i for i, val in enumerate(d_dic["is_change_err"]) if val == 1]
    err_values = [d_dic['metric_today'][i] for i in err_indices]
    plt.scatter(err_indices, err_values, color='red', label="abn_points", zorder=5)
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/res_{img_name}.png')


def vis_err_points_45(d_dic, output_path, figure_size=(10, 5)):
    output_path = f"{output_path}/45"
    title = "final_abn_45"
    xlabel, ylabel = '时间', '归一化值'

    num_images = ((len(d_dic['metric_today']) + 44) // 45)
    isfirst = True
    for j in range(num_images):
        start_idx = j * 45
        end_idx = min(len(d_dic['metric_today']), start_idx + 45)
        x = list(range(start_idx, end_idx))

        plt.figure(figsize=figure_size)
        plt.title(title)

        plt.plot(x, d_dic['metric_today'][start_idx:end_idx], label="today当前")
        plt.plot(x, d_dic['metric_yesterday'][start_idx:end_idx], label="yesterday同比(前一天)")
        plt.plot(x, d_dic["metric_lastweek"][start_idx:end_idx], label="lastweek同比(前一周)")

        for i in range(start_idx, end_idx):
            if d_dic['is_change_err'][i] == 1:  # 异常
                if isfirst:
                    plt.plot(i, d_dic['metric_today'][i], 'o', color='purple')
                    isfirst = False
                else:
                    plt.plot(i, d_dic['metric_today'][i], 'o', color='red')

        for iname, itvl in d_dic["interval"].items():
            plt.fill_between(range(start_idx, end_idx), itvl["highlimit"][start_idx: end_idx],
                             itvl['lowlimit'][start_idx: end_idx] \
                             , label='safe_interval', color='green', alpha=0.5)
        plt.plot(x, d_dic["curve"]["low"]["DTW"][start_idx: end_idx], label="dtw", alpha=0.5)

        plt.xlabel(xlabel)
        plt.ylabel(ylabel)
        plt.ylim(0, 1)
        plt.legend()
        plt.grid()
        plt.tight_layout()
        os.makedirs(output_path, exist_ok=True)
        plt.savefig(f'{output_path}/{title}_{j}.png')
        plt.close()
        # break


def vis_3curve_2interval(data_dic, output_path):
    alpha = 0.5
    xlabel, ylabel = '时间', '归一化值'
    for title, d_dic in data_dic.items():
        plt.figure(figsize=figure_size)
        plt.plot(d_dic['metric_today'], label="当前", alpha=alpha)
        plt.plot(d_dic['metric_yesterday'], label="同比(前一天)", alpha=alpha)
        plt.plot(d_dic["metric_lastweek"], label="同比(前一周)", alpha=alpha)
        plt.fill_between(range(len(d_dic['highlimit_day'])), d_dic['highlimit_day'], d_dic['lowlimit_day'] \
                         , label='boundary', alpha=alpha)
        plt.fill_between(range(len(d_dic['highlimit_week'])), d_dic['highlimit_week'], d_dic['lowlimit_week'] \
                         , label='boundary', alpha=alpha)

        # for i in range(len(d_dic['metric_today'])):
        #     if d_dic['metric_today'][i] < d_dic['highlimit_day'][i] and d_dic['metric_today'][i] > d_dic['lowlimit_day'][i]:
        #         continue
        #     if d_dic['metric_today'][i] < d_dic['highlimit_week'][i] and d_dic['metric_today'][i] > d_dic['lowlimit_week'][i]:
        #         continue
        #     plt.plot(i, d_dic['metric_today'][i], 'o', color='red')

        plt.title(title)
        plt.xlabel(xlabel)
        # plt.xticks(ticks=range(len(d_dic['metric_today'])))
        plt.ylabel(ylabel)
        # plt.ylim(0, 1)
        plt.legend()
        plt.grid()
        plt.tight_layout()
        plt.savefig(f'{output_path}/{title}.png')


def vis_3curve_2curve(data_dic, output_path, field):
    alpha = 0.5
    xlabel, ylabel = '时间', '归一化值'
    for title, d_dic in data_dic.items():
        plt.figure(figsize=figure_size)
        plt.plot(d_dic['metric_today'], label="当前", alpha=alpha)
        plt.plot(d_dic['metric_yesterday'], label="同比(前一天)", alpha=alpha)
        plt.plot(d_dic["metric_lastweek"], label="同比(前一周)", alpha=alpha)
        # plt.fill_between(range(len(d_dic['highlimit_day'])), d_dic['highlimit_day'], d_dic['lowlimit_day']\
        #                  , label = 'boundary', alpha=alpha)
        # plt.fill_between(range(len(d_dic['highlimit_week'])), d_dic['highlimit_week'], d_dic['lowlimit_week']\
        #                  , label='boundary', alpha=alpha)
        plt.plot(d_dic['curve_yesterday'], label=f"{field}(前一天)", alpha=alpha)
        plt.plot(d_dic['curve_lastweek'], label=f"{field}(前一周)", alpha=alpha)

        # for i in range(len(d_dic['metric_today'])):
        #     if d_dic['metric_today'][i] < d_dic['highlimit_day'][i] and d_dic['metric_today'][i] > d_dic['lowlimit_day'][i]:
        #         continue
        #     if d_dic['metric_today'][i] < d_dic['highlimit_week'][i] and d_dic['metric_today'][i] > d_dic['lowlimit_week'][i]:
        #         continue
        #     plt.plot(i, d_dic['metric_today'][i], 'o', color='red')

        plt.title(title)
        plt.xlabel(xlabel)
        # plt.xticks(ticks=range(len(d_dic['metric_today'])))
        plt.ylabel(ylabel)
        # plt.ylim(0, 1)
        plt.legend()
        plt.grid()
        plt.tight_layout()
        plt.savefig(f'{output_path}/{title}.png')


def vis_3curve_1curve(data_dic, output_path, field):
    alpha = 0.5
    xlabel, ylabel = '时间', '归一化值'
    for title, d_dic in data_dic.items():
        plt.figure(figsize=figure_size)
        plt.plot(d_dic['metric_today'], label="当前")
        plt.plot(d_dic['metric_yesterday'], label="同比(前一天)", alpha=alpha)
        plt.plot(d_dic["metric_lastweek"], label="同比(前一周)", alpha=alpha)
        # plt.fill_between(range(len(d_dic['highlimit_day'])), d_dic['highlimit_day'], d_dic['lowlimit_day']\
        #                  , label = 'boundary', alpha=alpha)
        # plt.fill_between(range(len(d_dic['highlimit_week'])), d_dic['highlimit_week'], d_dic['lowlimit_week']\
        #                  , label='boundary', alpha=alpha)
        plt.plot(d_dic['curve'], label=field, alpha=alpha)

        # for i in range(len(d_dic['metric_today'])):
        #     if d_dic['metric_today'][i] < d_dic['highlimit_day'][i] and d_dic['metric_today'][i] > d_dic['lowlimit_day'][i]:
        #         continue
        #     if d_dic['metric_today'][i] < d_dic['highlimit_week'][i] and d_dic['metric_today'][i] > d_dic['lowlimit_week'][i]:
        #         continue
        #     plt.plot(i, d_dic['metric_today'][i], 'o', color='red')

        plt.title(title)
        plt.xlabel(xlabel)
        # plt.xticks(ticks=range(len(d_dic['metric_today'])))
        plt.ylabel(ylabel)
        # plt.ylim(0, 1)
        plt.legend()
        plt.grid()
        plt.tight_layout()
        os.makedirs(output_path, exist_ok=True)
        plt.savefig(f'{output_path}/{title}.png')
