"""
原告警，满足以下任一条件则告警：
1. 向上波动大于1%，且历史值==0，告警收敛30分钟，防抖动配置5周期（分钟）累计5次
2. 向下波动超过500%，且当前值>=100，告警收敛30分钟，防抖动配置1周期（分钟）累计1次
3. 向上波动超过500%，且当前值>=100，告警收敛30分钟，防抖动配置1周期（分钟）累计1次
"""


def algo0(data, path_output=None):
    m_to, m_yes = data["metric_today"], data["metric_yesterday"]
    data["is_change_err"] = [0] * len(m_to)

    cnt = 0
    for i in range(1, len(m_to)):
        # 1. 向上波动大于1%，且昨天同一时刻为0，累计5次则告警
        if m_yes[i] == 0 and (m_to[i] - m_to[i - 1]) / (m_to[i - 1] + 1e-9) > 0.01:
            cnt += 1
            if cnt >= 5:
                data["is_change_err"][i] = 1
        else:
            cnt = 0

        # 2. 向下波动超过500%，且累计超过100次
        if m_to[i] >= 100 and (m_to[i - 1] - m_to[i]) / (m_to[i - 1] + 1e-9) > 5:
            data["is_change_err"][i] = 1

        # 3. 向上波动超过500%，且累计超过100次
        if m_to[i] >= 100 and (m_to[i] - m_to[i - 1]) / (m_to[i - 1] + 1e-9) > 5:
            data["is_change_err"][i] = 1

    return data
