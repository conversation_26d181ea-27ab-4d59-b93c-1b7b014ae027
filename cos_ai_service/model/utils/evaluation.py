# 对比时，各告警均不设置告警收敛和防抖动

import matplotlib.pyplot as plt

import os
from vis_data_preprocess import get_txt_data, data_preprocess, vis_origin_data
from vis_prod_data import extract_path_info
from default import figure_size
from algo0 import algo0
from vis_prod_data import change_analysis
from tqdm import tqdm


def vis_err_points(data, list_errs, output_path, figname, figure_size):
    """
    list_errs每个元素都是list，每个元素的长度与data["metric_today"]一致，若list_errs[i][j] == 1，表示算法i认为数据点j是异常点
    用不同颜色点标出不同算法认定的异常点
    """
    plt.figure(figsize=figure_size)
    plt.plot(data["metric_today"], label="today", alpha=0.5)
    plt.plot(data["metric_yesterday"], label="yesterday", alpha=0.5)
    plt.plot(data["metric_lastweek"], label="lastweek", alpha=0.5)

    colors = ['red', 'orange', 'purple']
    for idx, errs in enumerate(list_errs):
        err_idx = [i for i, v in enumerate(errs) if v == 1]
        err_val = [data["metric_today"][i] for i in err_idx]
        plt.scatter(err_idx, err_val, color=colors[idx % len(colors)], marker='o', label=f'err_algo_{idx}', alpha=0.6)

    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/{figname}.png')


if __name__ == '__main__':
    check_err_time, err_time = False, "2025-03-24 10:08:00"
    metric_data_dir = '/data/workspace/cos_ai_service/dataprod/avg_delay/case1'
    output_path = '/data/workspace/cos_ai_service/waste/output/'

    for root, dirs, files in os.walk(metric_data_dir):
        for t in tqdm(files):
            metric_data_path = os.path.join(root, t)
            if os.path.isfile(metric_data_path):
                # 数据预处理
                metric_data = get_txt_data(metric_data_path)
                dir_name, img_name = extract_path_info(metric_data_path)
                vis_origin_data(metric_data, output_path + f'/origin_data/{dir_name}', figure_size, img_name)
                metric_data = data_preprocess(metric_data)

                list_errs = []
                # 各个算法
                list_errs.append(algo0(metric_data)["is_change_err"])
                list_errs.append(change_analysis(metric_data, metric_data_path, output_path)["is_change_err"])

                # 可视化
                vis_err_points(metric_data, list_errs, output_path + '/comparison', img_name, figure_size)
