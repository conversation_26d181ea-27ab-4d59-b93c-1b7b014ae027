import time

import cupy as cp
from dtaidistance import dtw


# 生产环境的代码，实际上可视化效果不好，只能看见最后一个点的dtw数值
def check_threshold_rooftop(data_dic, field, thre_rooftop, idx):
    t = data_dic["curve"]["low"][field]
    if "DTW" not in data_dic["log"]:
        data_dic["log"]["DTW"] = [0] * len(t)
    for i in range(len(t)):
        if t[i] < thre_rooftop:  # 比喻成rooftop屋顶
            data_dic["is_change_err"][i] = 0  # 不高于rooftop为正常
        else:  # 高于阈值（异常）则记录dtw值 格式为data_dic["log"]["DTW"][i]
            data_dic["log"]["DTW"][i] = t[i]
    # tt = sum(data_dic["log"]["DTW"])
    return data_dic


def analysis_DTW(data):
    """分析DTW"""

    param_list = data["param"]["param_list"]
    time_window = int(param_list["time_window_dtw"])
    time_window_focus = int(param_list["time_window_focus"])
    thre = param_list["param_dtw"]

    metric_today, metric_yesterday, metric_lastweek = data['smooth']['ewma']["metric_today"], \
        data['smooth']['ewma'][
            "metric_yesterday"], data['smooth']['ewma']["metric_lastweek"]
    slice_yesterday, slice_week, slice = [0] * len(metric_today), [0] * len(metric_today), [0] * len(metric_today)
    ini_focus = max(0, len(metric_today) - time_window_focus)
    for i in range(ini_focus, len(metric_today)):
        start = max(0, i - time_window) if time_window != -1 else i - ini_focus
        end = i + 1
        # 处理周期一天以内的波形相似问题
        slice_yesterday[i] = dtw.distance(metric_today[start: end], metric_yesterday[start:end])
        slice_week[i] = dtw.distance(metric_today[start: end], metric_lastweek[start: end])
        slice[i] = min(slice_yesterday[i], slice_week[i])

    data["curve"]["low"]["DTW"] = slice
    data = check_threshold_rooftop(data, "DTW", thre, ini_focus)
    return data


def analysis_D2(data):
    dtw_yes, dtw_week = dtw.distance(data['downsample']["metric_today"], data['downsample']["metric_yesterday"]) \
        , dtw.distance(data['downsample']["metric_today"], data['downsample']["metric_lastweek"])
    dtw_all = min(dtw_yes, dtw_week)
    param_list = data["param"]["param_list"]
    thre_low, thre_high = param_list["param_dtw_low"], param_list["param_dtw_high"]
    need_alarm = "模糊区间"
    if dtw_all < thre_low:
        data["is_change_err"][-1] = 0
        need_alarm = "不告警"
    if dtw_all > thre_high:
        data["is_change_err"][-1] = 1
        need_alarm = "强制告警"
    data["log"]["DTW_all"] = f"dtw_all: {dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]，{need_alarm}"
    return data


def analysis_D3(data):
    metric_today, metric_yesterday, metric_lastweek = data['downsample']["metric_today"], data['downsample'][
        "metric_yesterday"], data['downsample']["metric_lastweek"]
    data["is_change_err"] = [0] * len(data["is_change_err"])
    # 计算最近的相似度是否发生变化
    time_window = 300
    time_window = int(time_window / 4320 * data["param"]["param_list"]["time_window_dtw"])  # 注意降采样了
    l_to, l_yes, l_last = data['downsample']["metric_today"][-time_window:], data['downsample'][
                                                                                 "metric_yesterday"][-time_window:], \
        data['downsample']["metric_lastweek"][-time_window:]

    dtw_all_yes, dtw_all_last, dtw_yes, dtw_last = \
        dtw.distance(metric_today, metric_yesterday), \
            dtw.distance(metric_today, metric_lastweek), \
            dtw.distance(l_to, l_yes), \
            dtw.distance(l_to, l_last)

    dtw_all = min(dtw_all_yes, dtw_all_last)
    dtw_l = min(dtw_yes, dtw_last)
    print("dtw_最近:", dtw_l)
    param_list = data["param"]["param_list"]
    thre_low, thre_high = param_list["param_dtw_low"], param_list["param_dtw_high"]
    if dtw_l > thre_low and dtw_all > thre_high:  # 若最近的差异度高，并且差异度不是周期偏移导致的（总体），告警
        data["is_change_err"][-1] = 1

    data["log"]["DTW_all"] = f"仅关注相似，{dtw_l:.3f}, {dtw_all:.3f}，参数:[{thre_low:.3f}, {thre_high:.3f}]"
    return data
