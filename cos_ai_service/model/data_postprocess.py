from tensorboard.manager import start


def get_abnormal_msg(data_dic, i):
    log = data_dic["log"]
    ice = data_dic["is_change_err"]
    if not log["in_valid_interval"]:  # 不在生效区间：
        return "[智能告警] 不在生效区间，无告警"
    try:
        msg_in_change = "关注时效" if data_dic["change_period"] else "仅关注相似性"
    except:
        msg_in_change = ""
    try:
        report_ratio, is_sparse, param_sparse = data_dic["log"]["report_ratio"], data_dic["log"]["is_sparse"], \
            data_dic["param"]["param_list_sparse"]["param_sparse"]
        msg_sparse = f"上报数据比例{report_ratio:.3f}，稀疏参数{param_sparse:.3f},数据是否稀疏：{is_sparse}\n"
    except:
        msg_sparse = ""

    try:
        val_dtw, param_dtw, algo1_status = log["DTW"][i], data_dic["param"]["param_list"]["param_dtw"], "全局" if \
            data_dic["param"]["param_list"]["time_window_focus"] == -1 else "时间窗内"
        msg_algo1 = f"波形差异度（dtw距离）：{val_dtw:.3f}（0表示波形一致）, dtw阈值参数（高于阈值为异常）：{param_dtw:.3f}， dtw关注区域：{algo1_status}\n"
    except:
        msg_algo1 = ""
    try:
        val_sigma = log["nsigma"][i]
        n_sigma = data_dic["param"]["param_list"]["param_sigma"]
        is_outlier = False if val_sigma < n_sigma else True
        msg_algo2 = f"数值偏离度(正数，<{n_sigma}表示在分布范围内，-1表示无法计算)：{val_sigma:.3f}，是否被算法判定为数值异常：{is_outlier}\n"
    except:
        msg_algo2 = ""
    try:
        log_dtw2, pdtw_hg, pdtw_lo = log["DTW_all"], data_dic["param"]["param_list"]["param_dtw_high"], \
            data_dic["param"]["param_list"]["param_dtw_low"]
        msg_algo4 = f"{log_dtw2}\n"
    except:
        msg_algo4 = ""
    res = (
        f"[智能告警] \n"  # 当前值：{t:.3f}, 前1小时均值：{t_avg:.3f}(前一小时值供参考，非时间窗大小)
        f"{msg_in_change}"
        f"{msg_sparse}"
        f"{msg_algo1}"
        f"{msg_algo2}"
        f"{msg_algo4}"
    )
    return res


# todo 修改报错格式
def get_abnormal_points(data_dic, is_prod_env=True):
    from view.hello.stub.trpc_test_rpchttp import pb
    abn_points = []
    ice = data_dic["is_change_err"]

    end = len(ice)
    start = end - data_dic["param"]["param_list"]["time_window_focus"]
    # for i, is_err in enumerate(ice):
    for i in range(start, end):
        if ice[i] == 0:  # 正常点不带报错
            continue
        # if is_prod_env and i < len(data_dic) - data_dic["param"]["param_list"]["time_window_focus"]:  # 仅关注最新时间的异常，生产时启用
        #     continue
        # 将abn_points 从字典转换为 Protobuf 消息
        abn_point = pb.Abnormalpoints(
            timestamp=data_dic["timestamps"][i],
            abnormal_msg=get_abnormal_msg(data_dic, i)
        )

        abn_points.append(abn_point)

    return abn_points
