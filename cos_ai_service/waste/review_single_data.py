import os
from os.path import exists

from model.utils.vis_data_preprocess import get_txt_data, data_preprocess, vis_ewma
from model.utils.vis_static_output import vis_dtw_threshold, vis_err_points, vis_err_points_45
from model.data_preprocess import smooth_ewma
from model.model_static.similarity import analysis_DTW
from model.model_static.outlier import analysis_nsigma
import matplotlib.pyplot as plt

if __name__ == '__main__':
    plt.rcParams['font.sans-serif'] = ['Hiragino Sans GB']
    time_window = 10
    figure_size = (10, 5)

    metric_data_path = '/data/workspace/cos_ai_service/waste/data3.txt'
    output_path = '/data/workspace/cos_ai_service/waste/output/review/data3'
    os.makedirs(output_path, exist_ok=True)

    # 测试dtw、vis_dtw_threshold
    data = get_txt_data(metric_data_path)

    if "param" not in data:
        data["param"] = {}  # 如果 param 不存在，创建 param 字典
    if "param_list" not in data["param"]:
        # 如果 param_list 不存在，填充默认值
        data["param"]["param_list"] = {
            "param_percent": 0.1,
            "param_smooth": 8,
            "param_dtw": 0.18,
            "param_sigma": 2,
            "time_window_focus": 180,
            "time_window_dtw": 10,
            "time_window_nsigma": 45
        }
    param_list = data["param"]["param_list"]

    data = data_preprocess(data, param_list["param_percent"])
    data = smooth_ewma(data, param_smooth=param_list["param_smooth"])
    vis_ewma(data, output_path, figure_size=figure_size)

    data = analysis_DTW(data, thre=param_list["param_dtw"], time_window=int(param_list["time_window_dtw"]), time_window_focus = -1) # 关注窗口为-1时，标出三天所有告警点
    vis_dtw_threshold(data, output_path, figure_size=figure_size)

    data = analysis_nsigma(data, param_sigma=param_list["param_sigma"], time_window=int(param_list["time_window_nsigma"]))
    vis_err_points(data, output_path)
    vis_err_points_45(data, output_path)
