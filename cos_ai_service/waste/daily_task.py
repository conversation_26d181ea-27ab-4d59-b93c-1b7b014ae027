import time
import subprocess
import schedule
import torch
from torch.utils import benchmark
from collections import defaultdict
import pandas as pd
import inspect
import threading
from concurrent.futures import ThreadPoolExecutor
from multiprocessing import Process, set_start_method

# 定义 GPU 利用率和显存使用率的阈值
GPU_UTILIZATION_THRESHOLD = 10  # 示例阈值，具体数值根据需求调整
MEMORY_USAGE_THRESHOLD = 20  # 示例阈值，具体数值根据需求调整

MONITOR_DURATION = 60  # 监控持续时间，以秒为单位
# 确保在主模块中设置
set_start_method('spawn', force=True)
device = torch.device('cuda')



def var_dict(*args):
    callers_local_vars = inspect.currentframe().f_back.f_locals.items()
    return dict([(name, val) for name, val in callers_local_vars if val is arg][0]
                for arg in args)


def walltime(stmt, arg_dict, duration=3):
    return benchmark.Timer(stmt=stmt, globals=arg_dict).blocked_autorange(
        min_run_time=duration).median

def matmul_task():
    # print("mat")
    matmul_tflops = defaultdict(lambda: {})
    n = 512
    a = torch.randn(n, n, dtype=torch.float16).to(device)
    b = torch.randn(n, n, dtype=torch.float16).to(device)
    t = walltime('a @ b', var_dict(a, b))
    matmul_tflops[f'n={n}'][torch.float16] = 2 * n ** 3 / t / 1e12
    del a, b


def vector_task():
    # print("vec")
    n = 1024 * 1024 * 1664
    vector = defaultdict(lambda: {})
    a = torch.randn(n).to(device)
    t = walltime('a * 1.2', var_dict(a))
    vector[n]['TFLOPS'] = n / t / 1e12
    vector[n]['GB/s'] = 8 * n / t / 1e9
    del a

def consume_gpu_resources():
    start = time.time()
    while time.time() - start < MONITOR_DURATION:
        vector_process = Process(target=vector_task)
        vector_process.start()

        # 在向量任务运行期间，不断执行矩阵乘法
        try:
            while vector_process.is_alive():  # 检查向量进程是否仍在运行
                matmul_task()  # 每次执行一个矩阵乘法任务
        finally:
            vector_process.join()  # 确保向量任务完成



def check_gpu_usage():
    try:
        result = subprocess.run(
            ['nvidia-smi', '--query-gpu=utilization.gpu,memory.used,memory.total', '--format=csv,noheader,nounits'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        if result.returncode != 0:
            print("Error running nvidia-smi:", result.stderr.decode('utf-8'))
            return None

        # 解析命令输出
        output = result.stdout.decode('utf-8').strip()
        gpu_utilization, memory_used, memory_total = map(float, output.split(', '))

        # 计算显存使用百分比
        memory_usage_percentage = (memory_used / memory_total) * 100

        # 实时输出当前的 GPU 使用率和显存使用百分比
        print(f"[Monitor] GPU Utilization: {gpu_utilization}%, Memory Usage: {memory_usage_percentage}%")

        return gpu_utilization, memory_usage_percentage

    except Exception as e:
        print("Error checking GPU usage:", str(e))
        return None


def monitor_gpu_during_execution():
    start_time = time.time()
    while time.time() - start_time < MONITOR_DURATION:
        check_gpu_usage()
        time.sleep(5)  # 每隔 5 秒监控一次


def monitor_gpu_and_consume():
    # 使用线程进行实时 GPU 监测
    monitoring_thread = threading.Thread(target=monitor_gpu_during_execution)
    monitoring_thread.start()

    # 执行 GPU 资源消耗任务
    consume_gpu_resources()

    # 等待监控线程结束
    monitoring_thread.join()


def job():
    monitor_gpu_and_consume()


# 使用 schedule 安排每天任务
schedule.every().day.at("09:20").do(job)

if __name__ == "__main__":
    job()
    print("一轮循环结束")
    # 如果要使用定时器，可以启用以下代码：
    while True:
        schedule.run_pending()
        time.sleep(1)
