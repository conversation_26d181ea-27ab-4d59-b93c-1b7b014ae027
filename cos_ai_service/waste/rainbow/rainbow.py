import time
import json
from rainbow_cpplib.rainbow_client import RainbowClient, FileDownloadMode

g_rc = None


def init_rainbow() -> RainbowClient:
    init_param = {
        "connectStr": "http://api.rainbow.woa.com:8080",  # 访问地址：【北极星】或者【域名】
        "isUsingPolaris": False,  # 是否使用北极星访问方式，True: connectStr需要填北极星地址，False：域名地址
        "fileCachePath": "./",  # 缓存文件目录
        "tokenConfig": {
            "isOpenSign": True,
            "app_id": "119862d0-a489-4a31-96fd-2984bab9b12d",
            "user_id": "$user_id",
            "secret_key": "$secret_key",
        },
    }
    # 初始化，建议使用全局变量，禁止使用RainbowClient(init_param，False)的初始化对象保持在局部变量中（局部对象会导致watch机制失效，add_listener无效）
    # bad_case请查看文件test_rainbow_bad_case.py
    return RainbowClient(init_param, is_singleton = False)

# 定义回调函数
def after_fork():
    print("Before fork")
    global g_rc
    g_rc = init_rainbow()
    g_rc.add_listener(group="kv_config", env_name="Default", callback=None)  # 增加回调函数