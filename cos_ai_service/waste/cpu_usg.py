import threading
import time
from functools import partial
import psutil
from model.utils.vis_data_preprocess import get_txt_data, data_preprocess
from model.data_preprocess import smooth_ewma

metric_data_path = '/data/workspace/cos_ai_service/view/stress_test/metric_data0.txt'
output_path = '/data/workspace/cos_ai_service/waste/output/static/dtw'
from model.utils.default import figure_size, default

default_param_list = default["param_list"]
default_valid_interval = default["param_valid_interval"]


def async_cpu_logger(delay):
    time.sleep(delay)
    print(f"CPU USG: {psutil.cpu_percent(interval=1)}")


def thre_cal(lst, thre):
    err_points = [0] * len(lst)
    for i in range(1, len(lst)):
        if lst[i] - lst[i - 1] > thre:
            err_points[i] = 1
    return err_points


time_duration = 120
# 异步监控
for i in range(10, time_duration + 1, 10):  # 每10s监控一次cpu占用，持续1min
    log_thread = threading.Thread(target=async_cpu_logger, args=(i,))
    log_thread.start()

lista = [1] * 1440
start_time = time.time()
while True:
    # # 不断计算斜率
    # thre_cal(lista, 10)

    # 数据预处理
    data = get_txt_data(metric_data_path)
    param_list = default["param_list"]
    data["param"] = default
    data = data_preprocess(data, param_list["param_percent"])

    if time.time() - start_time > time_duration + 1:
        break
