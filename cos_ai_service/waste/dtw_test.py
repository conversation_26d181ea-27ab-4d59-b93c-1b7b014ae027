# def tslearn_test():
#     import torch
#     from tslearn.metrics import dtw
#     device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
#     print(f"Using device: {device}")
#     lista = [1, 2, 5, 6, 3, 4, 8, 7, 9]
#     listb = [6, 4, 7, 5, 8, 9, 2, 1, 3]
#     s1 = torch.tensor(lista, dtype=torch.float32).reshape(-1, 1).to(device)  # (9, 1)
#     s2 = torch.tensor(listb, dtype=torch.float32).reshape(-1, 1).to(device)  # (9, 1)
#     sim = dtw(s1, s2, be="pytorch")
#     print("DTW Distance:", sim)
#     from dtaidistance import dtw
#     print(dtw.distance(lista, listb))


def cuml_test():
    from cuml.metrics import pairwise_distances
    import numpy as np
    list_a = [1.2, 3.4, 5.6, 7.8]  # 长度4
    list_b = [2.1, 4.3, 6.5, 8.7]  # 长度4

    # 转换为 2D 数组
    series_A = np.array(list_a).reshape(1, -1).astype(np.float32)
    series_B = np.array(list_b).reshape(1, -1).astype(np.float32)

    distances = pairwise_distances(series_A, series_B, metric='dtw')
    print(distances)


cuml_test()
