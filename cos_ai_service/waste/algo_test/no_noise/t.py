import numpy as np
import matplotlib.pyplot as plt
from scipy.constants import sigma

# 创建数组
arr = np.zeros(4320)
mid_point = len(arr) // 2
sig_len = 40
arr[mid_point-sig_len//2:mid_point+sig_len//2] = 1  # 中间10个点置1

# 创建画布
plt.figure(figsize=(840/100, 300/100), dpi=100)  # 精确控制像素尺寸
plt.plot(arr, color='black', linewidth=1)

# 隐藏坐标轴
plt.axis('off')

# 设置坐标范围
plt.xlim(0, len(arr))
plt.ylim(-0.1, 1.1)

# 保存图像
plt.savefig('pulse.png', bbox_inches='tight', pad_inches=0, dpi=100)
plt.close()
