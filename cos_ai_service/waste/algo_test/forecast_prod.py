import os

import numpy as np
import matplotlib.pyplot as plt

from model.forecast import TimeSeriesForecaster
from model.forecast import MLPModel
from model.forecast import plot_forecast
from model.forecast.evaluation import plot_forecast_pred, plot_forecast_pred_std
from model.forecast.validation import time_series_train_test_split
from model.forecast.scaler import SimpleScaler

from model.utils.vis_data_preprocess import get_txt_data, data_preprocess, downsample
from tqdm import tqdm


# metric_data_path = '/data/workspace/cos_ai_service/waste/dataprod/avg_delay/case/beijing0.txt'
# txt_name = metric_data_path.split('/')[-1]
# metric_data = get_txt_data(metric_data_path)
# metric_data = data_preprocess(metric_data, 0.15)
# series = np.array(metric_data['metric_today'])
#
# path_output_img = "/data/workspace/cos_ai_service/waste/output/prod/forecast/case"
# path_weight = f"/data/workspace/cos_ai_service/model/forecast/weight/weight_prod_{txt_name}.h5"

# series_size = 10079
# window_size = 1000
#
# for pred_size in [3000]:  # 变更时长 60,
#     train_series, test_series = time_series_train_test_split(series, series_size - pred_size)
#     scaler = SimpleScaler()  # 数据变换
#     train_series_t = scaler.fit_transform(train_series)
#
#     # # train
#     # model = MLPModel(window_size)
#     # fr = TimeSeriesForecaster(model)
#     # fr.fit(train_series_t, epochs=100, batch_size=50, validation_rate=0)
#     # fr.model.model.save_weights(path_weight)
#
#     # 加载weight 预测
#     model = MLPModel(window_size)  # 必须使用相同的 window_size
#     fr = TimeSeriesForecaster(model)
#     fr.model.model.load_weights(path_weight)
#
#     fr.roller.init(train_series_t[-window_size:])  # 加载模型权重后，fr.forecast 方法没有正确初始化滑动窗口，导致输入数据的形状不匹配
#     pred_series_t = fr.forecast(n_steps=pred_size)
#     pred_series = scaler.inverse_transform(pred_series_t)  # 预测结果逆变换
#
#     # 可视化
#     plot_forecast(train_series, test_series, pred_series, f"{path_output_img}/forecase_{pred_size}.png")
#     plot_forecast_pred(test_series, pred_series, f"{path_output_img}/forecase_pred_{pred_size}.png")

def get_bias(y_true, y_pred):
    res = y_pred[:len(y_true)] - y_true
    mean = np.mean(res)
    std = np.std(res)
    return mean, std


metric_data_path = '/data/workspace/cos_ai_service/waste/dataprod/avg_delay/case'

for mfile in tqdm(os.listdir(metric_data_path)):
    txt_name = mfile.split('.')[0]
    metric_data = get_txt_data(os.path.join(metric_data_path, mfile))
    metric_data = data_preprocess(metric_data, 0.15)
    series = np.array(metric_data['metric_today'])

    path_output_img = "/data/workspace/cos_ai_service/waste/output/prod/forecast/case"
    os.makedirs(path_output_img, exist_ok=True)

    path_weight = f"/data/workspace/cos_ai_service/model/forecast/weight"
    os.makedirs(path_weight, exist_ok=True)
    path_weight = os.path.join(path_weight, f"weight_prod_{txt_name}.h5")

    # # 去噪后再预测
    # metric_data = downsample(metric_data, 1000)
    # series = np.array(metric_data['downsample']['metric_today'])

    series_size = 10079
    window_size = 2880
    for pred_size in [300]:  # 变更时长 60,
        t = series_size - pred_size
        start_idx_itvl = series_size - (3000 + pred_size)
        train_series, test_series = series[:-pred_size], series[-pred_size:]
        train_series_interval = series[start_idx_itvl:-pred_size]
        scaler = SimpleScaler()  # 数据变换
        train_series_t = scaler.fit_transform(train_series)

        # train
        model = MLPModel(window_size)
        fr = TimeSeriesForecaster(model)
        fr.fit(train_series_t, epochs=10, batch_size=50, validation_rate=0)
        fr.model.model.save_weights(path_weight)

        # 加载weight 预测
        model = MLPModel(window_size)  # 必须使用相同的 window_size
        fr = TimeSeriesForecaster(model)
        fr.model.model.load_weights(path_weight)

        # 预测train_series_interval
        fr.roller.init(train_series_t[start_idx_itvl - window_size: start_idx_itvl])
        pred_series_t = fr.forecast(n_steps=len(train_series_interval))
        pred_series = scaler.inverse_transform(pred_series_t)
        mean, std = get_bias(train_series_interval, pred_series)

        plot_forecast(train_series[:start_idx_itvl], train_series_interval, pred_series,
                      f"{path_output_img}/dsforecase_train_interval_{pred_size}_{txt_name}.png")
        plot_forecast_pred(train_series_interval, pred_series,
                           f"{path_output_img}/dsforecase_pred_train_interval_{pred_size}_{txt_name}.png")

        # 这个初始化非常关键，模型根据这个窗口的长度预测未来的数据
        fr.roller.init(train_series_t[-window_size:])  # 加载模型权重后，fr.forecast 方法没有正确初始化滑动窗口，导致输入数据的形状不匹配
        pred_series_t = fr.forecast(n_steps=pred_size)
        pred_series = scaler.inverse_transform(pred_series_t)  # 预测结果逆变换
        pred_series -= mean
        plot_forecast_pred_std(test_series, pred_series, std,
                               f"{path_output_img}/forecase_std_{pred_size}_{txt_name}.png")
