import cupy as cp
import matplotlib.pyplot as plt

def cupy_point_anomaly_detection(a, b, c, similarity_threshold=0.85):
    """
    参数：
        a, b, c: 输入的三条时序数据（支持numpy/cupy数组）
        similarity_threshold: 整体波形相似度阈值（0-1）
    返回：
        bool: True表示a的最后一个点异常，False表示正常
    """
    # 数据转换和校验
    a = cp.asarray(a, dtype=cp.float32)
    b = cp.asarray(b, dtype=cp.float32)
    c = cp.asarray(c, dtype=cp.float32)
    # assert a.shape == b.shape == c.shape, "输入序列必须长度相同"

    # 整体波形相似性计算（优化版）
    def optimized_cos_sim(x, y):
        xy_dot = cp.dot(x, y)
        x_norm = cp.linalg.norm(x)
        y_norm = cp.linalg.norm(y)
        return xy_dot / (x_norm * y_norm + 1e-10)

    similarity_ab = optimized_cos_sim(a, b)
    similarity_ac = optimized_cos_sim(a, c)

    similarity_normal = cp.any(cp.array([similarity_ab,
                                         similarity_ac]) >= similarity_threshold)
    t = cp.array([similarity_ab, similarity_ac]).get()
    tt = max(similarity_ab, similarity_ac)
    is_change_err = cp.zeros_like(a, dtype=bool)
    if not similarity_normal.item():
        is_change_err[-1] = True
    return is_change_err
def plot_curves(a, b, c, is_change_err, title="Waveform"):
    """
    可视化 a、b、c 曲线，并标出异常点。
    """
    t = cp.linspace(0, 10 * cp.pi, len(a))

    plt.figure(figsize=(10, 6))
    plt.plot(t.get(), a.get(), label="a (sin(t))", color="blue")
    plt.plot(t.get(), b.get(), label="b (cos(t))", color="green")
    plt.plot(t.get(), c.get(), label="c (sin(t) * scale)", color="orange")

    # 标出异常点
    anomaly_indices = cp.where(is_change_err)[0]
    if len(anomaly_indices) > 0:
        plt.scatter(t[anomaly_indices].get(), a[anomaly_indices].get(), color="red", label="Anomaly Point",
                    zorder=5, s=100)

    plt.title(title)
    plt.xlabel("Time")
    plt.ylabel("Value")
    plt.legend()
    plt.grid(True)
    plt.show()

# 示例使用
if __name__ == "__main__":
    # 生成测试数据（直接在GPU创建）
    t = cp.linspace(0, 10 * cp.pi, 100)

    # 正常案例（超出值范围但波形相似）
    a_normal = cp.sin(t)
    a_normal[-1] = 2.0
    b_normal = cp.cos(t)
    c_normal = cp.sin(t) * 0.9
    is_change_err1 = cupy_point_anomaly_detection(a_normal, b_normal, c_normal)

    # 异常案例（波形不相似）
    a_abnormal = cp.sin(2 * t)
    b_abnormal = cp.cos(t)
    c_abnormal = cp.sin(t) * 0.8
    is_change_err2 = cupy_point_anomaly_detection(a_abnormal, b_abnormal, c_abnormal)
    if cp.any(is_change_err2):
        print(True)

    # 可视化
    plot_curves(a_normal, b_normal, c_normal, is_change_err1, title="Normal Case")
    plot_curves(a_abnormal, b_abnormal, c_abnormal, is_change_err2, title="Abnormal Case")
