import numpy as np
import matplotlib.pyplot as plt

from model.forecast import TimeSeriesForecaster
from model.forecast import MLPModel
from model.forecast import plot_forecast
from model.forecast.evaluation import plot_forecast_pred
from model.forecast.validation import time_series_train_test_split
from model.forecast.scaler import SimpleScaler

from model.utils.vis_data_preprocess import get_txt_data, data_preprocess


def _add_noise(y, add=True, multiply=False):
    u = np.max(y)
    if add:
        anoise = np.random.normal(0, u / 100, size=len(y))
    else:
        anoise = 0

    if multiply:
        mnoise = np.random.normal(loc=0, scale=u / 15, size=len(y))
    else:
        mnoise = 0
    return y * (1 + mnoise) + anoise


def generate_data(size=1000):
    x = np.linspace(0, 20 * np.pi, size)
    y = np.log(x + 1) + np.sqrt(x) + np.sin(x) + np.cos(2 * x) + \
        1 / 3 * np.sin(6 * x) + 1 / 4 * np.cos(10 * x) + \
        1 / 5 * np.sin(15 * x) + 1 / 5 * np.cos(14 * x)
    res = _add_noise(y, add=True, multiply=False)
    res = (res - np.min(res)) / (np.max(res) - np.min(res))

    return res


path_output_img = "/data/workspace/cos_ai_service/waste/output/test"
path_weight = "/data/workspace/cos_ai_service/model/weight/weight_ljh.h5"
series_size = 10000
window_size = 1000

for pred_size in [60, 300]:  # 变更时长
    series = generate_data(size=series_size)  # 生成数据
    train_series, test_series = time_series_train_test_split(series, series_size - pred_size)
    scaler = SimpleScaler()  # 数据变换
    train_series_t = scaler.fit_transform(train_series)
    # train
    model = MLPModel(window_size)
    fr = TimeSeriesForecaster(model)
    fr.fit(train_series_t, epochs=10, batch_size=50, validation_rate=0)
    fr.model.model.save_weights(path_weight)

    # 加载weight 预测
    model = MLPModel(window_size)  # 必须使用相同的 window_size
    fr = TimeSeriesForecaster(model)
    fr.model.model.load_weights(path_weight)

    fr.roller.init(train_series_t[-window_size:])  # 加载模型权重后，fr.forecast 方法没有正确初始化滑动窗口，导致输入数据的形状不匹配
    pred_series_t = fr.forecast(n_steps=pred_size)
    pred_series = scaler.inverse_transform(pred_series_t)  # 预测结果逆变换

    # 可视化
    plot_forecast(train_series, test_series, pred_series, f"{path_output_img}/forecase_{pred_size}.png")
    plot_forecast_pred(test_series, pred_series, f"{path_output_img}/forecase_pred_{pred_size}.png")
