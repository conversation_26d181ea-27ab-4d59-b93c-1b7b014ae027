import numpy as np
from dtaidistance import dtw


# 生成半波正弦数据（幅度1/2）
def generate_half_sine(length):
    x = np.linspace(0, np.pi, length)
    return np.sin(x) * 0.5


# 构造两个序列
lista = np.zeros(100)
listb = np.zeros(100)

# 填充波形部分
lista[:30] = generate_half_sine(30)  # 0-29索引位
listb[60:90] = generate_half_sine(30)  # 30-59索引位

# 可视化波形
import matplotlib.pyplot as plt

plt.figure(figsize=(10, 4))
plt.plot(lista, label='List A')
plt.plot(listb, label='List B')
plt.title("Waveform Comparison")
plt.legend()
plt.show()

# DTW距离计算
alignment = dtw.distance(lista.tolist(), listb.tolist())
print(f"DTW距离: {alignment:.4f}")

# 输出结果验证
print("ListA波形峰值位置:", np.argmax(lista))  # 应输出14或15（半波峰值位置）
print("ListB波形峰值位置:", np.argmax(listb))  # 应输出44或45
