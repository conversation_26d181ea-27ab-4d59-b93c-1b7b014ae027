import os
from datetime import datetime, timedelta

from model.data_preprocess import downsample, smooth_ewma
from model.utils.waste.similarity_prod import analysis_D2, analysis_DTW, analysis_D3
from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse
from model.utils.vis_data_preprocess import vis_2dic
from model.utils.vis_static_output import vis_dtw_threshold, vis_err_points, vis_nsigma
from vis_data_preprocess import get_txt_data, data_preprocess, vis_origin_data


def cut_data(metric_data, err_time):
    err_time_dt = datetime.strptime(err_time, "%Y-%m-%d %H:%M:%S")
    start_time, end_time = err_time_dt - timedelta(hours=2), err_time_dt

    timestamps = metric_data["timestamps"]
    valid_indices = []
    for i, ts in enumerate(timestamps):
        ts_dt = datetime.fromtimestamp(ts)
        if start_time <= ts_dt <= end_time:
            valid_indices.append(i)

    metric_data["timestamps"] = [timestamps[i] for i in valid_indices]
    for series in metric_data["metric_data"]["series"]:
        series["metric_val"] = [series["metric_val"][i] for i in valid_indices]
        series["val_status"] = [series["val_status"][i] for i in valid_indices]

    return metric_data


# from model.data_postprocess import get_abnormal_msg
def get_abnormal_msg(data_dic, i):
    log = data_dic["log"]
    ice = data_dic["is_change_err"]
    try:
        report_ratio, is_sparse, param_sparse = data_dic["log"]["report_ratio"], data_dic["log"]["is_sparse"], \
            data_dic["param"]["param_list_sparse"]["param_sparse"]
        msg_sparse = f"上报数据比例{report_ratio:.3f}，稀疏参数{param_sparse:.3f},数据是否稀疏：{is_sparse}\n"
    except:
        msg_sparse = ""

    try:
        val_dtw, param_dtw, algo1_status = log["DTW"][i], data_dic["param"]["param_list"]["param_dtw"], "全局" if \
            data_dic["param"]["param_list"]["time_window_focus"] == -1 else "时间窗内"
        msg_algo1 = f"dtw：{val_dtw:.3f}, 阈值：{param_dtw:.3f}\n"
    except:
        msg_algo1 = ""
    try:
        val_sigma = log["nsigma"][i]
        n_sigma = data_dic["param"]["param_list"]["param_sigma"]
        is_outlier = False if val_sigma < n_sigma else True
        msg_algo2 = f"sigma：{val_sigma:.3f}，是否被异常：{is_outlier}\n"
    except:
        msg_algo2 = ""
    try:
        log_dtw2, pdtw_hg, pdtw_lo = log["DTW_all"], data_dic["param"]["param_list"]["param_dtw_high"], \
            data_dic["param"]["param_list"]["param_dtw_low"]
        msg_algo4 = f"{log_dtw2}\n"
    except:
        msg_algo4 = ""
    res = (
        f"{msg_sparse}"
        f"{msg_algo1}"
        f"{msg_algo2}"
        f"{msg_algo4}"
    )
    return res


def get_abnormal_points(data_dic, is_prod_env=True):
    abn_points = []
    ice = data_dic["is_change_err"]

    for i, is_err in enumerate(ice):
        if is_err == 0:  # 正常点不带报错
            continue
        if is_prod_env and i < len(data_dic) - data_dic["param"]["param_list"]["time_window_focus"]:  # 仅关注最新时间的异常，生产时启用
            continue

        t = get_abnormal_msg(data_dic, i)
        abn_point = {
            "abnormal_msg": get_abnormal_msg(data_dic, i)
        }

        abn_points.append(abn_point)

    return abn_points


def extract_path_info(abs_path):
    # 获取文件名（带后缀）
    file_name = os.path.basename(abs_path)

    # 获取倒数第二层文件夹名
    dir_path = os.path.dirname(abs_path)  # 去掉文件名，得到目录部分
    parent_dir = os.path.basename(dir_path)  # 取目录的最后一层
    return parent_dir, file_name


def vis_algo_no_sparse(metric_data, metric_data_path, output_path):
    dir_name, img_name = extract_path_info(metric_data_path)
    vis_2dic(metric_data, metric_data["smooth"]["ewma"], output_path + f'/ewma/{dir_name}', figure_size, img_name)

    vis_dtw_threshold(metric_data, metric_data["log"]["DTW"], output_path + f"/dtw/{dir_name}", figure_size, img_name)

    vis_nsigma(metric_data, metric_data['interval']['nsigma']['lowlimit'],
               metric_data['interval']['nsigma']['highlimit'], output_path + f'/nsigma/{dir_name}', figure_size,
               img_name)

    vis_2dic(metric_data, metric_data["downsample"], output_path + f'/downsample/{dir_name}', figure_size,
             img_name)


def vis_algo_sparse(metric_data, metric_data_path, output_path):
    dir_name, img_name = extract_path_info(metric_data_path)
    vis_2dic(metric_data, metric_data["smooth"]["ewma"], output_path + f'/ewma/{dir_name}', figure_size, img_name)
    vis_nsigma(metric_data, metric_data['interval']['nsigma']['lowlimit'],
               metric_data['interval']['nsigma']['highlimit'], output_path + f'/nsigma/{dir_name}', figure_size,
               img_name)
    vis_2dic(metric_data, metric_data["downsample"], output_path + f'/downsample/{dir_name}', figure_size,
             img_name)


def algo_no_sparse(metric_data):
    in_change_period = metric_data["change_period"]
    in_change_period = True  # 暂时强制开启
    if not in_change_period:
        param_list = metric_data["param"]["param_list_no_change"]
        # 记得还原
        metric_data = analysis_nsigma(metric_data, param_sigma=param_list["param_sigma"],
                                      time_window=int(param_list["time_window_nsigma"]))
        # # 后面效果好可以干掉这个
        # metric_data = analysis_cos_sim(metric_data, thre=param_list["param_cos_sim"])
        metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
        metric_data = analysis_D3(metric_data)
    else:
        param_list = metric_data["param"]["param_list"]
        metric_data = smooth_ewma(metric_data, param_smooth=param_list["param_smooth"])
        metric_data = analysis_DTW(metric_data)
        metric_data = analysis_nsigma(metric_data, param_sigma=param_list["param_sigma"],
                                      time_window=int(param_list["time_window_nsigma"]))
        # # 后面效果好可以干掉这个
        # metric_data = analysis_cos_sim(metric_data, thre=param_list["param_cos_sim"])
        metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
        metric_data = analysis_D2(metric_data)
    return metric_data


def algo_sparse(metric_data):
    param_list = metric_data["param"]["param_list_sparse"]
    metric_data = smooth_ewma(metric_data, param_smooth=param_list["param_smooth"])
    metric_data = analysis_nsigma_sparse(metric_data, param_sigma=param_list["param_sigma"],
                                         time_window=int(param_list["time_window_nsigma"]))
    metric_data = downsample(metric_data, int(param_list["time_window_dtw"]))
    metric_data = analysis_D2(metric_data)
    return metric_data


def change_analysis(metric_data, metric_data_path, output_path):
    # 生效区间判断
    if not metric_data["log"]["in_valid_interval"]:
        print(f"最后一点不在生效区间内，算法跳出")
        metric_data["is_change_err"] = [0] * len(metric_data["is_change_err"])
        return metric_data

    if metric_data["log"]["is_sparse"]:
        # print("数据稀疏")
        metric_data = algo_sparse(metric_data)
        vis_algo_sparse(metric_data, metric_data_path, output_path)
    else:
        metric_data = algo_no_sparse(metric_data)
        vis_algo_no_sparse(metric_data, metric_data_path, output_path)
    print(get_abnormal_points(metric_data))  # 输出异常点
    return metric_data


from tqdm import tqdm
from default import figure_size, default

if __name__ == '__main__':
    check_err_time, err_time = False, "2025-03-24 10:08:00"
    metric_data_dir = './dataprod/case3_must_alarm'
    output_path = './waste/output/prod'

    for root, dirs, files in os.walk(metric_data_dir):
        for t in files:
            metric_data_path = os.path.join(root, t)

            # 预处理
            metric_data = get_txt_data(metric_data_path)
            if check_err_time:  # 暂时没用
                metric_data = cut_data(metric_data, err_time)

            dir_name, img_name = extract_path_info(metric_data_path)
            vis_origin_data(metric_data, output_path + f'/origin_data/{dir_name}', figure_size, img_name)
            metric_data = data_preprocess(metric_data, metric_data["param"]["param_list"]["param_percent"])

            # 算法
            if os.path.isfile(metric_data_path):
                print(t)
                change_analysis(metric_data, metric_data_path, output_path)

        for dir_name in dirs:
            metric_data_sub_dir = os.path.join(root, dir_name)
            for metric_data_file in tqdm(os.listdir(metric_data_sub_dir)):
                metric_data_path = os.path.join(metric_data_sub_dir, metric_data_file)

                # 预处理
                metric_data = get_txt_data(metric_data_path)
                if check_err_time:  # 暂时没用
                    metric_data = cut_data(metric_data, err_time)

                dir_name, img_name = extract_path_info(metric_data_path)
                vis_origin_data(metric_data, output_path + f'/origin_data/{dir_name}', figure_size, img_name)
                metric_data = data_preprocess(metric_data, metric_data["param"]["param_list"]["param_percent"])

                # 算法
                change_analysis(metric_data, metric_data_path, output_path)
