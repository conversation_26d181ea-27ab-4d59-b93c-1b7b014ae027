#!/bin/sh

trap 'echo sigterm && exit 15' SIGTERM

# 安装 agent
mkdir -p /data/zhiyan/agent/bin
mkdir -p /data/zhiyan/agent/etc
mkdir -p /data/zhiyan/agent/plugins

cd /tmp/teg_agent && ./install.sh

# 可选：如果 agent.yaml 需要复制
# cp /usr/local/zhiyan/agent/etc/agent.yaml /data/zhiyan/agent/etc/agent.yaml

# 启动 crond
crond

# 启动主服务
cd /
python3.8 view/hello/trpc_main.py

# 保持容器前台运行（如果主服务是后台进程）
# sleep infinity &
# wait
