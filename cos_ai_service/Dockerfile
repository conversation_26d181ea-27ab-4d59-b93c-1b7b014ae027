FROM mirrors.tencent.com/devcloud/codev-tlinux3-gpu:0.0.2

WORKDIR /
COPY . .

RUN yum update -y && \
    yum install -y epel-release && \
    yum install -y python38 python38-devel python38-pip cronie wget snappy-devel

# 先单独 copy requirements.txt，利用 Docker cache
COPY requirements.txt /app/
RUN pip3.8 install -r requirements.txt

# 下载并解压 agent 包，但不安装
RUN wget https://mirrors.tencent.com/repository/generic/zhiyan-monitor-agent/release/1.2.28/teg_agent.amd64.tgz -O /tmp/teg_agent_v1.2.28.tgz && \
    tar -zxf /tmp/teg_agent_v1.2.28.tgz -C /tmp

# 复制 entrypoint 启动脚本
COPY docker-entrypoint.sh /usr/local/docker-entrypoint.sh
RUN chmod +x /usr/local/docker-entrypoint.sh

EXPOSE 6666
EXPOSE 7777

ENTRYPOINT ["/bin/sh", "/usr/local/docker-entrypoint.sh"]