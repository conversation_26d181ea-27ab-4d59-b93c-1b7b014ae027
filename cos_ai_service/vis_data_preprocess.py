import json
import logging
import math
import os
import warnings

import matplotlib.pyplot as plt
import pandas as pd

logging.getLogger('matplotlib.font_manager').disabled = True
warnings.filterwarnings("ignore", category=UserWarning)  # 屏蔽字体报错
"""
这里和data_preprocess.py的逻辑一样，只是数据处理过程不同（此处数据来自本地）
"""


def get_txt_data(metric_txtPath):
    # 读取原始数据
    with open(metric_txtPath, 'r') as file:
        data = file.read()
    metric_data = json.loads(data)
    return metric_data


def get_norm_alpha(list_list_data):
    min_val, max_val = math.inf, -math.inf
    for list_data in list_list_data:
        min_val, max_val = min(min_val, min(list_data)), max(max_val, max(list_data))
    return min_val, max_val


def norm_list(min_val, max_val, list_list_data):
    for i, list_data in enumerate(list_list_data):
        list_list_data[i] = [(val - min_val) / (max_val - min_val) if val != 0 else 0 for val in list_data]
    return list_list_data


def check_single_interval(data_dic):
    # 将原本list中，处于边界内的置为0
    abnormal_today = data_dic["abnormal_tody"]
    highlimit, lowlimit = data_dic["highlimit"], data_dic["lowlimit"]
    for i in range(len(abnormal_today)):
        if data_dic["metric_today"][i] < highlimit and data_dic["metric_today"][i] > lowlimit:
            data_dic["abnormal_tody"][i] = 0
    return data_dic


def ckeck_in_valid_interval(metric_today, lolmt, hglmt, time_window_focus=3):
    t = metric_today[-time_window_focus:]
    for val in t:
        if lolmt > -9007199254740000 and hglmt < 9007199254740000 and val > lolmt and val < hglmt:
            return True
        if lolmt > -9007199254740000 and val > lolmt:
            return True
        if hglmt < 9007199254740000 and val < hglmt:
            return True
    return False


def is_sparse(data_dic, param_sparse):
    # 1 表示未上报
    series = data_dic["metric_data"]["series"]
    mtoday, mt_report = series[0]["metric_val"], series[0]["val_status"]
    if len(mt_report) == 0:
        return True, -1  # -1 表示上报数据为空
    report_num = len(mt_report) - sum(mt_report)
    report_ratio = report_num / max(1, len(mtoday))
    if report_ratio > param_sparse:
        return False, report_ratio
    return True, report_ratio


def check_param(metric_data):
    from default import default
    if "param_list" not in metric_data["param"]:
        metric_data["param"]["param_list"] = default["param_list"]
    if "param_valid_interval" not in metric_data["param"]:
        metric_data["param"]["param_valid_interval"] = default["param_valid_interval"]
    if "param_list_sparse" not in metric_data["param"]:
        metric_data["param"]["param_list_sparse"] = default["param_list_sparse"]
    if "param_list_no_change" not in metric_data["param"]:
        metric_data["param"]["param_list_no_change"] = default["param_list_no_change"]
    return metric_data


def data_preprocess(data):
    check_param(data)
    series = data["metric_data"]["series"]
    metric_today, metric_yesterday, metric_lastweek = series[0]["metric_val"], series[1]["metric_val"], series[2][
        "metric_val"]

    minval, maxval = get_norm_alpha([metric_today, metric_yesterday, metric_lastweek])
    list_list_data = [metric_today, metric_yesterday, metric_lastweek]
    list_list_data = norm_list(minval, maxval, list_list_data)

    param = data["param"]

    from model.utils.default import figure_size, default
    # 检查 param_valid_interval 是否存在，如果不存在则使用 default 中的值
    if "param_valid_interval" not in param:
        print("data_preprocess: 'param_valid_interval' not found in param, using default values.")
        param_valid_interval = default["param_valid_interval"]
    else:
        param_valid_interval = param["param_valid_interval"]

    in_valid_interval = True if ckeck_in_valid_interval(series[0]["metric_val"],
                                                        param_valid_interval["param_lolmt"],
                                                        param_valid_interval["param_hglmt"]) else False
    issparse, report_ratio = is_sparse(data, param_sparse=0.10)
    data_dic = {
        "timestamps": list(data["timestamps"]),
        "change_period": data["metric_data"]["change_period"] if data["metric_data"][
                                                                     "change_period"] != None else False,
        "origin_data": {
            "metric_today": series[0]["metric_val"],
            "metric_today_report_status": series[0]['val_status'],
            "metric_yesterday": series[1]["metric_val"],
            "metric_yesterday_report_status": series[1]['val_status'],
            "metric_lastweek": series[2]["metric_val"],
            "metric_lastweek_report_status": series[2]['val_status']
        },
        "metric_today": list_list_data[0],
        "metric_yesterday": list_list_data[1],
        "metric_lastweek": list_list_data[2],
        "interval": {},
        "curve": {"high": {}, "low": {}},  # 暂时没想好数值型的怎么统一格式
        "is_change_err": [0] * len(metric_today),  # 0 正常 历史遗留问题
        "log": {
            "is_sparse": issparse,
            "report_ratio": report_ratio,
            "in_valid_interval": in_valid_interval
        },
        "param": param
    }
    data_dic = check_report_status(data_dic)
    return data_dic


def vis_origin_data(d_dic, output_path, figure_size=(10, 5), figname=""):
    os.makedirs(output_path, exist_ok=True)
    alpha = 0.5
    xlabel, ylabel = '时间', '归一化值'
    plt.title("ewma")
    plt.figure(figsize=figure_size)

    series = d_dic['metric_data']['series']
    plt.plot(series[0]["metric_val"], label="today")
    plt.plot(series[1]["metric_val"], label="yesterday", alpha=alpha)
    plt.plot(series[2]["metric_val"], label="lastweek", alpha=alpha)
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/{figname}.png')


def vis_3val(val1, val2, val3, output_path, figure_size=(10, 5), figname=""):
    plt.figure(figsize=figure_size)
    plt.plot(val1, label="today")
    plt.plot(val2, label="yesterday")
    plt.plot(val3, label="lastweek")
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/{figname}.png')


def vis_2dic(d_dic1, d_dic2, output_path, figure_size, img_name):
    os.makedirs(output_path, exist_ok=True)
    fig, ax = plt.subplots(1, 2, figsize=(2 * figure_size[0], figure_size[1]))
    ax[0].plot(d_dic1['metric_today'], label="today")
    ax[0].plot(d_dic1['metric_yesterday'], label="yesterday")
    ax[0].plot(d_dic1["metric_lastweek"], label="lastweek")
    ax[0].set_ylim(0, 1)
    ax[0].legend()

    ax[1].plot(d_dic2['metric_today'], label="today")
    ax[1].plot(d_dic2['metric_yesterday'], label="yesterday")
    ax[1].plot(d_dic2["metric_lastweek"], label="lastweek")
    ax[1].set_ylim(0, 1)
    ax[1].legend()

    plt.grid()
    plt.tight_layout()
    plt.savefig(f'{output_path}/{img_name}.png')
    plt.close()


def vis_2dic_45(d_dic1, d_dic2, output_path, figure_size, img_name):
    num_images = ((len(d_dic1['metric_today']) + 44) // 45)
    for j in range(num_images):
        if j % 12 != 0:
            continue
        start_idx = j * 45
        end_idx = min(len(d_dic1['metric_today']), start_idx + 45)
        x = list(range(start_idx, end_idx))

        fig, ax = plt.subplots(1, 2, figsize=(2 * figure_size[0], figure_size[1]))
        ax[0].plot(x, d_dic1['metric_today'][start_idx:end_idx], label="today")
        ax[0].plot(x, d_dic1['metric_yesterday'][start_idx:end_idx], label="yesterday")
        ax[0].plot(x, d_dic1["metric_lastweek"][start_idx:end_idx], label="lastweek")
        ax[0].set_ylim(0, 1)
        ax[0].legend()

        ax[1].plot(x, d_dic2['metric_today'][start_idx:end_idx], label="today")
        ax[1].plot(x, d_dic2['metric_yesterday'][start_idx:end_idx], label="yesterday")
        ax[1].plot(x, d_dic2["metric_lastweek"][start_idx:end_idx], label="lastweek")
        ax[1].set_ylim(0, 1)
        ax[1].legend()

        plt.grid()
        plt.tight_layout()
        plt.savefig(f'{output_path}/{img_name}_{j}.png')
        plt.close()


import numpy as np


def denoise_hampel(ts, win, thr):
    # Hampel滤波
    avg = ts.rolling(win, center=True).median()
    residual = ts - avg
    std = residual.std()
    outliers = np.abs(residual) > thr * std
    ts[outliers] = np.nan  # 异常值丢弃
    # 线性插值填补被丢弃的异常值
    return ts.interpolate(method='linear')


def smart_denoise(data, window, thre):
    ts_today, ts_yesterday, ts_lastweek = pd.Series(data['metric_today']), pd.Series(
        data['metric_yesterday']), pd.Series(
        data['metric_lastweek'])

    # 不对当天降噪
    ts_today, ts_yesterday, ts_lastweek = ts_today, denoise_hampel(ts_yesterday, window, thre), denoise_hampel(
        ts_lastweek, window, thre)
    data["denoise"] = {
        "metric_today": ts_today,
        "metric_yesterday": ts_yesterday,
        "metric_lastweek": ts_lastweek
    }
    return data


from model.data_preprocess import downsample, check_report_status

if __name__ == '__main__':
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
    from model.utils.default import figure_size, default

    default_param_list = default["param_list"]
    default_valid_interval = default["param_valid_interval"]
    # # 查看单条数据
    # metric_data_path = '/data/workspace/cos_ai_service/view/stress_test/metric_data0.txt'
    # output_path = '/data/workspace/cos_ai_service/waste/output/static/dtw'
    #
    # from model.data_preprocess import smooth_ewma
    # data = get_txt_data(metric_data_path)
    # param_list = data["param"]["param_list"]
    # data = data_preprocess(data, param_list["param_percent"])
    # data = smooth_ewma(data, param_smooth=param_list["param_smooth"])
    #
    # vis_ewma(data, output_path, "ewma")

    # 查看文件夹下所有数据
    metric_data_dir = '/Users/<USER>/Documents/GitHub/cos_ai_service/dataprod/avg_delay/case1'
    output_path = '/Users/<USER>/Documents/GitHub/cos_ai_service/waste/output/prod/'

    for metric_data_file in os.listdir(metric_data_dir):
        metric_data_path = os.path.join(metric_data_dir, metric_data_file)
        img_name, _ = os.path.splitext(metric_data_file)
        metric_data = get_txt_data(metric_data_path)
        vis_origin_data(metric_data, output_path + 'origin_data', figure_size, img_name)

        metric_data = data_preprocess(metric_data)

        # metric_data = smooth_ewma(metric_data, param_smooth=default_param_list["param_smooth"])
        # vis_2dic(metric_data, metric_data["smooth"]["ewma"], output_path + 'ewma', figure_size, img_name)
        # vis_2dic_45(metric_data, metric_data["smooth"]["ewma"], output_path + 'ewma_45', figure_size, img_name)

        # hample效果不如ewma
        # metric_data = smart_denoise(metric_data, 10, 3)
        # vis_2dic(metric_data, metric_data["denoise"], output_path + 'denoise', figure_size, img_name)
        # vis_2dic_45(metric_data, metric_data["denoise"], output_path + 'denoise_45', figure_size, img_name)

        # metric_data = downsample(metric_data, 360)
        # vis_2dic(metric_data, metric_data["downsample"], output_path + 'downsample', figure_size, img_name)
