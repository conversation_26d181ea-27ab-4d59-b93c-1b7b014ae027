cos业务背景：https://iwiki.woa.com/p/4012730322

修改参数：
model/change_analysis.py

环境问题：

本地部署：

方式一：云研发选择对应的模版，进入后pip install -r requirements.txt，若报错python-dev相关问题，则执行dockerfile中更新的命令

注意：

若基础镜像为mirrors.tencent.com/todacc/trpc-python-compile_tlinux3.1:0.1.3，则该基础镜像支持trpc-python，可以桩代码生成，但是不支持部分算法模块的调试。
该镜像对应requirements_without_trpc_py.txt

若基础镜像为mirrors.tencent.com/devcloud/codev-tlinux3-gpu-pytorch:20241030，则该基础镜像支持所有算法的调试，但不支持trpc-python的桩代码生成
该镜像对应requirements_without_torch.txt，安装前需要根据dockerfile安装python-dev等

方式二：idc进入后用docker构建，我开发是docker
pull构建的，但若测试则基于dockerfile进行构建，https://iwiki.woa.com/p/4013540547

tke部署：

基于dockerfile构建，注意基础镜像源区分开发环境和运行环境

运行
用ide运行view/hello/trpc_main.py前记得编辑配置，设置工作目录为/data/workspace/cos_ai_service
python3.8 view/stress_test/test2.py


