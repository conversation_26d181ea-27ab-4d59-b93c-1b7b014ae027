# -*- coding: utf-8 -*-
"""This is RPC module
Code generated by trpc-go/trpc-go-cmdline. DO NOT EDIT.
Source: hello.proto
"""
from typing import List, Callable, Tuple

from trpc import client
from trpc import codec
from trpc import server
from trpc import context
from trpc.codec.message import Message
from . import pb

#pylint: disable=unnecessary-pass


# ChangeAnalysisService defines service
class ChangeAnalysisServicer:
    """Abstract base class of server"""
    
    #pylint: disable=invalid-name
    async def ChangeAnal(self, ctx: context.Context, request: pb.CARequest) -> pb.CAReply:
        """Abstract rpc method of server
        :param ctx: context of data processing.
        :param request: object of pb.
        """
        raise NotImplementedError('Method not implemented!')
    #pylint: disable=invalid-name
    async def WChangeAnal(self, ctx: context.Context, request: pb.WCARequest) -> pb.CAReply:
        """Abstract rpc method of server
        :param ctx: context of data processing.
        :param request: object of pb.
        """
        raise NotImplementedError('Method not implemented!')

ChangeAnalysisServiceName = "trpc.test.rpchttp.ChangeAnalysis"  # pylint: disable=invalid-name

# add ChangeAnalysisServicer to server
# pylint: disable=invalid-name
def register_ChangeAnalysisServicer_server(svr: server.Server, servicer: ChangeAnalysisServicer, fix_rpc_name: bool = False):
    """Register service to server"""
    rpc_method_handlers = []
    rpc_stream_handlers = []
    
    rpc_method_handlers.append(
        server.Method(
            name="/trpc.test.rpchttp.ChangeAnalysis/ChangeAnal",
            req_cls=pb.CARequest,
            rsp_cls=pb.CAReply,
            impl_func=servicer.ChangeAnal))
    
    rpc_method_handlers.append(
        server.Method(
            name="/trpc.test.rpchttp.ChangeAnalysis/WChangeAnal",
            req_cls=pb.WCARequest,
            rsp_cls=pb.CAReply,
            impl_func=servicer.WChangeAnal))
    
    desc = server.ServiceDesc(
        service_name="/" + ChangeAnalysisServiceName,
        methods=rpc_method_handlers)

    svr.register(desc, fix_rpc_name)


# client proxy
class ChangeAnalysisClientProxy:
    """Client proxy"""
    
    def ChangeAnal(self, ctx: context.Context, request: pb.CARequest, options: List[Callable] = None) -> pb.CAReply:
        """Client stream method
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of rsp pb
        """
        pass

    async def asyncChangeAnal(self, ctx: context.Context, request: pb.CARequest, options: List[Callable] = None) -> pb.CAReply:
        """Async client stream method
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of rsp pb
        """
        pass
    
    def WChangeAnal(self, ctx: context.Context, request: pb.WCARequest, options: List[Callable] = None) -> pb.CAReply:
        """Client stream method
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of rsp pb
        """
        pass

    async def asyncWChangeAnal(self, ctx: context.Context, request: pb.WCARequest, options: List[Callable] = None) -> pb.CAReply:
        """Async client stream method
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of rsp pb
        """
        pass
    

class ChangeAnalysisClientProxyImpl(ChangeAnalysisClientProxy):
    """Client proxy implementation"""
    def __init__(self, service_name: str = None, fix_rpc_name: bool = True):
        self.client = client.get_client()
        self.options = {}

    @staticmethod
    def _clone_client_message(ctx: context.Context, rpc_name: str, callee_method: str) -> Tuple[context.Context, Message]:
        """Clone client message
        :param ctx: context of data processing.
        :param rpc_name: name of rpc.
        :param callee_method: method of callee
        :return: tuple
        """
        ctx, msg = codec.clone_client_message(ctx)
        msg.set_client_rpc_name(rpc_name)
        msg.set_callee_service_name(ChangeAnalysisServiceName)
        msg.set_callee_method(callee_method)
        return ctx, msg

    
    def ChangeAnal(self, ctx: context.Context, request: pb.CARequest, options: List[Callable] = None) -> pb.CAReply:
        """Client rpc method implementation
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of pb
        """
        rsp_cls = pb.CAReply
        options = options or []

        ctx, msg = self._clone_client_message(ctx, '/trpc.test.rpchttp.ChangeAnalysis/ChangeAnal', 'ChangeAnal')
        return self.client.invoke_sync(ctx, request, rsp_cls, options)

    async def asyncChangeAnal(self, ctx: context.Context, request: pb.CARequest, options: List[Callable] = None) -> pb.CAReply:
        """Async client rpc method implementation
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of pb
        """
        rsp_cls = pb.CAReply
        options = options or []

        ctx, msg = self._clone_client_message(ctx, '/trpc.test.rpchttp.ChangeAnalysis/ChangeAnal', 'ChangeAnal')
        rsp = await self.client.invoke(ctx, request, rsp_cls, options)
        return rsp
    
    def WChangeAnal(self, ctx: context.Context, request: pb.WCARequest, options: List[Callable] = None) -> pb.CAReply:
        """Client rpc method implementation
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of pb
        """
        rsp_cls = pb.CAReply
        options = options or []

        ctx, msg = self._clone_client_message(ctx, '/trpc.test.rpchttp.ChangeAnalysis/WChangeAnal', 'WChangeAnal')
        return self.client.invoke_sync(ctx, request, rsp_cls, options)

    async def asyncWChangeAnal(self, ctx: context.Context, request: pb.WCARequest, options: List[Callable] = None) -> pb.CAReply:
        """Async client rpc method implementation
        :param ctx: context of data processing.
        :param request: object of pb.
        :param options: list of setting functions
        :return: object of pb
        """
        rsp_cls = pb.CAReply
        options = options or []

        ctx, msg = self._clone_client_message(ctx, '/trpc.test.rpchttp.ChangeAnalysis/WChangeAnal', 'WChangeAnal')
        rsp = await self.client.invoke(ctx, request, rsp_cls, options)
        return rsp
    