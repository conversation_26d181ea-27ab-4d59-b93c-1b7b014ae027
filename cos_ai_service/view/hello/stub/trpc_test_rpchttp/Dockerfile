## 开发环境、运行环境 不含cuda
#FROM mirrors.tencent.com/todacc/trpc-python-compile_tlinux3.1:0.1.3

# 开发环境 含cuda
FROM mirrors.tencent.com/devcloud/codev-tlinux3-gpu:0.0.2

WORKDIR /
COPY . .

RUN yum update -y && \
    yum install -y epel-release && \
    yum install -y python38 python38-devel python38-pip

RUN pip3.8 install -r requirements.txt

EXPOSE 6666
EXPOSE 7777

CMD pip3.8 install -r requirements.txt && python3.8 view/hello/trpc_main.py