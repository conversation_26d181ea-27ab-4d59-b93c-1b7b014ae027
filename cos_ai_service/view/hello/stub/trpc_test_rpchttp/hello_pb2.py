# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hello.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bhello.proto\x12\x11trpc.test.rpchttp\"\x80\x02\n\tCARequest\x12\x12\n\ntimestamps\x18\x01 \x03(\x05\x12\x32\n\x0bmetric_data\x18\x02 \x01(\x0b\x32\x1d.trpc.test.rpchttp.MetricData\x12\x32\n\x0bmetric_info\x18\x03 \x01(\x0b\x32\x1d.trpc.test.rpchttp.MetricInfo\x12\x38\n\x0cwmetric_data\x18\x06 \x01(\x0b\x32\".trpc.test.rpchttp.WholeMetricData\x12+\n\x05param\x18\x04 \x01(\x0b\x32\x1c.trpc.test.rpchttp.Parameter\x12\x10\n\x08trace_id\x18\x05 \x01(\t\"\xed\x01\n\nWCARequest\x12\x32\n\x0bmetric_data\x18\x02 \x01(\x0b\x32\x1d.trpc.test.rpchttp.MetricData\x12\x32\n\x0bmetric_info\x18\x03 \x01(\x0b\x32\x1d.trpc.test.rpchttp.MetricInfo\x12\x38\n\x0cwmetric_data\x18\x06 \x01(\x0b\x32\".trpc.test.rpchttp.WholeMetricData\x12+\n\x05param\x18\x04 \x01(\x0b\x32\x1c.trpc.test.rpchttp.Parameter\x12\x10\n\x08trace_id\x18\x05 \x01(\t\"\x8a\x01\n\x0fWholeMetricData\x12\r\n\x05label\x18\x01 \x01(\t\x12\x12\n\ntimestamps\x18\x02 \x03(\x05\x12\x12\n\nmetric_val\x18\x03 \x03(\x01\x12\x12\n\nval_status\x18\x04 \x03(\x05\x12\x15\n\ris_change_err\x18\x05 \x03(\x05\x12\x15\n\rchange_period\x18\x06 \x03(\x05\"e\n\nMetricData\x12)\n\x06series\x18\x01 \x03(\x0b\x32\x19.trpc.test.rpchttp.Metric\x12\x15\n\ris_change_err\x18\x02 \x03(\x05\x12\x15\n\rchange_period\x18\x03 \x01(\x08\"?\n\x06Metric\x12\r\n\x05label\x18\x01 \x01(\t\x12\x12\n\nmetric_val\x18\x02 \x03(\x01\x12\x12\n\nval_status\x18\x03 \x03(\x05\"\'\n\nMetricInfo\x12\x19\n\x11zhiyan_cal_method\x18\x01 \x01(\t\"\x8e\x02\n\tParameter\x12\x17\n\x0fparam_sensitive\x18\x01 \x01(\x05\x12;\n\x14param_valid_interval\x18\x02 \x01(\x0b\x32\x1d.trpc.test.rpchttp.OMInterval\x12\x32\n\nparam_list\x18\x03 \x01(\x0b\x32\x1e.trpc.test.rpchttp.OMParamList\x12\x39\n\x11param_list_sparse\x18\x04 \x01(\x0b\x32\x1e.trpc.test.rpchttp.OMParamList\x12<\n\x14param_list_no_change\x18\x05 \x01(\x0b\x32\x1e.trpc.test.rpchttp.OMParamList\"6\n\nOMInterval\x12\x13\n\x0bparam_lolmt\x18\x02 \x01(\x02\x12\x13\n\x0bparam_hglmt\x18\x03 \x01(\x02\"\xa8\x02\n\x0bOMParamList\x12\x15\n\rparam_percent\x18\x01 \x01(\x02\x12\x14\n\x0cparam_sparse\x18\x02 \x01(\x02\x12\x14\n\x0cparam_smooth\x18\x03 \x01(\x02\x12\x11\n\tparam_dtw\x18\x04 \x01(\x02\x12\x13\n\x0bparam_sigma\x18\x05 \x01(\x02\x12\x15\n\rparam_cos_sim\x18\x06 \x01(\x02\x12\x18\n\x10param_downsample\x18\x07 \x01(\x02\x12\x15\n\rparam_dtw_low\x18\x08 \x01(\x02\x12\x16\n\x0eparam_dtw_high\x18\t \x01(\x02\x12\x19\n\x11time_window_focus\x18\n \x01(\x05\x12\x17\n\x0ftime_window_dtw\x18\x0b \x01(\x05\x12\x1a\n\x12time_window_nsigma\x18\x0c \x01(\x05\"g\n\x07\x43\x41Reply\x12:\n\x0f\x61\x62normal_points\x18\x01 \x03(\x0b\x32!.trpc.test.rpchttp.Abnormalpoints\x12\x0e\n\x06logmsg\x18\x02 \x01(\t\x12\x10\n\x08trace_id\x18\x03 \x01(\t\"9\n\x0e\x41\x62normalpoints\x12\x11\n\ttimestamp\x18\x01 \x01(\x05\x12\x14\n\x0c\x61\x62normal_msg\x18\x02 \x01(\t2\xa6\x01\n\x0e\x43hangeAnalysis\x12H\n\nChangeAnal\x12\x1c.trpc.test.rpchttp.CARequest\x1a\x1a.trpc.test.rpchttp.CAReply\"\x00\x12J\n\x0bWChangeAnal\x12\x1d.trpc.test.rpchttp.WCARequest\x1a\x1a.trpc.test.rpchttp.CAReply\"\x00\x62\x06proto3')



_CAREQUEST = DESCRIPTOR.message_types_by_name['CARequest']
_WCAREQUEST = DESCRIPTOR.message_types_by_name['WCARequest']
_WHOLEMETRICDATA = DESCRIPTOR.message_types_by_name['WholeMetricData']
_METRICDATA = DESCRIPTOR.message_types_by_name['MetricData']
_METRIC = DESCRIPTOR.message_types_by_name['Metric']
_METRICINFO = DESCRIPTOR.message_types_by_name['MetricInfo']
_PARAMETER = DESCRIPTOR.message_types_by_name['Parameter']
_OMINTERVAL = DESCRIPTOR.message_types_by_name['OMInterval']
_OMPARAMLIST = DESCRIPTOR.message_types_by_name['OMParamList']
_CAREPLY = DESCRIPTOR.message_types_by_name['CAReply']
_ABNORMALPOINTS = DESCRIPTOR.message_types_by_name['Abnormalpoints']
CARequest = _reflection.GeneratedProtocolMessageType('CARequest', (_message.Message,), {
  'DESCRIPTOR' : _CAREQUEST,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.CARequest)
  })
_sym_db.RegisterMessage(CARequest)

WCARequest = _reflection.GeneratedProtocolMessageType('WCARequest', (_message.Message,), {
  'DESCRIPTOR' : _WCAREQUEST,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.WCARequest)
  })
_sym_db.RegisterMessage(WCARequest)

WholeMetricData = _reflection.GeneratedProtocolMessageType('WholeMetricData', (_message.Message,), {
  'DESCRIPTOR' : _WHOLEMETRICDATA,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.WholeMetricData)
  })
_sym_db.RegisterMessage(WholeMetricData)

MetricData = _reflection.GeneratedProtocolMessageType('MetricData', (_message.Message,), {
  'DESCRIPTOR' : _METRICDATA,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.MetricData)
  })
_sym_db.RegisterMessage(MetricData)

Metric = _reflection.GeneratedProtocolMessageType('Metric', (_message.Message,), {
  'DESCRIPTOR' : _METRIC,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.Metric)
  })
_sym_db.RegisterMessage(Metric)

MetricInfo = _reflection.GeneratedProtocolMessageType('MetricInfo', (_message.Message,), {
  'DESCRIPTOR' : _METRICINFO,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.MetricInfo)
  })
_sym_db.RegisterMessage(MetricInfo)

Parameter = _reflection.GeneratedProtocolMessageType('Parameter', (_message.Message,), {
  'DESCRIPTOR' : _PARAMETER,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.Parameter)
  })
_sym_db.RegisterMessage(Parameter)

OMInterval = _reflection.GeneratedProtocolMessageType('OMInterval', (_message.Message,), {
  'DESCRIPTOR' : _OMINTERVAL,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.OMInterval)
  })
_sym_db.RegisterMessage(OMInterval)

OMParamList = _reflection.GeneratedProtocolMessageType('OMParamList', (_message.Message,), {
  'DESCRIPTOR' : _OMPARAMLIST,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.OMParamList)
  })
_sym_db.RegisterMessage(OMParamList)

CAReply = _reflection.GeneratedProtocolMessageType('CAReply', (_message.Message,), {
  'DESCRIPTOR' : _CAREPLY,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.CAReply)
  })
_sym_db.RegisterMessage(CAReply)

Abnormalpoints = _reflection.GeneratedProtocolMessageType('Abnormalpoints', (_message.Message,), {
  'DESCRIPTOR' : _ABNORMALPOINTS,
  '__module__' : 'hello_pb2'
  # @@protoc_insertion_point(class_scope:trpc.test.rpchttp.Abnormalpoints)
  })
_sym_db.RegisterMessage(Abnormalpoints)

_CHANGEANALYSIS = DESCRIPTOR.services_by_name['ChangeAnalysis']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _CAREQUEST._serialized_start=35
  _CAREQUEST._serialized_end=291
  _WCAREQUEST._serialized_start=294
  _WCAREQUEST._serialized_end=531
  _WHOLEMETRICDATA._serialized_start=534
  _WHOLEMETRICDATA._serialized_end=672
  _METRICDATA._serialized_start=674
  _METRICDATA._serialized_end=775
  _METRIC._serialized_start=777
  _METRIC._serialized_end=840
  _METRICINFO._serialized_start=842
  _METRICINFO._serialized_end=881
  _PARAMETER._serialized_start=884
  _PARAMETER._serialized_end=1154
  _OMINTERVAL._serialized_start=1156
  _OMINTERVAL._serialized_end=1210
  _OMPARAMLIST._serialized_start=1213
  _OMPARAMLIST._serialized_end=1509
  _CAREPLY._serialized_start=1511
  _CAREPLY._serialized_end=1614
  _ABNORMALPOINTS._serialized_start=1616
  _ABNORMALPOINTS._serialized_end=1673
  _CHANGEANALYSIS._serialized_start=1676
  _CHANGEANALYSIS._serialized_end=1842
# @@protoc_insertion_point(module_scope)
