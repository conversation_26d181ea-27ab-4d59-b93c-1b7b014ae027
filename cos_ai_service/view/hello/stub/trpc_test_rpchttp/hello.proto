syntax = "proto3";
package trpc.test.rpchttp; //修改

service ChangeAnalysis {
  rpc ChangeAnal (CARequest) returns (CAReply) {}
  rpc WChangeAnal (WCARequest) returns (CAReply) {}
}

message CARequest {
  repeated int32 timestamps = 1;      // 时间戳列表
  MetricData metric_data = 2;
  MetricInfo metric_info = 3;
  WholeMetricData wmetric_data = 6; // 干掉
  Parameter param = 4;
  string trace_id = 5;
}

message WCARequest {
  MetricData metric_data = 2;
  MetricInfo metric_info = 3;
  WholeMetricData wmetric_data = 6;
  Parameter param = 4;
  string trace_id = 5;
}

message WholeMetricData {
  string label = 1;                     // 指标标签
  repeated int32 timestamps = 2;
  repeated double metric_val = 3;        // 指标值列表
  repeated int32 val_status = 4;
  repeated int32 is_change_err = 5;
  repeated int32 change_period = 6;      // 变更时间戳
}


message MetricData {
  repeated Metric series = 1;
  repeated int32 is_change_err = 2;
  bool change_period = 3;      // 变更时间戳
}
message Metric {
  string label = 1;                     // 指标标签
  repeated double metric_val = 2;        // 指标值列表
  repeated int32 val_status = 3;        // 上报状态 默认0是上报
}

message MetricInfo {
  string zhiyan_cal_method = 1;
}

message Parameter{
  int32 param_sensitive = 1; // 算法灵敏度
  OMInterval param_valid_interval = 2;
  OMParamList param_list = 3;
  OMParamList param_list_sparse = 4; // 针对稀疏数据的参数
  OMParamList param_list_no_change = 5;  // 针对非变更期间的参数
}
message OMInterval{
  float param_lolmt = 2;
  float param_hglmt = 3;
}
message OMParamList {
  float param_percent = 1;
  float param_sparse = 2;
  float param_smooth = 3;
  float param_dtw = 4;
  float param_sigma = 5;
  float param_cos_sim = 6;

  float param_downsample = 7; //注意这个要更新
  float param_dtw_low = 8;
  float param_dtw_high = 9;

  int32 time_window_focus = 10;
  int32 time_window_dtw = 11;
  int32 time_window_nsigma = 12;
}


message CAReply {
  repeated Abnormalpoints abnormal_points = 1;      // 变更故障时间戳
  string logmsg = 2;                 // 暂时没想好，预留str输出
  string trace_id = 3;
}

message Abnormalpoints {
    int32 timestamp = 1;
    string abnormal_msg = 2;
}