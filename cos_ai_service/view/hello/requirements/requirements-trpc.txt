# proxy pypi
--index-url https://mirrors.cloud.tencent.com/pypi/simple/
# private pypi
--extra-index-url https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple/


# Install package need

wheel

# add the requirements for trpc project

protobuf==3.19.4
PyYAML==5.1.0
aiohttp==3.6.2
netifaces==0.10.9
concurrent-log-asyncio==0.10.1
automaxprocs==2.0.0
Cython==0.29.21
six==1.15.0
pytest==5.2.2
python-snappy>=0.5.4
uvloop>=0.10.2
python-gflags>=3.1.2
setproctitle

# trpc framework and plugins

trpc>=0.6.3

# trpc plugins
trpc-log-atta
trpc-metrics-m007
trpc-metrics-runtime
trpc-naming-polaris<=0.4.2
trpc-opentracing-tjg

# sdk 

attaapi
m007-metrics
polaris-python<0.3.2
tconf-python
tjg-opentracing


# add for your project
# Add the stub package like this:
#
# git+http://git.woa.com/trpc-python/stubs/stub-package.git#egg=stub-package
# 
#
