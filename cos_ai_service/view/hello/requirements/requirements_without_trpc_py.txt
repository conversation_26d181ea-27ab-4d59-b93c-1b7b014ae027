# numpy
pandas
# torch
# torchvision
# torchaudio
tqdm
dtaidistance
cupy-cuda12x

# proxy pypi
--index-url https://mirrors.cloud.tencent.com/pypi/simple/
# private pypi
--extra-index-url https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple/

# trpc-python依赖
python-gflags==3.1.2
protobuf>=2.5,<=3.19.4
PyYAML
aiohttp>3.6.2
netifaces==0.10.9
uvloop>=0.10.2
concurrent-log-asyncio==0.10.1
automaxprocs==2.0.0
Cython>=0.29.21
six==1.15.0
aiocron>=1.6
pytest>=5.2.2
python-snappy>=0.5.4

# 用于管理 protobuf 的包
trpc-pb==0.3.0
trpc==0.8.0 #注意不要漏了
trpc_metrics_zhiyan==0.1.0a2 #注意不要漏了

#北极星
polaris-python
trpc-naming-polaris
trpc_metrics_runtime

# 不必装的
psutil
matplotlib # 用于绘图
schedule # 用于定时任务