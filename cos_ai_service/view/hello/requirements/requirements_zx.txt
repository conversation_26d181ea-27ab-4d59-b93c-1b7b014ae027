python==3.8
aiocron==1.8
aiohappyeyeballs==2.4.4
aiohttp==3.6.2
aiosignal==1.3.1
async-timeout==3.0.1
atomicwrites==1.4.1
attaapi==1.0.1
attrs==24.2.0
automaxprocs==2.0.0
backports.zoneinfo==0.2.1
certifi==2024.8.30
chardet==3.0.4
charset-normalizer==3.4.0
coloredlogs==15.0.1
concurrent-log-asyncio==0.10.1
contourpy==1.1.1
coverage==7.6.1
cramjam==2.9.1
croniter==5.0.1
cycler==0.12.1
Cython==0.29.21
dtaidistance==2.3.12
dtw==1.4.0
enum34==1.1.10
exceptiongroup==1.2.2
fastdtw==0.3.4
filelock==3.16.1
flatbuffers==24.3.25
fonttools==4.55.3
frozenlist==1.5.0
fsspec==2024.9.0
huggingface-hub==0.26.1
humanfriendly==10.0
idna==3.10
importlib-resources==6.4.5
iniconfig==2.0.0
jinja2==3.1.4
kiwisolver==1.4.7
logten==1.0.10
m007-metrics==0.3.4
MarkupSafe==2.1.5
matplotlib==3.7.5
more-itertools==10.5.0
mpmath==1.3.0
multidict==4.7.6
netifaces==0.10.9
networkx==3.1
numpy==1.24.4
nvidia-cublas-cu12==12.1.3.1
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==8.9.2.26
nvidia-cufft-cu12==11.0.2.54
nvidia-curand-cu12==10.3.2.106
nvidia-cusolver-cu12==11.4.5.107
nvidia-cusparse-cu12==12.1.0.106
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.77
nvidia-nvtx-cu12==12.1.105
onnxruntime==1.16.3
opentracing==2.4.0
packaging==24.1
pandas==2.0.3
pillow==10.4.0
pluggy==0.13.1
polaris-python==0.3.1
portalocker==3.0.0
propcache==0.2.0
protobuf==3.19.4
psutil==6.1.1
py==1.11.0
pyparsing==3.1.4
pytest==5.2.2
pytest-asyncio==0.24.0
pytest-cov==5.0.0
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-gflags==3.1.2
python-snappy==0.7.3
pytz==2024.2
PyYAML==6.0.2
regex==2024.9.11
requests==2.32.3
scipy==1.10.1
setproctitle==1.3.4
six==1.15.0
sympy==1.13.3
tconf-python==0.1.20
tegmonitor==3.1.6
tjg-opentracing==0.0.4
tokenizers==0.12.1
tomli==2.2.1
torch==2.3.0
torch-zx==3.1.20240926
torchvision==0.18.0
tqdm==4.66.5
transformers==4.21.2
triton==2.3.0
trpc==0.8.0
trpc-log-atta==1.2.1
trpc-log-zhiyan==0.2.3
trpc-metrics-m007==0.1.5
trpc-metrics-runtime==0.0.7
trpc-metrics-zhiyan==0.1.0a1
trpc-naming-polaris==0.4.2
trpc-opentracing-tjg==0.0.7
typing-extensions==4.12.2
tzdata==2024.2
tzlocal==5.2
urllib3==2.2.3
uvloop==0.21.0
wcwidth==0.2.13
yarl==1.15.2
zipp==3.20.2