global: #全局配置
  namespace: Development            #环境类型，分正式 production 和非正式 development 两种类型
  env_name: test                    #环境名称，非正式环境下多环境的名称
  container_name:                   #容器名
  local_ip:                         #宿主机 IP

server: #服务端配置
  app: test                                        #业务的应用名
  server: rpchttp                                   #进程服务名
  filter:
    - zhiyan
  bin_path: /usr/local/trpc/bin/                   #二进制可执行文件和框架配置文件所在路径
  conf_path: /usr/local/trpc/conf/                 #业务配置
  data_path: /usr/local/trpc/data/                 #业务数据
  task_wait_interval: 100                         #任务等待间隔 单位 毫秒 等待100ms组batch 立即执行handler
  task_batch_size: 10                            #任务批处理大小
  # todo 加gpu负载均衡
  #task_queue_size: 1000                         # 默认1000 100ms内超过1000个任务会被丢弃
  task_consumer_num: 1                            # 1个进程内同时几个线程消费任务队列
  # worker_num: 16                                 #工作进程数 默认机器核心数
  service: #业务服务提供的 service，可以有多个
    - name: trpc.test.rpchttp.ChangeAnalysis.restful      #service 的路由名称
      ip: 0.0.0.0
      port: 6666
      network: tcp
      protocol: http
      timeout: 600000
      registry: polaris
    #    - name: trpc.test.rpchttp.ChangeAnalysis.tcp      #service 的路由名称
    #      ip: 0.0.0.0
    #      port: 7777
    #      network: tcp
    #      protocol: trpc
    #      timeout: 1000                             #请求最长处理时间 单位 毫秒
    #      registry: polaris



client: #客户端调用的后端配置
  timeout: 1000                                    #针对所有后端的请求最长处理时间
  namespace: Development                           #针对所有后端的环境
  service: #针对单个后端的配置
    - name: trpc.test.rpchttp.ChangeAnalysis      #后端服务的 service name
      network: tcp                                 #后端服务的网络类型 tcp udp 配置优先
      timeout: 1000                                 #请求最长处理时间
      protocol: trpc               #应用层协议 trpc http
  filter:
    - zhiyan


plugins: #插件配置
  config:
    rainbow:
      addr: "http://api.rainbow.woa.com:8080"  # 七彩石平台地址
      providers:
        - name: rainbow1
          group: change_analysis.apiserver
          type: kv
          enable_sign: true
          app_id: "119862d0-a489-4a31-96fd-2984bab9b12d"
          user_id: "f6209589590e479681bd2eaaef58ff47"
          secret_key:
          group_name: 'changeanalysis-ai'  # 对应七彩石上的分组名
          env_name: test  # 对应七彩石上的环境名，如果没有区分环境，则不填

  log: #日志配置
    default: #默认日志的配置，可支持多输出
      - writer: console                           #控制台标准输出 默认
        level: debug                              #标准输出日志的级别
      - writer: file                              #本地文件日志
        level: info                               #本地文件滚动日志的级别
        formatter: json                           #标准输出日志的格式
        writer_config:
          filename: ./trpc.log                      #本地文件滚动日志存放的路径
          max_size: 10                              #本地文件滚动日志的大小 单位 MB
          max_backups: 10                           #最大日志文件数
          max_age: 7                                #最大日志保留天数
          compress: false                          #日志文件是否压缩
      - writer: zhiyan
        level: debug
        remote_config:
          report_topic: 'sdk-3de51e75410afba8'
          report_addr: 'log-report-gz.zhiyan.tencent-cloud.net:11001'
  registry: #服务注册配置
    polaris: #北极星名字注册服务的配置
      register_self: true                             #是否注册，默认为 false，由 123 平台注册
      heartbeat_interval: 4000                         #名字注册服务心跳上报间隔
      # debug: true                                    #是否开启北极星 sdk 的debug 日志
      service:
        - name: trpc.test.rpchttp.ChangeAnalysis.restful
          namespace: Test              #环境类型，分正式Production和非正式Development两种类型
          token:
  selector:
    polaris:
      protocol: trpc
  metrics:
    zhiyan: # 监控宝设置
      attrAppMark: 7175_145846_ChangeAnalysis-AI #自定义的属性上报使用的智研指标组
      attrMetricGroup: default #自定义属性上报时添加的维度
      frameCode: trpc      #自定义的主调上报appMark
      env: test # 环境名称
      defaultCustomAppMark: 7175_145846_ChangeAnalysis-AI
      defaultCustomMetricGroup: default #智研上报sdk的配置
      agentConfig:
        #智研agent的相关配置
        agent:
          #agent_config_file: /data/zhiyan/agent/etc/agent.yaml
          queue_size: 1000001
          send_gap: 1001
        logconfig:
          path: ./ #注意这个路径
          name: zhiyan_python_sdk.log
          level: debug
          size: 104857600
          number: 5
          close_log: false
      reportMode: "attr"
      # 智研监控宝模调监控应用标记默认值是体验项目，使用时必须自己重新定义
      aModAppMark: 7175_145846_ChangeAnalysis-AI
      pModAppMark: 7175_145846_ChangeAnalysis-AI
      # 智研监控宝模调监控指标组默认是default，与其界面完全对不上
      aModMetricGroup: "client_report"
      pModMetricGroup: "server_report"
