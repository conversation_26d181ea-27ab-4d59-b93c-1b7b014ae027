# -*- coding: utf-8 -*-
"""Business logic module """
from trpc import context

from view.hello.stub.trpc_test_rpchttp import pb
from view.hello.stub.trpc_test_rpchttp import rpc
from trpc import task
import os
import json
from model.data_preprocess import data_preprocess
import time
import datetime
from trpc.log import logger
import asyncio

from sympy import false

from model.data_preprocess import data_preprocess, smooth_ewma, check_interval, timeseries_list2str, \
    in_valid_interval
from model.model_static.similarity import analysis_DTW, analysis_cos_sim, analysis_D2  # , analysis_D3
from model.model_static.outlier import analysis_nsigma, analysis_nsigma_sparse
from model.data_postprocess import get_abnormal_points
from trpc.metrics import report_attr, report_custom
from trpc.trpc_abc.metrics import StatPolicy, StatValue, CustomData
from trpc.server import Context

from model.data_postprocess import get_abnormal_msg

from model.data_preprocess import downsample
from trpc import task
from model.change_analysis import algo_sparse
import numpy as np
import asyncio
from model.change_analysis import change_analysis


class ChangeAnalysisServicer(rpc.ChangeAnalysisServicer):
    """Provides methods that implement functionality of ChangeAnalysis servicer."""

    def __init__(self):
        pass

    async def ChangeAnal(self, ctx: context.Context, request: pb.CARequest) -> pb.CAReply:
        # 在这里获取配置生效，不能在change_analysis中生效的原因是进程隔离，InitConfigPlugin 的配置初始化仅在其所属的worker进程中生效，change_analysis运行在独立子进程，会继承父进程的初始空配置
        from config import global_conf
        default_param = global_conf()
        if not request.param.HasField("param_list"):
            t = "param_list"
            param_list = pb.OMParamList(
                param_sparse=default_param[t]["param_sparse"],
                param_percent=default_param[t]["param_percent"],
                param_smooth=default_param[t]["param_smooth"],
                param_dtw=default_param[t]["param_dtw"],
                param_downsample=default_param[t]["param_downsample"],
                param_sigma=default_param[t]["param_sigma"],
                param_cos_sim=default_param[t]["param_cos_sim"],
                param_dtw_low=default_param[t]["param_dtw_low"],
                param_dtw_high=default_param[t]["param_dtw_high"],
                time_window_focus=default_param[t]["time_window_focus"],
                time_window_dtw=default_param[t]["time_window_dtw"],
                time_window_nsigma=default_param[t]["time_window_nsigma"]
            )
            request.param.param_list.CopyFrom(param_list)
        if not request.param.HasField("param_valid_interval"):
            param_valid_interval = pb.OMInterval(
                param_lolmt=default_param["param_valid_interval"]["param_lolmt"],
                param_hglmt=default_param["param_valid_interval"]["param_hglmt"]
            )
            request.param.param_valid_interval.CopyFrom(param_valid_interval)
        if not request.param.HasField("param_list_sparse"):
            t = "param_list_sparse"
            param_list_sparse = pb.OMParamList(
                param_sparse=default_param[t]["param_sparse"],
                param_percent=default_param[t]["param_percent"],
                param_smooth=default_param[t]["param_smooth"],
                param_dtw=default_param[t]["param_dtw"],
                param_downsample=default_param[t]["param_downsample"],
                param_sigma=default_param[t]["param_sigma"],
                param_cos_sim=default_param[t]["param_cos_sim"],
                param_dtw_low=default_param[t]["param_dtw_low"],
                param_dtw_high=default_param[t]["param_dtw_high"],
                time_window_focus=default_param[t]["time_window_focus"],
                time_window_dtw=default_param[t]["time_window_dtw"],
                time_window_nsigma=default_param[t]["time_window_nsigma"]
            )
            request.param.param_list_sparse.CopyFrom(param_list_sparse)
        if not request.param.HasField("param_list_no_change"):
            t = "param_list_no_change"
            param_list_no_change = pb.OMParamList(
                param_sparse=default_param[t]["param_sparse"],
                param_percent=default_param[t]["param_percent"],
                param_smooth=default_param[t]["param_smooth"],
                param_dtw=default_param[t]["param_dtw"],
                param_downsample=default_param[t]["param_downsample"],
                param_sigma=default_param[t]["param_sigma"],
                param_cos_sim=default_param[t]["param_cos_sim"],
                param_dtw_low=default_param[t]["param_dtw_low"],
                param_dtw_high=default_param[t]["param_dtw_high"],
                time_window_focus=default_param[t]["time_window_focus"],
                time_window_dtw=default_param[t]["time_window_dtw"],
                time_window_nsigma=default_param[t]["time_window_nsigma"]
            )
            request.param.param_list_no_change.CopyFrom(param_list_no_change)
        """Abstract rpc method needed to implementation"""
        data = {
            "timestamps": request.timestamps,
            "metric_data": request.metric_data,
            "metric_info": request.metric_info,
            "param": request.param,
            "trace_id": request.trace_id
        }

        abnormal_points, logmsg = await change_analysis(ctx, data)  # ctx 用来搞日志的

        rsp_pb = pb.CAReply()
        rsp_pb.abnormal_points.extend(abnormal_points)
        rsp_pb.logmsg = logmsg
        rsp_pb.trace_id = data["trace_id"]

        return rsp_pb
