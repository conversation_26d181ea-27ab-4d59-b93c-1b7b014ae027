# -*- coding: utf-8 -*-
"""This is server module"""

import os
import sys
from typing import List
import gflags
import trpc
from trpc.log import logger
import subprocess
import trpc_metrics_runtime
import trpc_naming_polaris

# 七彩石读取配置
import trpc_rainbow
from trpc.config import with_provider, with_codec, sync_load
from trpc.plugin import register_plugin, PluginType, PluginInitPos
from config import global_conf, set_global_conf, cb

import logging
from zhiyanlog import setup_logger
from zhiyanlog import zhiyan, __remove_internal_loggers
from zhiyanlog import LogFormatter, <PERSON>sonFormatter


def print_debug_info():
    # 获取桩代码目录
    stub_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "stub"))
    sys.path.append(stub_path)  # noqa
    print("stub_path:", stub_path)
    # 获取根目录
    # root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    sys.path.append(root_dir)
    print("root_dir: ", root_dir)
    print(f"当前工作目录(cwd):{os.getcwd()}")


print_debug_info()

# 导入其他插件
import gpu_preheat
from view.hello.stub.trpc_test_rpchttp import rpc
from change_analysis import ChangeAnalysisServicer


def test_driver():
    # 驱动检测
    try:
        result_zx = subprocess.run(["zx-smi"], capture_output=True, text=True)
        print(result_zx.stdout)
    except FileNotFoundError:
        print("Error: 'zx-smi' command not found.")
    except Exception as e:
        print(f"An error occurred while running 'zx-smi': {e}")

    print("-" * 50)

    try:
        result = subprocess.run(["nvidia-smi"], capture_output=True, text=True)
        print(result.stdout)
    except FileNotFoundError:
        print("Error: 'nvidia-smi' command not found.")
    except Exception as e:
        print(f"An error occurred while running 'nvidia-smi': {e}")


test_driver()


@register_plugin(PluginType.USER_DEFINED, "load_config", init_pos=PluginInitPos.WORKER)
class InitConfigPlugin:
    """配置初始化插件
    InitConfigPlugin通过@register_plugin被注册为worker级插件
    """

    def __init__(self):
        # config.yaml为七彩石上配置文件名，rainbow1为trpc_python.yaml中对应的provider name
        conf = sync_load("default", with_provider("rainbow1"), with_codec("JsonDecoder"))
        set_global_conf(conf.config_data)
        conf.watch(cb)


try:
    # import sdk and plugins
    # pylint: disable=unused-import
    import trpc_metrics_zhiyan
    import trpc_log_zhiyan
    import trpc_naming_polaris
    import trpc_log_atta

    # DO NOT remove the plugin below!
    import trpc_metrics_runtime

except ImportError as e:
    logger.info(f"fail to import plugins {e}")
    # raise e

# gflags cmd
FLAGS = gflags.FLAGS

# define config file
gflags.DEFINE_string('conf', os.path.abspath(os.path.join(os.path.dirname(__file__), "trpc_python.yaml")),
                     'trpc python framework config file')


def usage():
    """使用方式"""

    help_str = f"\nFlags from {__file__} :\n"
    for name, val in FLAGS.FlagDict().items():
        help_str += f"  --{name} ({val.help}) val: {val.value} type: {type(val.value).__name__} \n"
        help_str += f"    default : {val.default} \n"
    help_str += "\nstart server with conf, for example: `python3 trpc_main.py  --conf=trpc_python.yaml` \n"
    print(help_str)
    sys.exit(1)


def parse_args(argv: List[str]):
    """解析命令行参数
    :param argv: 命令行参数
    """

    if "-c" in argv:
        index = argv.index("-c")
        argv[index] = "--conf"
    try:
        res = FLAGS(argv)
        if len(res) > 1:
            print(f"args: `{res[1:]}` is invalid !!! \n")
    except Exception as err:  # pylint: disable=broad-exception-caught
        if isinstance(err, gflags.UnrecognizedFlagError):
            if err.flagname in ["help", "h"]:
                usage()
        print(f"{err}\n")
        usage()


def serve(conf_path: str):
    """启动服务
    :param conf_path: 配置文件路径
    """

    svr = trpc.new("view/hello/trpc_python.yaml")

    rpc.register_ChangeAnalysisServicer_server(svr, ChangeAnalysisServicer())

    svr.serve()


def zhiyan_log():
    format = LogFormatter(color=False)
    logger = setup_logger(formatter=format, logger=logging.getLogger(),
                          level=logging.INFO, file_log_level=logging.INFO)
    __remove_internal_loggers(logger)
    zhiyan.setup('sdk-3de51e75410afba8',
                 env='dev',
                 formatter=format,
                 logger=logger,
                 debug=False)


def main():
    """主函数"""

    # 解析命令行参数
    parse_args(sys.argv)
    logger.info("load config %s", FLAGS.conf)

    # 日志汇
    from trpc import fork_hook
    fork_hook.register_at_fork(after_in_child=zhiyan_log)
    # ######===== 【导入并注册rainbow】  start=================
    # import cos_ai_service.rainbow.rainbow as rainbow
    #
    # # 2. 注册子进程创建后回调函数，在子进程中初始化自己的七彩石对象
    # from trpc import fork_hook
    # fork_hook.register_at_fork(after_in_child=rainbow.after_fork)
    # ######===== 【导入并注册rainbow】  end=================

    # 启动服务
    serve(FLAGS.conf)


if __name__ == '__main__':
    # sys.path.append(os.path.abspath(os.path.dirname(__file__)))  # 工作目录 ljh

    sys.exit(main())
