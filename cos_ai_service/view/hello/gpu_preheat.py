from trpc.plugin import PluginType, Task, register_plugin
from model.model_static.dtw_gpu import test_dtw_batch
from trpc.log import logger
import numpy as np
from model.model_static.dtw_gpu import dtw_cuda_batch


def handler(reqs):
    logger.info("插件handler进入")

    x_batch = [np.array(data["x"], dtype=np.float32) for data in reqs]
    y_batch = [np.array(data["y"], dtype=np.float32) for data in reqs]

    slice_batch = dtw_cuda_batch(x_batch, y_batch)

    rsps = slice_batch
    return rsps


def initializer():
    # 任务初始化
    test_dtw_batch()  # 改成batch预热
    print("预热完成")


@register_plugin(plugin_type=PluginType.USER_DEFINED, plugin_name="task_test",
                 task=Task(handler, initializer=initializer, use_c_task=False))  # 16核 发到2个task上  #, task_num=2
def load_task():
    pass
