# -*- coding: utf-8 -*-
"""This is test client module"""

import logging
import os
import sys
import asyncio

from trpc import context
from trpc.client.options import with_target
from trpc.client.options import with_protocol
from trpc.client.options import with_serialization_type
from trpc.client.options import with_network
from trpc.codec.serialization import SerializationType

# add the stub to the path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "stub")))  # noqa


from trpc_test_rpchttp import pb
from trpc_test_rpchttp import rpc

test_logger = logging.getLogger()
test_logger.setLevel(level=logging.DEBUG)
async_test = []
sync_test = []
    

import json
import os
#pylint: disable=invalid-name
def test_sync_ChangeAnalysis_ChangeAnal():
    """Test ChangeAnalysis of ChangeAnal"""
    proxy = rpc.ChangeAnalysisClientProxyImpl()

    # req = pb.CARequest()    # 空
    def read_data(data_file_path):
        req = pb.CARequest()
        with open(data_file_path, "r") as f:
            data = json.load(f)
        req.timestamps.extend(data["timestamps"])

        # 填充 MetricData
        req_metric_data = pb.MetricData()
        req_metric_data.is_change_err.extend(data["metric_data"].get("is_change_err", []))
        req_metric_data.change_period.extend(data["metric_data"].get("change_period", []))
        for series in data["metric_data"].get("series", []):
            t = pb.Metric()
            t.label = series["label"]
            t.metric_val.extend(series["metric_val"])
            t.val_status.extend(series["val_status"])
            req_metric_data.series.append(t) # 添加到 metric_data.series
        req.metric_data.CopyFrom(req_metric_data)

        # 填充 MetricInfo  这里没用到直接为空
        t = pb.MetricInfo()
        # tt = data["metric_info"][
        # t.zhiyan_cal_method =
        req.metric_info.CopyFrom(t)

        # 填充 Parameter
        param_data = data.get("param", {})
        req.param.param_sensitive = param_data.get("param_sensitive", 0)
        # OMParamList
        if "param_list" in param_data:
            om_param_list = pb.OMParamList()
            pl = param_data["param_list"]
            om_param_list.param_percent = pl.get("param_percent", 0.0)
            om_param_list.param_smooth = pl.get("param_smooth", 0.0)
            om_param_list.param_dtw = pl.get("param_dtw", 0.0)
            om_param_list.param_sigma = pl.get("param_sigma", 0.0)
            om_param_list.time_window_focus = pl.get("time_window_focus", 0)
            om_param_list.time_window_dtw = pl.get("time_window_dtw", 0)
            om_param_list.time_window_nsigma = pl.get("time_window_nsigma", 0)
            req.param.param_list.CopyFrom(om_param_list)
        return req
    req = read_data(os.path.join(os.path.dirname(__file__), '../stress_test/metric_data.txt'))
    options = [
        with_target('ip://127.0.0.1:8000'),
        with_protocol('trpc'),
        with_serialization_type(SerializationType.PB),
        with_network('tcp')
    ]
    ctx = context.Context()
    try:
        ret = proxy.ChangeAnal(ctx, req, options)
        print(f"ChangeAnal ret is {ret}")
        assert ret is not None
    except Exception as exc:  # pylint: disable=broad-exception-caught
        test_logger.exception(exc)
        assert 0
sync_test.append(test_sync_ChangeAnalysis_ChangeAnal)

# 只测rpc是否可用 不考虑高并发 不测试
# #pylint: disable=invalid-name
# async def test_ChangeAnalysis_ChangeAnal():
#     """Async test ChangeAnalysis of ChangeAnal"""
#     proxy = rpc.ChangeAnalysisClientProxyImpl()
#     req = pb.CARequest()
#     options = [
#         with_target('ip://127.0.0.1:8000'),
#         with_protocol('trpc'),
#         with_serialization_type(SerializationType.PB),
#         with_network('tcp')
#     ]
#     ctx = context.Context()
#     try:
#         ret = await proxy.asyncChangeAnal(ctx, req, options)
#         print(f"ChangeAnal ret is {ret}")
#         assert ret is not None
#     except Exception as exc:  # pylint: disable=broad-exception-caught
#         test_logger.exception(exc)
#         assert 0
# async_test.append(test_ChangeAnalysis_ChangeAnal())
    


if __name__ == "__main__":
    for test_func in sync_test:
        test_func()
    loop = asyncio.get_event_loop()
    loop.run_until_complete(asyncio.gather(*async_test))
