# -*- coding: utf-8 -*-
import os, sys
from trpc.log import logger

######################################################################################################
from aiohttp import web
from aiohttp import web_request
from trpc import context
from trpc.http import routes
import random

@routes.route("/test")
async def test_route(ctx: context.Context, request: web_request.Request) -> bytes:
    index = random.randrange(0, 100)%3                                             # 请求随机选择任务处理器
    logger.info(f"service: start request to task_test, index={index}, pid={os.getpid()}")
    r0 = await task.send_to_task(ctx, f"task_test_{index}", 100)               # 发送请求到任务处理器
    logger.info(f"service: finish request to task_test, index={index}, pid={os.getpid()}")
    return web.json_response({"data": "json", "index": index, "pid": r0})

######################################################################################################
from trpc.plugin import register_plugin
from trpc.plugin import PluginType
from trpc.plugin import Task
from trpc import task

def task_handler(reqs):
    # Handle the plugin task_test.
    logger.info(f"task: handler start, reqs={reqs}, pid={os.getpid()}")
    return [os.getpid()]

def task_initializer():
    # Initialize the plugin task_test.
    logger.info(f"task: initializer done, pid={os.getpid()}")

# See also https://git.woa.com/wslearn/trpc_batch_inference/blob/master/inference.py#L33
@register_plugin(plugin_type=PluginType.USER_DEFINED, plugin_name="task_test_0", task=Task(task_handler, initializer=task_initializer))
@register_plugin(plugin_type=PluginType.USER_DEFINED, plugin_name="task_test_1", task=Task(task_handler, initializer=task_initializer))
@register_plugin(plugin_type=PluginType.USER_DEFINED, plugin_name="task_test_2", task=Task(task_handler, initializer=task_initializer))
def task_load():
    # To load the plugin task_test.
    logger.info(f"task: load done, pid={os.getpid()}")

######################################################################################################
import trpc

def serve(conf_path: str):
    svr = trpc.new(conf_path)
    svr.serve()

def main():
    # 获取配置文件绝对路径
    config = os.path.abspath(os.path.join(os.path.dirname(__file__), "trpc_python.yaml"))
    logger.info(f"main: load config {config}, pid={os.getpid()}")
    serve(config)

if __name__ == '__main__':
    sys.exit(main())
