from flask import Flask, request, jsonify
from ai_change.utils.data_preprocess import *
from ai_change.model_static.n_sigma import *
app = Flask(__name__)

# def f(data):
#     # 计算输入数据的求和
#     total = sum(data)
#     # 返回True如果总和大于0，否则False
#     return total > 0

def f(data):
    cnt = 0
    data = data_preprocess(data)
    for i in range (6):
        cnt += test_speed_n_sigma(data, 2.2)
    return cnt
@app.route('/predict', methods=['POST'])
def predict():
    data = request.get_json()
    result = f(data)
    return jsonify({'处理点数': result})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
