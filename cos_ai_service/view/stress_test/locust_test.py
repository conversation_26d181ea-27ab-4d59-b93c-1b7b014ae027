import subprocess
import time
import psutil
import matplotlib.pyplot as plt
from locust import HttpUser, task, between, events
import threading
import json

# 全局变量用于监控系统资源
cpu_usage = []
memory_usage = []
gpu_usage = []
monitoring_active = False
monitoring_thread = None

txt_path = "waste/dataprod/case/n_9e064935-fc5f-45ec-9bcf-3b391b7135be.txt"
check_gpu = True


def get_zx_gpu_dused():
    """获取GPU使用率"""
    try:
        output = subprocess.check_output(['zx-smi']).decode("utf-8")
        lines = output.split('\n')
        gpu_dused = []
        for line in lines:
            if '%' in line:
                parts = line.split()
                dused = parts[9].replace('%', '')
                gpu_dused.append(float(dused))
        return gpu_dused
    except Exception as e:
        return []


def monitor_system():
    """系统资源监控线程"""
    global monitoring_active, cpu_usage, memory_usage, gpu_usage

    while monitoring_active:
        cpu_usage.append(psutil.cpu_percent())
        memory_usage.append(psutil.virtual_memory().percent)

        if check_gpu:
            try:
                gpu_data = get_zx_gpu_dused()
                if gpu_data:
                    gpu_usage.append(gpu_data)
            except Exception as e:
                pass

        time.sleep(0.1)  # 每100ms采样一次


class ChangeAnalysisUser(HttpUser):
    """Locust 用户类"""
    wait_time = between(1.8, 1.8)  # 请求间隔时间
    host = "http://127.0.0.1:6666"  # 目标服务器地址

    def on_start(self):
        """用户开始时执行"""
        # 读取测试数据并确保使用UTF-8编码
        try:
            with open(txt_path, 'r', encoding='utf-8') as f:
                self.test_data = f.read()
        except Exception as e:
            print(f"读取测试文件失败: {e}")
            self.test_data = "{}"

    @task
    def change_analysis_request(self):
        """发送变化分析请求"""
        headers = {
            'Content-Type': 'application/json; charset=utf-8'  # 明确指定UTF-8编码
        }

        try:
            # 将数据编码为UTF-8字节
            data_bytes = self.test_data.encode('utf-8')

            # 使用正确的 with 语句块处理响应
            with self.client.post(
                    "/trpc.test.rpchttp.ChangeAnalysis/ChangeAnal",
                    data=data_bytes,
                    headers=headers,
                    catch_response=True
            ) as response:
                # 在 with 块内处理响应
                if response.status_code == 200:
                    response.success()
                else:
                    response.failure(f"请求失败，状态码: {response.status_code}")

        except Exception as e:
            print(f"请求异常: {e}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """测试开始时的回调"""
    global monitoring_active, monitoring_thread, cpu_usage, memory_usage, gpu_usage

    # 清空之前的数据
    cpu_usage.clear()
    memory_usage.clear()
    gpu_usage.clear()

    # 开始系统监控
    monitoring_active = True
    monitoring_thread = threading.Thread(target=monitor_system, daemon=True)
    monitoring_thread.start()

    print("开始负载测试和系统监控...")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """测试结束时的回调"""
    global monitoring_active, cpu_usage, memory_usage, gpu_usage

    # 停止监控
    monitoring_active = False

    print(f"测试完成，总请求数: {environment.stats.total.num_requests}")
    print(f"平均响应时间: {environment.stats.total.avg_response_time:.2f}ms")
    print(f"失败率: {environment.stats.total.fail_ratio:.2%}")

    # 绘制系统资源使用图表
    if cpu_usage and memory_usage:
        plt.figure(figsize=(12, 8))

        plt.subplot(2, 1, 1)
        plt.plot(cpu_usage, label='CPU Usage (%)', color='blue')
        plt.plot(memory_usage, label='Memory Usage (%)', color='red')
        plt.xlabel('Time (samples)')
        plt.ylabel('Utilization (%)')
        plt.title('System Resource Utilization During Load Test')
        plt.legend()
        plt.grid(True)

        # GPU使用率图表
        if gpu_usage and check_gpu:
            plt.subplot(2, 1, 2)
            try:
                # 假设只显示第一个GPU的数据
                gpu_data = [data[0] if data else 0 for data in gpu_usage]
                plt.plot(gpu_data, label='GPU 0 Usage (%)', color='green')
                plt.xlabel('Time (samples)')
                plt.ylabel('GPU Utilization (%)')
                plt.title('GPU Utilization During Load Test')
                plt.legend()
                plt.grid(True)
            except Exception as e:
                print(f"GPU数据绘制失败: {e}")

        plt.tight_layout()
        plt.savefig('view/stress_test/locust_system_utilization.png', dpi=300, bbox_inches='tight')
        print("系统资源使用图表已保存为 locust_system_utilization.png")


if __name__ == "__main__":
    # 这里可以添加一些配置或直接运行测试的代码
    print("请使用以下命令运行 Locust 测试:")
    print("locust -f locust_test.py --host=http://127.0.0.1:6666")
    print("")
    print("或者使用命令行参数直接运行:")
    print(
        "locust -f locust_test.py --host=http://127.0.0.1:6666 --users=3000 --spawn-rate=100 --run-time=60s --headless")
