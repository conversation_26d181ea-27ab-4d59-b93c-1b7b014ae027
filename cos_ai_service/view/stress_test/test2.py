import subprocess
import concurrent.futures
import time
from tqdm import tqdm
import psutil
import matplotlib.pyplot as plt

txt_path = "/data/workspace/cos_ai_service/waste/dataprod/case/emptyReply.txt"
check_gpu = False


def task():
    # 若没收到数据，将metric_data.txt改为绝对路径
    cmd = f"curl -X POST -d @{txt_path} -H 'Content-Type: application/json' http://127.0.0.1:6666/trpc.test.rpchttp.ChangeAnalysis/ChangeAnal"
    process = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    return process.stdout


def get_zx_gpu_dused():
    output = subprocess.check_output(['zx-smi']).decode("utf-8")
    lines = output.split('\n')
    gpu_dused = []
    for line in lines:
        if '%' in line:
            parts = line.split()
            dused = parts[9].replace('%', '')
            gpu_dused.append(float(dused))
    return gpu_dused


if __name__ == "__main__":
    con_num = 1
    start = time.time()
    cpu_usage = []
    memory_usage = []
    gpu_usage = []
    if not check_gpu:
        print(task())
    else:
        with concurrent.futures.ThreadPoolExecutor(max_workers=con_num) as executor:
            for i in tqdm(range(con_num)):
                future = executor.submit(task)
                # print(future.result())
                # 监测CPU利用率、内存利用率、gpu利用率
                cpu_usage.append(psutil.cpu_percent())
                memory_usage.append(psutil.virtual_memory().percent)
                try:
                    gpu_usage.append(get_zx_gpu_dused())
                except Exception as e:
                    print("gpu利用率获取失败")
        print(f"并发数{con_num}, 耗时{time.time() - start}")
        # 绘制曲线
        plt.figure()
        plt.plot(cpu_usage, label='CPU')
        plt.plot(memory_usage, label='Memory')
        try:
            plt.plot(gpu_usage[0], label=f'GPU {0} dused')
        except Exception as e:
            pass
        plt.xlabel('Time')
        plt.ylabel('Utilization (%)')
        # plt.title('System Utilization During Execution')
        plt.legend()
        plt.savefig('view/stress_test/system_utilization.png')
